using Kantoku.Api.Factories.Response;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Dtos.Common;
using Kantoku.Api.Filters.Domains;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class CommonController : BaseController
{
    private readonly ICommonService commonService;

    public CommonController(
        ITResponseFactory responseFactory,
        ICommonService commonService
    ) : base(responseFactory)
    {
        this.commonService = commonService;
    }

    /// <summary>
    /// Get all request types
    /// </summary>
    /// <returns>List of request types</returns>
    [HttpGet("request-types")]
    public async Task<GeneralResponse<RequestTypesResponseDto>> GetRequestTypes()
    {
        try
        {
            var result = await commonService.GetAllRequestTypes();
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<RequestTypesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get all leave types
    /// </summary>
    /// <returns>List of leave types</returns>
    [HttpGet("leave-types")]
    public async Task<GeneralResponse<LeaveTypesResponseDto>> GetLeaveTypes()
    {
        try
        {
            var result = await commonService.GetAllLeaveTypes();
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<LeaveTypesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get all statuses
    /// </summary>
    /// <returns>List of all statuses</returns>
    [HttpGet("status")]
    public async Task<GeneralResponse<StatusesResponseDto>> GetStatus()
    {
        try
        {
            var result = await commonService.GetStatusByGroup(GroupConstant.REQUEST);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<StatusesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get all request statuses
    /// </summary>
    /// <returns>List of request statuses</returns>
    [HttpGet("status/request")]
    public async Task<GeneralResponse<StatusesResponseDto>> GetRequestStatus()
    {
        try
        {
            var result = await commonService.GetStatusByGroup(GroupConstant.REQUEST);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<StatusesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get all project statuses
    /// </summary>
    /// <returns>List of project statuses</returns>
    [HttpGet("status/project")]
    public async Task<GeneralResponse<StatusesResponseDto>> GetProjectStatus()
    {
        try
        {
            var result = await commonService.GetStatusByGroup(GroupConstant.PROJECT);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<StatusesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get all employee statuses
    /// </summary>
    /// <returns>List of employee statuses</returns>
    [HttpGet("status/employee")]
    public async Task<GeneralResponse<StatusesResponseDto>> GetEmployeeStatus()
    {
        try
        {
            var result = await commonService.GetStatusByGroup(GroupConstant.EMPLOYEE);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<StatusesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get all project types
    /// </summary>
    /// <returns>List of project types</returns>
    [HttpGet("project-types")]
    public async Task<GeneralResponse<ProjectTypesResponseDto>> GetProjectTypes()
    {
        try
        {
            var result = await commonService.GetAllProjectTypes();
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ProjectTypesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get all customer types
    /// </summary>
    /// <returns>List of customer types</returns>
    [HttpGet("customer-types")]
    public async Task<GeneralResponse<CustomerTypesResponseDto>> GetCustomerTypes([FromQuery] CustomerTypeFilter filter)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest<CustomerTypesResponseDto>();
            }
            var result = await commonService.GetAllCustomerTypes(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<CustomerTypesResponseDto>(e.ErrorCode);
        }
    }
}