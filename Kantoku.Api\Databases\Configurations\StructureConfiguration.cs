using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class StructureConfiguration(string schema) : IEntityTypeConfiguration<Structure>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Structure> builder)
    {
        builder.ToTable("structure", Schema, tb => tb.<PERSON>om<PERSON>("Phòng ban"));

        builder.Has<PERSON>ey(e => e.StructureUid).HasName("structure_pkey");

        builder.Property(e => e.StructureUid)
            .HasColumnName("structure_uid");
        builder.Property(e => e.StructureCode)
            .HasColumnName("structure_code");
        builder.Property(e => e.StructureName)
            .HasColumnName("structure_name");
        builder.Property(e => e.StructureParentUid)
            .HasColumnName("parent_uid");
        builder.Property(e => e.<PERSON>)
            .HasDefaultValue(false)
            .HasColumnName("is_hidden");
        builder.Property(e => e.IsDefault)
            .HasDefaultValue(false)
            .HasColumnName("is_default");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");

        builder.HasOne(d => d.Org)
            .WithMany(p => p.Structures)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("structure_org_id_fkey");

        builder.HasOne(d => d.Parent)
            .WithMany(p => p.Children)
            .HasForeignKey(d => d.StructureParentUid)
            .HasConstraintName("structure_structure_parent_id_fkey");
    }
} 