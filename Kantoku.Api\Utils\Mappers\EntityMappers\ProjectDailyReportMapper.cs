// using Kantoku.Api.Databases.Models;
// using Kantoku.Api.Dtos.Project.Request;
// using Kantoku.Api.Dtos.Project.Response;
// using Kantoku.Api.Utils.Helpers;

// namespace Kantoku.Api.Utils.Mappers.EntityMappers;

// public static class ProjectDailyReportMapper
// {
//     #region Entity to DTO mappings

//     /// <summary>
//     /// Maps a ProjectDailyReport entity to a ProjectDailyReportResponseDto
//     /// </summary>
//     public static ProjectDailyReportResponseDto ToProjectDailyReportResponseDto(
//         this ProjectDailyReport report,
//         IEnumerable<OutSource>? outSources = null,
//         IEnumerable<Employee>? employees = null,
//         IEnumerable<EmployeeRank>? employeeRanks = null,
//         string employeeRankingDefinitionType = "")
//     {
//         if (report == null)
//             return new ProjectDailyReportResponseDto();

//         return new ProjectDailyReportResponseDto
//         {
//             ReportId = report.ProjectDailyReportUid.ToString(),
//             ProjectId = report.ProjectUid.ToString(),
//             ProjectCode = report.Project?.ProjectCode,
//             ProjectName = report.Project?.ProjectName,
//             Address = report.Project?.Address,
//             ReportDate = report.ReportDate.ToString("yyyy-MM-dd"),
//             Description = report.Description,
//             EmployeeWorkloads = MapEmployeeWorkloads(report.EmployeeWorkload, employees, employeeRanks, employeeRankingDefinitionType),
//             OutSourceWorkloads = MapOutSourceWorkloads(report.OutSourceWorkload, outSources)
//         };
//     }

//     /// <summary>
//     /// Maps a collection of ProjectDailyReport entities to a ProjectDailyReportsResponseDto
//     /// </summary>
//     public static ProjectDailyReportsResponseDto ToProjectDailyReportsResponseDto(
//         this IEnumerable<ProjectDailyReport> reports,
//         int pageNum,
//         int pageSize,
//         int totalRecords,
//         IEnumerable<OutSource>? outSources = null,
//         IEnumerable<Employee>? employees = null,
//         IEnumerable<EmployeeRank>? employeeRanks = null,
//         string employeeRankingDefinitionType = "")
//     {
//         if (reports == null)
//             return new ProjectDailyReportsResponseDto();

//         return new ProjectDailyReportsResponseDto
//         {
//             Items = reports.Select(r => r.ToProjectDailyReportResponseDto(
//                 outSources,
//                 employees,
//                 employeeRanks,
//                 employeeRankingDefinitionType)),
//             PageNum = pageNum,
//             PageSize = pageSize,
//             TotalRecords = totalRecords
//         };
//     }

//     private static IEnumerable<EmployeeWorkloadResponseDto> MapEmployeeWorkloads(
//         IEnumerable<EmployeeWorkload>? workloads,
//         IEnumerable<Employee>? employees,
//         IEnumerable<EmployeeRank>? employeeRanks,
//         string employeeRankingDefinitionType)
//     {
//         if (workloads == null || !workloads.Any())
//             return Enumerable.Empty<EmployeeWorkloadResponseDto>();

//         var result = new List<EmployeeWorkloadResponseDto>();

//         foreach (var workload in workloads)
//         {
//             var employee = employees?.FirstOrDefault(e => e.EmployeeUid == workload.EmployeeUid);
            
//             // Find the employee's rank
//             string? rankingName = null;
//             if (employee != null && employeeRanks != null)
//             {
//                 var employeeRank = employeeRanks.FirstOrDefault(er => 
//                     er.EmployeeUid == employee.EmployeeUid && 
//                     er.DefinitionType == employeeRankingDefinitionType);
//                 rankingName = employeeRank?.Ranking?.RankingName;
//             }

//             result.Add(new EmployeeWorkloadResponseDto
//             {
//                 EmployeeId = workload.EmployeeUid.ToString(),
//                 EmployeeName = employee?.EmployeeName,
//                 RankingName = rankingName,
//                 WorkloadOnMainConstruction = workload.MainConsWorkload,
//                 WorkloadOnSubConstruction = workload.SubConsWorkload
//             });
//         }

//         return result;
//     }

//     private static IEnumerable<OutSourceWorkloadResponseDto> MapOutSourceWorkloads(
//         IEnumerable<OutSourceWorkload>? workloads,
//         IEnumerable<OutSource>? outSources)
//     {
//         if (workloads == null || !workloads.Any())
//             return Enumerable.Empty<OutSourceWorkloadResponseDto>();

//         var result = new List<OutSourceWorkloadResponseDto>();

//         foreach (var workload in workloads)
//         {
//             var outSource = outSources?.FirstOrDefault(os => os.OutSourceUid == workload.OutSourceUid);
            
//             result.Add(new OutSourceWorkloadResponseDto
//             {
//                 OutSourceId = workload.OutSourceUid.ToString(),
//                 OutSourceName = outSource?.OutSourceName,
//                 WorkloadOnMainConstruction = workload.MainConsWorkload,
//                 WorkloadOnSubConstruction = workload.SubConsWorkload
//             });
//         }

//         return result;
//     }

//     #endregion

//     #region DTO to Entity mappings

//     /// <summary>
//     /// Maps a CreateDailyReportRequestDto to a ProjectDailyReport entity
//     /// </summary>
//     public static ProjectDailyReport? ToEntity(this CreateDailyReportRequestDto dto, Guid projectUid, Guid orgUid)
//     {
//         if (dto == null)
//             return null;

//         DateOnly reportDate;
//         if (!DateOnly.TryParse(dto.ReportDate, out reportDate))
//             return null;

//         var entity = new ProjectDailyReport
//         {
//             ProjectDailyReportUid = GuidHelper.GenerateUUIDv7(),
//             ProjectUid = projectUid,
//             OrgUid = orgUid,
//             ReportDate = reportDate,
//             Description = dto.Description,
//             IsDeleted = false,
//             IsApproved = false
//         };

//         // Map employee workload if exists
//         if (dto.EmployeeWorkload != null && dto.EmployeeWorkload.Any())
//         {
//             entity.EmployeeWorkload = dto.EmployeeWorkload.Select(ew => new EmployeeWorkload
//             {
//                 EmployeeUid = Guid.Parse(ew.EmployeeId),
//                 MainConsWorkload = ew.WorkloadOnMainConstruction,
//                 SubConsWorkload = ew.WorkloadOnSubConstruction
//             }).ToList();
//         }

//         // Map outsource workload if exists
//         if (dto.OutSourceWorkload != null && dto.OutSourceWorkload.Any())
//         {
//             entity.OutSourceWorkload = dto.OutSourceWorkload.Select(ow => new OutSourceWorkload
//             {
//                 OutSourceUid = Guid.Parse(ow.OutSourceId),
//                 MainConsWorkload = ow.WorkloadOnMainConstruction,
//                 SubConsWorkload = ow.WorkloadOnSubConstruction
//             }).ToList();
//         }

//         return entity;
//     }

//     /// <summary>
//     /// Updates a ProjectDailyReport entity from an UpdateDailyReportRequestDto
//     /// </summary>
//     public static void UpdateFromDto(this ProjectDailyReport entity, UpdateDailyReportRequestDto dto)
//     {
//         if (entity == null || dto == null)
//             return;

//         if (dto.Description != null)
//             entity.Description = dto.Description;

//         // Update employee workload if exists
//         if (dto.EmployeeWorkload != null)
//         {
//             entity.EmployeeWorkload = dto.EmployeeWorkload.Select(ew => new EmployeeWorkload
//             {
//                 EmployeeUid = Guid.Parse(ew.EmployeeId),
//                 MainConsWorkload = ew.WorkloadOnMainConstruction,
//                 SubConsWorkload = ew.WorkloadOnSubConstruction
//             }).ToList();
//         }

//         // Update outsource workload if exists
//         if (dto.OutSourceWorkload != null)
//         {
//             entity.OutSourceWorkload = dto.OutSourceWorkload.Select(ow => new OutSourceWorkload
//             {
//                 OutSourceUid = Guid.Parse(ow.OutSourceId),
//                 MainConsWorkload = ow.WorkloadOnMainConstruction,
//                 SubConsWorkload = ow.WorkloadOnSubConstruction
//             }).ToList();
//         }
//     }

//     #endregion
// } 