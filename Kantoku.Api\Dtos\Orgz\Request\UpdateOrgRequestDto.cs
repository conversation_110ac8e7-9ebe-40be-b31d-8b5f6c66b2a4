using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.Orgz.Request;

public class UpdateOrgRequestDto
{
    /// <summary>
    /// Organization code 
    /// </summary>
    public string? OrgCode { get; set; }

    /// <summary>
    /// Organization name
    /// </summary>
    public string? OrgName { get; set; }

    /// <summary>
    /// Organization sub name
    /// </summary>
    public string? OrgSubName { get; set; }

    /// <summary>
    /// Organization postal code
    /// </summary>
    public string? PostalCode { get; set; }

    /// <summary>
    /// Organization address
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Organization phone number
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Organization email
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Organization fax
    /// </summary>
    public string? Fax { get; set; }

    /// <summary>
    /// Organization website
    /// </summary>
    public string? Website { get; set; }

    /// <summary>
    /// Organization registration number
    /// </summary>
    public string? RegistrationNumber { get; set; }

    /// <summary>
    /// Organization registration date
    /// </summary>
    public string? RegistrationDate { get; set; }

    /// <summary>
    /// Organization registration license type
    /// </summary>
    public bool? RegistrationLicenseType { get; set; }

    /// <summary>
    /// Organization legal organization number
    /// </summary>
    public string? LegalOrgNumber { get; set; }

    /// <summary>
    /// Organization legal tax number
    /// </summary>
    public string? LegalTaxNumber { get; set; }

    /// <summary>
    /// Organization legal representative
    /// </summary>
    public string? LegalRepresentative { get; set; }

    /// <summary>
    /// Organization description
    /// </summary>
    public string? Description { get; set; }
}
