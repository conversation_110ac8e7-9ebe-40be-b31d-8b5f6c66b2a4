﻿using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Dtos.Base.Response;

namespace Kantoku.Api.Dtos.PaymentType.Response;

/// <summary>
/// Represents the response data for a payment type.
/// </summary>
public class PaymentTypeResponseDto : BaseResponseDto
{
    /// <summary>
    ///  the unique identifier for the payment type. (*)
    /// </summary>
    public string? PaymentTypeId { get; set; }

    /// <summary>
    ///  the name of the payment type. (*)
    /// </summary>
    public string? PaymentTypeName { get; set; }

    /// <summary>
    ///  the description of the payment type.
    /// </summary>
    public string? Description { get; set; }
}

