using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.Employee.Request;

public class CreateEmployeeRequestDto
{
    /// <summary>
    /// Employee's email address (*)
    /// </summary>
    [Required]
    public string Email { get; set; } = null!;

    /// <summary>
    /// Unique employee code (*)
    /// </summary>
    [Required]
    public string EmployeeCode { get; set; } = null!;

    /// <summary>
    /// Employee's personal information
    /// </summary>
    public CreateEmployeeInfoRequestDto? EmployeeInfo { get; set; }

    /// <summary>
    /// List of employee's email addresses
    /// </summary>
    public IEnumerable<CreateEmployeeMailRequestDto>? EmployeeMails { get; set; }

    /// <summary>
    /// List of employee's phone numbers
    /// </summary>
    public IEnumerable<CreateEmployeePhoneRequestDto>? EmployeePhones { get; set; }

    /// <summary>
    /// Employee's residential address
    /// </summary>
    public string? EmployeeAddress { get; set; }

    /// <summary>
    /// ID of the organizational structure
    /// </summary>
    public string? StructureId { get; set; }

    /// <summary>
    /// ID of the employee's position
    /// </summary>
    public string? PositionId { get; set; }

    /// <summary>
    /// Type of employee
    /// </summary>
    public bool? EmployeeType { get; set; }

    /// <summary>
    /// Employee's salary in day (*)
    /// </summary>      
    [Required]
    public int SalaryInDay { get; set; }

    /// <summary>   
    /// Employee's salary in hour (*)
    /// </summary>
    [Required]
    public int SalaryInHour { get; set; }

    /// <summary>
    /// Employee's salary in month (*)
    /// </summary>
    [Required]
    public int SalaryInMonth { get; set; }

    /// <summary>
    /// Current working status of the employee
    /// </summary>
    public string? WorkingStatus { get; set; }
    
    /// <summary>
    /// Standard working hours of the employee
    /// </summary>
    public int StandardWorkingHours { get; set; }

    /// <summary>
    /// Employee's active working date
    /// </summary>
    [DateTimeValidator(typeof(DateOnly))]
    [JsonPropertyName("workingFromDate")]
    public string? ActiveDate { get; set; }

    /// <summary>
    /// List of role IDs assigned to the employee (*)
    /// </summary>
    [Required]
    public IEnumerable<string> RoleIds { get; set; } = [];

    /// <summary>
    /// Base leave days allocated to the employee
    /// </summary>
    public float? BaseLeave { get; set; }

    /// <summary>
    /// Expiration date of base leave
    /// </summary>
    [DateTimeValidator(typeof(DateOnly))]
    public string? BaseLeaveExpire { get; set; }
}

public class CreateEmployeeInfoRequestDto
{
    /// <summary>
    /// Full name of the employee
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// Gender of the employee
    /// </summary>
    public bool? Gender { get; set; }

    /// <summary>
    /// Date of birth
    /// </summary>
    public string? BirthDate { get; set; }

    /// <summary>
    /// Place of birth
    /// </summary>
    public string? BirthPlace { get; set; }
}

public class CreateEmployeeMailRequestDto
{
    /// <summary>
    /// Email address
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Indicates if this is the primary email
    /// </summary>
    public bool? IsPrimary { get; set; }
}

public class CreateEmployeePhoneRequestDto
{
    /// <summary>
    /// Phone number
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// Indicates if this is the primary phone number
    /// </summary>
    public bool? IsPrimary { get; set; }
}