using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Orgz = Kantoku.Api.Databases.Models.Org;

namespace Kantoku.Api.Databases.Configurations;

public class OrgConfiguration(string schema) : IEntityTypeConfiguration<Orgz>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Orgz> builder)
    {
        builder.HasKey(e => e.OrgUid).HasName("org_pkey");

        builder.ToTable("org", Schema);

        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.OrgCode)
            .HasColumnName("org_code");
        builder.Property(e => e.OrgName)
            .HasColumnName("org_name");
        builder.Property(e => e.OrgSubName)
            .HasColumnName("org_sub_name");
        builder.Property(e => e.PostalCode)
            .HasColumnName("postal_code");
        builder.Property(e => e.Address)
            .HasColumnName("org_address");
        builder.Property(e => e.PhoneNumber)
            .HasColumnName("phone_number");
        builder.Property(e => e.Email)
            .HasColumnName("email");
        builder.Property(e => e.Fax)
            .HasColumnName("fax");
        builder.Property(e => e.Website)
            .HasColumnName("website");
        builder.Property(e => e.RegistrationNumber)
            .HasColumnName("registration_number");
        builder.Property(e => e.RegistrationDate)
            .HasColumnName("registration_date");
        builder.Property(e => e.RegistrationLicenseType)
            .HasColumnName("registration_license_type");
        builder.Property(e => e.LegalOrgNumber)
            .HasColumnName("legal_org_number");
        builder.Property(e => e.LegalTaxNumber)
            .HasColumnName("legal_tax_number");
        builder.Property(e => e.LegalRepresentative)
            .HasColumnName("legal_representative");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.LogoUrl)
            .HasColumnName("logo_url");
        builder.Property(e => e.EnableAutoCheckOut)
            .HasColumnName("enable_auto_check_out");
        builder.Property(e => e.EmployeeRankingDefinitionType)
            .HasColumnName("employee_ranking_definition_type");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
    }
}
