﻿using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Filters.Domains;

public class InputCostFilter : BaseFilter
{
    [FromQuery(Name = "keyword")]
    public string? Keyword { get; set; }

    [FromQuery(Name = "projectId")]
    public Guid? ProjectId { get; set; }

    [FromQuery(Name = "constructionId")]
    public Guid? ConstructionId { get; set; }

    [FromQuery(Name = "vendorId")]
    public Guid? VendorId { get; set; }

    [FromQuery(Name = "title")]
    public string? Title { get; set; }

    [FromQuery(Name = "paymentTypeId")]
    public Guid? PaymentTypeId { get; set; }

    [FromQuery(Name = "entryTypeId")]
    public Guid? EntryTypeId { get; set; }

    [FromQuery(Name = "issueDateFrom")]
    public string? IssueDateFrom { get; set; }

    [FromQuery(Name = "issueDateTo")]
    public string? IssueDateTo { get; set; }

    [FromQuery(Name = "paymentDateFrom")]
    public string? PaymentDateFrom { get; set; }

    [FromQuery(Name = "paymentDateTo")]
    public string? PaymentDateTo { get; set; }

    [FromQuery(Name = "totalAmountMin")]
    public string? TotalAmountMin { get; set; }

    [FromQuery(Name = "totalAmountMax")]
    public string? TotalAmountMax { get; set; }
}
