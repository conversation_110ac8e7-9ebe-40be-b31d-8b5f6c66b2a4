using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.Schedule.Response;

public class ScheduledEmployeeShiftResponsesDto
{
    [JsonPropertyName("items")]
    public IEnumerable<ScheduledEmployeeShiftResponseDto>? Items { get; set; }
}

public class ScheduledEmployeeShiftResponseDto
{
    [JsonPropertyName("employeeShiftId")]
    public string? EmployeeShiftId { get; set; }

    [JsonPropertyName("projectScheduleId")]
    public string? ProjectScheduleId { get; set; }

    [JsonPropertyName("employeeName")]
    public string? EmployeeName { get; set; }

    [JsonPropertyName("employeeCode")]
    public string? EmployeeCode { get; set; }

    [JsonPropertyName("workingDate")]
    public string? WorkingDate { get; set; }

    [JsonPropertyName("scheduledStartTime")]
    public string? ScheduledStartTime { get; set; }

    [JsonPropertyName("scheduledEndTime")]
    public string? ScheduledEndTime { get; set; }

    [JsonPropertyName("totalScheduledWorkTime")]
    public float? TotalScheduledWorkTime { get; set; }

    [JsonPropertyName("assignedWorkload")]
    public float? AssignedWorkload { get; set; }

    [JsonPropertyName("workingRole")]
    public string? WorkingRole { get; set; }
}