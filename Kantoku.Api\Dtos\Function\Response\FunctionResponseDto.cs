using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.Function.Response;

public class FunctionResponseDto : SimpleFunctionResponseDto
{
    [JsonPropertyName("icon")]
    public string? Icon { get; set; }

    [JsonPropertyName("path")]
    public string? FunctionUrl { get; set; }

    [JsonPropertyName("component")] 
    public string? Component { get; set; }

    [JsonPropertyName("redirect")]
    public string? Redirect { get; set; }

    [JsonPropertyName("parentId")]
    public string? ParentId { get; set; }

    [JsonPropertyName("hideInBreadcrumb")]
    public bool? HideInBreadcrumb { get; set; }

    [Json<PERSON>ropertyName("hideChildrenInMenu")]
    public bool? HideChildrenInMenu { get; set; }

    [JsonPropertyName("locale")]
    public string? Locale { get; set; }

    [JsonPropertyName("displayOrder")]
    public short? DisplayOrder { get; set; }
}
