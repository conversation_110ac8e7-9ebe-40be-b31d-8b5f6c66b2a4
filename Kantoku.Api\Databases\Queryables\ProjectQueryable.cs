using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class ProjectQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedCost { get; set; } = false;
    public bool IncludedProjectType { get; set; } = false;
    public bool IncludedContractor { get; set; } = false;
    public bool IncludedCustomer { get; set; } = false;
    public bool IncludedRequests { get; set; } = false;
    public bool IncludedProjectSchedules { get; set; } = false;
    public bool IncludedEmployeeShifts { get; set; } = false;
    public bool IncludedManagers { get; set; } = false;
    public bool IncludedProjectWorkShifts { get; set; } = false;
    public bool IncludedConstructions { get; set; } = false;
    public bool IncludedDailyReports { get; set; } = false;
    public bool IncludedProjectRankingCosts { get; set; } = false;

    public ProjectQueryableOptions TrackingOptions()
    {
        IsTracking = true;
        return this;
    }

    public ProjectQueryableOptions SplitQueryOptions()  
    {
        IsSplitQuery = true;
        return this;
    }

}

public interface IProjectQueryable
{
    IQueryable<Project> GetProjectsQuery(
        ProjectQueryableOptions options
    );
    IQueryable<Project> GetProjectsQueryIncluded(
        ProjectQueryableOptions options,
        IQueryable<Project>? query = null
    );
    IQueryable<Project> GetProjectsQueryFiltered(
        ProjectQueryableOptions options,
        ProjectFilter filter,
        IQueryable<Project>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class ProjectQueryable(PostgreDbContext context) :
    BaseQueryable<Project>(context), IProjectQueryable
{
    public IQueryable<Project> GetProjectsQuery(
        ProjectQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<Project> GetProjectsQueryIncluded(
        ProjectQueryableOptions options,
        IQueryable<Project>? query = null
    )
    {
        query ??= GetProjectsQuery(options);
        query = query.Include(p => p.Status);
        query = query.Include(p => p.ProjectType);
        if (options.IncludedProjectType || options.IncludedAll)
        {
            query = query.Include(p => p.ProjectType);
        }
        if (options.IncludedManagers || options.IncludedAll)
        {
            query = query.Include(p => p.Managers)
                .ThenInclude(pm => pm.Employee);
        }
        if (options.IncludedRequests || options.IncludedAll)
        {
            query = query.Include(p => p.Requests)
                .ThenInclude(r => r.Approver1);
            query = query.Include(p => p.Requests)
                .ThenInclude(r => r.Approver2);
            query = query.Include(p => p.Requests)
                .ThenInclude(r => r.Author);
        }
        if (options.IncludedProjectWorkShifts || options.IncludedAll)
        {
            query = query.Include(p => p.ProjectWorkShifts)
                .ThenInclude(pws => pws.WorkShift);
        }
        if (options.IncludedProjectSchedules || options.IncludedAll)
        {
            query = query.Include(p => p.ProjectSchedules)
                .ThenInclude(ps => ps.EmployeeShifts)
                    .ThenInclude(es => es.Employee);
            query = query.Include(p => p.ProjectSchedules)
                .ThenInclude(ps => ps.OutSourceShifts)
                    .ThenInclude(os => os.OutSource);
            query = query.AsSplitQuery();
        }
        if (options.IncludedEmployeeShifts || options.IncludedAll)
        {
            query = query.Include(p => p.EmployeeShifts)
                .ThenInclude(es => es.Employee);
        }
        if (options.IncludedConstructions || options.IncludedAll)
        {
            query = query.Include(p => p.Constructions)
                .ThenInclude(c => c.ConstructionCosts)
                    .ThenInclude(cc => cc.CategorizedCosts);
        }
        if (options.IncludedCustomer || options.IncludedAll)
        {
            query = query.Include(p => p.Customer);
        }
        if (options.IncludedContractor || options.IncludedAll)
        {
            query = query.Include(p => p.Contractor);
        }
        if (options.IncludedDailyReports || options.IncludedAll)
        {
            query = query.Include(p => p.ProjectDailyReports);
        }
        if (options.IncludedProjectRankingCosts || options.IncludedAll)
        {
            query = query.Include(p => p.ProjectRankingCosts)
                .ThenInclude(prc => prc.Ranking);
        }
        return query;
    }

    public IQueryable<Project> GetProjectsQueryFiltered(
        ProjectQueryableOptions options,
        ProjectFilter filter,
        IQueryable<Project>? query = null
    )
    {
        query ??= GetProjectsQueryIncluded(options);

        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(p => p.OrgUid == filter.OrgId);
        }
        if (filter.TypeId is not null && filter.TypeId != Guid.Empty)
        {
            query = query.Where(p => p.ProjectTypeUid == filter.TypeId);
        }
        if (!string.IsNullOrEmpty(filter.StatusCode))
        {
            query = query.Where(p => p.StatusCode == filter.StatusCode);
        }
        if (!string.IsNullOrEmpty(filter.ProjectName))
        {
            query = query.Where(p => p.ProjectName.Contains(filter.ProjectName) || p.ProjectCode.Contains(filter.ProjectName));
        }
        if (!string.IsNullOrEmpty(filter.Keyword))
        {
            query = query.Where(p => p.ProjectName.Contains(filter.Keyword)
            || p.ProjectCode.Contains(filter.Keyword));
        }
        if (!string.IsNullOrEmpty(filter.ExStartDate))
        {
            var expectedStartDate = DateOnly.Parse(filter.ExStartDate);
            query = query.Where(p => p.ExpectedStartDate >= expectedStartDate);
        }
        if (!string.IsNullOrEmpty(filter.ExEndDate))
        {
            var expectedEndDate = DateOnly.Parse(filter.ExEndDate);
            query = query.Where(p => p.ExpectedEndDate <= expectedEndDate);
        }
        if (!string.IsNullOrEmpty(filter.ActStartDate))
        {
            var actualStartDate = DateOnly.Parse(filter.ActStartDate);
            query = query.Where(p => p.ActualStartDate >= actualStartDate);
        }
        if (!string.IsNullOrEmpty(filter.ActEndDate))
        {
            var actualEndDate = DateOnly.Parse(filter.ActEndDate);
            query = query.Where(p => p.ActualEndDate <= actualEndDate);
        }
        if (filter.BudgetMin.HasValue)
        {
            query = query.Where(p => p.InitialBudget >= filter.BudgetMin.Value || p.InitialBudget == null);
        }
        if (filter.BudgetMax.HasValue)
        {
            query = query.Where(p => p.InitialBudget <= filter.BudgetMax.Value || p.InitialBudget == null);
        }
        if (filter.CostMin.HasValue)
        {
            query = query.Where(p => p.ActualBudget >= filter.CostMin.Value || p.ActualBudget == null);
        }
        if (filter.CostMax.HasValue)
        {
            query = query.Where(p => p.ActualBudget <= filter.CostMax.Value || p.ActualBudget == null);
        }
        if (filter.ContractorUid is not null && filter.ContractorUid != Guid.Empty)
        {
            query = query.Where(p => p.ContractorUid == filter.ContractorUid);
        }
        if (filter.CustomerUid is not null && filter.CustomerUid != Guid.Empty)
        {
            query = query.Where(p => p.CustomerUid == filter.CustomerUid);
        }
        return query;
    }
}


