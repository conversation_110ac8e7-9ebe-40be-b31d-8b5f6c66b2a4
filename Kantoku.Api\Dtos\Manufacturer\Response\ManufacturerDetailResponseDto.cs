﻿using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Base.Response;

namespace Kantoku.Api.Dtos.Manufacturer.Response;

/// <summary>
/// Data transfer object for manufacturer details
/// </summary>
public class ManufacturerDetailResponseDto : ManufacturerResponseDto
{
    /// <summary>
    ///  the list of items
    /// </summary>
    public IEnumerable<EquipmentResponseDto>? Items { get; set; }

    /// <summary>
    /// Page number of the item list
    /// </summary>
    public int ItemListPageNum { get; set; }

    /// <summary>
    /// Page size of the item list
    /// </summary>
    public int ItemListPageSize { get; set; }

    /// <summary>
    /// Total records of the item list
    /// </summary>
    public int ItemListTotalRecords { get; set; }
}

public class EquipmentResponseDto
{
    /// <summary>
    ///  the equipment unique identifier
    /// </summary>
    public string? EquipmentId { get; set; }

    /// <summary>
    ///  the equipment code
    /// </summary>
    public string? EquipmentCode { get; set; }

    /// <summary>
    ///  the equipment name
    /// </summary>
    public string? EquipmentName { get; set; }

    /// <summary>
    ///  the equipment sub name
    /// </summary>
    public string? EquipmentSubName { get; set; }

    /// <summary>
    ///  the category name
    /// </summary>
    public string? CategoryName { get; set; }

    /// <summary>
    ///  the size
    /// </summary>
    public string? Size { get; set; }

    /// <summary>
    ///  the serial number
    /// </summary>
    public string? SerialNumber { get; set; }

    /// <summary>    
    ///  the model
    /// </summary>
    public string? Model { get; set; }

    /// <summary>
    ///  the fuel consumption
    /// </summary>
    public string? FuelConsumption { get; set; }

    /// <summary>
    ///  the equipment status
    /// </summary>
    public string? EquipmentStatus { get; set; }

    /// <summary>
    ///  the last maintenance date
    /// </summary>
    public string? LastMaintenanceDate { get; set; }

    /// <summary>
    ///  the description
    /// </summary>
    public string? Description { get; set; }
}

