using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.FunctionAccessibility.Response;

public class FunctionPrivilegesResponseDto
{
    [JsonPropertyName("Items")]
    public IEnumerable<FunctionPrivilegeResponseDto>? Items { get; set; }
}

public class FunctionPrivilegeResponseDto : SimpleFunctionPrivilegeResponseDto
{
    [JsonPropertyName("RoleId")]
    public string? RoleId { get; set; }

    [JsonPropertyName("IsHeader")]
    public bool IsHeader { get; set; }
}