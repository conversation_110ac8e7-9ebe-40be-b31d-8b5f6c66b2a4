using System.Text.Json.Serialization;
using Kantoku.Api.Dtos.Role;

namespace Kantoku.Api.Dtos.Auth.Response;

public class UserLoginResponseDto
{
    public string? EmployeeId { get; set; }

    public string? LoginId { get; set; }

    public string? Name { get; set; }

    public string? Email { get; set; }

    public string? Address { get; set; }

    public string? Phone { get; set; }

    public bool? Gender { get; set; }

    public string? Birthday { get; set; }

    public string? OrgId { get; set; }

    public string? OrgCode { get; set; }

    public string? OrgName { get; set; }

    public bool? FirstChangePass { get; set; }

    public bool? IsManager { get; set; }

    public string? AccessToken { get; set; }

    public string? RefreshToken { get; set; }
}
