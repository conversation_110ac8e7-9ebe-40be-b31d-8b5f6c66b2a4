﻿using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Models;

public class RoleFunction : AuditableEntity
{
    public Guid FunctionUid { get; set; }

    public Guid RoleUid { get; set; }

    [AuditProperty]
    public bool CanRead { get; set; } = true;

    [AuditProperty]
    public bool CanCreate { get; set; } = false;

    [AuditProperty]
    public bool CanUpdate { get; set; } = false;

    [AuditProperty]
    public bool CanDelete { get; set; } = false;

    public virtual Function Function { get; set; } = null!;

    public virtual Role Role { get; set; } = null!;
}
