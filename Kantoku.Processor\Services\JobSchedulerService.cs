using System.Collections.Concurrent;
using Cronos;
using Kantoku.Processor.Data.Repositories;
using Kantoku.Processor.Models.BatchJobManager;
using Kantoku.Processor.Services.Interfaces;
using Microsoft.Extensions.Options;
using Kantoku.Processor.Configurations;

namespace Kantoku.Processor.Services
{
    /// <summary>
    /// Service that schedules and triggers batch jobs based on CRON expressions
    /// </summary>
    public class JobSchedulerService : IJobSchedulerService, IHostedService
    {
        private readonly IBatchJobRepository _jobRepository;
        private readonly IBatchExecutionService _executionService;
        private readonly ILogger<JobSchedulerService> _logger;
        private readonly JobScheduleConfig _options;
        
        private readonly ConcurrentDictionary<Guid, Timer> _jobTimers = new();
        private readonly ConcurrentDictionary<Guid, DateTime?> _nextExecutionTimes = new();
        private readonly SemaphoreSlim _executionSemaphore;
        
        private bool _isStarted;
        private Timer? _refreshTimer;
        private CancellationTokenSource? _cancellationTokenSource;
        
        public JobSchedulerService(
            IBatchJobRepository jobRepository,
            IBatchExecutionService executionService,
            IServiceProvider serviceProvider,
            IOptions<JobScheduleConfig> options,
            ILogger<JobSchedulerService> logger)
        {
            _jobRepository = jobRepository;
            _executionService = executionService;
            _logger = logger;
            _options = options.Value;
            
            // Initialize semaphore for concurrent job execution limiting
            _executionSemaphore = new SemaphoreSlim(_options.MaxConcurrentJobs);
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            if (_isStarted)
                return;
                
            _logger.LogInformation("Starting job scheduler service");
            
            _cancellationTokenSource = new CancellationTokenSource();
            
            // Load all enabled jobs and schedule them
            await RescheduleAllJobsAsync();
            
            // Start a timer to periodically refresh the job schedules
            _refreshTimer = new Timer(
                async _ => await RefreshJobSchedulesAsync(), 
                null, 
                TimeSpan.FromSeconds(_options.PollIntervalSeconds), 
                TimeSpan.FromSeconds(_options.PollIntervalSeconds));
                
            _isStarted = true;
            
            _logger.LogInformation("Job scheduler service started");
        }

        public async Task StopAsync(CancellationToken cancellationToken)
        {
            if (!_isStarted)
                return;
                
            _logger.LogInformation("Stopping job scheduler service");
            
            // Cancel all running jobs
            _cancellationTokenSource?.Cancel();
            
            // Dispose all timers
            foreach (var timer in _jobTimers.Values)
            {
                await timer.DisposeAsync();
            }
            
            if (_refreshTimer != null)
            {
                await _refreshTimer.DisposeAsync();
                _refreshTimer = null;
            }
            
            _jobTimers.Clear();
            _nextExecutionTimes.Clear();
            
            _isStarted = false;
            
            _logger.LogInformation("Job scheduler service stopped");
        }

        public DateTime? GetNextExecutionTime(BatchJob job)
        {
            // Return cached value if available
            if (_nextExecutionTimes.TryGetValue(job.JobId, out var nextTime))
            {
                return nextTime;
            }
            
            // Calculate next execution time
            try
            {
                if (!job.IsEnabled || string.IsNullOrEmpty(job.Interval))
                {
                    return null;
                }
                
                var cronExpression = CronExpression.Parse(job.Interval);
                var nextOccurrence = cronExpression.GetNextOccurrence(DateTime.UtcNow);
                
                // Cache the result
                _nextExecutionTimes[job.JobId] = nextOccurrence;
                
                return nextOccurrence;
            }
            catch (Exception ex)
            {   
                _logger.LogError(ex, "Failed to parse CRON expression '{CronExpression}' for job {JobName}", 
                    job.Interval, job.JobName);
                    
                return null;
            }
        }

        public async Task<bool> TriggerJobAsync(Guid jobId)
        {
            _logger.LogInformation("Manual trigger requested for job ID {JobId}", jobId);
            
            var job = await _jobRepository.GetJobByIdAsync(jobId);
            
            if (job == null)
            {
                _logger.LogWarning("Cannot trigger job with ID {JobId}: Job not found", jobId);
                return false;
            }
            
            if (_executionService.IsJobRunning(jobId))  
            {
                _logger.LogWarning("Cannot trigger job {JobName} (ID: {JobId}): Job is already running", 
                    job.JobName, jobId);
                return false;
            }
            
            // Execute the job in a background task
            _ = ExecuteJobAsync(job);
            
            return true;
        }

        public async Task RescheduleAllJobsAsync()
        {
            _logger.LogInformation("Rescheduling all jobs");
            
            // Clear existing timers
            foreach (var timer in _jobTimers.Values)
            {
                await timer.DisposeAsync();
            }
            
            _jobTimers.Clear();
            _nextExecutionTimes.Clear();
            
            // Get all enabled jobs
            var enabledJobs = await _jobRepository.GetEnabledJobsAsync();
            
            foreach (var job in enabledJobs)
            {
                ScheduleJob(job);
            }
            
            _logger.LogInformation("Scheduled {JobCount} jobs", enabledJobs.Count());
        }
        
        private void ScheduleJob(BatchJob job)
        {
            try
            {
                if (!job.IsEnabled || string.IsNullOrEmpty(job.Interval))
                {
                    _logger.LogInformation("Job {JobName} (ID: {JobId}) is disabled or has no CRON expression", 
                        job.JobName, job.JobId);
                    return;
                }
                
                // Parse the CRON expression
                var cronExpression = CronExpression.Parse(job.Interval);
                
                // Calculate the next occurrence
                var nextOccurrence = cronExpression.GetNextOccurrence(DateTime.UtcNow);
                
                if (nextOccurrence == null)
                {
                    _logger.LogWarning("No future occurrences for job {JobName} (ID: {JobId}) with CRON '{CronExpression}'", 
                        job.JobName, job.JobId, job.Interval);
                    return;
                }
                
                // Store the next execution time
                _nextExecutionTimes[job.JobId] = nextOccurrence;
                
                // Calculate delay until next occurrence
                var delay = nextOccurrence.Value - DateTime.UtcNow;
                
                if (delay < TimeSpan.Zero)
                {
                    delay = TimeSpan.Zero;
                }
                
                _logger.LogInformation("Job {JobName} (ID: {JobId}) scheduled to run at {NextExecution} (in {Delay})", 
                    job.JobName, job.JobId, nextOccurrence, delay);
                
                // Create a timer to trigger the job at the specified time
                var timer = new Timer(
                    _ => OnJobTimerElapsed(job),
                    null,
                    delay,
                    Timeout.InfiniteTimeSpan); // No repeat, we'll reschedule after execution
                
                // Store the timer
                _jobTimers[job.JobId] = timer;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to schedule job {JobName} (ID: {JobId})", job.JobName, job.JobId);
            }
        }
        
        private void OnJobTimerElapsed(BatchJob job)
        {
            _logger.LogInformation("Timer elapsed for job {JobName} (ID: {JobId})", job.JobName, job.JobId);
            
            // Execute the job in a background task
            _ = ExecuteJobAsync(job);
            
            // Reschedule the job for the next occurrence
            _ = RescheduleJobAsync(job);
        }
        
        private async Task RescheduleJobAsync(BatchJob job)
        {
            try
            {
                // Get the latest job configuration from the database
                job = await _jobRepository.GetJobByIdAsync(job.JobId) ?? job;
                
                // Remove old timer if exists
                if (_jobTimers.TryRemove(job.JobId, out var oldTimer))
                {
                    await oldTimer.DisposeAsync();
                }
                
                // Schedule the job again if it's still enabled
                if (job.IsEnabled)
                {
                    ScheduleJob(job);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to reschedule job {JobName} (ID: {JobId})", job.JobName, job.JobId);
            }
        }
        
        private async Task ExecuteJobAsync(BatchJob job)
        {
            // Don't execute disabled jobs
            if (!job.IsEnabled)
            {
                _logger.LogInformation("Skipping execution of disabled job {JobName} (ID: {JobId})", 
                    job.JobName, job.JobId);
                return;
            }
            
            // Don't execute if the service is already executing the maximum number of concurrent jobs
            if (!await _executionSemaphore.WaitAsync(0))
            {
                _logger.LogWarning("Maximum number of concurrent jobs ({MaxJobs}) reached. Skipping execution of job {JobName} (ID: {JobId})", 
                    _options.MaxConcurrentJobs, job.JobName, job.JobId);
                return;
            }
            
            try
            {
                // Create a timeout cancellation token
                using var timeoutCts = new CancellationTokenSource(TimeSpan.FromMinutes(_options.JobTimeoutMinutes));
                using var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(
                    timeoutCts.Token,
                    _cancellationTokenSource?.Token ?? CancellationToken.None);
                
                await _executionService.ExecuteJobAsync(job, linkedCts.Token);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to execute job {JobName} (ID: {JobId})", job.JobName, job.JobId);
            }
            finally
            {
                // Release the semaphore to allow another job to execute
                _executionSemaphore.Release();
            }
        }
        
        private async Task RefreshJobSchedulesAsync()
        {
            try
            {
                _logger.LogDebug("Refreshing job schedules");
                
                // Get all enabled jobs
                var enabledJobs = await _jobRepository.GetEnabledJobsAsync();
                var enabledJobIds = enabledJobs.Select(j => j.JobId).ToHashSet();
                
                // Find jobs that are no longer enabled or have been deleted
                var jobsToRemove = _jobTimers.Keys.Where(id => !enabledJobIds.Contains(id)).ToList();
                
                // Remove those jobs
                foreach (var jobId in jobsToRemove)
                {
                    if (_jobTimers.TryRemove(jobId, out var timer))
                    {
                        await timer.DisposeAsync();
                        _logger.LogInformation("Removed schedule for job ID {JobId}", jobId);
                    }
                    
                    _nextExecutionTimes.TryRemove(jobId, out _);
                }
                
                // Check for new or modified jobs
                foreach (var job in enabledJobs)
                {
                    // If the job is not scheduled, schedule it
                    if (!_jobTimers.ContainsKey(job.JobId))
                    {
                        _logger.LogInformation("Found new enabled job {JobName} (ID: {JobId}). Scheduling it.", 
                            job.JobName, job.JobId);
                        ScheduleJob(job);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to refresh job schedules");
            }
        }
    }
} 