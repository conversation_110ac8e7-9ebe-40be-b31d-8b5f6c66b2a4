using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.EmployeeShift.Request;

public class CreateDailyApprovalRequestDto
{
    /// <summary>
    /// Status of the approval (true for approved, false for rejected)
    /// </summary>
    public bool ApprovalStatus { get; set; }

    /// <summary>
    /// Reason for rejection when approval status is false
    /// </summary>
    public string? RejectReason { get; set; }
}