using System.Text.Json;
using System.Text.Json.Serialization;
using Kantoku.Api.Utils.Attributes.Class;

namespace Kantoku.Api.Services;

public class ErrorMessageEntry
{
    [JsonPropertyName("errorStatus")]
    public int ErrorStatus { get; set; }

    [JsonPropertyName("errorCode")]
    public required string ErrorCode { get; set; }

    [JsonPropertyName("errorMessage")]
    public required string ErrorMessage { get; set; }
}

[Service(ServiceLifetime.Singleton)]
public class MultiLanguageErrorService : BaseService<MultiLanguageErrorService>
{
    // Holds data: Key = language code (e.g., "en", "vi"), Value = Dictionary<ErrorCode, Entry>
    private readonly Dictionary<string, Dictionary<string, ErrorMessageEntry>> _allMessages;
    private static string PathToErrorFiles(string languageCode)
        => Path.Combine(AppContext.BaseDirectory, "Resources", "Errors", $"ErrorMessage.{languageCode}.json");

    public MultiLanguageErrorService(
        Serilog.ILogger logger, IHttpContextAccessor httpContextAccessor)
    : base(logger, httpContextAccessor)
    {
        _allMessages = new Dictionary<string, Dictionary<string, ErrorMessageEntry>>(StringComparer.OrdinalIgnoreCase);
        // LoadAllMessagesAsync(Path.Combine(AppContext.BaseDirectory, "Resources", "ErrorMessages"));
    }

    public async Task LoadAllMessagesAsync(string directoryPath, string filePrefix = "ErrorMessage.", string fileSuffix = ".json")
    {
        if (!Directory.Exists(directoryPath))
        {
            logger.Error("Error message directory not found: {Directory}", directoryPath);
            // Decide if this is critical: throw new DirectoryNotFoundException(...) or just log?
            return;
        }

        // Example: Find files like ErrorMessage.en.json, ErrorMessage.vi.json
        string searchPattern = $"{filePrefix}*{fileSuffix}";
        var files = Directory.GetFiles(directoryPath, searchPattern);

        if (files.Length == 0)
        {
            logger.Warning("No error message files found matching pattern '{Pattern}' in directory {Directory}", searchPattern, directoryPath);
            return;
        }

        foreach (var filePath in files)
        {
            try
            {
                // Extract language code from filename (e.g., "en" from "ErrorMessage.en.json")
                string fileName = Path.GetFileNameWithoutExtension(filePath); // ErrorMessage.en
                string langCode = fileName.Replace(filePrefix, "", StringComparison.OrdinalIgnoreCase); // en

                if (string.IsNullOrWhiteSpace(langCode))
                {
                    logger.Warning("Could not determine language code from file: {FilePath}", filePath);
                    continue;
                }

                logger.Debug("Loading messages for language '{LangCode}' from file: {FilePath}", langCode, filePath);

                using FileStream openStream = File.OpenRead(filePath);
                var messages = await JsonSerializer.DeserializeAsync<List<ErrorMessageEntry>>(openStream);
                if (messages != null && messages.Any())
                {
                    // Convert List to Dictionary for fast lookup by errorCode
                    var langDictionary = messages.ToDictionary(
                        entry => entry.ErrorCode,
                        entry => entry,
                        StringComparer.OrdinalIgnoreCase // Case-insensitive error codes
                    );

                    // Add or update the language dictionary in the main dictionary
                    _allMessages[langCode] = langDictionary;
                    logger.Debug("Successfully loaded {Count} messages for language '{LangCode}'", langDictionary.Count, langCode);
                }
                else
                {
                    logger.Warning("No messages found or parsed for language '{LangCode}' in file {FilePath}", langCode, filePath);
                }
            }
            catch (JsonException jsonEx)
            {
                logger.Error(jsonEx, "Failed to parse JSON file: {FilePath}", filePath);
                // Continue to next file or re-throw depending on requirements
            }
            catch (Exception ex)
            {
                logger.Error(ex, "An error occurred while reading file: {FilePath}", filePath);
                // Continue to next file or re-throw
            }
        }
        logger.Information("Finished preloading error messages. Loaded data for {Count} languages.", _allMessages.Count);
    }

    public void LoadMessagesSynchronous(string directoryPath, string filePrefix = "ErrorMessage.", string fileSuffix = ".json")
    {
        logger.Information("Starting synchronous preload from: {Directory}", directoryPath);
        if (!Directory.Exists(directoryPath)) { /* ... error handling ... */ return; }

        string searchPattern = $"{filePrefix}*{fileSuffix}";
        var files = Directory.GetFiles(directoryPath, searchPattern);
        if (!files.Any()) { /* ... warning ... */ return; }

        foreach (var filePath in files)
        {
            try
            {
                string fileName = Path.GetFileNameWithoutExtension(filePath);
                string langCode = fileName.Replace(filePrefix, "", StringComparison.OrdinalIgnoreCase);
                if (string.IsNullOrWhiteSpace(langCode)) continue;

                string jsonContent = File.ReadAllText(filePath);
                var messages = JsonSerializer.Deserialize<List<ErrorMessageEntry>>(jsonContent);
                if (messages != null && messages.Any())
                {
                    var langDictionary = messages.ToDictionary(e => e.ErrorCode, e => e, StringComparer.OrdinalIgnoreCase);
                    _allMessages[langCode] = langDictionary;
                    logger.Debug("Sync Load: Loaded {Count} for '{LangCode}'", langDictionary.Count, langCode);
                }
            }
            catch (Exception ex)
            {
                logger.Error(ex, "Sync Load failed for file: {FilePath}", filePath);
            }
        }
        logger.Information("Finished synchronous preload. Loaded data for {Count} languages.", _allMessages.Count);
    }

    public ErrorMessageEntry GetErrorMessage(string errorCode)
    {
        ErrorMessageEntry defaultEntry = new()
        {
            ErrorStatus = 500,
            ErrorCode = errorCode,
            ErrorMessage = "Unknown error"
        };

        var languageCode = GetCurrentLanguageCode();
        var defaultLanguageCode = "ja";
        // Try to get from the requested language
        if (_allMessages.TryGetValue(languageCode, out var langMessages))
        {
            langMessages.TryGetValue(errorCode, out var entry);
            if (entry != null)
            {
                return entry;
            }
        }

        // If not found in specific language, try fallback to default language (e.g., English)
        if (!string.Equals(languageCode, defaultLanguageCode, StringComparison.OrdinalIgnoreCase))
        {
            logger.Debug("Message for code '{ErrorCode}' not found in '{LangCode}', attempting fallback to '{DefaultLangCode}'", errorCode, languageCode, defaultLanguageCode);
            if (_allMessages.TryGetValue(defaultLanguageCode, out var defaultLangMessages))
            {
                defaultLangMessages.TryGetValue(errorCode, out var entry);
                if (entry != null)
                {
                    return entry;
                }
            }
        }

        return defaultEntry;
    }
}