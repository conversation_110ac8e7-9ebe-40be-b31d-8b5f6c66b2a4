namespace Kantoku.Api.Dtos.OutSource.Request;

public class OutSourcePriceRequestDto
{
    /// <summary>
    /// The price per hour of the outsource
    /// </summary>
    public int? PricePerHour { get; set; }

    /// <summary>
    /// The price per day of the outsource
    /// </summary>
    public int? PricePerDay { get; set; }

    /// <summary>
    /// The price per week of the outsource
    /// </summary>
    public int? PricePerWeek { get; set; }

    /// <summary>
    /// The price per month of the outsource
    /// </summary>
    public int? PricePerMonth { get; set; }

    /// <summary>
    /// The description of the outsource price
    /// </summary>s
    public string? Description { get; set; }
}

