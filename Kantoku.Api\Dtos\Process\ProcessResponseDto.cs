﻿namespace Kantoku.Api.Dtos.Process
{
    public class ProcessResponseDto
    {
        public string? ProcessId { get; set; }

        public string? ProjectId { get; set; }

        public string? ProcessCode { get; set; }

        public string? ProcessName { get; set; }

        public string? Description { get; set; }    

        public string? StartDate { get; set; }

        public string? EndDate { get; set; }

        public string? ParentId { get; set; }

        public ICollection<ProcessResponseDto> Children { get; set; } = [];

        public float Workload { get; set; }

        public string? WorkloadUnitId { get; set; }

        public float? InitialCost { get; set; }

        public string? EmployeeRequirement { get; set; }

        public string? CreateTime { get; set; }

        public string? UpdateTime { get; set; }
    }
}
