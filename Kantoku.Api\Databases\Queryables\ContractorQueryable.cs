using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class ContractorQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedProject { get; set; } = false;
    public bool IncludedConstructionPaymentRequests { get; set; } = false;
}

public interface IContractorQueryable
{
    IQueryable<Contractor> GetContractorQuery(
        ContractorQueryableOptions options
    );

    IQueryable<Contractor> GetContractorQueryIncluded(
        ContractorQueryableOptions options,
        IQueryable<Contractor>? query = null
    );

    IQueryable<Contractor> GetContractorQueryFilter(
        ContractorFilter filter,
        ContractorQueryableOptions options,
        IQueryable<Contractor>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class ContractorQueryable(PostgreDbContext context) :
    BaseQueryable<Contractor>(context), IContractorQueryable
{
    public IQueryable<Contractor> GetContractorQuery(
        ContractorQueryableOptions options
    )
    {
        var query = base.GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<Contractor> GetContractorQueryIncluded(
        ContractorQueryableOptions options,
        IQueryable<Contractor>? query = null
    )
    {
        query ??= GetContractorQuery(options);
        if (options.IncludedProject || options.IncludedAll)
        {
            query = query.Include(c => c.Projects);
        }
        if (options.IncludedConstructionPaymentRequests || options.IncludedAll)
        {
            query = query.Include(c => c.ConstructionPaymentRequests);
        }
        return query;

    }

    public IQueryable<Contractor> GetContractorQueryFilter(
        ContractorFilter filter,
        ContractorQueryableOptions options,
        IQueryable<Contractor>? query = null
    )
    {
        query ??= GetContractorQueryIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(c => c.OrgUid == filter.OrgId);
        }
        if (string.IsNullOrEmpty(filter.Keyword) == false)
        {
            query = query.Where(c => c.ContractorName.Contains(filter.Keyword));
        }
        return query;
    }
}

