using Kantoku.Api.Dtos.Function.Request;
using Kantoku.Api.Dtos.Function.Response;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class FunctionController : BaseController
{
    private readonly IFunctionService functionService;

    public FunctionController(
        ITResponseFactory responseFactory,
        IFunctionService functionService
    ) : base(responseFactory)
    {
        this.functionService = functionService;
    }

    /// <summary>
    /// Gets list of configured functions for the current user
    /// </summary>
    /// <returns>List of configured functions</returns>
    [HttpGet]
    public async Task<GeneralResponse<SimpleFunctionsResponseDto>> GetFunctionList()
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<SimpleFunctionsResponseDto>();

            var res = await functionService.GetFunctionList();
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<SimpleFunctionsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Creates a new function
    /// </summary>
    /// <param name="dto">Function creation request</param>
    /// <returns>Boolean indicating success</returns>
    [HttpPost]
    public async Task<GeneralResponse<bool>> CreateFunction(CreateFunctionRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<bool>();

            var res = await functionService.CreateFunction(dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }
}