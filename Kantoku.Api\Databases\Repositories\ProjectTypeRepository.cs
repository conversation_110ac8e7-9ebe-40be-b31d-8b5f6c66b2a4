using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;

namespace Kantoku.Api.Databases.Repositories;

public interface IProjectTypeRepository
{
    Task<ProjectType?> GetById(string projectTypeUid);
    Task<IEnumerable<ProjectType>> GetAll(string languageCode);
}

[Repository(ServiceLifetime.Scoped)]
public class ProjectTypeRepository : BaseRepository<ProjectType>, IProjectTypeRepository
{
    public ProjectTypeRepository(
        PostgreDbContext context,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor
    )
        : base(context, logger, httpContextAccessor)
    {
    }

    public async Task<ProjectType?> GetById(string projectTypeUid)
    {
        try
        {
            if (!Guid.TryParse(projectTypeUid, out Guid guidId))
            {
                logger.Error("Invalid project type uid: {ProjectTypeUid}", projectTypeUid);
                return null;
            }
            var projectType = await context.ProjectTypes
                    .Where(pt => pt.ProjectTypeUid == guidId)
                    .FirstOrDefaultAsync();
            return projectType;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting project type by uid {ProjectTypeUid}", projectTypeUid);
            return null;
        }
    }

    public async Task<IEnumerable<ProjectType>> GetAll(string languageCode)
    {
        try
        {
            var projectTypes = await context.ProjectTypes
                .ToListAsync();
            return projectTypes;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all project types");
            return [];
        }
    }
}
