using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class OutSourcePriceQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedOutSource { get; set; } = false;
}

public interface IOutSourcePriceQueryable
{
    IQueryable<OutSourcePrice> GetOutSourcePriceQuery(
        OutSourcePriceQueryableOptions options
    );
    IQueryable<OutSourcePrice> GetOutSourcePriceQueryIncluded(
        OutSourcePriceQueryableOptions options,
        IQueryable<OutSourcePrice>? query = null
    );
    IQueryable<OutSourcePrice> GetOutSourcePriceQueryFiltered(
        OutSourcePriceFilter filter,
        OutSourcePriceQueryableOptions options,
        IQueryable<OutSourcePrice>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class OutSourcePriceQueryable(PostgreDbContext context) :
    BaseQueryable<OutSourcePrice>(context), IOutSourcePriceQueryable
{
    public IQueryable<OutSourcePrice> GetOutSourcePriceQuery(
        OutSourcePriceQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<OutSourcePrice> GetOutSourcePriceQueryIncluded(
        OutSourcePriceQueryableOptions options,
        IQueryable<OutSourcePrice>? query = null
    )
    {
        query ??= GetOutSourcePriceQuery(options);

        if (options.IncludedOutSource || options.IncludedAll)
        {
            query = query.Include(s => s.OutSource);
        }

        return query;
    }

    public IQueryable<OutSourcePrice> GetOutSourcePriceQueryFiltered(
        OutSourcePriceFilter filter,
        OutSourcePriceQueryableOptions options,
        IQueryable<OutSourcePrice>? query = null
    )
    {
        query ??= GetOutSourcePriceQueryIncluded(options);

        if (!string.IsNullOrEmpty(filter.Keyword))
        {
            query = query.Where(s => s.OutSource.OutSourceName.Contains(filter.Keyword) || s.OutSource.OutSourceCode.Contains(filter.Keyword));
        }
        if (DateOnly.TryParse(filter.StartDate, out var startDate))
        {
            query = query.Where(s => s.EffectiveDate >= startDate);
        }
        if (DateOnly.TryParse(filter.EndDate, out var endDate))
        {
            query = query.Where(s => s.EffectiveDate <= endDate);
        }

        return query;
    }
}

