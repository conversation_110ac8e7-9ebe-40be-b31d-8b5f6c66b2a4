using Kantoku.Api.Factories.Response;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Dtos.ConstructionCost.Response;
using Kantoku.Api.Dtos.ConstructionCost.Request;

namespace Kantoku.Api.Controllers;

[Route("api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class ConstructionCostController : BaseController
{
    private readonly IConstructionCostService constructionCostService;
    private readonly IAuditLogService auditLogService;

    public ConstructionCostController(IConstructionCostService constructionService,
        IAuditLogService auditLogService,
        ITResponseFactory responseFactory) : base(responseFactory)
    {
        this.constructionCostService = constructionService;
        this.auditLogService = auditLogService;
    }

    [HttpPost]
    public async Task<GeneralResponse<bool>> CreateConstructionCost([FromBody] ConstructionCostCreateRequestDto request)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<bool>();

            var result = await constructionCostService.CreateConstructionCost(request);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    [HttpPut("{id}")]
    public async Task<GeneralResponse<bool>> UpdateConstructionCost([FromRoute] Guid id, [FromBody] ConstructionCostUpdateRequestDto request)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<bool>();

            var result = await constructionCostService.UpdateConstructionCost(id, request);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Retrieves cost information for a specific construction within a date range
    /// </summary>
    /// <param name="id">The ID of the construction to get costs for</param>
    /// <param name="startDate">The start date of the cost period (format: yyyy-MM-dd)</param>
    /// <param name="endDate">The end date of the cost period (format: yyyy-MM-dd)</param>
    /// <returns>Cost details for the specified construction within the date range</returns>
    [HttpGet("construction/{id}")]
    public async Task<GeneralResponse<ConstructionCostSummaryResponseDto>> GetCostsByConstructionId([FromRoute] Guid id, [FromQuery] string startDate, [FromQuery] string endDate)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ConstructionCostSummaryResponseDto>();

            var result = await constructionCostService.GetConstructionCostByConstructionId(id, startDate, endDate);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ConstructionCostSummaryResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Retrieves cost information for a specific project within a date range
    /// </summary>
    /// <param name="id">The ID of the project to get costs for</param>
    /// <param name="startDate">The start date of the cost period (format: yyyy-MM-dd)</param>
    /// <param name="endDate">The end date of the cost period (format: yyyy-MM-dd)</param>
    /// <returns>Cost details for the specified construction within the date range</returns>
    [HttpGet("project/{id}")]
    public async Task<GeneralResponse<ConstructionCostSummariesResponseDto>> GetCostsByProjectId([FromRoute] Guid id, [FromQuery] string startDate, [FromQuery] string endDate)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ConstructionCostSummariesResponseDto>();

            var result = await constructionCostService.GetConstructionCostsByProjectId(id, startDate, endDate);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<ConstructionCostSummariesResponseDto>(e.ErrorCode);
        }
    }
}
