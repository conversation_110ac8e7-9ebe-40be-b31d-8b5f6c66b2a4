namespace Kantoku.Api.Dtos.Project.Response;

public class ProjectProgressResponseDto
{
    /// <summary>
    /// Constructions progress
    /// </summary>
    public IEnumerable<ConstructionProgressResponseDto> ConstructionsProgress { get; set; } = [];
}

public class ConstructionProgressResponseDto
{
    /// <summary>
    /// Construction Id
    /// </summary>
    public string? ConstructionId { get; set; }

    /// <summary>
    /// Is Primary Construction
    /// </summary>
    public bool IsPrimary { get; set; }

    /// <summary>
    /// Total Claimed Amount
    /// </summary>
    public long TotalClaimedAmount { get; set; }

    /// <summary>
    /// Ratio of claimable amount to total cost amount
    /// = TotalClaimedAmount / (SUM of ContractualCosts)
    /// </summary>
    public float DisbursementProgressRatio { get; set; }

    /// <summary>
    /// Remaining amount of claimable
    /// = (SUM of ContractualCosts) - TotalClaimedAmount
    /// </summary>
    public long RemainingClaimableAmount { get; set; }
}