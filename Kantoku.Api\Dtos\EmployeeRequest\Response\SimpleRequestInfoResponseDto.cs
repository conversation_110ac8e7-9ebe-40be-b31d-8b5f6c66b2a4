using Kantoku.Api.Dtos.Base.Response;

namespace Kantoku.Api.Dtos.EmployeeRequest.Response;

public class SimpleRequestInfoResponseDto : BaseResponseDto
{
    public string? RequestId { get; set; }
    public string? RequestUserName { get; set; }
    public string? RequestFrom { get; set; }
    public string? RequestTo { get; set; }
    public string? RequestTypeCode { get; set; }
    public string? RequestTypeName { get; set; }
    public bool? IsUserRequestedLeave { get; set; }
    public string? Description { get; set; }
    public string? StatusCode { get; set; }
    public string? StatusName { get; set; }
    public string? Approver1Name { get; set; }
    public string? Approver2Name { get; set; }
}