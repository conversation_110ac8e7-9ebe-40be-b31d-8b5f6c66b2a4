﻿using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Models;

public class Customer : AuditableEntity
{
    public Guid CustomerUid { get; set; }

    [AuditProperty]
    public string CustomerCode { get; set; } = null!;

    [AuditProperty]
    public string CustomerName { get; set; } = null!;

    [AuditProperty]
    public string? CustomerSubName { get; set; }

    [AuditProperty]
    public string? CustomerTypeCode { get; set; }

    [AuditProperty]
    public ContactPerson? ContactPerson { get; set; }

    [AuditProperty]
    public string? Description { get; set; }

    [AuditProperty]
    public string? CorporateNumber { get; set; }

    [AuditProperty]
    public string? Address { get; set; }

    [AuditProperty]
    public string? PhoneNumber { get; set; }

    [AuditProperty]
    public string? Email { get; set; }

    public bool IsDeleted { get; set; }

    public Guid OrgUid { get; set; }

    public string? LogoUrl { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual CustomerType CustomerType { get; set; } = null!;

    public virtual ICollection<Project> Projects { get; set; } = [];
}