using Kantoku.Processor.Configurations;
using Kantoku.Processor.Data.Contexts;
using Kantoku.Processor.Data.Repositories;
using Kantoku.Processor.Jobs;
using Kantoku.Processor.Models.BatchJobManager;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Kantoku.Processor.Data.Seeders;

/// <summary>
/// Class for seeding initial data in the database
/// </summary>
public class DatabaseSeeder
{
    private readonly JobDbContext _jobContext;
    private readonly JobScheduleConfig _jobScheduleConfig;
    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly ILogger<DatabaseSeeder> _logger;

    public DatabaseSeeder(
        JobDbContext jobContext,
        IOptions<JobScheduleConfig> jobScheduleConfig,
        IServiceScopeFactory serviceScopeFactory,
        ILogger<DatabaseSeeder> logger)
    {
        _jobContext = jobContext;
        _jobScheduleConfig = jobScheduleConfig.Value;
        _serviceScopeFactory = serviceScopeFactory;
        _logger = logger;
    }

    /// <summary>
    /// Seeds the database with initial data
    /// </summary>
    public async Task SeedAsync()
    {
        try
        {
            _logger.LogInformation("Starting database seeding");

            // Ensure databases are created and migrations are applied
            await _jobContext.Database.MigrateAsync();

            // Seed batch jobs
            await SeedBatchJobsAsync();

            _logger.LogInformation("Database seeding completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error seeding database");
            throw;
        }
    }

    /// <summary>
    /// Seeds the database with batch job configurations
    /// </summary>
    private async Task SeedBatchJobsAsync()
    {
        _logger.LogInformation("Seeding batch jobs");

        var seedTasks = new List<Task>
        {
            SeedCheckOutJobAsync(),
            SeedCheckOutReminderJobAsync(),
            SeedEmployeeCostCalculateJobAsync()
        };

        foreach (var task in seedTasks)
        {
            await task;
        }

        _logger.LogInformation("Seeded {Count} batch jobs", seedTasks.Count);
    }

    public async Task SeedCheckOutJobAsync()
    {
        _logger.LogInformation("Seeding check out job");

        var checkOutJob = new BatchJob
        {
            JobId = Guid.NewGuid(),
            JobName = nameof(AutoCheckoutJob),
            JobDescription = "Check out job",
            IsEnabled = true,
            Interval = "0 */1 * * *",
            JobType = typeof(AutoCheckoutJob).FullName!,
            LastRunAt = null,
            Configuration = null
        };
        using var scope = _serviceScopeFactory.CreateScope();
        var batchJobRepository = scope.ServiceProvider.GetRequiredService<IBatchJobRepository>();
        var isConfigured = await batchJobRepository.ExistsAsync(checkOutJob.JobName);
        if (isConfigured)
        {
            _logger.LogInformation("Check out job is already configured");
            return;
        }

        await _jobContext.BatchJobs.AddAsync(checkOutJob);
        await _jobContext.SaveChangesAsync();

        _logger.LogInformation("Seeded check out job");
    }

    public async Task SeedCheckOutReminderJobAsync()
    {
        _logger.LogInformation("Seeding check out reminder job");

        var checkOutJob = new BatchJob
        {
            JobId = Guid.NewGuid(),
            JobName = nameof(CheckOutReminderJob),
            JobDescription = "Check out reminder job",
            IsEnabled = true,
            Interval = "* * * * *",
            JobType = typeof(CheckOutReminderJob).FullName!,
            LastRunAt = null,
            Configuration = null
        };
        using var scope = _serviceScopeFactory.CreateScope();
        var batchJobRepository = scope.ServiceProvider.GetRequiredService<IBatchJobRepository>();
        var isConfigured = await batchJobRepository.ExistsAsync(checkOutJob.JobName);
        if (isConfigured)
        {
            _logger.LogInformation("Check out reminder job is already configured");
            return;
        }

        await _jobContext.BatchJobs.AddAsync(checkOutJob);
        await _jobContext.SaveChangesAsync();

        _logger.LogInformation("Seeded check out reminder job");
    }

    public async Task SeedEmployeeCostCalculateJobAsync()
    {
        _logger.LogInformation("Seeding employee cost calculate job");

        var checkOutJob = new BatchJob
        {
            JobId = Guid.NewGuid(),
            JobName = nameof(EmployeeCostCalculateJob),
            JobDescription = "Employee cost calculate job",
            IsEnabled = true,
            Interval = "0 0 * * *",
            JobType = typeof(EmployeeCostCalculateJob).FullName!,
            LastRunAt = null,
            Configuration = null
        };
        using var scope = _serviceScopeFactory.CreateScope();
        var batchJobRepository = scope.ServiceProvider.GetRequiredService<IBatchJobRepository>();
        var isConfigured = await batchJobRepository.ExistsAsync(checkOutJob.JobName);
        if (isConfigured)
        {
            _logger.LogInformation("Employee cost calculate job is already configured");
            return;
        }

        await _jobContext.BatchJobs.AddAsync(checkOutJob);
        await _jobContext.SaveChangesAsync();

        _logger.LogInformation("Seeded employee cost calculate job");
    }
}
