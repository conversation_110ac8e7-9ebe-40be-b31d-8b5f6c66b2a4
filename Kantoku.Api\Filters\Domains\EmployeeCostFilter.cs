using Kantoku.Api.Filters.Domains;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Filters.Domains;

public class EmployeeCostFilter : BaseFilter
{
    /// <summary>
    /// The keyword to search for employee (name or code)
    /// </summary>
    [FromQuery(Name = "keyword")]
    public string? Keyword { get; set; }

    /// <summary>
    /// The date from to filter (yyyy-MM-dd)
    /// </summary>
    [FromQuery(Name = "dateFrom")]
    public string? DateFrom { get; set; }

    /// <summary>
    /// The date to to filter (yyyy-MM-dd)
    /// </summary>
    [FromQuery(Name = "dateTo")]
    public string? DateTo { get; set; }
}

