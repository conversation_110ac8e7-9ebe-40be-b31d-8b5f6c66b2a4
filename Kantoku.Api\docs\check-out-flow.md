# Employee Check-out Flow Documentation

This document describes the business flow for employee check-out functionality.

## API Endpoints

### Check-out
- **URL**: `/attendance/{shiftId}/checkout`
- **Method**: PUT
- **Consumes**: application/json
- **Produces**: application/json
- **Path Parameters**:
  - `shiftId` (GUID): Unique identifier of the shift

## Request Payload

```json
{
    "coordinates": {
        "latitude": number,    // Decimal value of latitude
        "longitude": number    // Decimal value of longitude
    },
    "deviceId": string        // Unique identifier of the device used for check-out
}
```

## Business Flow

```mermaid
graph TD
    A[Employee] -->|Initiates Check-out| B[Validate Shift]
    
    B -->|Shift not found| C[Return 404: Shift not found]
    B -->|Shift found| D{Already checked out?}
    
    D -->|Yes| E[Return 400: Already checked out]
    D -->|No| F{Checked in?}
    
    F -->|No| G[Return 400: Not checked in yet]
    F -->|Yes| H[Process Check-out]
    
    H -->|Update| I[Set Check-out Time]
    I -->|Get Location| J[Geocode coordinates]
    J -->|Geocoding failed| K[Log error, continue with coordinates only]
    J -->|Success| L[Update Check-out Location]
    K -->|Continue| L
    
    L -->|DB Operation| M[Return Shift Details]
```

## Data Processing Steps

1. **Input Validation**
   - Verify shift exists
   - Validate coordinate values (latitude: -90 to 90, longitude: -180 to 180)
   - Verify deviceId format

2. **State Validation**
   - Verify shift is checked in
   - Verify shift is not already checked out
   - Verify all breaks are checked out

3. **Location Processing**
   - Geocode the provided coordinates to get address details
   - If geocoding fails, proceed with coordinates only
   - Store both coordinates and address (if available)

4. **Time Processing**
   - Record check-out time in UTC
   - Calculate total working hours
   - Convert to project's timezone for display

## Database Operations

### Shifts Table Update

```sql
UPDATE employee_shift
SET 
    check_out_time = @currentUtcTime,
    check_out_latitude = @latitude,
    check_out_longitude = @longitude,
    check_out_address = @geocodedAddress,
    device_id = @deviceId,
    last_modified_at = @currentUtcTime,
    last_modified_by = @employeeId
WHERE employee_shift_uid = @shiftId
```

## Response

### Success Response (200 OK)
```json
{
    "shiftId": "guid",
    "employeeId": "guid",
    "projectId": "guid",
    "checkInTime": "ISO8601 DateTime",
    "checkOutTime": "ISO8601 DateTime",
    "checkOutLocation": {
        "latitude": number,
        "longitude": number,
        "address": "string"
    },
    "deviceId": "string",
    "totalWorkingHours": number
}
```

### Error Responses

#### 400 Bad Request
```json
{
    "errorCode": "SFT18",
    "message": "Already Checked Out",
    "status": 400
}
```
```json
{
    "errorCode": "SFT11",
    "message": "Not Checked In Yet",
    "status": 400
}
```
```json
{
    "errorCode": "GPS02",
    "message": "Location info invalid",
    "status": 400
}
```

#### 404 Not Found
```json
{
    "errorCode": "SFT01",
    "message": "Shift Not Exist",
    "status": 404
}
```

#### 500 Internal Server Error
```json
{
    "errorCode": "SFT06",
    "message": "Check Out Failed",
    "status": 500
}
```
```json
{
    "errorCode": "KAN500",
    "message": "Unknown Error",
    "status": 500
}
``` 