using Kantoku.Api.Filters.Domains;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Filters.Domains;

public class ConstructionCostFilter : BaseFilter
{
    [FromQuery(Name = "dateFrom")]
    public string? DateFrom { get; set; }

    [FromQuery(Name = "dateTo")]
    public string? DateTo { get; set; }

    [FromQuery(Name = "isPrimary")]
    public bool? IsPrimary { get; set; }

    [FromQuery(Name = "constructionId")]
    public Guid? ConstructionId { get; set; }

    [FromQuery(Name = "projectId")]
    public Guid? ProjectId { get; set; }
}

