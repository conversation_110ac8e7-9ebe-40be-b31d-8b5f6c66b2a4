# Kantoku Service

Kantoku Service is the core API backend for the Kantoku ERP (Enterprise Resource Planning) system. This project serves as the central hub for managing and orchestrating various business processes and resources.

## Overview

The Kantoku Service provides a robust and scalable API infrastructure to support diverse ERP functionalities. It's designed to streamline operations, enhance productivity, and facilitate efficient resource management for businesses of all sizes.

## Table of Contents

- [Features](#features)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
  - [Local Setup](#local-setup)
  - [Docker Setup](#docker-setup)
- [Generate Scaffold Entities](#generate-scaffold-entities)
- [Database Migration](#database-migration)
- [Usage](#usage)
- [Contributing](#contributing)

## Features

- RESTful API endpoints for comprehensive ERP operations
- Seamless integration with SQL Server using Entity Framework Core
- Docker support for simplified deployment and scaling
- Flexible architecture to accommodate growing business needs

## Prerequisites

- .NET 6.0 SDK or later
- SQL Server
- Docker (optional, for containerized deployment)

## Installation


### Local Setup

Follow these steps to install and run the Kantoku Service:

1. **Clone the repository:**
   ```
   git clone https://github.com/your-repo/kantoku-service.git
   cd kantoku-service
   ```

2. **Install .NET SDK:**
   Ensure you have .NET 6.0 SDK or later installed on your machine. You can download it from the official [.NET website](https://dotnet.microsoft.com/download).

3. **Set up SQL Server:**
   Make sure you have SQL Server installed and running. Create a new database for the Kantoku Service.

4. **Configure the application:**
   - Prepare a configuration file `appsettings.{Environment}.json` for your environment.
   - Update the `launchSettings.json` file with your SQL Server connection string and other necessary settings.

5. **Restore NuGet packages:**
   ```
   dotnet restore
   ```

6. **Build the project:**
   ```
   dotnet build
   ```

7. **Run the application:**
   ```
   dotnet run
   ```

### Docker Setup

To deploy using Docker:

1. Build the Docker image:
   ```
   docker build -t kantoku-service:latest .
   ```

2. Run the Docker container:
   ```
   docker run -d -p 8080:4869 kantoku-service:latest
   ```

### Generate Scaffold Entities

To create entity models from your exist database:

```
dotnet ef dbcontext scaffold "Server=your_server;Database=your_database;User Id=your_user;Password=your_password;" Microsoft.EntityFrameworkCore.SqlServer -o Models
```

## Database Migration

To migrate the database, use the following commands:

1. Create a new migration:
   ```
   dotnet ef migration add Initial
   ```

2. Apply Migration:
   ```
   dotnet ef database update
   ```

## Usage

Once installed, the API will be available at the configured endpoint (default: `http://localhost:4869` for local development).

Refer to the API documentation (available at `/swagger` in development mode) for detailed information on available endpoints and their usage.

## Contributing

We welcome contributions to enhance the Kantoku Service. Please review our contribution guidelines before submitting pull requests.

---

Kantoku Service - Empowering businesses with cutting-edge ERP solutions.

