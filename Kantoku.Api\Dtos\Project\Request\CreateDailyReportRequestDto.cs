using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.Project.Request;

public class CreateDailyReportRequestDto
{
    /// <summary>
    /// Report date in format yyyy-MM-dd
    /// </summary>
    [Required]
    [DateTimeValidator(typeof(DateOnly))]
    public string ReportDate { get; set; } = null!;

    /// <summary>
    /// Description of daily report
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// List of employee workload records
    /// </summary>
    public IEnumerable<EmployeeWorkloadRequestDto>? EmployeeWorkload { get; set; }

    /// <summary>
    /// List of outsource workload records  
    /// </summary>
    public IEnumerable<OutSourceWorkloadRequestDto>? OutSourceWorkload { get; set; }
}


