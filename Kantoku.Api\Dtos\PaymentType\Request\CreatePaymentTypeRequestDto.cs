﻿using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.PaymentType.Request;

/// <summary>
/// Data transfer object for creating a new payment type
/// </summary>
public class CreatePaymentTypeRequestDto
{
    /// <summary>
    /// The name of the payment type (*)
    /// </summary>
    [Required]
    public string PaymentTypeName { get; set; } = null!;

    /// <summary>
    /// The description of the payment type
    /// </summary>
    public string? Description { get; set; }
}

