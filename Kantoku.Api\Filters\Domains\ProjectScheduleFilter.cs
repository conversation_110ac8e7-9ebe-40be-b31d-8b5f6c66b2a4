using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Filters.Domains;

public class ProjectScheduleFilter : BaseFilter
{
    [FromQuery(Name = "searchKeyword")]
    public string? SearchKeyword { get; set; }

    [FromQuery(Name = "fromDate")]
    [Required]
    public DateOnly FromDate { get; set; }

    [FromQuery(Name = "toDate")]
    [Required]
    public DateOnly ToDate { get; set; }
}

