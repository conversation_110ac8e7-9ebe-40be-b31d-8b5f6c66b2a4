﻿using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Dtos.Item.Request;
using Kantoku.Api.Dtos.Vendor.Request;

namespace Kantoku.Api.Dtos.InputCostItem.Request;

/// <summary>
/// Data transfer object for creating a new input cost item
/// </summary>
public class CreateInputCostItemRequestDto
{
    /// <summary>
    /// The ID of the construction 
    /// </summary>
    [Required]
    public required Guid ConstructionId { get; set; }

    /// <summary>
    /// The transaction date of the cost item (*)
    /// </summary>
    [Required]
    public required string TransactionDate { get; set; }

    /// <summary>
    /// The ID of the item (*)
    /// </summary>
    [Required]
    public CreateItemBaseRequestDto Item { get; set; } = null!;

    /// <summary>
    /// The unit of measurement for the item (*)
    /// </summary>
    [Required]
    public string Unit { get; set; } = null!;

    /// <summary>
    /// The ID of the vendor
    /// </summary>
    public CreateVendorBaseRequestDto? Vendor { get; set; }

    /// <summary>
    /// The quantity of the item
    /// </summary>
    public float Quantity { get; set; }

    /// <summary>
    /// The price per unit
    /// </summary>
    public int Price { get; set; }

    /// <summary>
    /// The tax rate applied
    /// </summary>
    public float? TaxRate { get; set; }

    /// <summary>
    /// The total non-taxed amount
    /// </summary>
    public long? TotalNonTaxed { get; set; }

    /// <summary>
    /// The total taxed amount
    /// </summary>
    public long? TotalTaxed { get; set; }

    /// <summary>
    /// Additional description or notes
    /// </summary>
    public string? Description { get; set; }
}
