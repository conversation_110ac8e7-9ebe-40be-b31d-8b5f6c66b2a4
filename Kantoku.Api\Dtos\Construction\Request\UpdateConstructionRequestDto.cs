namespace Kantoku.Api.Dtos.Construction.Request;

/// <summary>
/// Data transfer object for updating a construction request
/// </summary>
public class UpdateConstructionRequestDto
{
    /// <summary>
    /// The name of the construction project (*)
    /// </summary>
    public string? ConstructionName { get; set; }

    /// <summary>
    /// Additional details or notes about the construction project
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Initial cost details for the construction project (*)
    /// </summary>
    public InitalCostRequestDto? InitialCost { get; set; }
}

