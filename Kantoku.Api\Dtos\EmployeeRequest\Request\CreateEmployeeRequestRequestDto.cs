using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.EmployeeRequest.Request;

public class CreateEmployeeRequestRequestDto
{
    /// <summary>
    /// Request start date and time (*)
    /// The date and time that request starts
    /// </summary>
    [DateTimeValidator(typeof(DateTime))]
    [Required]
    public string RequestFrom { get; set; } = null!;

    /// <summary>
    /// Request end date and time (*)
    /// The date and time that request ends
    /// </summary>
    [DateTimeValidator(typeof(DateTime))]
    [Required]
    public string RequestTo { get; set; } = null!;

    /// <summary>
    /// Request type code (*)
    /// The code indicating type of request
    /// </summary>
    [Required]
    public string RequestTypeCode { get; set; } = null!;

    /// <summary>
    /// Leave type code 
    /// The code indicating type of leave
    /// </summary>
    public string? LeaveTypeCode { get; set; }

    /// <summary>
    /// Project ID
    /// The ID of the project associated with the request
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// Description
    /// Additional details about the request
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Is user request
    /// Whether the request is a user request
    /// </summary>
    public bool? IsUserRequestedLeave { get; set; }
}