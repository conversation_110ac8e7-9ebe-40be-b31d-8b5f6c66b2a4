﻿namespace Kantoku.Api.Dtos.InputCostItem.Response;

/// <summary>
/// Response DTO for input cost items with pagination
/// </summary>
public class InputCostItemsResponseDto
{
    /// <summary>
    /// Collection of input cost items
    /// </summary>
    public IEnumerable<InputCostItemResponseDto> Items { get; set; } = [];

    /// <summary>
    /// Current page number
    /// </summary>
    public int PageNum { get; set; }

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of records available
    /// </summary>
    public int TotalRecords { get; set; }
}
