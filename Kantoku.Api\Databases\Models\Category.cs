using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Models;

public class Category : AuditableEntity
{
    public Guid CategoryUid { get; set; }

    [AuditProperty]
    public string CategoryCode { get; set; } = null!;

    [AuditProperty]
    public string CategoryName { get; set; } = null!;

    public ICollection<TranslatedCategory>? TranslatedCategory { get; set; }

    [AuditProperty]
    public string? Description { get; set; }

    public bool IsDeleted { get; set; }

    public bool IsDefault { get; set; }

    public Guid? OrgUid { get; set; }

    public Guid? ParentUid { get; set; }

    public string RootCategoryCode { get; set; } = null!;

    public virtual Org? Org { get; set; }

    public virtual Category? ParentCategory { get; set; }

    public virtual ICollection<Category> Children { get; set; } = [];

    public virtual ICollection<Item> Items { get; set; } = [];

    public virtual ICollection<CategorizedCost> CategorizedCosts { get; set; } = [];
}

public class TranslatedCategory
{
    public string LanguageCode { get; set; } = null!;

    public string CategoryName { get; set; } = null!;

    public string CategoryDescription { get; set; } = null!;
}
