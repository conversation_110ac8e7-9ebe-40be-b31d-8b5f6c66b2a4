namespace Kantoku.Processor.Dtos;

using System.Text.Json.Serialization;
using System.Collections.Generic;

public class GeocodingResponseDto
{
    [JsonPropertyName("results")]
    public List<GeocodingResultDto> Results { get; set; } = new();

    [JsonPropertyName("status")]
    public string Status { get; set; } = string.Empty;
}

public class GeocodingResultDto
{
    [JsonPropertyName("address_components")]
    public List<AddressComponentDto> AddressComponents { get; set; } = new();

    [JsonPropertyName("formatted_address")]
    public string FormattedAddress { get; set; } = string.Empty;

    [JsonPropertyName("geometry")]
    public GeometryDto Geometry { get; set; } = new();

    [JsonPropertyName("place_id")]
    public string PlaceId { get; set; } = string.Empty;

    [JsonPropertyName("plus_code")]
    public PlusCodeDto? PlusCode { get; set; }

    [JsonPropertyName("types")]
    public List<string> Types { get; set; } = new();
}

public class AddressComponentDto
{
    [JsonPropertyName("long_name")]
    public string LongName { get; set; } = string.Empty;

    [JsonPropertyName("short_name")]
    public string ShortName { get; set; } = string.Empty;

    [JsonPropertyName("types")]
    public List<string> Types { get; set; } = new();
}

public class GeometryDto
{
    [JsonPropertyName("location")]
    public LocationDto Location { get; set; } = new();

    [JsonPropertyName("location_type")]
    public string LocationType { get; set; } = string.Empty;

    [JsonPropertyName("viewport")]
    public ViewportDto Viewport { get; set; } = new();
}

public class LocationDto
{
    [JsonPropertyName("lat")]
    public double Lat { get; set; }

    [JsonPropertyName("lng")]
    public double Lng { get; set; }
}

public class ViewportDto
{
    [JsonPropertyName("northeast")]
    public LocationDto Northeast { get; set; } = new();

    [JsonPropertyName("southwest")]
    public LocationDto Southwest { get; set; } = new();
}

public class PlusCodeDto
{
    [JsonPropertyName("compound_code")]
    public string? CompoundCode { get; set; }

    [JsonPropertyName("global_code")]
    public string? GlobalCode { get; set; }
}