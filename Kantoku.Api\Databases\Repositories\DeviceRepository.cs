using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Databases.Queryables;

namespace Kantoku.Api.Databases.Repositories;

public interface IDeviceTokenRepository
{
    Task<IEnumerable<DeviceToken>> GetByEmployeeIds(IEnumerable<Guid> employeeIds);
    Task<IEnumerable<DeviceToken>> GetByEmployeeId(Guid employeeId);
    Task<IEnumerable<DeviceToken>> GetByOrgId(Guid orgId);
    Task<DeviceToken?> GetByFirebaseToken(string firebaseToken);
    Task<DeviceToken?> GetById(Guid deviceTokenId);
    Task<DeviceToken?> Create(DeviceToken deviceToken);
    Task<bool> Update(DeviceToken deviceToken);
    Task<bool> Delete(DeviceToken deviceToken);
}

[Repository(ServiceLifetime.Scoped)]
public class DeviceTokenRepository : BaseRepository<DeviceToken>, IDeviceTokenRepository
{
    private readonly IDeviceTokenQueryable DeviceTokenQueryable;
    public DeviceTokenRepository(
        PostgreDbContext context,
        IDeviceTokenQueryable DeviceTokenQueryable,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor
    )
        : base(context, logger, httpContextAccessor)
    {
        this.DeviceTokenQueryable = DeviceTokenQueryable;
    }

    public async Task<IEnumerable<DeviceToken>> GetByEmployeeIds(IEnumerable<Guid> employeeIds)
    {
        try
        {
            var query = DeviceTokenQueryable.GetDeviceTokenQuery(new DeviceTokenQueryableOptions())
                .Where(n => employeeIds.Contains(n.EmployeeUid));

            var result = await query.ToListAsync();
            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting deviceToken tokens for employees {EmployeeIds}", employeeIds);
            return [];
        }
    }

    public async Task<IEnumerable<DeviceToken>> GetByEmployeeId(Guid employeeId)
    {
        try
        {
            var query = DeviceTokenQueryable.GetDeviceTokenQuery(new DeviceTokenQueryableOptions())
                .Where(n => n.EmployeeUid == employeeId);

            var result = await query.ToListAsync();
            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting deviceToken tokens for employee {EmployeeId}", employeeId);
            return [];
        }
    }

    public async Task<IEnumerable<DeviceToken>> GetByOrgId(Guid orgId)
    {
        try
        {
            var query = DeviceTokenQueryable.GetDeviceTokenQuery(new DeviceTokenQueryableOptions())
                .Where(n => n.Employee.OrgUid == orgId);

            var result = await query.ToListAsync();
            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting deviceToken tokens for org {OrgId}", orgId);
            return [];
        }
    }

    public async Task<DeviceToken?> GetById(Guid deviceTokenId)
    {
        try
        {
            var query = DeviceTokenQueryable.GetDeviceTokenQuery(new DeviceTokenQueryableOptions())
                .Where(n => n.DeviceTokenUid == deviceTokenId);

            return await query.FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting deviceToken token by id {deviceTokenId}", deviceTokenId);
            return null;
        }
    }

    public async Task<DeviceToken?> GetByFirebaseToken(string firebaseToken)
    {
        try
        {
            var query = DeviceTokenQueryable.GetDeviceTokenQuery(new DeviceTokenQueryableOptions())
                .Where(n => n.FirebaseToken == firebaseToken);

            return await query.FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error checking if deviceToken exists by firebase token {firebaseToken}", firebaseToken);
            return null;
        }
    }

    public async Task<DeviceToken?> Create(DeviceToken deviceToken)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.DeviceTokens.AddAsync(deviceToken);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(deviceToken.DeviceTokenUid);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating deviceToken: {DeviceToken}", deviceToken);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<bool> Update(DeviceToken deviceToken)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.DeviceTokens.Update(deviceToken);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating deviceToken: {DeviceToken}", deviceToken);
            await transaction.RollbackAsync();
            return false;
        }
    }

    public async Task<bool> Delete(DeviceToken deviceToken)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.DeviceTokens.Remove(deviceToken);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error deleting deviceToken: {DeviceToken}", deviceToken);
            return false;
        }
    }
}