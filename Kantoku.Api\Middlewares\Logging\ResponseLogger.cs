using System.Text;
using Kantoku.Api.Factories.Stream;

namespace Kantoku.Api.Middlewares.Logging;

public class ResponseLogger : IMiddleware
{
    private readonly Serilog.ILogger logger;

    public ResponseLogger(Serilog.ILogger logger)
    {
        this.logger = logger.ForContext<ResponseLogger>();
    }

    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        StringBuilder logBuilder = new StringBuilder();
        try
        {
            if (context.Request.Method == "OPTIONS")
            {
                await next(context);
            }
            else
            {
                logBuilder.Append("Response = ").Append(" \n")
                                    .Append("Response from: ").Append(context.Request.Path).Append(" \n")
                                    .Append("Status code  : ").Append(context.Response.StatusCode).Append(" \n")
                                    .Append("Header       : ").Append(string.Join(", ", context.Response.Headers.Select(h => $"{h.Key}: {h.Value}").ToArray())).Append(" \n");

                var originalBodyStream = context.Response.Body;
                using var memoryStream = RecyclableStreamFactory.GetStream();
                context.Response.Body = memoryStream;
                await next(context);
                memoryStream.Position = 0;
                var body = await new StreamReader(memoryStream).ReadToEndAsync();
                memoryStream.Position = 0;
                var endpoint = context.Request.Path.Value;
                if (endpoint != null && !(endpoint.Contains("logo", StringComparison.OrdinalIgnoreCase)
                                            || endpoint.Contains("avatar", StringComparison.OrdinalIgnoreCase)
                                            || endpoint.Contains("image", StringComparison.OrdinalIgnoreCase)))
                {
                    logBuilder.Append("Body       : ").Append(body).Append(" \n")
                        .Append(" \n");
                }
                await memoryStream.CopyToAsync(originalBodyStream);
                logger.Information(logBuilder.ToString());
            }
        }
        catch (Exception e)
        {
            logger.Error(e, "An error occurred while logging the response: {Error} at {StackTrace}", e.Message, e.StackTrace);
        }
    }
}
