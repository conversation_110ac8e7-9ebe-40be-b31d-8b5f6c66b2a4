using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Databases.Queryables;

namespace Kantoku.Api.Databases.Repositories;

public interface IEventCalendarRepository : IBaseRepository<EventCalendar>
{
    Task<EventCalendar?> GetById(Guid eventUid, EventCalendarQueryableOptions options);
    Task<IEnumerable<EventCalendar>> GetByDate(DateOnly from, DateOnly to, EventCalendarQueryableOptions options);
    Task<(IEnumerable<EventCalendar>, int)> GetByFilter(EventCalendarFilter filter, EventCalendarQueryableOptions options);
    Task<bool> IsExists(string eventName);
    Task<EventCalendar?> Create(EventCalendar eventCalendar, EventCalendarQueryableOptions options);
    Task<EventCalendar?> Update(EventCalendar eventCalendar, EventCalendarQueryableOptions options);

    Task<Guid?> Create(EventCalendar eventCalendar);
    Task<bool> Update(EventCalendar eventCalendar);
}

[Repository(ServiceLifetime.Scoped)]
public class EventCalendarRepository : BaseRepository<EventCalendar>, IEventCalendarRepository
{
    private readonly IEventCalendarQueryable eventCalendarQueryable;
    public EventCalendarRepository(
        PostgreDbContext context,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor,
        IEventCalendarQueryable eventCalendarQueryable
    ) : base(context, logger, httpContextAccessor)
    {
        this.eventCalendarQueryable = eventCalendarQueryable;
    }

    public async Task<EventCalendar?> GetById(Guid eventUid, EventCalendarQueryableOptions options)
    {
        try
        {
            var query = eventCalendarQueryable.GetEventCalendarQuery(options)
                .Where(e => e.EventUid == eventUid);
            return await query.FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting event calendar by id");
            return null;
        }
    }

    public async Task<(IEnumerable<EventCalendar>, int)> GetByFilter(EventCalendarFilter filter, EventCalendarQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = eventCalendarQueryable.GetEventCalendarQueryFiltered(filter, options);

            var eventCalendars = await query
                .OrderBy(e => e.EventUid)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var totalRecords = await query
                .CountAsync();

            return (eventCalendars, totalRecords);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting event calendars");
            return ([], 0);
        }
    }

    public async Task<IEnumerable<EventCalendar>> GetByDate(DateOnly from, DateOnly to, EventCalendarQueryableOptions options)
    {
        try
        {
            var query = eventCalendarQueryable.GetEventCalendarQuery(options);
            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting event calendars by date range");
            return [];
        }
    }

    public async Task<bool> IsExists(string eventName)
    {
        try
        {
            return await context.EventCalendars.AnyAsync(e => e.EventName.Equals(eventName));
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error checking if event exists");
            return false;
        }
    }

    public async Task<EventCalendar?> Create(EventCalendar eventCalendar, EventCalendarQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.EventCalendars.Add(eventCalendar);
            var result = await context.SaveChangesAsync();
            if (result > 0)
            {
                await transaction.CommitAsync();
                return await GetById(eventCalendar.EventUid, new EventCalendarQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating event calendar");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<EventCalendar?> Update(EventCalendar eventCalendar, EventCalendarQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.EventCalendars.Update(eventCalendar);
            var result = await context.SaveChangesAsync();
            if (result > 0)
            {
                await transaction.CommitAsync();
                return await GetById(eventCalendar.EventUid, new EventCalendarQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating event calendar");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Guid?> Create(EventCalendar eventCalendar)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.EventCalendars.Add(eventCalendar);
            var result = await context.SaveChangesAsync();
            if (result > 0)
            {
                await transaction.CommitAsync();
                return eventCalendar.EventUid;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating event calendar");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<bool> Update(EventCalendar eventCalendar)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.EventCalendars.Update(eventCalendar);
            var result = await context.SaveChangesAsync();
            if (result > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating event calendar");
            await transaction.RollbackAsync();
            return false;
        }
    }
}