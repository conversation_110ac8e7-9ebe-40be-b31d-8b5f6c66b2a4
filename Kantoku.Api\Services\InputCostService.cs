using Kantoku.Api.Databases.Models;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.InputCost.Request;
using Kantoku.Api.Dtos.InputCost.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public interface IInputCostService
{
    Task<ResultDto<DetailedInputCostResponseDto>> GetById(Guid inputCostId);
    Task<ResultDto<InputCostsResponseDto>> GetByFilter(InputCostFilter filter);
    Task<ResultDto<byte[]>> GetImage(string imageUrl, string orgId);
    Task<ResultDto<DetailedInputCostResponseDto>> Create(CreateInputCostRequestDto requestDto);
    Task<ResultDto<DetailedInputCostResponseDto>> Update(Guid inputCostId, UpdateInputCostRequestDto requestDto);
    Task<ResultDto<bool>> Delete(Guid inputCostId);
    Task<ResultDto<bool>> DeleteImage(Guid inputCostId, string imageUrl);
}

[Service(ServiceLifetime.Scoped)]
public partial class InputCostService : BaseService<InputCostService>, IInputCostService
{
    private readonly IInputCostRepository inputCostRepository;
    private readonly IInputCostItemRepository inputCostItemRepository;
    private readonly IVendorRepository vendorRepository;
    private readonly IItemRepository itemRepository;
    private readonly IFileService fileService;
    private readonly InputCostQueryableOptions options = new()
    {
        IncludedProject = true,
        IncludedEntryType = true,
        IncludedVendor = true,
        IncludedPaymentType = true,
        IncludedInputCostItems = true,
        IncludedConstruction = true,
    };
    public InputCostService(
        IInputCostRepository inputCostRepository,
        IInputCostItemRepository inputCostItemRepository,
        IVendorRepository vendorRepository,
        IItemRepository itemRepository,
        IFileService fileService,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor) : base(logger, httpContextAccessor)
    {
        this.inputCostRepository = inputCostRepository;
        this.inputCostItemRepository = inputCostItemRepository;
        this.vendorRepository = vendorRepository;
        this.itemRepository = itemRepository;
        this.fileService = fileService;
    }

    public async Task<ResultDto<InputCostsResponseDto>> GetByFilter(InputCostFilter filter)
    {
        var (total, inputCosts) = await inputCostRepository.GetByFilter(filter, options);
        if (inputCosts.Any() == false || total == 0)
        {
            logger.Information("No input cost found");
            return new ErrorResultDto<InputCostsResponseDto>(ResponseCodeConstant.INPUTCOST_NOT_EXIST);
        }

        var result = inputCosts.ToInputCostsResponseDto(filter.PageNum, filter.PageSize, total);
        return new SuccessResultDto<InputCostsResponseDto>(result);
    }

    public async Task<ResultDto<DetailedInputCostResponseDto>> GetById(Guid inputCostId)
    {
        var inputCost = await inputCostRepository.GetById(inputCostId, options);
        if (inputCost is null)
        {
            logger.Error("Input cost not found");
            return new ErrorResultDto<DetailedInputCostResponseDto>(ResponseCodeConstant.INPUTCOST_NOT_EXIST);
        }
        var result = inputCost.ToDetailedInputCostResponseDto();
        return new SuccessResultDto<DetailedInputCostResponseDto>(result);
    }

    public async Task<ResultDto<byte[]>> GetImage(string imageUrl, string orgId)
    {
        var file = await fileService.DownloadFile(imageUrl, orgId);
        if (file == null || file.Data == null || file.Data.DataAsBytes == null)
        {
            logger.Error("Image not found");
            return new ErrorResultDto<byte[]>(ResponseCodeConstant.NOT_EXIST);
        }
        return new SuccessResultDto<byte[]>(file.Data.DataAsBytes);
    }

    public async Task<ResultDto<DetailedInputCostResponseDto>> Create(CreateInputCostRequestDto requestDto)
    {
        if (!GetCurrentOrgGuid(out var orgUid))
        {
            logger.Error("Organization not found");
            return new ErrorResultDto<DetailedInputCostResponseDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }

        var newInputCost = requestDto.ToEntity(orgUid);
        if (newInputCost is null)
        {
            logger.Error("Input cost create failed");
            return new ErrorResultDto<DetailedInputCostResponseDto>(ResponseCodeConstant.INPUTCOST_CREATE_FAILED);
        }
        if (requestDto.Images != null && requestDto.Images.Any())
        {
            var imageUrls = await UploadImages(requestDto.Images, newInputCost);
            if (imageUrls != null && imageUrls.Any())
            {
                newInputCost.ImageUrls = imageUrls.ToList();
            }
        }

        var createdInputCost = await inputCostRepository.Create(newInputCost, options);
        if (createdInputCost is null)
        {
            logger.Error("Input cost create failed");
            return new ErrorResultDto<DetailedInputCostResponseDto>(ResponseCodeConstant.INPUTCOST_CREATE_FAILED);
        }

        var result = createdInputCost.ToDetailedInputCostResponseDto();
        return new SuccessResultDto<DetailedInputCostResponseDto>(result);
    }

    public async Task<ResultDto<DetailedInputCostResponseDto>> Update(Guid inputCostId, UpdateInputCostRequestDto requestDto)
    {
        options.IsTracking = true;
        var existInputCost = await inputCostRepository.GetById(inputCostId, options);
        if (existInputCost is null)
        {
            logger.Error("Input cost not found");
            return new ErrorResultDto<DetailedInputCostResponseDto>(ResponseCodeConstant.INPUTCOST_NOT_EXIST);
        }
        existInputCost.UpdateFromDto(requestDto);

        if (requestDto.Images?.Any() == true)
        {
            var imageUrls = await UploadImages(requestDto.Images, existInputCost, false);
            if (imageUrls?.Any() == true)
            {
                var existImageUrls = existInputCost.ImageUrls?.OrderBy(url => url).ToList() ?? [];
                existInputCost.ImageUrls = existImageUrls.Concat(imageUrls).ToList();
            }
        }

        var updatedInputCost = await inputCostRepository.Update(existInputCost, options);
        if (updatedInputCost is null)
        {
            logger.Error("Input cost update failed");
            return new ErrorResultDto<DetailedInputCostResponseDto>(ResponseCodeConstant.INPUTCOST_UPDATE_FAILED);
        }

        var result = updatedInputCost.ToDetailedInputCostResponseDto();
        return new SuccessResultDto<DetailedInputCostResponseDto>(result);
    }

    public async Task<ResultDto<bool>> Delete(Guid inputCostId)
    {
        var existInputCost = await inputCostRepository.GetById(inputCostId, new InputCostQueryableOptions());
        if (existInputCost is null)
        {
            logger.Error("Input cost not found");
            return new ErrorResultDto<bool>(ResponseCodeConstant.INPUTCOST_NOT_EXIST);
        }
        existInputCost.IsDeleted = true;
        var isDeleted = await inputCostRepository.Update(existInputCost);
        if (!isDeleted)
        {
            logger.Error("Input cost delete failed");
            return new ErrorResultDto<bool>(ResponseCodeConstant.INPUTCOST_DELETE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> DeleteImage(Guid inputCostId, string imageUrl)
    {
        var existInputCost = await inputCostRepository.GetById(inputCostId, new InputCostQueryableOptions());
        if (existInputCost is null)
        {
            logger.Error("Input cost not found");
            return new ErrorResultDto<bool>(ResponseCodeConstant.INPUTCOST_NOT_EXIST);
        }
        if (existInputCost.ImageUrls == null || !existInputCost.ImageUrls.Any(url => url == imageUrl))
        {
            logger.Error("Input cost image not found");
            return new ErrorResultDto<bool>(ResponseCodeConstant.INPUTCOST_UPDATE_FAILED);
        }
        var isDeleted = await fileService.DeleteFileAsync(imageUrl);
        if (isDeleted.Data == false)
        {
            logger.Error("Input cost image delete failed");
            return new ErrorResultDto<bool>(ResponseCodeConstant.INPUTCOST_UPDATE_FAILED);
        }
        existInputCost.ImageUrls = existInputCost.ImageUrls.Where(url => url != imageUrl).ToList();
        var isUpdated = await inputCostRepository.Update(existInputCost, new InputCostQueryableOptions());
        if (isUpdated is null)
        {
            logger.Error("Input cost image update failed");
            return new ErrorResultDto<bool>(ResponseCodeConstant.INPUTCOST_UPDATE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }

    private async Task<IEnumerable<string>> UploadImages(IEnumerable<IFormFile> images, InputCost inputCost, bool isCreate = true)
    {
        var path = StorageConstant.InvoiceImage(inputCost.InputCostUid.ToString());
        var existFileNames = inputCost.ImageUrls?.OrderBy(url => url).ToList() ?? [];
        var latestIndex = existFileNames
            .Select(url =>
            {
                var match = FileNameRegex().Match(url);
                return match.Success ? int.Parse(match.Groups[1].Value) : 0;
            })
            .DefaultIfEmpty(0)
            .Max();

        var renamedImages = images.Select((img, i) =>
        {
            var newFileName = $"{inputCost.OriginalNumber}_{latestIndex + i + 1}.jpg";

            var ms = new MemoryStream();
            img.CopyTo(ms);
            ms.Position = 0;

            return new FormFile(
                baseStream: ms,
                baseStreamOffset: 0,
                length: ms.Length,
                name: newFileName,
                fileName: newFileName)
            {
                Headers = new HeaderDictionary(),
                ContentType = img.ContentType ?? "image/jpeg"
            };
        }).ToList();

        try
        {
            var uploadResults = await fileService.UploadFilesAsync(renamedImages, path);
            if (uploadResults?.Data?.Items?.Any() == true)
            {
                var newUrls = uploadResults.Data.Items
                    .Where(r => r.FileUrl != null)
                    .Select(r => r.FileUrl!)
                    .ToList();
                if (isCreate)
                {
                    inputCost.ImageUrls = (inputCost.ImageUrls ?? []).Concat(newUrls).ToList();
                    await inputCostRepository.Update(inputCost, new InputCostQueryableOptions());
                }
                return newUrls;
            }
            return [];
        }
        finally
        {
            foreach (var file in renamedImages)
            {
                file.OpenReadStream().Dispose();
            }
        }
    }

    [System.Text.RegularExpressions.GeneratedRegex(@"_(\d+)\.jpg$")]
    private static partial System.Text.RegularExpressions.Regex FileNameRegex();
}
