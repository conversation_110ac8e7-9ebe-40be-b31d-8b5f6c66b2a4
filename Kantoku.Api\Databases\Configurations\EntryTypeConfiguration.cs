using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class EntryTypeConfiguration(string schema) : IEntityTypeConfiguration<EntryType>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<EntryType> builder)
    {
        builder.HasKey(e => e.EntryTypeUid).HasName("entry_type_pkey");

        builder.ToTable("entry_type", Schema);

        builder.Property(e => e.EntryTypeUid)
            .HasColumnName("entry_type_uid");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.EntryTypeName)
            .HasColumnName("entry_type_name");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");

        builder.HasOne(d => d.Org)
            .WithMany(p => p.EntryTypes)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("entry_type_org_id_fkey");
    }
} 