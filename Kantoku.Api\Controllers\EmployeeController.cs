using Kantoku.Api.Dtos.Employee.Request;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Employee.Response;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class EmployeeController : BaseController
{
    private readonly IEmployeeService employeeService;
    private readonly IAuditLogService auditLogService;

    public EmployeeController(
        ITResponseFactory responseFactory,
        IEmployeeService employeeService,
        IAuditLogService auditLogService) : base(responseFactory)
    {
        this.employeeService = employeeService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get all employees in current organization with filtering
    /// </summary>
    /// <param name="filter">Filter parameters for employee search</param>
    /// <returns>Filtered list of employees</returns>
    [HttpGet]
    public async Task<GeneralResponse<EmployeesResponseDto>> GetAllEmployees([FromQuery] EmployeeFilter filter)
    {
        try
        {
            var result = await employeeService.GetEmployeesByFilter(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<EmployeesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get simplified employee information for all employees in current organization
    /// </summary>
    /// <returns>List of simplified employee information</returns>
    [HttpGet("simple")]
    public async Task<GeneralResponse<SimpleEmployeesResponseDto>> GetSimpleEmployeeInfo()
    {
        try
        {
            var result = await employeeService.GetSimpleEmployees();
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<SimpleEmployeesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get all employees in current organization with filtering
    /// </summary>
    /// <param name="projectId">Project ID</param>
    /// <returns>Filtered list of   </returns>
    [HttpGet("project-managers")]
    public async Task<GeneralResponse<BaseEmployeesResponseDto>> GetProjectManagers([FromQuery] Guid? projectId)
    {
        try
        {
            var result = await employeeService.GetProjectManagers(projectId);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<BaseEmployeesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get all employees in current organization with approval authority
    /// </summary>
    /// <returns>Filtered list of employees with approval authority</returns>
    [HttpGet("approval-authority")]
    public async Task<GeneralResponse<BaseEmployeesResponseDto>> GetApprovalAuthority()
    {
        try
        {
            var result = await employeeService.GetHasApprovalAuthorityEmployees();
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<BaseEmployeesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get avatars for all employees in current organization
    /// </summary>
    /// <returns>List of employee avatars</returns>
    [HttpGet("avatar")]
    public async Task<GeneralResponse<EmployeesAvatarResponseDto>> GetAvatar()
    {
        try
        {
            var result = await employeeService.GetEmployeeAvatar();
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<EmployeesAvatarResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get detailed information about the current logged-in employee
    /// </summary>
    /// <returns>Current employee's detailed information</returns>
    [HttpGet("current/info")]
    public async Task<GeneralResponse<EmployeeResponseDto>> GetCurrentUserInfo()
    {
        try
        {
            var result = await employeeService.GetEmployeeInfo();
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<EmployeeResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get detailed information about the current logged-in employee
    /// </summary>
    /// <returns>Current employee's detailed information</returns>
    [HttpGet("current/role")]
    public async Task<GeneralResponse<EmployeeRolesResponseDto>> GetCurrentUserRole()
    {
        try
        {
            var result = await employeeService.GetEmployeeRole();
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<EmployeeRolesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Check if the current logged-in employee is a project manager
    /// </summary>
    /// <returns>True if the employee is a project manager, false otherwise</returns>
    [HttpGet("current/manager")]
    public async Task<GeneralResponse<bool>> IsProjectManager()
    {
        try
        {
            var result = await employeeService.IsProjectManager();
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get invited employees in current organization
    /// </summary>
    /// <param name="filter">Filter parameters for invited employees</param>
    /// <returns>Filtered list of invited employees</returns>
    [HttpGet("invitation")]
    public async Task<GeneralResponse<InvitedEmployeesResponseDto>> GetInvitedEmployees([FromQuery] EmployeeInvitationFilter filter)
    {
        try
        {
            var result = await employeeService.GetInvitedEmployees(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<InvitedEmployeesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Send invitation to an employee to join the current organization
    /// </summary>
    /// <param name="requestDto">Employee email information</param>
    /// <returns>Success response</returns>
    [HttpPost("invitation")]
    public async Task<GeneralResponse<bool>> InviteEmployee([FromBody] EmployeeInvitationRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<bool>();

            await employeeService.InviteEmployee(requestDto);
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete an invitation employee from the current organization
    /// </summary>
    /// <param name="invitationId">Employee invitation ID</param>
    /// <returns>Success response</returns>
    [HttpDelete("invitation/{invitationId}")]
    public async Task<GeneralResponse<bool>> DeleteInvitationEmployee([FromRoute] Guid invitationId)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<bool>();

            await employeeService.DeleteInvitedEmployee(invitationId);
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update existing employee information
    /// </summary>
    /// <param name="id">Employee ID to update</param>
    /// <param name="requestDto">Updated employee details</param>
    /// <returns>Updated employee information</returns>
    [HttpPut("{id}")]
    public async Task<GeneralResponse<EmployeeResponseDto>> UpdateEmployee([FromRoute] Guid id, [FromBody] UpdateEmployeeRequestDto requestDto)
    {
        try
        {
            var result = await employeeService.UpdateEmployee(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<EmployeeResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete an employee from the organization
    /// </summary>
    /// <param name="id">Employee ID to delete</param>
    /// <returns>Success response</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> DeleteEmployee([FromRoute] Guid id)
    {
        try
        {
            await employeeService.DeleteEmployee(id);
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for a specific employee
    /// </summary>
    /// <param name="id">Employee ID</param>
    /// <param name="filter">Filter parameters for audit logs</param>
    /// <returns>Filtered audit log entries</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetLogs([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<AuditLogResponseDto>();

            var result = await auditLogService.GetAuditLogsByEntity<Employee>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}