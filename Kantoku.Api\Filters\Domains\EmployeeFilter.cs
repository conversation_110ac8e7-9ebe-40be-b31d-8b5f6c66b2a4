using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Filters.Domains;

public class EmployeeFilter : BaseFilter
{
    [FromQuery(Name = "keyword")]
    public string? Keyword { get; set; }

    [FromQuery(Name = "employeeType")]
    public bool? EmployeeType { get; set; }

    [FromQuery(Name = "structureId")]
    public Guid? StructureId { get; set; }

    [FromQuery(Name = "positionId")]
    public Guid? PositionId { get; set; }

    [FromQuery(Name = "rankId")]
    public Guid? RankId { get; set; }

    [FromQuery(Name = "workingStatus")]
    public string? WorkingStatus { get; set; }

    [FromQuery(Name = "hasApprovalAuthority")]
    public bool? HasApprovalAuthority { get; set; }
}
