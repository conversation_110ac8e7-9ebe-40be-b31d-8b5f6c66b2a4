namespace Kantoku.Processor.Models.BatchJobManager;

/// <summary>
/// Represents the execution history of a batch job
/// </summary>
public class BatchJobHistory
{
    /// <summary>
    /// Unique identifier for the job history record
    /// </summary>
    public Guid FiredId { get; set; }
    
    /// <summary>
    /// Foreign key to the associated BatchJob
    /// </summary>
    public Guid JobId { get; set; }
    
    /// <summary>
    /// When the job execution started
    /// </summary>
    public DateTime StartedAt { get; set; }
    
    /// <summary>
    /// When the job execution completed (null if still running or terminated unexpectedly)
    /// </summary>
    public DateTime? CompletedAt { get; set; }
    
    /// <summary>
    /// Current status of the job execution
    /// </summary>
    public JobExecutionStatus Status { get; set; }
    
    /// <summary>
    /// Error message if the job failed
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// Detailed execution log
    /// </summary>
    public string? ExecutionLog { get; set; }
}

/// <summary>
/// Represents the status of a job execution
/// </summary>
public enum JobExecutionStatus
{
    /// <summary>
    /// Job is currently running
    /// </summary>
    InProgress = 0,
    
    /// <summary>
    /// Job completed successfully
    /// </summary>
    Success = 1,
    
    /// <summary>
    /// Job failed due to an error
    /// </summary>
    Failed = 2,
    
    /// <summary>
    /// Job was manually cancelled
    /// </summary>
    Cancelled = 3,
    
    /// <summary>
    /// Job timed out
    /// </summary>
    TimedOut = 4
}
