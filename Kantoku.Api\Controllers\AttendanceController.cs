using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.EmployeeShift.Request;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Dtos.EmployeeShift.Response;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class AttendanceController : BaseController
{
    private readonly IEmployeeShiftService attendanceService;
    private readonly IAuditLogService auditLogService;

    public AttendanceController(
        ITResponseFactory responseFactory,
        IEmployeeShiftService attendanceService,
        IAuditLogService auditLogService
    ) : base(responseFactory)
    {
        this.attendanceService = attendanceService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get shift of current login user by date range
    /// </summary>
    /// <param name="fromDate">Query the result from this date</param>
    /// <param name="toDate">Query the result to this date</param>
    /// <returns>List of employee shifts within date range</returns>
    [HttpGet]
    public async Task<GeneralResponse<ShiftsResponseDto>> GetShiftByDate([FromQuery] string fromDate, [FromQuery] string toDate)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ShiftsResponseDto>();
            if (!DateOnly.TryParse(fromDate, out var dateOnlyFrom) || !DateOnly.TryParse(toDate, out var dateOnlyTo))
            {
                return BadRequest<ShiftsResponseDto>();
            }
            var res = await attendanceService.GetEmployeeShiftByDate(dateOnlyFrom, dateOnlyTo);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ShiftsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get shift of current login user by date range
    /// </summary>
    /// <param name="shiftId">Shift id</param>
    /// <returns>List of employee shifts within date range</returns>
    [HttpGet("{shiftId}")]
    public async Task<GeneralResponse<ShiftResponseDto>> GetShiftById([FromRoute] Guid shiftId)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ShiftResponseDto>();
            var res = await attendanceService.GetEmployeeShiftByid(shiftId);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ShiftResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get summary of shift of specific employee by date range
    /// </summary>
    /// <param name="employeeId">Employee id</param>
    /// <param name="fromDate">Query the result from this date</param>
    /// <param name="toDate">Query the result to this date</param>
    /// <returns>List of employee shifts for specified employee within date range</returns>
    [HttpGet("employee/{employeeId}")]
    public async Task<GeneralResponse<ShiftsResponseDto>> GetEmployeeShift([FromRoute] Guid employeeId, [FromQuery] string fromDate, [FromQuery] string toDate)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ShiftsResponseDto>();

            if (!DateOnly.TryParse(fromDate, out var dateOnlyFrom) || !DateOnly.TryParse(toDate, out var dateOnlyTo))
            {
                return BadRequest<ShiftsResponseDto>();
            }
            var res = await attendanceService.GetEmployeeShiftByDate(dateOnlyFrom, dateOnlyTo, employeeId);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ShiftsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get shift details for specific project on given date
    /// </summary>
    /// <param name="projectId">Project id</param>
    /// <param name="date">Working date</param>
    /// <returns>List of employee shifts for specified project</returns>
    [HttpGet("projects/{projectId}")]
    public async Task<GeneralResponse<WorksiteShiftsResponseDto>> GetProjectAttendance([FromRoute] Guid projectId, [FromQuery] string date)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<WorksiteShiftsResponseDto>();

            if (!DateOnly.TryParse(date, out var dateFrom))
            {
                return BadRequest<WorksiteShiftsResponseDto>();
            }

            var dateTo = dateFrom.AddDays(1);
            var res = await attendanceService.GetEmpShiftByProject(projectId, dateFrom, dateTo);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<WorksiteShiftsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get shift details for all managed worksites on given date
    /// </summary>
    /// <param name="date">Working date</param>
    /// <returns>List of employee shifts grouped by worksite</returns>
    [HttpGet("manages")]
    public async Task<GeneralResponse<WorksitesShiftsResponseDto>> GetWorksitesAttendance([FromQuery] string date)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<WorksitesShiftsResponseDto>();
            if (!DateOnly.TryParse(date, out var dateFrom))
            {
                return BadRequest<WorksitesShiftsResponseDto>();
            }
            var dateTo = dateFrom.AddDays(1);
            var res = await attendanceService.GetEmpShiftByProjects(dateFrom, dateTo);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<WorksitesShiftsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// add new employee shift in case employee has forgot to checkin
    /// </summary>
    /// <param name="dto">Checkin request details</param>
    /// <returns>Created shift information</returns>
    [HttpPost]
    public async Task<GeneralResponse<ShiftResponseDto>> AddShift([FromBody] CreateShiftRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ShiftResponseDto>();

            var res = await attendanceService.AddShift(dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ShiftResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// add new employee shift in case employee has forgot to checkin
    /// </summary>
    /// <param name="dto">Checkin request details</param>
    /// <returns>Created shift information</returns>
    [HttpPost("multiple")]
    public async Task<GeneralResponse<IEnumerable<Guid>>> AddMultipleShift([FromBody] CreateMultipleShiftRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<IEnumerable<Guid>>();

            var res = await attendanceService.CreateMultipleShifts(dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<IEnumerable<Guid>>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Perform unscheduled checkin for employee
    /// </summary>
    /// <param name="dto">Checkin request details</param>
    /// <returns>Created shift information</returns>
    [HttpPost("checkin")]
    public async Task<GeneralResponse<ShiftResponseDto>> Checkin([FromBody] UnscheduledCheckInRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ShiftResponseDto>();

            var res = await attendanceService.Checkin(dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ShiftResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Perform checkin for scheduled shift
    /// </summary>
    /// <param name="shiftId">Shift id</param>
    /// <param name="dto">Checkin request details</param>
    /// <returns>Updated shift information</returns>
    [HttpPut("{shiftId}/checkin")]
    public async Task<GeneralResponse<ShiftResponseDto>> Checkin([FromRoute] Guid shiftId, [FromBody] CheckInOutRequestDto dto)
    {
        try
        {
            var res = await attendanceService.Checkin(shiftId, dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ShiftResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Perform checkout for current shift
    /// </summary>
    /// <param name="shiftId">Shift id</param>
    /// <param name="dto">Checkout request details</param>
    /// <returns>Updated shift information</returns>
    [HttpPut("{shiftId}/checkout")]
    public async Task<GeneralResponse<ShiftResponseDto>> CheckOut([FromRoute] Guid shiftId, [FromBody] CheckInOutRequestDto dto)
    {
        try
        {
            var res = await attendanceService.CheckOut(shiftId, dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ShiftResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Record break start time for current shift
    /// </summary>
    /// <param name="shiftId">Shift id</param>
    /// <returns>Updated shift information</returns>
    [HttpPut("{shiftId}/breakin")]
    public async Task<GeneralResponse<ShiftResponseDto>> BreakIn([FromRoute] Guid shiftId)
    {
        try
        {
            var res = await attendanceService.BreakIn(shiftId);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ShiftResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Record break end time for current shift
    /// </summary>
    /// <param name="shiftId">Shift id</param>
    /// <returns>Updated shift information</returns>
    [HttpPut("{shiftId}/breakout")]
    public async Task<GeneralResponse<ShiftResponseDto>> BreakOut([FromRoute] Guid shiftId)
    {
        try
        {
            var res = await attendanceService.BreakOut(shiftId);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ShiftResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Synchronize shift data from offline mode or create new shift for outsource employee
    /// </summary>
    /// <param name="dto">Shift synchronization details</param>
    /// <returns>Success response</returns>
    [HttpPost("sync")]
    public async Task<GeneralResponse<bool>> SyncShift([FromBody] SynchronizeShiftRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<bool>();

            var res = await attendanceService.SynchronizeOfflineCheckin(dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update shift information for specific employee
    /// </summary>
    /// <param name="shiftId">Shift id</param>
    /// <param name="dto">Updated shift details</param>
    /// <returns>Updated shift information</returns>
    [HttpPut("{shiftId}")]
    public async Task<GeneralResponse<ShiftResponseDto>> UpdateShiftInfo([FromRoute] Guid shiftId, [FromBody] UpdateShiftRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ShiftResponseDto>();

            var res = await attendanceService.UpdateShift(shiftId, dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ShiftResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update multiple shifts for specific employee
    /// </summary>
    /// <param name="dto">Updated shift details</param>
    /// <returns>Updated shift information</returns>
    [HttpPut("multiple")]
    public async Task<GeneralResponse<bool>> UpdateMultipleShifts([FromBody] UpdateMultipleShiftRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<bool>();

            var res = await attendanceService.UpdateMultipleShifts(dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete shift for specific employee
    /// </summary>
    /// <param name="shiftId">Shift id</param>
    /// <returns>Deleted shift information</returns>
    [HttpDelete("{shiftId}")]
    public async Task<GeneralResponse<bool>> DeleteShift([FromRoute] Guid shiftId)
    {
        try
        {
            var res = await attendanceService.DeleteShift(shiftId);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Alternative endpoint to request approval for daily attendance
    /// </summary>
    /// <param name="shiftId">Shift id</param>
    /// <returns>Created approval request information</returns>
    [HttpPost("{shiftId}/request")]
    public async Task<GeneralResponse<ShiftResponseDto>> RequestForApproval2([FromRoute] Guid shiftId)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ShiftResponseDto>();

            var res = await attendanceService.RequestDailyApproval(shiftId);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ShiftResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Alternative endpoint to approve daily attendance request
    /// </summary>
    /// <param name="shiftId">Shift id</param>
    /// <param name="dto">Approval request details</param>
    /// <returns>Updated approval information</returns>
    [HttpPut("{shiftId}/approve")]
    public async Task<GeneralResponse<ShiftResponseDto>> ApproveAttendance2([FromRoute] Guid shiftId, [FromBody] CreateDailyApprovalRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<ShiftResponseDto>();

            var res = await attendanceService.ApproveEmployeeShift(shiftId, dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<ShiftResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for specific employee shift
    /// </summary>
    /// <param name="id">Employee shift id</param>
    /// <param name="filter">Audit log filter parameters</param>
    /// <returns>List of audit log entries</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetAttendanceLogs(
        [FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<AuditLogResponseDto>();

            var res = await auditLogService.GetAuditLogsByEntity<EmployeeShift>(id, filter);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
