using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Helpers;
using Kantoku.Api.Dtos.OutSource.Request;
using Kantoku.Api.Dtos.OutSource.Response;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Dtos.File;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public interface IOutSourceService
{
    Task<ResultDto<OutSourceResponseDto>> GetOutSourceById(Guid outSourceId);
    Task<ResultDto<byte[]>> GetLogo(Guid outSourceId, string orgId);
    Task<ResultDto<OutSourcesResponseDto>> GetOutSourceByFilter(OutSourceFilter filter);
    Task<ResultDto<OutSourceResponseDto>> CreateOutSource(CreateOutSourceRequestDto requestDto);
    Task<ResultDto<OutSourceResponseDto>> UpdateOutSource(Guid outSourceId, UpdateOutSourceRequestDto requestDto);
    Task<ResultDto<bool>> DeleteOutSource(Guid outSourceId);

    Task<ResultDto<OutSourcePricesResponseDto>> GetOutSourcePrice(OutSourcePriceFilter filter);
    Task<ResultDto<OutSourcePricesResponseDto>> GetOutSourcePriceByOutSourceUid(Guid outSourceId, OutSourcePriceFilter filter);
    Task<ResultDto<OutSourcePriceResponseDto>> CreateOutSourcePrice(Guid outSourceId, OutSourcePriceRequestDto requestDto);
    Task<ResultDto<OutSourcePriceResponseDto>> UpdateOutSourcePrice(Guid outSourcePriceId, OutSourcePriceRequestDto requestDto);
}


[Service(ServiceLifetime.Scoped)]
public class OutSourceService : BaseService<OutSourceService>, IOutSourceService
{
    private readonly IOutSourceRepository outSourceRepository;
    private readonly IOutSourcePriceRepository outSourcePriceRepository;
    private readonly IFileService fileService;

    public OutSourceService(
        IOutSourceRepository outSourceRepository,
        IOutSourcePriceRepository outSourcePriceRepository,
        IFileService fileService,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor) : base(logger, httpContextAccessor)
    {
        this.outSourceRepository = outSourceRepository;
        this.outSourcePriceRepository = outSourcePriceRepository;
        this.fileService = fileService;
    }

    public async Task<ResultDto<OutSourcesResponseDto>> GetOutSourceByFilter(OutSourceFilter filter)
    {
        var (outSources, total) = await outSourceRepository.GetByFilter(filter, new OutSourceQueryableOptions());
        if (!outSources.Any() || total == 0)
        {
            logger.Information("No outSources found");
            return new ErrorResultDto<OutSourcesResponseDto>(ResponseCodeConstant.OUTSOURCE_NOT_EXIST);
        }
        var result = outSources.ToOutSourcesResponseDto(filter.PageNum, filter.PageSize, total);
        return new SuccessResultDto<OutSourcesResponseDto>(result);
    }

    public async Task<ResultDto<OutSourceResponseDto>> GetOutSourceById(Guid outSourceId)
    {
        var outSource = await outSourceRepository.GetById(outSourceId, new OutSourceQueryableOptions());
        if (outSource is null)
        {
            logger.Error("OutSource {OutSourceId} not found", outSourceId);
            return new ErrorResultDto<OutSourceResponseDto>(ResponseCodeConstant.OUTSOURCE_NOT_EXIST);
        }
        var res = outSource.ToOutSourceResponseDto();
        return new SuccessResultDto<OutSourceResponseDto>(res);
    }

    public async Task<ResultDto<byte[]>> GetLogo(Guid outSourceId, string orgId)
    {
        var outSource = await outSourceRepository.GetById(outSourceId, new OutSourceQueryableOptions());
        if (outSource == null)
        {
            logger.Error("OutSource {OutSourceId} not found", outSourceId);
            return new ErrorResultDto<byte[]>(ResponseCodeConstant.OUTSOURCE_NOT_EXIST);
        }
        if (outSource.LogoUrl == null)
        {
            logger.Error("OutSource {OutSourceId} have no logo", outSourceId);
            return new ErrorResultDto<byte[]>(ResponseCodeConstant.OUTSOURCE_LOGO_NOT_EXIST);
        }
        var logo = await DownloadLogo(outSource.LogoUrl, orgId);
        if (logo == null || logo.Length == 0)
        {
            logger.Error("OutSource {OutSourceId} logo not found", outSourceId);
            return new ErrorResultDto<byte[]>(ResponseCodeConstant.OUTSOURCE_LOGO_NOT_EXIST);
        }
        return new SuccessResultDto<byte[]>(logo);
    }

    public async Task<ResultDto<OutSourceResponseDto>> CreateOutSource(CreateOutSourceRequestDto requestDto)
    {
        if (!GetCurrentOrgGuid(out var orgGuid))
        {
            logger.Error("Failed to get current org guid");
            return new ErrorResultDto<OutSourceResponseDto>(ResponseCodeConstant.OUTSOURCE_CREATE_FAILED);
        }
        var newOutSource = requestDto.ToEntity(orgGuid);
        if (newOutSource is null)
        {
            logger.Error("Failed to create outSource");
            return new ErrorResultDto<OutSourceResponseDto>(ResponseCodeConstant.OUTSOURCE_CREATE_FAILED);
        }
        if (requestDto.Logo is not null)
        {
            var logoResponse = await UploadLogo(newOutSource.OutSourceUid.ToString(), requestDto.Logo);
            if (logoResponse is not null)
            {
                newOutSource.LogoUrl = logoResponse.FileUrl;
            }
        }
        var createdOutSource = await outSourceRepository.Create(newOutSource);
        if (createdOutSource is null)
        {
            logger.Error("Failed to create outSource");
            return new ErrorResultDto<OutSourceResponseDto>(ResponseCodeConstant.OUTSOURCE_CREATE_FAILED);
        }
        var result = createdOutSource.ToOutSourceResponseDto();
        return new SuccessResultDto<OutSourceResponseDto>(result);
    }

    public async Task<ResultDto<OutSourceResponseDto>> UpdateOutSource(Guid outSourceId, UpdateOutSourceRequestDto requestDto)
    {
        var existOutSource = await outSourceRepository.GetById(outSourceId, new OutSourceQueryableOptions());
        if (existOutSource is null)
        {
            logger.Error("OutSource {OutSourceId} not found", outSourceId);
            return new ErrorResultDto<OutSourceResponseDto>(ResponseCodeConstant.OUTSOURCE_NOT_EXIST);
        }

        existOutSource.UpdateFromDto(requestDto);
        if (requestDto.Logo is not null)
        {
            _ = await UploadLogo(existOutSource.OutSourceUid.ToString(), requestDto.Logo);
        }

        var updatedOutSource = await outSourceRepository.Update(existOutSource);
        if (updatedOutSource is null)
        {
            logger.Error("Failed to update outSource {OutSourceId}", outSourceId);
            return new ErrorResultDto<OutSourceResponseDto>(ResponseCodeConstant.OUTSOURCE_UPDATE_FAILED);
        }
        var result = updatedOutSource.ToOutSourceResponseDto();
        return new SuccessResultDto<OutSourceResponseDto>(result);
    }

    public async Task<ResultDto<bool>> DeleteOutSource(Guid outSourceId)
    {
        var outSource = await outSourceRepository.GetById(outSourceId, new OutSourceQueryableOptions());
        if (outSource is null)
        {
            logger.Error("OutSource {OutSourceId} not found", outSourceId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.OUTSOURCE_NOT_EXIST);
        }
        outSource.IsDeleted = true;
        var updatedOutSource = await outSourceRepository.Update(outSource);
        if (updatedOutSource is null)
        {
            logger.Error("Failed to delete outSource {OutSourceId}", outSourceId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.OUTSOURCE_DELETE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<OutSourcePricesResponseDto>> GetOutSourcePrice(OutSourcePriceFilter filter)
    {
        var outSourceFilter = new OutSourceFilter
        {
            PageNum = filter.PageNum,
            PageSize = filter.PageSize,
            Keyword = filter.Keyword
        };
        var (outSource, total) = await outSourceRepository.GetByFilter(outSourceFilter, new OutSourceQueryableOptions
        {
            IncludedOutSourcePrice = true
        });
        if (outSource is null || total == 0 || !outSource.Any())
        {
            logger.Error("OutSource have no prices");
            return new ErrorResultDto<OutSourcePricesResponseDto>(ResponseCodeConstant.OUTSOURCE_PRICE_NOT_EXIST);
        }
        var result = OutSourcePriceResponseDtoParser(outSource, filter, total);
        return new SuccessResultDto<OutSourcePricesResponseDto>(result);
    }

    public async Task<ResultDto<OutSourcePricesResponseDto>> GetOutSourcePriceByOutSourceUid(Guid outSourceId, OutSourcePriceFilter filter)
    {
        var (outSourcePrice, total) = await outSourcePriceRepository.GetByOutSourceUid(outSourceId, filter, new OutSourcePriceQueryableOptions
        {
            IncludedOutSource = true
        });
        if (outSourcePrice is null || total == 0 || !outSourcePrice.Any())
        {
            logger.Error("OutSource {OutSourceId} have no prices", outSourceId);
            return new ErrorResultDto<OutSourcePricesResponseDto>(ResponseCodeConstant.OUTSOURCE_PRICE_NOT_EXIST);
        }
        var result = OutSourcePriceResponseDtoParser(outSourcePrice, filter, total);
        return new SuccessResultDto<OutSourcePricesResponseDto>(result);
    }

    public async Task<ResultDto<OutSourcePriceResponseDto>> CreateOutSourcePrice(Guid outSourceId, OutSourcePriceRequestDto requestDto)
    {
        var outSourcePriceOptions = new OutSourcePriceQueryableOptions
        {
            IncludedOutSource = true
        };
        var outSource = await outSourceRepository.GetById(outSourceId, new OutSourceQueryableOptions { IncludedOutSourcePrice = true });
        if (outSource is null)
        {
            logger.Error("OutSource {OutSourceId} not found", outSourceId);
            return new ErrorResultDto<OutSourcePriceResponseDto>(ResponseCodeConstant.OUTSOURCE_NOT_EXIST);
        }
        var currentPrice = await outSourcePriceRepository.GetLatestPrice(outSourceId, outSourcePriceOptions);
        if (currentPrice is not null)
        {
            currentPrice.EffectiveDate = DateOnly.FromDateTime(DateTime.Today);
            var updatedOutSourcePrice = await outSourcePriceRepository.Update(currentPrice, outSourcePriceOptions);
            if (updatedOutSourcePrice is null)
            {
                logger.Error("Failed to update outSourcePrice {OutSourcePriceId}", currentPrice.OutSourcePriceUid);
                return new ErrorResultDto<OutSourcePriceResponseDto>(ResponseCodeConstant.OUTSOURCE_PRICE_UPDATE_FAILED);
            }
            var _createdOutSourcePrice = await outSourcePriceRepository.Create(new OutSourcePrice
            {
                OutSourceUid = outSource.OutSourceUid,
                PricePerDay = requestDto.PricePerDay ?? currentPrice.PricePerDay,
                EffectiveDate = DateOnly.FromDateTime(DateTime.Today),
                Description = requestDto.Description ?? currentPrice.Description
            }, outSourcePriceOptions);
            if (_createdOutSourcePrice is null)
            {
                logger.Error("Failed to create outSourcePrice");
                return new ErrorResultDto<OutSourcePriceResponseDto>(ResponseCodeConstant.OUTSOURCE_PRICE_CREATE_FAILED);
            }
            var _result = OutSourcePriceResponseDtoParser(_createdOutSourcePrice);
            return new SuccessResultDto<OutSourcePriceResponseDto>(_result);
        }

        var createdOutSourcePrice = await outSourcePriceRepository.Create(new OutSourcePrice
        {
            OutSourceUid = outSource.OutSourceUid,
            PricePerDay = requestDto.PricePerDay ?? 0,
            EffectiveDate = DateOnly.FromDateTime(DateTime.Today),
            Description = requestDto.Description
        }, outSourcePriceOptions);
        if (createdOutSourcePrice is null)
        {
            logger.Error("Failed to create outSourcePrice");
            return new ErrorResultDto<OutSourcePriceResponseDto>(ResponseCodeConstant.OUTSOURCE_PRICE_CREATE_FAILED);
        }
        var result = OutSourcePriceResponseDtoParser(createdOutSourcePrice);
        return new SuccessResultDto<OutSourcePriceResponseDto>(result);
    }

    public async Task<ResultDto<OutSourcePriceResponseDto>> UpdateOutSourcePrice(Guid outSourcePriceId, OutSourcePriceRequestDto requestDto)
    {
        var outSourcePriceOptions = new OutSourcePriceQueryableOptions
        {
            IncludedOutSource = true
        };
        var outSourcePrice = await outSourcePriceRepository.GetById(outSourcePriceId, outSourcePriceOptions);
        if (outSourcePrice is null)
        {
            logger.Error("OutSourcePrice {OutSourcePriceId} not found", outSourcePriceId);
            return new ErrorResultDto<OutSourcePriceResponseDto>(ResponseCodeConstant.OUTSOURCE_PRICE_NOT_EXIST);
        }
        try
        {
            ObjectHelper.UpdateEntityFromDto(requestDto, outSourcePrice);
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Failed to update outSourcePrice {OutSourcePriceId}", outSourcePriceId);
            return new ErrorResultDto<OutSourcePriceResponseDto>(ResponseCodeConstant.OUTSOURCE_PRICE_UPDATE_FAILED);
        }

        var updatedOutSourcePrice = await outSourcePriceRepository.Update(outSourcePrice, outSourcePriceOptions);
        if (updatedOutSourcePrice is null)
        {
            logger.Error("Failed to update outSourcePrice {OutSourcePriceId}", outSourcePriceId);
            return new ErrorResultDto<OutSourcePriceResponseDto>(ResponseCodeConstant.OUTSOURCE_PRICE_UPDATE_FAILED);
        }
        var result = OutSourcePriceResponseDtoParser(updatedOutSourcePrice);
        return new SuccessResultDto<OutSourcePriceResponseDto>(result);
    }

    private OutSourcePricesResponseDto OutSourcePriceResponseDtoParser(IEnumerable<OutSourcePrice> outSourcePrice, OutSourcePriceFilter filter, int total)
    {
        try
        {
            var res = outSourcePrice
                .Where(p => !DateOnly.TryParse(filter.StartDate, out var startDate) || startDate <= p.EffectiveDate)
                .Where(p => !DateOnly.TryParse(filter.EndDate, out var endDate) || endDate >= p.EffectiveDate)
                .Select(p => OutSourcePriceResponseDtoParser(p))
                .OrderByDescending(p => p.EffectiveDate)
                .ToList();
            return new OutSourcePricesResponseDto
            {
                Items = res,
                PageNum = filter.PageNum,
                PageSize = filter.PageSize,
                TotalRecords = total
            };
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Failed to parse outSource price response dto");
            return new OutSourcePricesResponseDto();
        }
    }

    private OutSourcePricesResponseDto OutSourcePriceResponseDtoParser(IEnumerable<OutSource> outSource, OutSourcePriceFilter filter, int total)
    {
        try
        {
            var res = outSource
                .SelectMany(o => OutSourcePriceResponseDtoParser(o, filter))
                .OrderBy(p => p.OutSourceCode)
                .OrderByDescending(p => p.EffectiveDate)
                .ToList();

            return new OutSourcePricesResponseDto
            {
                Items = res,
                PageNum = filter.PageNum,
                PageSize = filter.PageSize,
                TotalRecords = total
            };
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Failed to parse outSource price response dto");
            return new OutSourcePricesResponseDto();
        }
    }

    private IEnumerable<OutSourcePriceResponseDto> OutSourcePriceResponseDtoParser(OutSource outSource, OutSourcePriceFilter filter)
    {
        try
        {
            DateOnly? filterStartDate = DateOnly.TryParse(filter.StartDate, out var startDate) ? startDate : null;
            DateOnly? filterEndDate = DateOnly.TryParse(filter.EndDate, out var endDate) ? endDate : null;
            var outSourcePrices = outSource.OutSourcePrices
                .Where(osp => osp.PricePerDay > 0)
                .Where(osp => osp.EffectiveDate >= filterStartDate && osp.EffectiveDate <= filterEndDate)
                .OrderByDescending(p => p.EffectiveDate)
                .ToList();
            if (outSourcePrices.Count == 0)
            {
                return [new OutSourcePriceResponseDto
                {
                    OutSourceId = outSource.OutSourceUid.ToString(),
                    OutSourceCode = outSource.OutSourceCode,
                    OutSourceName = outSource.OutSourceName,
                    EffectiveDate = filterStartDate?.ToString("yyyy-MM-dd"),
                }];
            }

            var res = outSourcePrices
                .Select(outSourcePrice => new OutSourcePriceResponseDto
                {
                    OutSourceId = outSource.OutSourceUid.ToString(),
                    OutSourceCode = outSource.OutSourceCode,
                    OutSourceName = outSource.OutSourceName,
                    OutSourcePriceId = outSourcePrice.OutSourcePriceUid.ToString(),
                    PricePerDay = outSourcePrice.PricePerDay,
                    EffectiveDate = outSourcePrice.EffectiveDate?.ToString("yyyy-MM-dd"),
                    Description = outSourcePrice.Description
                });
            return res;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Failed to parse outSource price response dto");
            return [];
        }
    }

    private OutSourcePriceResponseDto OutSourcePriceResponseDtoParser(OutSourcePrice outSourcePrice)
    {
        try
        {
            var res = new OutSourcePriceResponseDto
            {
                OutSourceId = outSourcePrice.OutSourceUid.ToString(),
                OutSourceCode = outSourcePrice.OutSource.OutSourceCode,
                OutSourceName = outSourcePrice.OutSource.OutSourceName,
                OutSourcePriceId = outSourcePrice.OutSourcePriceUid.ToString(),
                PricePerDay = outSourcePrice.PricePerDay,
                EffectiveDate = outSourcePrice.EffectiveDate?.ToString("yyyy-MM-dd"),
                Description = outSourcePrice.Description
            };
            return res;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Failed to parse outSource price response dto");
            return new OutSourcePriceResponseDto();
        }
    }

    private async Task<FileMetadataResponseDto?> UploadLogo(string outSourceUid, IFormFile logo)
    {
        try
        {
            var path = StorageConstant.OutSourceLogo(outSourceUid);
            var fileName = "logo.jpg";
            var objectName = path + fileName;
            var fileMetadata = await fileService.UploadFileAsync(logo, objectName);
            if (fileMetadata is null || fileMetadata.Data is null)
            {
                logger.Error("Upload logo failed");
                return null;
            }
            return fileMetadata.Data;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error uploading logo");
            return null;
        }
    }

    private async Task<byte[]> DownloadLogo(string logoUrl, string orgId)
    {
        try
        {
            var file = await fileService.DownloadFile(logoUrl, orgId);
            if (file is null || file.Data is null)
            {
                logger.Error("File not found");
                return [];
            }
            return file.Data.DataAsBytes;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting logo");
            return [];
        }
    }
}
