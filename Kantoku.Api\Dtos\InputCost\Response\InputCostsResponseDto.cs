﻿using Kantoku.Api.Dtos.InputCost;

namespace Kantoku.Api.Dtos.InputCost.Response;

/// <summary>
/// Response DTO for input costs with pagination
/// </summary>
public class InputCostsResponseDto
{
    /// <summary>
    /// List of input cost items
    /// </summary>
    public IEnumerable<InputCostResponseDto> Items { get; set; } = [];

    /// <summary>
    /// Current page number
    /// </summary>
    public int PageNum { get; set; }

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of records available
    /// </summary>
    public int TotalRecords { get; set; }
}
