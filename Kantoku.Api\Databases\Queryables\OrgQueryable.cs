using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Utils.Attributes.Class;
using Orgz = Kantoku.Api.Databases.Models.Org;

namespace Kantoku.Api.Databases.Queryables;

public class OrgQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
}

public interface IOrgQueryable
{
    IQueryable<Orgz> GetOrgQuery(
        OrgQueryableOptions options
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class OrgQueryable(PostgreDbContext context) :
    BaseQueryable<Orgz>(context), IOrgQueryable
{

    public IQueryable<Orgz> GetOrgQuery(
        OrgQueryableOptions options
    )
    {
        var query = GetQuery(options);
        return query;
    }
}
