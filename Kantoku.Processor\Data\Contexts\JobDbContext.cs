using Kantoku.Processor.Configurations;
using Kantoku.Processor.Models.BatchJobManager;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;

namespace Kantoku.Processor.Data.Contexts;

/// <summary>
/// Database context for batch job management
/// </summary>
public class JobDbContext : DbContext
{
    public JobDbConfig JobDbConfig { get; }
    public JobDbContext(DbContextOptions<JobDbContext> options, IOptions<JobDbConfig> jobDbConfig)
        : base(options)
    {
        JobDbConfig = jobDbConfig.Value;
    }

    public DbSet<BatchJob> BatchJobs { get; set; } = null!;
    public DbSet<BatchJobHistory> BatchJobHistory { get; set; } = null!;

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        => optionsBuilder.UseNpgsql(JobDbConfig.BuildConnectionString(), x => x.MigrationsHistoryTable("__EFMigrationsHistory", "dev"));

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure BatchJob entity
        modelBuilder.Entity<BatchJob>(entity =>
        {
            entity.HasKey(e => e.JobId).HasName("batch_job_pkey");

            entity.ToTable("batch_job", JobDbConfig.Schema);

            entity.Property(e => e.JobId)
                .HasColumnName("job_id");
            entity.Property(e => e.JobName)
                .HasColumnName("job_name");
            entity.Property(e => e.JobDescription)
                .HasColumnName("job_description");
            entity.Property(e => e.IsEnabled)
                .HasColumnName("is_enabled");
            entity.Property(e => e.Interval)
                .HasColumnName("interval");
            entity.Property(e => e.LastRunAt)
                .HasColumnName("last_run_at");
            entity.Property(e => e.JobType)
                .HasColumnName("job_type");
            entity.Property(e => e.Configuration)
                .HasColumnName("configuration");
        });

        // Configure BatchJobHistory entity
        modelBuilder.Entity<BatchJobHistory>(entity =>
        {
            entity.HasKey(e => e.FiredId).HasName("batch_job_history_pkey");

            entity.ToTable("batch_job_history", JobDbConfig.Schema);

            entity.Property(e => e.FiredId)
                .HasColumnName("fired_id");
            entity.Property(e => e.JobId)
                .HasColumnName("job_id");
            entity.Property(e => e.ErrorMessage)
                .HasColumnName("error_message");
            entity.Property(e => e.ExecutionLog)
                .HasColumnName("execution_log");
            entity.Property(e => e.StartedAt)
                .HasColumnName("started_at");
            entity.Property(e => e.CompletedAt)
                .HasColumnName("completed_at");
            entity.Property(e => e.Status)
                .HasColumnName("status");
        });
    }
}
