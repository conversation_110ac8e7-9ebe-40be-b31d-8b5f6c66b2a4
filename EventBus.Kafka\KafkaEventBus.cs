using Confluent.Kafka;
using EventBus.Interfaces;
using EventBus.Kafka.Configuration;
using EventBus.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Collections.Concurrent;
using System.Text;

namespace EventBus.Kafka;

/// <summary>
/// Kafka implementation of the event bus
/// </summary>
public class KafkaEventBus : IEventBus, IDisposable
{
    private readonly KafkaConfig _config;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<KafkaEventBus> _logger;
    private readonly ConcurrentDictionary<string, List<EventSubscription>> _subscriptions;
    private readonly IProducer<string, string> _producer;
    private IConsumer<string, string>? _consumer;
    private CancellationTokenSource? _cancellationTokenSource;
    private Task? _consumerTask;
    private bool _disposed;

    public KafkaEventBus(
        IOptions<KafkaConfig> config,
        IServiceProvider serviceProvider,
        ILogger<KafkaEventBus> logger)
    {
        _config = config.Value;
        _serviceProvider = serviceProvider;
        _logger = logger;
        _subscriptions = new ConcurrentDictionary<string, List<EventSubscription>>();

        var producerConfig = new ProducerConfig
        {
            BootstrapServers = _config.BootstrapServers,
            Acks = ParseAcks(_config.Producer.Acks),
            BatchSize = _config.Producer.BatchSize,
            LingerMs = _config.Producer.LingerMs
        };

        _producer = new ProducerBuilder<string, string>(producerConfig).Build();
    }

    public async Task PublishAsync<T>(T @event, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var eventType = typeof(T).Name;
            var topicName = GetTopicName(eventType);
            var eventData = JsonConvert.SerializeObject(@event);

            var message = new Message<string, string>
            {
                Key = Guid.NewGuid().ToString(),
                Value = eventData,
                Headers = new Headers
                {
                    { "EventType", Encoding.UTF8.GetBytes(eventType) },
                    { "Timestamp", Encoding.UTF8.GetBytes(DateTime.UtcNow.ToString("O")) }
                }
            };

            var result = await _producer.ProduceAsync(topicName, message, cancellationToken);

            _logger.LogInformation("Published event {EventType} to topic {Topic} at offset {Offset}",
                eventType, topicName, result.Offset);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish event {EventType}", typeof(T).Name);
            throw;
        }
    }

    public void Subscribe<T, TH>()
        where T : class
        where TH : class, IEventHandler<T>
    {
        var eventType = typeof(T).Name;
        var handlerType = typeof(TH).Name;

        var subscription = new EventSubscription
        {
            EventType = eventType,
            HandlerType = handlerType
        };

        _subscriptions.AddOrUpdate(eventType,
            new List<EventSubscription> { subscription },
            (key, existing) =>
            {
                existing.Add(subscription);
                return existing;
            });

        _logger.LogInformation("Subscribed {HandlerType} to {EventType}", handlerType, eventType);
    }

    public void Unsubscribe<T, TH>()
        where T : class
        where TH : class, IEventHandler<T>
    {
        var eventType = typeof(T).Name;
        var handlerType = typeof(TH).Name;

        if (_subscriptions.TryGetValue(eventType, out var subscriptions))
        {
            subscriptions.RemoveAll(s => s.HandlerType == handlerType);
            if (subscriptions.Count == 0)
            {
                _subscriptions.TryRemove(eventType, out _);
            }
        }

        _logger.LogInformation("Unsubscribed {HandlerType} from {EventType}", handlerType, eventType);
    }

    public Task StartAsync(CancellationToken cancellationToken = default)
    {
        if (_consumer != null)
        {
            _logger.LogWarning("Consumer is already started");
            return Task.CompletedTask;
        }

        var consumerConfig = new ConsumerConfig
        {
            BootstrapServers = _config.BootstrapServers,
            GroupId = _config.GroupId,
            AutoOffsetReset = ParseAutoOffsetReset(_config.AutoOffsetReset),
            EnableAutoCommit = _config.EnableAutoCommit,
            SessionTimeoutMs = _config.SessionTimeoutMs,
            FetchMinBytes = _config.Consumer.FetchMinBytes
        };

        _consumer = new ConsumerBuilder<string, string>(consumerConfig).Build();
        _cancellationTokenSource = new CancellationTokenSource();

        var topics = _subscriptions.Keys.Select(GetTopicName).ToList();
        if (topics.Any())
        {
            _consumer.Subscribe(topics);
            _consumerTask = Task.Run(() => ConsumeMessages(_cancellationTokenSource.Token), cancellationToken);
            _logger.LogInformation("Started Kafka consumer for topics: {Topics}", string.Join(", ", topics));
        }

        return Task.CompletedTask;
    }

    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        if (_cancellationTokenSource != null)
        {
            _cancellationTokenSource.Cancel();
        }

        if (_consumerTask != null)
        {
            await _consumerTask;
        }

        _consumer?.Close();
        _consumer?.Dispose();
        _consumer = null;

        _logger.LogInformation("Stopped Kafka consumer");
    }

    private async Task ConsumeMessages(CancellationToken cancellationToken)
    {
        try
        {
            while (!cancellationToken.IsCancellationRequested && _consumer != null)
            {
                try
                {
                    var consumeResult = _consumer.Consume(cancellationToken);
                    if (consumeResult?.Message != null)
                    {
                        await ProcessMessage(consumeResult.Message);
                    }
                }
                catch (ConsumeException ex)
                {
                    _logger.LogError(ex, "Error consuming message");
                }
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Consumer operation was cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error in consumer loop");
        }
    }

    private async Task ProcessMessage(Message<string, string> message)
    {
        try
        {
            var eventTypeHeader = message.Headers.FirstOrDefault(h => h.Key == "EventType");
            if (eventTypeHeader == null)
            {
                _logger.LogWarning("Message missing EventType header");
                return;
            }

            var eventType = Encoding.UTF8.GetString(eventTypeHeader.GetValueBytes());

            if (!_subscriptions.TryGetValue(eventType, out var subscriptions))
            {
                _logger.LogDebug("No subscriptions found for event type {EventType}", eventType);
                return;
            }

            foreach (var subscription in subscriptions.Where(s => s.IsActive))
            {
                await ProcessSubscription(subscription, message.Value, eventType);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing message");
        }
    }

    private async Task ProcessSubscription(EventSubscription subscription, string messageValue, string eventType)
    {
        try
        {
            using var scope = _serviceProvider.CreateScope();
            var handlerType = Type.GetType(subscription.HandlerType);

            if (handlerType == null)
            {
                _logger.LogError("Handler type {HandlerType} not found", subscription.HandlerType);
                return;
            }

            var handler = scope.ServiceProvider.GetService(handlerType);
            if (handler == null)
            {
                _logger.LogError("Handler {HandlerType} not registered in DI container", subscription.HandlerType);
                return;
            }

            // Get the event type and deserialize
            var eventTypeObj = Type.GetType(eventType);
            if (eventTypeObj == null)
            {
                _logger.LogError("Event type {EventType} not found", eventType);
                return;
            }

            var eventObj = JsonConvert.DeserializeObject(messageValue, eventTypeObj);
            if (eventObj == null)
            {
                _logger.LogError("Failed to deserialize event {EventType}", eventType);
                return;
            }

            // Call the handler
            var handleMethod = handlerType.GetMethod("HandleAsync");
            if (handleMethod != null)
            {
                var task = (Task?)handleMethod.Invoke(handler, new[] { eventObj, CancellationToken.None });
                if (task != null)
                {
                    await task;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing subscription {HandlerType} for event {EventType}",
                subscription.HandlerType, eventType);
        }
    }

    private string GetTopicName(string eventType)
    {
        return $"{_config.TopicPrefix}.{eventType.ToLowerInvariant()}";
    }

    private static Acks ParseAcks(string acksValue)
    {
        return acksValue.ToLowerInvariant() switch
        {
            "0" or "none" => Acks.None,
            "1" or "leader" => Acks.Leader,
            "-1" or "all" => Acks.All,
            _ => Acks.All // Default to safest option
        };
    }

    private static AutoOffsetReset ParseAutoOffsetReset(string offsetResetValue)
    {
        return offsetResetValue.ToLowerInvariant() switch
        {
            "earliest" => AutoOffsetReset.Earliest,
            "latest" => AutoOffsetReset.Latest,
            "none" => AutoOffsetReset.Error,
            _ => AutoOffsetReset.Earliest // Default
        };
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            StopAsync().GetAwaiter().GetResult();
            _producer?.Dispose();
            _cancellationTokenSource?.Dispose();
            _disposed = true;
        }
        GC.SuppressFinalize(this);
    }
}
