using EventBus.Interfaces;
using Microsoft.Extensions.Logging;

namespace EventBus.Kafka;

/// <summary>
/// Kafka implementation of the event bus that combines producer and consumer functionality
/// Acts as a facade for backward compatibility
/// </summary>
public class KafkaEventBus : IEventBus, IDisposable
{
    private readonly IEventProducer _producer;
    private readonly IEventConsumer _consumer;
    private readonly ILogger<KafkaEventBus> _logger;
    private bool _disposed;

    public KafkaEventBus(
        IEventProducer producer,
        IEventConsumer consumer,
        ILogger<KafkaEventBus> logger)
    {
        _producer = producer;
        _consumer = consumer;
        _logger = logger;
    }

    public async Task PublishAsync<T>(T @event, CancellationToken cancellationToken = default) where T : class
    {
        await _producer.PublishAsync(@event, cancellationToken);
    }

    public void Subscribe<T, TH>()
        where T : class
        where TH : class, IEventHandler<T>
    {
        _consumer.Subscribe<T, TH>();
    }

    public void Unsubscribe<T, TH>()
        where T : class
        where TH : class, IEventHandler<T>
    {
        _consumer.Unsubscribe<T, TH>();
    }

    public async Task StartAsync(CancellationToken cancellationToken = default)
    {
        await _consumer.StartAsync(cancellationToken);
    }

    public async Task StopAsync(CancellationToken cancellationToken = default)
    {
        await _consumer.StopAsync(cancellationToken);
    }

    public void Dispose()
    {
        if (_disposed) return;

        try
        {
            if (_producer is IDisposable producerDisposable)
                producerDisposable.Dispose();

            if (_consumer is IDisposable consumerDisposable)
                consumerDisposable.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing Kafka event bus");
        }
        finally
        {
            _disposed = true;
        }
    }
}
