using Kantoku.Api.Dtos.ConstructionCost.Response;

namespace Kantoku.Api.Dtos.Contractor.Response;

public class ContractorDetailResponseDto : ContractorResponseDto
{
    // /// <summary>
    // /// List of payment requests made by the contractor
    // /// </summary>
    // public IEnumerable<ContractorPaymentResponseDto>? PaymentRequests { get; set; }

    // /// <summary>
    // /// Page number of the payment request
    // /// </summary>
    // public int PaymentRequestPageNum { get; set; }

    // /// <summary>
    // /// Page size of the payment request
    // /// </summary>
    // public int PaymentRequestPageSize { get; set; }

    // /// <summary>
    // /// Total records of the payment request
    // /// </summary>
    // public int PaymentRequestTotalRecords { get; set; }

    /// <summary>
    /// List of projects contracted by the contractor
    /// </summary>
    public IEnumerable<ContractedProjectResponseDto>? Projects { get; set; }

    /// <summary>
    /// Page number of the project
    /// </summary>
    public int ProjectListPageNum { get; set; }

    /// <summary>
    /// Page size of the project
    /// </summary>
    public int ProjectListPageSize { get; set; }

    /// <summary>
    /// Total records of the project
    /// </summary>  
    public int ProjectListTotalRecords { get; set; }
}

public class ContractedProjectResponseDto
{
    /// <summary>
    /// Project ID  
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// Project code
    /// </summary>
    public string? ProjectCode { get; set; }

    /// <summary>
    /// Project name
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// Address
    /// </summary>
    public string? Address { get; set; }
}

public class ContractorPaymentResponseDto : ConstructionPaymentResponseDto
{
    /// <summary>
    /// project name
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// Construction name
    /// </summary>
    public string? ConstructionName { get; set; }

    /// <summary>
    /// Request payment from
    /// </summary>
    public string? RequestPaymentFrom { get; set; }

    /// <summary>
    /// Request payment to
    /// </summary>
    public string? RequestPaymentTo { get; set; }

    /// <summary>
    /// Payment date
    /// </summary>
    public string? PaymentDate { get; set; }
}