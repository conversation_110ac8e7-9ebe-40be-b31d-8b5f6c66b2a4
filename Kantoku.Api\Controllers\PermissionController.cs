using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Kantoku.Api.Utils.Exceptions;
using Kantoku.Api.Dtos.FunctionAccessibility.Request;
using Kantoku.Api.Dtos.FunctionAccessibility.Response;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class PermissionController : BaseController
{
    private readonly IPermissionService permissionService;
    private readonly IAuditLogService auditLogService;
    public PermissionController(
        IPermissionService permissionService,
        ITResponseFactory responseFactory,
        IAuditLogService auditLogService) :
        base(responseFactory)
    {
        this.permissionService = permissionService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Gets a list of all menu privileges in the system
    /// </summary>
    /// <returns>List of menu privileges with their associated permissions</returns>
    [HttpGet("menu")]
    public async Task<GeneralResponse<MenuPrivilegeResponseDto>> GetMenuPrivileges()
    {
        try
        {
            var res = await permissionService.GetMenuPrivileges();
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<MenuPrivilegeResponseDto>(e.ErrorCode);
        }
    }

    [HttpGet("menu/employee/current")]
    public async Task<GeneralResponse<SimpleFunctionPrivilegesResponseDto>> GetMenuPrivilegeByCurrentEmployee()
    {
        try
        {
            var res = await permissionService.GetMenuPrivilegeByCurrentEmployee();
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<SimpleFunctionPrivilegesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Retrieves menu privileges associated with a specific role
    /// </summary>
    /// <param name="id">Role identifier</param>
    /// <returns>Menu privileges for the specified role</returns>
    [HttpGet("menu/role/{id}")]
    public async Task<GeneralResponse<RolePrivilegeResponseDto>> GetMenuPrivilege([FromRoute] Guid id)
    {
        try
        {
            var res = await permissionService.GetMenuPrivilegeByRoleId(id);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<RolePrivilegeResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Retrieves menu privileges associated with a specific structure
    /// </summary>
    /// <param name="id">Structure identifier</param>
    /// <returns>Menu privileges for the specified structure</returns>
    [HttpGet("menu/structure/{id}")]
    public async Task<GeneralResponse<StructurePrivilegeResponseDto>> GetStructurePrivilege([FromRoute] Guid id)
    {
        try
        {
            var res = await permissionService.GetMenuPrivilegeByStructureId(id);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<StructurePrivilegeResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Updates menu privileges for a role or structure
    /// </summary>
    /// <param name="dto">Update menu privilege request containing the new permission settings</param>
    /// <returns>Updated menu privilege information</returns>
    [HttpPut("menu")]
    public async Task<GeneralResponse<FunctionPrivilegeResponseDto>> UpdateMenuPrivilege([FromBody] UpdateMenuPrivilegeRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<FunctionPrivilegeResponseDto>();

            var result = await permissionService.UpdateMenuPrivilege(dto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<FunctionPrivilegeResponseDto>(e.ErrorCode);
        }
    }
}
