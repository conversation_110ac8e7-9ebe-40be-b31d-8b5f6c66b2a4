using Kantoku.Api.Dtos.CategorizedCost.Response;

namespace Kantoku.Api.Dtos.ConstructionCost.Response;

/// <summary>
/// Response DTO for cost details
/// </summary>
public class ConstructionPartialCostResponseDto
{
    /// <summary>
    /// Indicates if the construction cost has been created or summarized data
    /// </summary>
    public string? ConstructionCostId { get; set; }

    /// <summary>
    /// Start date of the current report period
    /// </summary>
    public string? ReportFrom { get; set; }

    /// <summary>
    /// End date of the current report period
    /// </summary>
    public string? ReportTo { get; set; }

    /// <summary>
    /// Claim details associated with the cost
    /// </summary>
    public ConstructionPaymentResponseDto? ConstructionPaymentRequest { get; set; }

    /// <summary>
    /// Total cost amount
    /// </summary>
    public long? TotalCost { get; set; }

    /// <summary>
    /// Amount allocated for risk management
    /// </summary>
    public int? RiskAmount { get; set; }

    /// <summary>
    /// Costs broken down by categories
    /// </summary>
    public IEnumerable<HumanCategorizedCostResponseDto>? CategorizedCosts { get; set; }
}
