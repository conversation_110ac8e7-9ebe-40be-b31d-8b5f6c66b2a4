{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"JobConnection": "Host=localhost;Database=kantoku_jobs;Username=********;Password=********;Port=5432", "AppConnection": "Host=localhost;Database=kantoku_app;Username=********;Password=********;Port=5432"}, "BatchProcessing": {"PollIntervalSeconds": 30, "MaxConcurrentJobs": 5, "JobTimeoutMinutes": 60}, "KafkaConfig": {"BootstrapServers": "localhost:9092", "GroupId": "kantoku-processor", "AutoOffsetReset": "earliest", "EnableAutoCommit": true, "SessionTimeoutMs": 30000, "TopicPrefix": "kantoku", "DefaultPartitions": 3, "DefaultReplicationFactor": 1, "Producer": {"Acks": "all", "BatchSize": 16384, "LingerMs": 5}, "Consumer": {"FetchMinBytes": 1}}, "AllowedHosts": "*"}