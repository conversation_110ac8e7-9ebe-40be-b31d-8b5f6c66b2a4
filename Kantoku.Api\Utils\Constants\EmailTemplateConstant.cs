namespace Kantoku.Api.Utils.Constants
{
    public class EmailTemplateConstant
    {
        // confirmation of account creation (send to account holder)
        public const string ACCOUNT_CREATED_NOTI_TEMPLATE = "AccountCreatedNotificationTemplate";

        // confirmation of invited employee has accepted the invitation (send to invitor)
        public const string EMPLOYEE_INVITATION_ACCEPTED_NOTI_TEMPLATE = "EmployeeInvitationAcceptedNotificationTemplate";

        // confirmation of employee invitation has been sent (send to invitor)
        public const string EMPLOYEE_INVITATION_SENT_NOTI_TEMPLATE = "EmployeeInvitationSentNotificationTemplate";

        // confirmation of employee invitation has been sent (send to invited employee)
        public const string EMPLOYEE_INVITATION_TEMPLATE = "EmployeeInvitationTemplate";

        // confirmation of employee linked (send to invited and linked successfully employee)
        public const string EMPLOYEE_LINKED_NOTI_TEMPLATE = "EmployeeLinkedNotificationTemplate";

        // confirmation of employee creation (send to employee)
        public const string EMPLOYEE_CREATED_NOTI_TEMPLATE = "EmployeeCreatedNotificationTemplate";

        // confirmation of org creation (send to org creator)
        public const string ORG_CREATED_NOTI_TEMPLATE = "OrgCreatedNotificationTemplate";

        // notify the password has been recovered
        public const string PASSWORD_RECOVER_TEMPLATE = "PasswordRecoverTemplate";

        // notify the password has been reset randomly
        public const string PASSWORD_RESET_TEMPLATE = "PasswordResetTemplate";

        // notify the OTP code for multiple purposes (activate account, reset password, etc.)
        public const string OTP_TEMPLATE = "OTPTemplate";

    }
}

