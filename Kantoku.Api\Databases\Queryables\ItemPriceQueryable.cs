using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class ItemPriceQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedItem { get; set; } = false;
    public bool IncludedVendor { get; set; } = false;
}

public interface IItemPriceQueryable
{
    IQueryable<ItemPrice> GetItemPricesQuery(
        ItemPriceQueryableOptions options
    );
    IQueryable<ItemPrice> GetItemPricesQueryIncluded(
        ItemPriceQueryableOptions options,
        IQueryable<ItemPrice>? query = null
    );
    IQueryable<ItemPrice> GetItemPricesQueryFiltered(
        ItemPriceFilter filter,
        ItemPriceQueryableOptions options,
        IQueryable<ItemPrice>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class ItemPriceQueryable(PostgreDbContext context) :
    BaseQueryable<ItemPrice>(context), IItemPriceQueryable
{
    public IQueryable<ItemPrice> GetItemPricesQuery(
        ItemPriceQueryableOptions options
    )
    {
        var query = GetQuery(options);
        return query;
    }

    public IQueryable<ItemPrice> GetItemPricesQueryIncluded(
        ItemPriceQueryableOptions options,
        IQueryable<ItemPrice>? query = null
    )
    {
        query ??= GetItemPricesQuery(options);
        if (options.IncludedItem || options.IncludedAll)
        {
            query = query.Include(p => p.Item)
                .ThenInclude(i => i.Category);
            query = query.Include(p => p.Item)
                .ThenInclude(i => i.Manufacturer);
        }
        if (options.IncludedVendor || options.IncludedAll)
        {
            query = query.Include(p => p.Vendor);
        }

        return query;
    }

    public IQueryable<ItemPrice> GetItemPricesQueryFiltered(
        ItemPriceFilter filter,
        ItemPriceQueryableOptions options,
        IQueryable<ItemPrice>? query = null
    )
    {
        query ??= GetItemPricesQueryIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(p => p.OrgUid == filter.OrgId);
        }
        if (filter.VendorKeyword is not null)
        {
            query = query.Where(p => p.Vendor.VendorCode.Contains(filter.VendorKeyword));
        }
        if (DateOnly.TryParse(filter.FromDate, out var filterFromDate))
        {
            query = query.Where(p => filterFromDate <= p.ValidFrom);
        }
        if (DateOnly.TryParse(filter.ToDate, out var filterToDate))
        {
            query = query.Where(p => p.ValidTo <= filterToDate);
        }
        if (filter.PriceMin is not null)
        {
            query = query.Where(p => filter.PriceMin <= p.Price);
        }
        if (filter.PriceMax is not null)
        {
            query = query.Where(p => p.Price <= filter.PriceMax);
        }

        return query;
    }
}

