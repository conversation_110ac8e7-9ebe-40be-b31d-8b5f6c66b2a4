using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Kantoku.Api.Utils.Helpers;

public static class ObjectHelper
{
    static readonly JsonSerializerOptions options = new()
    {
        WriteIndented = true,
        DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
    };

    public static void UpdateEntityFromDto<TDto, TEntity>(TDto dto, TEntity entity, bool? IgnoreNullValue = true)
    {
        foreach (var dtoProperty in typeof(TDto).GetProperties())
        {
            var dtoPropertyValue = dtoProperty.GetValue(dto);
            var dtoPropertyName = dtoProperty.Name;
            try
            {
                // handle id to uid
                if (dtoPropertyName.EndsWith("Id") && dtoPropertyValue != null && dtoPropertyValue.ToString() != "")
                {
                    var entityUidPropertyName = string.Concat(dtoPropertyName.AsSpan(0, dtoPropertyName.Length - 2), "Uid");
                    var entityUidProperty = typeof(TEntity).GetProperty(entityUidPropertyName);
                    if (entityUidProperty != null
                    && (entityUidProperty.PropertyType == typeof(Guid) || entityUidProperty.PropertyType == typeof(Guid?))
                    && Guid.TryParse(dtoPropertyValue.ToString(), out var uidValue))
                    {
                        entityUidProperty.SetValue(entity, uidValue);
                    }
                }
            }
            catch (Exception)
            {
                continue;
            }
            try
            {
                // get entity property match dto property name
                var entityProperty = typeof(TEntity).GetProperty(dtoPropertyName);

                if (entityProperty is not null)
                {
                    // if enable override with null value (for delete property's value in entity)
                    // can only override nullable entity's property
                    if (dtoPropertyValue == null && IsNullableEntityType(entityProperty.PropertyType) && IgnoreNullValue == false)
                    {
                        entityProperty.SetValue(entity, null);
                        continue;
                    }
                    // ignore if entity property is not nullable
                    else if (dtoPropertyValue == null && !IsNullableEntityType(entityProperty.PropertyType))
                    {
                        continue;
                    }
                    // handle non-null values
                    else if (dtoPropertyValue != null)
                    {
                        // if current dtoProperty is an object type
                        if (dtoPropertyValue.GetType().IsClass && dtoPropertyValue.GetType() != typeof(string))
                        {
                            continue;
                        }
                        // handle string to datetime
                        if ((entityProperty.PropertyType == typeof(DateTime) || entityProperty.PropertyType == typeof(DateTime?))
                            && DateTime.TryParse(dtoPropertyValue.ToString(), out var dateTimeValue))
                        {
                            entityProperty.SetValue(entity, dateTimeValue);
                            continue;
                        }
                        // handle string to dateonly
                        else if ((entityProperty.PropertyType == typeof(DateOnly) || entityProperty.PropertyType == typeof(DateOnly?))
                            && DateOnly.TryParse(dtoPropertyValue.ToString(), out var dateOnlyValue))
                        {
                            entityProperty.SetValue(entity, dateOnlyValue);
                            continue;
                        }
                        // handle string to timeonly
                        else if ((entityProperty.PropertyType == typeof(TimeOnly) || entityProperty.PropertyType == typeof(TimeOnly?))
                            && TimeOnly.TryParse(dtoPropertyValue.ToString(), out var timeOnlyValue))
                        {
                            entityProperty.SetValue(entity, timeOnlyValue);
                            continue;
                        }

                        // handle other type
                        entityProperty.SetValue(entity, dtoPropertyValue);
                    }
                }
            }
            catch (Exception)
            {
                continue;
            }
        }
    }

    private static bool IsNullableEntityType(Type type)
    {
        return type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>)
               || type == typeof(string)
               || !type.IsValueType;
    }

    public static TTarget Map<TSource, TTarget>(TSource source)
    {
        try
        {
            if (source is null)
                throw new ArgumentNullException(nameof(source));

            var target = Activator.CreateInstance<TTarget>();

            var sourceType = typeof(TSource);
            var targetType = typeof(TTarget);

            foreach (var sourceProperty in sourceType.GetProperties())
            {
                var targetProperty = targetType.GetProperty(sourceProperty.Name);
                if (targetProperty is not null)
                {
                    var sourceValue = sourceProperty.GetValue(source);

                    // Check for null or empty string before mapping
                    if (sourceValue is null || (sourceValue is string strValue && string.IsNullOrEmpty(strValue)))
                    {
                        if (!targetProperty.PropertyType.IsValueType)
                        {
                            targetProperty.SetValue(target, null);
                        }
                        continue;
                    }

                    var targetPropertyType = targetProperty.PropertyType;

                    // Handle nullable value types
                    if (targetPropertyType.IsGenericType && targetPropertyType.GetGenericTypeDefinition() == typeof(Nullable<>))
                    {
                        var underlyingType = Nullable.GetUnderlyingType(targetPropertyType);
                        if (underlyingType == typeof(DateTime))
                        {
                            if (DateTime.TryParse(sourceValue.ToString(), out var dateTimeValue))
                            {
                                targetProperty.SetValue(target, dateTimeValue);
                            }
                            else
                            {
                                throw new FormatException("Invalid date format");
                            }
                        }
                        else if (underlyingType == typeof(DateOnly))
                        {
                            if (DateTime.TryParse(sourceValue.ToString(), out var dateTimeValue))
                            {
                                targetProperty.SetValue(target, DateOnly.FromDateTime(dateTimeValue));
                            }
                            else
                            {
                                throw new FormatException("Invalid date format");
                            }
                        }
                        else if (underlyingType == typeof(TimeOnly))
                        {
                            if (DateTime.TryParse(sourceValue.ToString(), out var dateTimeValue))
                            {
                                targetProperty.SetValue(target, TimeOnly.FromDateTime(dateTimeValue));
                            }
                            else
                            {
                                throw new FormatException("Invalid date format");
                            }
                        }
                        else
                        {
                            try
                            {
                                targetProperty.SetValue(target, Convert.ChangeType(sourceValue, underlyingType ?? throw new InvalidOperationException()));
                            }
                            catch (InvalidCastException)
                            {
                                throw new InvalidCastException($"Cannot convert {sourceValue} to {underlyingType}");
                            }
                        }
                    }
                    else if (targetPropertyType == typeof(DateTime) && sourceValue is string)
                    {
                        if (DateTime.TryParse(sourceValue.ToString(), out var dateTimeValue))
                        {
                            targetProperty.SetValue(target, dateTimeValue);
                        }
                        else
                        {
                            throw new FormatException("Invalid date format");
                        }
                    }
                    else if (targetPropertyType == typeof(DateOnly))
                    {
                        if (DateTime.TryParse(sourceValue.ToString(), out var dateTimeValue))
                        {

                            targetProperty.SetValue(target, DateOnly.FromDateTime(dateTimeValue));
                        }
                        else
                        {
                            throw new FormatException("Invalid date format");
                        }
                    }
                    else if (targetPropertyType == typeof(TimeOnly))
                    {
                        if (DateTime.TryParse(sourceValue.ToString(), out var dateTimeValue))
                        {
                            targetProperty.SetValue(target, TimeOnly.FromDateTime(dateTimeValue));
                        }
                        else
                        {
                            throw new FormatException("Invalid date format");
                        }
                    }
                    else
                    {
                        try
                        {
                            targetProperty.SetValue(target, Convert.ChangeType(sourceValue, targetPropertyType));
                        }
                        catch (InvalidCastException)
                        {
                            throw new InvalidCastException($"Cannot convert {sourceValue} to {targetPropertyType}");
                        }
                    }
                }
            }
            return target;
        }
        catch (System.Exception)
        {
            throw new Exception("Error mapping object");
        }
    }

    public static bool CompareObjects<T>(T obj1, T obj2)
    {
        try
        {
            if (obj1 is null && obj2 is null) return true;
            if (obj1 is null || obj2 is null) return false;

            var type = obj1 is not null ? obj1.GetType() : obj2 is not null ? obj2.GetType() : typeof(T);

            // Handle primitive types, string, and value types
            if (type.IsPrimitive || type == typeof(string) || type.IsValueType)
            {
                return obj1?.Equals(obj2) ?? false;
            }

            // For complex types, compare each property
            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);

            foreach (var property in properties)
            {
                var value1 = property.GetValue(obj1);
                var value2 = property.GetValue(obj2);

                if (value1 is null && value2 is null)
                    continue;

                if (value1 is null || value2 is null)
                    return false;

                // Recursively compare nested objects
                if (!property.PropertyType.IsPrimitive && property.PropertyType != typeof(string) && property.PropertyType.IsClass)
                {
                    if (!CompareObjects(value1, value2))
                    {
                        return false;
                    }
                }
                else if (!value1.Equals(value2))
                {
                    return false;
                }
            }

            return true;
        }
        catch (System.Exception)
        {
            return false;
        }
    }

    public static bool IsNullObject<T>(T obj)
    {
        if (obj is null) return true;

        var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);
        foreach (var property in properties)
        {
            var value = property.GetValue(obj);
            if (value is not null)
            {
                return false;
            }
        }
        return true;
    }

    public static T? Clone<T>(T obj)
    {
        try
        {
            if (obj is null) return default;

            var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance)
                                      .Where(prop => prop.GetMethod?.IsVirtual != true)
                                      .ToDictionary(prop => prop.Name, prop => prop.GetValue(obj));

            var json = JsonSerializer.Serialize(properties, options);
            return JsonSerializer.Deserialize<T>(json);
        }
        catch (System.Exception)
        {
            return default;
        }
    }
}