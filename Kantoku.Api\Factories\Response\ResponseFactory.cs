using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Mappers;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Attributes.Class;

namespace Kantoku.Api.Factories.Response;

public interface IResponseFactory
{
    GeneralResponse Success();
    GeneralResponse BadRequest();
    GeneralResponse Unauthorized();
    GeneralResponse Forbidden();
    GeneralResponse Fail(string code);
}

[Service(ServiceLifetime.Scoped)]
public class ResponseFactory : IResponseFactory
{
    private readonly IErrorService errorService;
    public ResponseFactory(IErrorService errorService)
    {
        this.errorService = errorService;
    }

    private GeneralResponse SucessBuilder()
    {
        int status = StatusCodeMapper.MapFromCode(ResponseCodeConstant.SUCCESS);
        var message = errorService.GetErrorMessage(ResponseCodeConstant.SUCCESS);
        return new GeneralResponse().WithStatus(status)
            .WithCode(ResponseCodeConstant.SUCCESS)
            .WithMessage(message ?? string.Empty);
    }

    private GeneralResponse FailBuilder(string code)
    {
        int status = StatusCodeMapper.MapFromCode(code);
        var message = errorService.GetErrorMessage(code);
        return new GeneralResponse().WithStatus(status)
            .WithCode(code)
            .WithMessage(message ?? string.Empty);
    }

    public GeneralResponse Success()
    {
        return SucessBuilder();
    }

    public GeneralResponse Fail(string code)
    {
        return FailBuilder(code);
    }

    public GeneralResponse BadRequest()
    {
        return FailBuilder(ResponseCodeConstant.BAD_REQUEST);
    }

    public GeneralResponse Unauthorized()
    {
        return FailBuilder(ResponseCodeConstant.UNAUTHORIZED);
    }

    public GeneralResponse Forbidden()
    {
        return FailBuilder(ResponseCodeConstant.FORBIDDEN);
    }
}
