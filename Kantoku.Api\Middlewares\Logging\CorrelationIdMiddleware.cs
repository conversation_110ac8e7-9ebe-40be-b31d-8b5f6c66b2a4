using Kantoku.Api.Services;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Helpers;
using Serilog.Context;

namespace Kantoku.Api.Middlewares.Logging
{
    public class CorrelationIdMiddleware : IMiddleware
    {
        private readonly ILogger<CorrelationIdMiddleware> logger;
        public CorrelationIdMiddleware(ILogger<CorrelationIdMiddleware> logger)
        {
            this.logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            string correlationId = context.Request.Headers[HeaderConstant.CorrelationId].FirstOrDefault() ?? GuidHelper.GenerateUUIDv7String();

            context.Request.Headers[HeaderConstant.CorrelationId] = correlationId;
            context.Response.Headers[HeaderConstant.CorrelationId] = correlationId;

            using (LogContext.PushProperty(HeaderConstant.CorrelationId, correlationId))
            {
                await next(context);
            }
        }
    }
}
