﻿using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Dtos.EntryType.Request;
using Kantoku.Api.Dtos.EntryType.Response;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Controllers;

[Route("api/v1/cost/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class EntryTypeController : BaseController
{
    private readonly IEntryTypeService entryTypeService;
    private readonly IAuditLogService auditLogService;
    public EntryTypeController(IEntryTypeService entryTypeService,
        IAuditLogService auditLogService,
        ITResponseFactory responseFactory)
        : base(responseFactory)
    {
        this.entryTypeService = entryTypeService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get entry types by filter
    /// </summary>
    /// <param name="filter">Filter parameters</param>
    /// <returns>List of entry types</returns>
    [HttpGet]
    public async Task<GeneralResponse<EntryTypesResponseDto>> GetByFilter([FromQuery] EntryTypeFilter filter)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<EntryTypesResponseDto>();

            var result = await entryTypeService.GetByFilter(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<EntryTypesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get entry type by id
    /// </summary>
    /// <param name="id">Entry type id</param>
    /// <returns>Entry type details</returns>
    [HttpGet("{id}")]
    public async Task<GeneralResponse<EntryTypeResponseDto>> GetById([FromRoute] Guid id)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<EntryTypeResponseDto>();

            var result = await entryTypeService.GetById(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<EntryTypeResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create new entry type
    /// </summary>
    /// <param name="requestDto">Entry type creation data</param>
    /// <returns>Created entry type</returns>
    [HttpPost]
    public async Task<GeneralResponse<EntryTypeResponseDto>> Create([FromBody] CreateEntryTypeRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<EntryTypeResponseDto>();

            var result = await entryTypeService.Create(requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<EntryTypeResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update existing entry type
    /// </summary>
    /// <param name="id">Entry type id</param>
    /// <param name="requestDto">Entry type update data</param>
    /// <returns>Updated entry type</returns>
    [HttpPut("{id}")]
    public async Task<GeneralResponse<EntryTypeResponseDto>> Update([FromRoute] Guid id, [FromBody] UpdateEntryTypeRequestDto requestDto)
    {
        try
        {
            var result = await entryTypeService.Update(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<EntryTypeResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete entry type
    /// </summary>
    /// <param name="id">Entry type id</param>
    /// <returns>Success response</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> Delete([FromRoute] Guid id)
    {
        try
        {
            await entryTypeService.Delete(id);
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for entry type
    /// </summary>
    /// <param name="id">Entry type id</param>
    /// <param name="filter">Filter parameters</param>
    /// <returns>List of audit logs</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetAuditLogByEntity([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<EntryType>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
