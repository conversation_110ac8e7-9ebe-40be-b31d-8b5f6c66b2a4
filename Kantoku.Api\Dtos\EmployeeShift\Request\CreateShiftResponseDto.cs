using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.EmployeeShift.Request;

public class CreateShiftRequestDto
{
    /// <summary>
    /// The project id where the shift takes place
    /// </summary>
    [Required]
    public required Guid ProjectId { get; set; }

    /// <summary>
    /// The employee id who is assigned to the shift
    /// </summary>
    public Guid? EmployeeId { get; set; }

    /// <summary>
    /// The location where the shift takes place
    /// </summary>
    public string? WorkingLocation { get; set; }

    /// <summary>
    /// The time when the shift starts
    /// </summary>
    [DateTimeValidator(typeof(DateTime))]
    public string? CheckInTime { get; set; }

    /// <summary>
    /// The time when the shift ends
    /// </summary>
    [DateTimeValidator(typeof(DateTime))]
    public string? CheckOutTime { get; set; }

    /// <summary>
    /// List of break periods during the shift
    /// </summary>
    public IEnumerable<CreateBreakItem> BreakList { get; set; } = [];

    /// <summary>
    /// Additional notes or description about the shift
    /// </summary>
    public string? Description { get; set; }
}