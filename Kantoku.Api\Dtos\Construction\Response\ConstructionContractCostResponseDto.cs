namespace Kantoku.Api.Dtos.Construction.Response;

public class ConstructionsContractCostResponseDto
{
    /// <summary>
    /// Main construction contract cost
    /// </summary>
    public ContractCostResponseDto? MainConstructionContractCost { get; set; }

    /// <summary>
    /// Total amount of the main construction cost
    /// </summary>
    public long TotalMainConstructionCost { get; set; } = 0;

    /// <summary>
    /// Sub construction contract cost
    /// </summary>
    public ContractCostResponseDto? SubConstructionContractCost { get; set; }

    /// <summary>
    /// Total amount of the sub construction cost
    /// </summary>
    public long TotalSubConstructionCost { get; set; } = 0;

    /// <summary>
    /// Overall construction contract cost
    /// </summary>
    public ContractCostResponseDto? OverallConstructionContractCost { get; set; }

    /// <summary>
    /// Total amount of the overall construction cost
    /// </summary>
    public long TotalOverallConstructionCost { get; set; } = 0;
}

public class  ContractCostResponseDto
{
    /// <summary>
    /// Items of the initial cost
    /// </summary>
    public IEnumerable<ContractCostItemResponseDto> InitialCostItems { get; set; } = [];

    /// <summary>
    /// Total amount of the initial cost
    /// </summary>
    public long TotalInitialCost { get; set; } = 0;

    /// <summary>
    /// Items of the modified cost
    /// </summary>
    public IEnumerable<ContractCostItemResponseDto> ModifiedCostItems { get; set; } = [];

    /// <summary>
    /// Total amount of the modified cost
    /// </summary>
    public long TotalModifiedCost { get; set; } = 0;
}

public class ContractCostItemResponseDto
{
    /// <summary>
    /// Sequence number for the initial cost 1st, 2nd, 3rd, etc.
    /// </summary>
    public short SequenceNumber { get; set; } = 0;

    /// <summary>
    /// Amount of the initial cost
    /// </summary>
    public long Amount { get; set; } = 0;
}
