using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class EmployeeLeaveConfiguration(string schema) : IEntityTypeConfiguration<EmployeeLeave>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<EmployeeLeave> builder)
    {
        builder.HasKey(e => e.EmployeeLeaveUid).HasName("employee_leave_pkey");

        builder.ToTable("employee_leave", Schema);

        builder.HasIndex(e => new { e.EmployeeUid, e.BaseLeaveExpire }, "employee_leave_ukey").IsUnique();

        builder.Property(e => e.EmployeeLeaveUid)
            .HasColumnName("employee_leave_uid");
        builder.Property(e => e.EmployeeUid)
            .HasColumnName("employee_uid");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.BaseLeave)
            .HasColumnName("base_leave");
        builder.Property(e => e.BaseLeaveExpire)
            .HasColumnName("base_leave_expire");
        builder.Property(e => e.LastRemainLeave)
            .HasColumnName("last_remain_leave");
        builder.Property(e => e.LastRemainLeaveExpire)
            .HasColumnName("last_remain_leave_expire");
        builder.Property(e => e.SelfTakenLeave)
            .HasColumnName("self_taken_leave");
        builder.Property(e => e.OrgTakenLeave)
            .HasColumnName("org_taken_leave");

        builder.HasOne(d => d.Employee).WithMany(p => p.EmployeeLeaves)
            .HasForeignKey(d => d.EmployeeUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("employee_leave_employee_id_fkey");
    }
}
