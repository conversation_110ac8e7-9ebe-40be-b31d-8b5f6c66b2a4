FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

COPY ["Kantoku.Processor.csproj", "./"]
RUN dotnet restore "Kantoku.Processor.csproj"
COPY . .
ARG configuration=Release
RUN dotnet build "Kantoku.Processor.csproj" -c $configuration -o /app/build

FROM build AS publish
RUN dotnet publish "Kantoku.Processor.csproj" -c Release -o /app/publish

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Kantoku.Processor.dll"] 