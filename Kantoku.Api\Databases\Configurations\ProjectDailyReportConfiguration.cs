using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Kantoku.Api.Databases.Configurations;

public class ProjectDailyReportConfiguration(string schema) : IEntityTypeConfiguration<ProjectDailyReport>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<ProjectDailyReport> builder)
    {
        builder.HasKey(e => e.ProjectDailyReportUid).HasName("project_daily_report_pkey");

        builder.ToTable("project_daily_report", Schema);

        builder.HasIndex(e => new { e.ProjectUid, e.ReportDate }, "project_daily_report_project_report_date_ukey")
            .IsUnique();

        builder.Property(e => e.ProjectDailyReportUid)
            .HasColumnName("project_daily_report_uid");
        builder.Property(e => e.ProjectUid)
            .HasColumnName("project_uid");
        builder.Property(e => e.ReportDate)
            .HasColumnType("date")
            .HasColumnName("report_date");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.EmployeeWorkload)
            .HasColumnName("employee_workload")
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<IEnumerable<EmployeeWorkload>>(v)
            ).Metadata.SetValueComparer(
                new ValueComparer<IEnumerable<EmployeeWorkload>>(
                    (c1, c2) => (c1 ?? Enumerable.Empty<EmployeeWorkload>()).SequenceEqual(c2 ?? Enumerable.Empty<EmployeeWorkload>()),
                    c => c.Aggregate(0, (a, v) => a ^ v.GetHashCode()),
                    c => c.ToList()
                )
            );
        builder.Property(e => e.OutSourceWorkload)
            .HasColumnName("outsource_workload")
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<IEnumerable<OutSourceWorkload>>(v)
            ).Metadata.SetValueComparer(
                new ValueComparer<IEnumerable<OutSourceWorkload>>(
                    (c1, c2) => (c1 ?? Enumerable.Empty<OutSourceWorkload>()).SequenceEqual(c2 ?? Enumerable.Empty<OutSourceWorkload>()),
                    c => c.Aggregate(0, (a, v) => a ^ v.GetHashCode()),
                    c => c.ToList()
                )
            );
        builder.Property(e => e.IsApproved)
            .HasColumnName("is_approved");
        builder.Property(e => e.ApprovedTime)
            .HasColumnName("approved_time");
        builder.Property(e => e.ApproverUid)
            .HasColumnName("approver_uid");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");

        builder.HasOne(d => d.Project).WithMany(p => p.ProjectDailyReports)
            .HasForeignKey(d => d.ProjectUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("project_daily_report_project_id_fkey");

        builder.HasOne(d => d.Approver).WithMany(p => p.ProjectDailyReports)
            .HasForeignKey(d => d.ApproverUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("project_daily_report_approver_id_fkey");

        builder.HasOne(d => d.Org).WithMany(p => p.ProjectDailyReports)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("project_daily_report_org_id_fkey");
    }
}

