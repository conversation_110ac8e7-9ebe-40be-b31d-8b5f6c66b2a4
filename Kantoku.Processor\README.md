# Kantoku.Processor - Batch Processing Application

This guide outlines a batch processing application with .NET 8, Entity Framework Core 8, and PostgreSQL. The application allows managing batch jobs with configurable execution schedules through database configuration.

## Table of Contents

1. [Project Structure](#project-structure)
2. [Setting Up the Project](#setting-up-the-project)
3. [Database Design](#database-design)
4. [Core Components](#core-components)
5. [Implementation Steps](#implementation-steps)
6. [Configuration and Deployment](#configuration-and-deployment)
7. [Using the Application](#using-the-application)
8. [Creating Custom Jobs](#creating-custom-jobs)
9. [Troubleshooting](#troubleshooting)

## Project Structure

```
Kantoku.Processor/
├── Data/
│   ├── JobDbContext.cs
│   ├── AppDbContext.cs
│   ├── DatabaseSeeder.cs
│   ├── Migrations/
│   └── Repositories/
│       ├── IBatchJobRepository.cs
│       └── BatchJobRepository.cs
├── Models/
│   ├── BatchJob.cs
│   └── BatchJobHistory.cs
├── Services/
│   ├── BatchExecutionService.cs
│   ├── JobSchedulerService.cs
│   ├── JobFactory.cs
│   ├── JobSchedulerHostedService.cs
│   └── Interfaces/
│       ├── IBatchExecutionService.cs
│       └── IJobSchedulerService.cs
├── Jobs/
│   ├── IJob.cs
│   ├── JobBase.cs
│   ├── SampleJob.cs
│   ├── DatabaseMaintenanceJob.cs
│   ├── FileProcessingJob.cs
│   └── [Custom Job Implementations]
├── Program.cs
├── job_tables.sql
└── appsettings.json
```

## Setting Up the Project

### 1. Create a New Project

```bash
dotnet new web -n Kantoku.Processor
cd Kantoku.Processor
```

### 2. Add Required Packages

```bash
dotnet add package Microsoft.EntityFrameworkCore --version 8.0.0
dotnet add package Microsoft.EntityFrameworkCore.Tools --version 8.0.0
dotnet add package Npgsql.EntityFrameworkCore.PostgreSQL --version 8.0.0
dotnet add package Cronos --version 0.7.1
dotnet add package Microsoft.Extensions.Hosting --version 8.0.0
```

## Database Design

The application uses two separate database contexts:

1. **JobDbContext**: Manages batch jobs and their execution history
2. **AppDbContext**: Handles application-specific entities

All entities use UUID (Guid) as their primary keys.

### Job Database Tables

1. **BatchJobs**
   - `Id` (UUID, PK)
   - `Name` (string) - Unique name of the job
   - `Description` (string) - Job description
   - `IsEnabled` (bool) - Whether the job is active
   - `CronExpression` (string) - When to run the job
   - `JobType` (string) - The fully qualified name of the job implementation
   - `CreatedAt` (DateTime)
   - `UpdatedAt` (DateTime)
   - `LastRunAt` (DateTime?) - When the job was last executed
   - `Configuration` (JSON) - Job-specific configuration

2. **BatchJobHistory**
   - `Id` (UUID, PK)
   - `BatchJobId` (UUID, FK)
   - `StartedAt` (DateTime)
   - `CompletedAt` (DateTime?)
   - `Status` (enum) - Success, Failed, InProgress, Cancelled, TimedOut
   - `ErrorMessage` (string)
   - `ExecutionLog` (string)

## Core Components

### 1. Database Contexts

Two separate DbContext classes for different concerns:
- **JobDbContext**: Manages batch jobs and their execution history
- **AppDbContext**: Handles application-specific entities

### 2. Batch Job Model

A batch job represents a task that needs to be executed based on a schedule.

### 3. Job Scheduler Service

The service responsible for scheduling jobs according to their CRON expressions.

### 4. Batch Execution Service

Handles the actual execution of batch jobs, including logging and error handling.

### 5. Job Base Class

An abstract base class that all job implementations will inherit from.

## Using the Application

### Running the Application

```bash
dotnet run
```

### Setting Up the Databases

You can use the provided SQL script to create the necessary tables:

```bash
psql -U ******** -d kantoku_jobs -f job_tables.sql
```

### Managing Batch Jobs

Jobs are managed by directly modifying the database tables. Here are examples of the operations you can perform:

#### Creating a New Job

Insert a new row in the `BatchJobs` table:

```sql
INSERT INTO "BatchJobs" (
    "Id",
    "Name", 
    "Description", 
    "IsEnabled", 
    "CronExpression", 
    "JobType", 
    "Configuration", 
    "CreatedAt", 
    "UpdatedAt"
) VALUES (
    uuid_generate_v4(),
    'MyCustomJob',
    'A custom job that does something',
    TRUE,
    '0 */15 * * * *',
    'Kantoku.Processor.Jobs.SampleJob',
    '{"iterationCount":5,"delayMilliseconds":1000,"message":"Hello, World!"}',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);
```

#### Enabling or Disabling a Job

Update the `IsEnabled` flag:

```sql
UPDATE "BatchJobs"
SET "IsEnabled" = FALSE, "UpdatedAt" = CURRENT_TIMESTAMP
WHERE "Id" = 'your-uuid-here';
```

#### Modifying Job Configuration

Update the job configuration:

```sql
UPDATE "BatchJobs"
SET 
    "Configuration" = '{"iterationCount":10,"delayMilliseconds":500,"message":"Updated message"}',
    "UpdatedAt" = CURRENT_TIMESTAMP
WHERE "Id" = 'your-uuid-here';
```

### CRON Expression Examples

- Every minute: `* * * * *`
- Every 15 minutes: `*/15 * * * *`
- Every hour: `0 * * * *`
- Every day at midnight: `0 0 * * *`
- Every Monday at 9 AM: `0 9 * * 1`
- Every weekday at 5 PM: `0 17 * * 1-5`

## Creating Custom Jobs

To create a custom job implementation, follow these steps:

1. Create a new class in the Jobs folder that inherits from `JobBase`
2. Implement the `ExecuteAsync` method
3. Add a nested class for configuration if needed
4. Insert a record in the `BatchJobs` table with the fully qualified name of your job class

Example:

```csharp
using Kantoku.Processor.Models;

namespace Kantoku.Processor.Jobs
{
    public class MyCustomJob : JobBase
    {
        public class MyCustomJobConfig
        {
            public string MyParameter { get; set; } = "Default Value";
            public int MyNumber { get; set; } = 42;
        }

        public MyCustomJob(BatchJob jobConfiguration)
            : base()
        {
            JobConfiguration = jobConfiguration;
        }

        public override async Task ExecuteAsync()
        {
            try
            {
                LogInformation("Starting my custom job");
                
                var config = GetTypedConfiguration<MyCustomJobConfig>() ?? new MyCustomJobConfig();
                
                LogInformation($"Using parameter: {config.MyParameter} and number: {config.MyNumber}");
                
                // Your job logic here
                await Task.Delay(1000, CancellationToken);
                
                LogInformation("Custom job completed successfully");
            }
            catch (OperationCanceledException)
            {
                LogWarning("Job was cancelled");
                throw;
            }
            catch (Exception ex)
            {
                LogError("Job failed", ex);
                throw;
            }
        }
    }
}
```

## Configuration and Deployment

### appsettings.json Configuration

```json
{
  "ConnectionStrings": {
    "JobConnection": "Host=localhost;Database=kantoku_jobs;Username=********;Password=********;Port=5432",
    "AppConnection": "Host=localhost;Database=kantoku_app;Username=********;Password=********;Port=5432"
  },
  "BatchProcessing": {
    "PollIntervalSeconds": 30,
    "MaxConcurrentJobs": 5,
    "JobTimeoutMinutes": 60
  }
}
```

### Running Migrations

There are two approaches to set up the database:

#### A. Using Manual Migration Scripts

For the job database, we provide manual migration scripts that you can run directly:

1. PowerShell (Windows):
```powershell
# Navigate to the Kantoku.Processor directory
cd Kantoku.Processor

# Run the migration script (will use appsettings.Development.json by default)
.\apply_migration.ps1

# Or specify a custom connection string
.\apply_migration.ps1 "Host=localhost;Database=kantoku_jobs;Username=********;Password=********;Port=5432"
```

2. Bash (Linux/macOS):
```bash
# Navigate to the Kantoku.Processor directory
cd Kantoku.Processor

# Make the script executable (skip this step on Windows)
chmod +x apply_migration.sh

# Run the migration script (will use appsettings.Development.json by default)
./apply_migration.sh

# Or specify a custom connection string
./apply_migration.sh "host=localhost port=5432 dbname=kantoku_jobs user=******** password=********"
```

> **Note for Windows Users**: If you're running on Windows with WSL or Git Bash, you may need to ensure the script has executable permissions. On PowerShell or Command Prompt, simply use the PowerShell script instead.

This approach is useful when you want to apply migrations without relying on EF Core tools.

#### B. Using Entity Framework Core Migrations

If you prefer using EF Core migrations:

```bash
# For JobDbContext
dotnet ef migrations add InitialJobCreate --context JobDbContext -o Data/Migrations/Job
dotnet ef database update --context JobDbContext

# For AppDbContext
dotnet ef migrations add InitialAppCreate --context AppDbContext -o Data/Migrations/App
dotnet ef database update --context AppDbContext
```

### Docker Deployment

The application includes Docker support. To run it with Docker:

```bash
docker-compose up -d
```

The Docker configuration sets up two separate PostgreSQL databases:
- `job-db` for batch job management (kantoku_jobs)
- `app-db` for application entities (kantoku_app)

## Troubleshooting

### Common Issues

#### Jobs Not Running

1. Check if the job is enabled in the database
2. Verify the CRON expression
3. Check if the job type is correctly specified
4. Look at the job history records for any error messages

#### Database Connection Issues

1. Verify the connection strings for both databases
2. Check if PostgreSQL is running
3. Ensure both databases exist

#### Job Execution Failures

1. Check the job history records for error messages
2. Verify that the job configuration is correct
3. Check the logs for more detailed error information

### Logging

The application uses the standard .NET logging system. You can view logs:

- In the console when running in development mode
- In the configured log providers (file, database, etc.)
- Through Docker logs when running in a container

---

This guide provides the foundation for building a robust batch processing application. The implementation includes all necessary components and examples to get you started. Configuration is done primarily through database tables rather than an API. 