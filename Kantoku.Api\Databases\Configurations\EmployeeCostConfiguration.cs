using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class EmployeeCostConfiguration(string schema) : IEntityTypeConfiguration<EmployeeCost>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<EmployeeCost> builder)
    {
        builder.HasKey(e => e.EmployeeCostUid).HasName("employee_cost_pkey");

        builder.ToTable("employee_cost", Schema);

        builder.Property(e => e.EmployeeCostUid)
            .HasColumnName("employee_cost_uid");
        builder.Property(e => e.EmployeeUid)
            .HasColumnName("employee_uid");
        builder.Property(e => e.EffectiveDate)
            .HasColumnName("effective_date")
            .HasColumnType("date");
        builder.Property(e => e.DailyCostAmount)
            .HasColumnName("daily_cost_amount");

        builder.HasOne(d => d.Employee).WithMany(p => p.EmployeeCosts)
            .HasForeignKey(d => d.EmployeeUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("employee_cost_employee_id_fkey");
    }
}
