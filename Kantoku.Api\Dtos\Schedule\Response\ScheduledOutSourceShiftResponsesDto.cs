using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.Schedule.Response;

public class ScheduledOutSourceShiftResponsesDto
{
    [JsonPropertyName("items")]
    public IEnumerable<ScheduledOutSourceShiftResponseDto>? Items { get; set; }

}

public class ScheduledOutSourceShiftResponseDto
{
    [JsonPropertyName("outSourceShiftId")]
    public string? OutSourceShiftId { get; set; }

    [JsonPropertyName("projectScheduleId")]
    public string? ProjectScheduleId { get; set; }

    [JsonPropertyName("outSourceName")]
    public string? OutSourceName { get; set; }

    [JsonPropertyName("outSourceCode")]
    public string? OutSourceCode { get; set; }

    [JsonPropertyName("workingDate")]
    public string? WorkingDate { get; set; }

    [JsonPropertyName("scheduledStartTime")]
    public string? ScheduledStartTime { get; set; }

    [JsonPropertyName("scheduledEndTime")]
    public string? ScheduledEndTime { get; set; }

    [JsonPropertyName("assignedWorkload")]
    public float? AssignedWorkload { get; set; }

    [JsonPropertyName("workingRole")]
    public string? WorkingRole { get; set; }
}