using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.InputCost.Request;
using Kantoku.Api.Dtos.InputCost.Response;
using Kantoku.Api.Dtos.Vendor.Response;
using Kantoku.Api.Utils.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class InputCostMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps an InputCost entity to an InputCostResponseDto
    /// </summary>
    /// <param name="inputCost">The InputCost entity to map</param>
    /// <returns>The mapped InputCostResponseDto</returns>
    public static InputCostResponseDto ToInputCostResponseDto(this InputCost inputCost)
    {
        if (inputCost == null)
            return new InputCostResponseDto();

        return new InputCostResponseDto
        {
            InputCostId = inputCost.InputCostUid.ToString(),
            Title = inputCost.Title,
            IssueDate = inputCost.IssueDate.ToString("yyyy-MM-dd"),
            PaymentDate = inputCost.PaymentDate?.ToString("yyyy-MM-dd"),
            OriginalNumber = inputCost.OriginalNumber,
            ProjectId = inputCost.Construction?.Project?.ProjectUid.ToString(),
            ProjectName = inputCost.Construction?.Project?.ProjectName,
            ConstructionId = inputCost.ConstructionUid.ToString(),
            ConstructionName = inputCost.Construction?.ConstructionName,
            EntryTypeId = inputCost.EntryTypeUid?.ToString(),
            EntryTypeName = inputCost.EntryType?.EntryTypeName,
            VendorId = inputCost.VendorUid?.ToString(),
            VendorName = inputCost.Vendor?.VendorName,
            PaymentTypeId = inputCost.PaymentTypeUid?.ToString(),
            PaymentTypeName = inputCost.PaymentType?.PaymentTypeName,
            Description = inputCost.Description,
            TotalAmount = inputCost.TotalAmount,
            ImageUrls = inputCost.ImageUrls,
        };
    }

    /// <summary>
    /// Maps a collection of InputCost entities to an InputCostsResponseDto
    /// </summary>
    /// <param name="inputCosts">The collection of InputCost entities to map</param>
    /// <param name="pageNum">The page number</param>
    /// <param name="pageSize">The page size</param>
    /// <param name="totalRecords">The total number of records</param>
    /// <returns>The mapped InputCostsResponseDto</returns>
    public static InputCostsResponseDto ToInputCostsResponseDto(
        this IEnumerable<InputCost> inputCosts,
        int pageNum,
        int pageSize,
        int totalRecords
    )
    {
        if (inputCosts == null)
            return new InputCostsResponseDto();

        return new InputCostsResponseDto
        {
            Items = inputCosts.Select(ic => ic.ToInputCostResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    public static DetailedInputCostResponseDto ToDetailedInputCostResponseDto(this InputCost inputCost)
    {
        if (inputCost == null)
            return new DetailedInputCostResponseDto();

        var inputCostItems = inputCost.InputCostItems.Select(i => i.ToInputCostItemResponseDto());
        return new DetailedInputCostResponseDto
        {
            InputCostId = inputCost.InputCostUid.ToString(),
            Title = inputCost.Title,
            IssueDate = inputCost.IssueDate.ToString("yyyy-MM-dd"),
            PaymentDate = inputCost.PaymentDate?.ToString("yyyy-MM-dd"),
            OriginalNumber = inputCost.OriginalNumber,
            ProjectId = inputCost.Construction?.Project?.ProjectUid.ToString(),
            ProjectName = inputCost.Construction?.Project?.ProjectName,
            ConstructionId = inputCost.ConstructionUid.ToString(),
            ConstructionName = inputCost.Construction?.ConstructionName,
            EntryTypeId = inputCost.EntryTypeUid?.ToString(),
            EntryTypeName = inputCost.EntryType?.EntryTypeName,
            VendorId = inputCost.VendorUid?.ToString(),
            VendorName = inputCost.Vendor?.VendorName,
            PaymentTypeId = inputCost.PaymentTypeUid?.ToString(),
            PaymentTypeName = inputCost.PaymentType?.PaymentTypeName,
            Description = inputCost.Description,
            TotalAmount = inputCost.TotalAmount,
            InputCostItems = inputCostItems
        };
    }

    public static VendorInvoice ToVendorInvoice(this InputCost inputCost)
    {
        if (inputCost == null)
            return new VendorInvoice();

        return new VendorInvoice
        {
            InputCostId = inputCost.InputCostUid.ToString(),
            InvoiceTitle = inputCost.Title,
            IssueDate = inputCost.IssueDate.ToString("yyyy-MM-dd"),
            PaymentDate = inputCost.PaymentDate?.ToString("yyyy-MM-dd"),
            OriginalNumber = inputCost.OriginalNumber,
            Description = inputCost.Description,
            TotalAmount = inputCost.TotalAmount,
        };
    }

    #endregion

    #region DTO to Entity mappings

    // Map CreateInputCostRequestDto to InputCost entity
    public static InputCost? ToEntity(this CreateInputCostRequestDto dto, Guid orgUid)
    {
        if (dto == null)
            return null;

        var entity = new InputCost
        {
            InputCostUid = GuidHelper.GenerateUUIDv7(),
            OrgUid = orgUid,
            Title = dto.Title,
            IssueDate = DateOnly.Parse(dto.IssueDate),
            PaymentDate = DateOnly.TryParse(dto.PaymentDate, out var paymentDate)
                ? paymentDate
                : null,
            OriginalNumber = dto.OriginalNumber,
            ConstructionUid = Guid.Parse(dto.ConstructionId),
            EntryTypeUid = Guid.TryParse(dto.EntryTypeId, out var entryTypeUid)
                ? entryTypeUid
                : null,
            PaymentTypeUid = Guid.TryParse(dto.PaymentTypeId, out var paymentTypeUid)
                ? paymentTypeUid
                : null,
            Description = dto.Description
        };

        if (dto.Vendor is not null && Guid.TryParse(dto.Vendor.VendorId, out var vendorUid))
        {
            entity.VendorUid = vendorUid;
        }
        else if (dto.Vendor is not null && !string.IsNullOrEmpty(dto.Vendor.VendorName))
        {
            var newVendor = new Vendor
            {
                VendorUid = GuidHelper.GenerateUUIDv7(),
                VendorCode = $"UNKNOWN_CODE_{DateTime.Now:yyyyMMdd}",
                VendorName = dto.Vendor.VendorName ?? $"UNKNOWN_NAME_{DateTime.Now:yyyyMMdd}",
                OrgUid = orgUid,
                IsDeleted = false,
            };
            entity.VendorUid = newVendor.VendorUid;
            entity.Vendor = newVendor;
        }

        var inputCostItems = new List<InputCostItem>();

        foreach (var inputCostItem in dto.InputCostItems ?? [])
        {
            if (Guid.TryParse(inputCostItem.Item.ItemId, out var itemUid))
            {
                inputCostItems.Add(new InputCostItem
                {
                    InputCostItemUid = GuidHelper.GenerateUUIDv7(),
                    ConstructionUid = entity.ConstructionUid,
                    InputCostUid = entity.InputCostUid,
                    ItemUid = itemUid,
                    VendorUid = entity.VendorUid,
                    OrgUid = orgUid,
                    Unit = inputCostItem.Unit,
                    Quantity = inputCostItem.Quantity,
                    Price = inputCostItem.Price,
                    TaxRate = inputCostItem.TaxRate,
                    TotalNonTaxed = inputCostItem.TotalNonTaxed ?? (long)(inputCostItem.Price * inputCostItem.Quantity),
                    TotalTaxed = inputCostItem.TotalTaxed ?? (long)(inputCostItem.Price * inputCostItem.Quantity * (1 + (inputCostItem.TaxRate ?? 0))),
                    Description = inputCostItem.Description,
                    TransactionDate = DateOnly.Parse(inputCostItem.TransactionDate),
                });
            }
            else
            {
                if (!Guid.TryParse(inputCostItem.Item.CategoryId, out _))
                {
                    continue;
                }
                var newItem = new Item
                {
                    ItemUid = GuidHelper.GenerateUUIDv7(),
                    ItemCode = $"UNKNOWN_CODE_{DateTime.Now:yyyyMMdd}",
                    ItemName = inputCostItem.Item.ItemName ?? $"UNKNOWN_NAME_{DateTime.Now:yyyyMMdd}",
                    CategoryUid = Guid.Parse(inputCostItem.Item.CategoryId),
                    OrgUid = orgUid,
                    IsDeleted = false,
                };
                inputCostItems.Add(new InputCostItem
                {
                    InputCostItemUid = GuidHelper.GenerateUUIDv7(),
                    ConstructionUid = entity.ConstructionUid,
                    InputCostUid = entity.InputCostUid,
                    ItemUid = newItem.ItemUid,
                    Item = newItem,
                    VendorUid = entity.VendorUid,
                    OrgUid = orgUid,
                    Unit = inputCostItem.Unit,
                    Quantity = inputCostItem.Quantity,
                    Price = inputCostItem.Price,
                    TaxRate = inputCostItem.TaxRate,
                    TotalNonTaxed = inputCostItem.TotalNonTaxed ?? (long)(inputCostItem.Price * inputCostItem.Quantity),
                    TotalTaxed = inputCostItem.TotalTaxed ?? (long)(inputCostItem.Price * inputCostItem.Quantity * (1 + (inputCostItem.TaxRate ?? 0))),
                    Description = inputCostItem.Description,
                    TransactionDate = DateOnly.Parse(inputCostItem.TransactionDate),
                });
            }
        }

        entity.TotalAmount = dto.TotalAmount ?? inputCostItems.Sum(ici => ici.TotalTaxed ?? ici.TotalNonTaxed ?? 0);
        entity.InputCostItems = inputCostItems;

        return entity;
    }

    // Map UpdateInputCostRequestDto to InputCost entity
    public static void UpdateFromDto(this InputCost entity, UpdateInputCostRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        entity.Title = dto.Title ?? entity.Title;
        entity.IssueDate = DateOnly.TryParse(dto.IssueDate, out var issueDate)
            ? issueDate
            : entity.IssueDate;
        entity.PaymentDate = DateOnly.TryParse(dto.PaymentDate, out var paymentDate)
            ? paymentDate
            : entity.PaymentDate;
        entity.OriginalNumber = dto.OriginalNumber ?? entity.OriginalNumber;
        entity.ConstructionUid = Guid.TryParse(dto.ConstructionId, out var constructionUid)
            ? constructionUid
            : entity.ConstructionUid;
        entity.EntryTypeUid = Guid.TryParse(dto.EntryTypeId, out var entryTypeUid)
            ? entryTypeUid
            : entity.EntryTypeUid;
        entity.VendorUid = Guid.TryParse(dto.VendorId, out var vendorUid)
            ? vendorUid
            : entity.VendorUid;
        entity.PaymentTypeUid = Guid.TryParse(dto.PaymentTypeId, out var paymentTypeUid)
            ? paymentTypeUid
            : entity.PaymentTypeUid;
        entity.TotalAmount = dto.TotalAmount ?? entity.TotalAmount;
        entity.Description = dto.Description ?? entity.Description;

        if (dto.InputCostItems != null && dto.InputCostItems.Any())
        {
            var existInputCostItems = entity.InputCostItems.ToList();

            foreach (var ici in existInputCostItems ?? [])
            {
                var iciDto = dto.InputCostItems
                    .Where(iciDto => iciDto.InputCostItemId == ici.InputCostItemUid.ToString())
                    .FirstOrDefault();
                if (iciDto is null)
                {
                    continue;
                }
                //handle delete
                if (iciDto.IsDeleted == true)
                {
                    ici.IsDeleted = true;
                    continue;
                }
                ici.UpdateFromDto(iciDto);
            }

            foreach (var iciDto in dto.InputCostItems ?? [])
            {
                if (iciDto.Item is not null && Guid.TryParse(iciDto.Item.ItemId, out var itemUid))
                {
                    var inputCostItem = new InputCostItem
                    {
                        InputCostItemUid = GuidHelper.GenerateUUIDv7(),
                        InputCostUid = entity.InputCostUid,
                        ConstructionUid = entity.ConstructionUid,
                        TransactionDate = DateOnly.TryParse(iciDto.TransactionDate, out var transactionDate)
                            ? transactionDate
                            : entity.IssueDate,
                        ItemUid = itemUid,
                        Unit = iciDto.Unit,
                        OrgUid = entity.OrgUid,
                        VendorUid = entity.VendorUid,
                        Quantity = iciDto.Quantity ?? 0,
                        Price = iciDto.Price ?? 0,
                        TaxRate = iciDto.TaxRate,
                        TotalNonTaxed = iciDto.TotalNonTaxed ?? (long)(iciDto.Price ?? 0 * iciDto.Quantity ?? 0),
                        TotalTaxed = iciDto.TotalTaxed ?? (long)(iciDto.Price ?? 0 * iciDto.Quantity ?? 0 * (1 + (iciDto.TaxRate ?? 0))),
                        Description = iciDto.Description,
                    };
                    entity.InputCostItems.Add(inputCostItem);
                    continue;
                }
                if (iciDto.Item is not null && string.IsNullOrEmpty(iciDto.Item.ItemName) && Guid.TryParse(iciDto.Item.CategoryId, out var categoryUid))
                {
                    var newItem = new Item
                    {
                        ItemUid = GuidHelper.GenerateUUIDv7(),
                        ItemCode = $"UNKNOWN_CODE_{DateTime.Now:yyyyMMdd}",
                        ItemName = iciDto.Item.ItemName ?? $"UNKNOWN_NAME_{DateTime.Now:yyyyMMdd}",
                        CategoryUid = categoryUid,
                        OrgUid = entity.OrgUid,
                        IsDeleted = false,
                    };
                    var inputCostItem = new InputCostItem
                    {
                        InputCostItemUid = GuidHelper.GenerateUUIDv7(),
                        InputCostUid = entity.InputCostUid,
                        TransactionDate = DateOnly.TryParse(iciDto.TransactionDate, out var transactionDate)
                            ? transactionDate
                            : entity.IssueDate,
                        Item = newItem,
                        ItemUid = newItem.ItemUid,
                        Unit = iciDto.Unit,
                        OrgUid = entity.OrgUid,
                        VendorUid = entity.VendorUid,
                        Quantity = iciDto.Quantity ?? 0,
                        Price = iciDto.Price ?? 0,
                        TaxRate = iciDto.TaxRate,
                        TotalNonTaxed = iciDto.TotalNonTaxed ?? (long)(iciDto.Price ?? 0 * iciDto.Quantity ?? 0),
                        TotalTaxed = iciDto.TotalTaxed ?? (long)(iciDto.Price ?? 0 * iciDto.Quantity ?? 0 * (1 + (iciDto.TaxRate ?? 0))),
                        Description = iciDto.Description,
                    };
                    entity.InputCostItems.Add(inputCostItem);
                    continue;
                }
            }
        }
    }

    #endregion
}