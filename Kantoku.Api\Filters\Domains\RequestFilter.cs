using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Filters.Domains;

public class RequestFilter : BaseFilter
{
    [FromQuery(Name = "fromDate")]
    public string? FromDate { get; set; }

    [FromQuery(Name = "toDate")]
    public string? ToDate { get; set; }

    [FromQuery(Name = "statusCode")]
    public string? StatusCode { get; set; }

    [FromQuery(Name = "requestTypeCode")]
    public string? RequestTypeCode { get; set; }

    [FromQuery(Name = "statusCodes")]
    public IEnumerable<string>? StatusCodes { get; set; }

    [FromQuery(Name = "requestTypeCodes")]
    public IEnumerable<string>? RequestTypeCodes { get; set; }

    [FromQuery(Name = "isUserRequestedLeave")]
    public bool? IsUserRequestedLeave { get; set; }
}