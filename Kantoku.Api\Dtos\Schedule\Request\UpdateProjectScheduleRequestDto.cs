using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.Schedule.Request;

public class UpdateProjectScheduleRequestDto
{
    /// <summary>
    /// Planned workload
    /// </summary>
    [JsonPropertyName("plannedWorkload")]
    public float? PlannedWorkload { get; set; }

    /// <summary>
    /// Estimated workload
    /// </summary>
    [JsonPropertyName("estimatedWorkload")]
    public float? PresignedWorkload { get; set; }

    /// <summary>
    /// Description
    /// </summary>
    [JsonPropertyName("description")]
    public string? Description { get; set; }
}