using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Databases.Queryables;

namespace Kantoku.Api.Databases.Repositories;

public interface IEmployeeInvitationRepository
{
    Task<(IEnumerable<EmployeeInvitation>, int)> GetByFilter(EmployeeInvitationFilter filter, EmployeeInvitationQueryableOptions options);
    Task<EmployeeInvitation?> GetById(Guid employeeInvitationId, EmployeeInvitationQueryableOptions options);
    Task<bool> IsExist(string email);
    Task<EmployeeInvitation?> Create(EmployeeInvitation pendingEmployee);
    Task<EmployeeInvitation?> Update(EmployeeInvitation pendingEmployee);
}

[Repository(ServiceLifetime.Scoped)]
public class EmployeeInvitationRepository : BaseRepository<EmployeeInvitation>, IEmployeeInvitationRepository
{
    private readonly IEmployeeInvitationQueryable employeeInvitationQueryable;

    public EmployeeInvitationRepository(
        PostgreDbContext context,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor,
        IEmployeeInvitationQueryable employeeInvitationQueryable
    ) : base(context, logger, httpContextAccessor)
    {
        this.employeeInvitationQueryable = employeeInvitationQueryable;
    }

    public async Task<(IEnumerable<EmployeeInvitation>, int)> GetByFilter(EmployeeInvitationFilter filter, EmployeeInvitationQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = employeeInvitationQueryable.GetEmployeeInvitationQueryFiltered(filter, options);

            var invitedEmployees = await query
                .OrderByDescending(e => e.ExpiredTime)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var totalRecords = await query
                .CountAsync();

            return (invitedEmployees, totalRecords);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting pending employees by email");
            return ([], 0);
        }
    }

    public async Task<bool> IsExist(string email)
    {
        try
        {
            var query = employeeInvitationQueryable.GetEmployeeInvitationQuery(new EmployeeInvitationQueryableOptions())
                .Where(e => e.Email.Equals(email))
                .Where(e => e.OrgUid == GetCurrentOrgUid())
                .Where(e => e.IsAccepted == false && e.IsDeleted == false && e.ExpiredTime > DateTime.Now);
            return await query.AnyAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error checking if invitation exists");
            return false;
        }
    }

    public async Task<EmployeeInvitation?> GetById(Guid employeeInvitationId, EmployeeInvitationQueryableOptions options)
    {
        try
        {
            var query = employeeInvitationQueryable.GetEmployeeInvitationQuery(options)
                .Where(e => e.EmployeeInvitationUid == employeeInvitationId);
            return await query.FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting employee invitation by employeeInvitationId");
            return null;
        }
    }

    public async Task<EmployeeInvitation?> Create(EmployeeInvitation pendingEmployee)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.EmployeeInvitations.AddAsync(pendingEmployee);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return pendingEmployee;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating pending employee: {EmployeeInvitation}", pendingEmployee);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<EmployeeInvitation?> Update(EmployeeInvitation pendingEmployee)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.EmployeeInvitations.Update(pendingEmployee);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return pendingEmployee;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating pending employee: {EmployeeInvitation}", pendingEmployee);
            await transaction.RollbackAsync();
            return null;
        }
    }
}

