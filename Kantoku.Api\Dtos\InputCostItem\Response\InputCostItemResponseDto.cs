﻿using Kantoku.Api.Dtos.Base.Response;

namespace Kantoku.Api.Dtos.InputCostItem.Response;

/// <summary>
/// Response DTO for input cost item details
/// </summary>
public class InputCostItemResponseDto : BaseResponseDto
{
    /// <summary>
    ///  the input cost item identifier
    /// </summary>
    public string? InputCostItemId { get; set; }

    /// <summary>
    ///  the transaction date
    /// </summary>
    public string? TransactionDate { get; set; }

    /// <summary>
    ///  the item identifier
    /// </summary>
    public string? ItemId { get; set; }

    /// <summary>
    ///  the item name
    /// </summary>
    public string? ItemName { get; set; }

    /// <summary>
    ///  the category code
    /// </summary>
    public string? CategoryCode { get; set; }

    /// <summary>
    ///  the category name
    /// </summary>
    public string? CategoryName { get; set; }

    /// <summary>
    ///  the unit
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    ///  the vendor identifier
    /// </summary>
    public string? VendorId { get; set; }

    /// <summary>
    ///  the vendor name
    /// </summary>
    public string? VendorName { get; set; }

    /// <summary>
    ///  the quantity
    /// </summary>
    public float? Quantity { get; set; }

    /// <summary>
    ///  the price
    /// </summary>
    public int? Price { get; set; }

    /// <summary>
    ///  the average price
    /// </summary>
    public int? AveragePrice { get; set; }

    /// <summary>
    ///  the tax rate
    /// </summary>
    public float? TaxRate { get; set; }

    /// <summary>
    ///  the total average amount
    /// </summary>
    public long? TotalAverageAmount { get; set; }

    /// <summary>
    ///  the total non-taxed
    /// </summary>
    public long? TotalNonTaxed { get; set; }

    /// <summary>
    ///  the total amount after tax
    /// </summary>
    public long? TotalTaxed { get; set; }

    /// <summary>
    ///  the description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    ///  the input cost identifier
    /// </summary>
    public string? InputCostId { get; set; }

    /// <summary>
    ///  the construction identifier
    /// </summary>
    public string? ConstructionId { get; set; }

    /// <summary>
    ///  the original input cost number
    /// </summary>
    public string? OriginalInputCostNumber { get; set; }
}
