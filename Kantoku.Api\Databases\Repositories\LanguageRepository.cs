using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;

namespace Kantoku.Api.Databases.Repositories;

public interface ILanguageRepository
{
    Task<IEnumerable<Language>> GetAll();
    Task<Language?> GetByCode(string code);
}

[Repository(ServiceLifetime.Scoped)]
public class LanguageRepository : BaseRepository<Language>, ILanguageRepository
{
    public LanguageRepository(PostgreDbContext context, Serilog.ILogger logger, IHttpContextAccessor httpContextAccessor)
        : base(context, logger, httpContextAccessor)
    {
    }

    public async Task<IEnumerable<Language>> GetAll()
    {
        try
        {
            return await context.Languages.ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all languages");
            return [];
        }
    }

    public async Task<Language?> GetByCode(string code)
    {
        try
        {
            return await context.Languages
                .Where(l => l.LanguageCode.Equals(code))
                .FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting language by code {Code}", code);
            return null;
        }
    }
}