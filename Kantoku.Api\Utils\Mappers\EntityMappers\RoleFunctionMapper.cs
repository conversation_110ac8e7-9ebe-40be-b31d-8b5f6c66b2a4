using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.FunctionAccessibility.Request;
using Kantoku.Api.Dtos.FunctionAccessibility.Response;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class RoleFunctionMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a Role entity to a RolePrivilegeResponseDto
    /// </summary>
    /// <param name="role">The Role entity to map</param>
    /// <param name="leafFunctions">The list of leaf functions</param>
    /// <param name="roleFunctions">The list of role functions</param>
    /// <returns>A RolePrivilegeResponseDto representing the Role entity</returns>
    public static RolePrivilegeResponseDto ToRolePrivilegeResponseDto(
        this Role role,
        IEnumerable<Function> leafFunctions,
        IEnumerable<RoleFunction> roleFunctions)
    {
        var roleFunctionPrivileges = leafFunctions.Select(f =>
        {
            var functionPrivilege = new FunctionPrivilegeResponseDto
            {
                FunctionId = f.FunctionUid.ToString(),
                FunctionName = f.FunctionName,
                RoleId = role.RoleUid.ToString(),
                IsHeader = f.Children.Count > 0,
            };
            if (f.Children.Count == 0)
            {
                functionPrivilege.CanRead = roleFunctions.FirstOrDefault(rf =>
                    rf.RoleUid == role.RoleUid && rf.FunctionUid == f.FunctionUid)?.CanRead ?? false;
                functionPrivilege.CanCreate = roleFunctions.FirstOrDefault(rf =>
                    rf.RoleUid == role.RoleUid && rf.FunctionUid == f.FunctionUid)?.CanCreate ?? false;
                functionPrivilege.CanUpdate = roleFunctions.FirstOrDefault(rf =>
                    rf.RoleUid == role.RoleUid && rf.FunctionUid == f.FunctionUid)?.CanUpdate ?? false;
                functionPrivilege.CanDelete = roleFunctions.FirstOrDefault(rf =>
                    rf.RoleUid == role.RoleUid && rf.FunctionUid == f.FunctionUid)?.CanDelete ?? false;
            }
            return functionPrivilege;
        }).ToList();

        return new RolePrivilegeResponseDto
        {
            StructureId = role.Structure?.StructureUid.ToString(),
            RoleId = role.RoleUid.ToString(),
            RoleName = role.RoleName,
            FunctionPrivileges = roleFunctionPrivileges
        };
    }

    /// <summary>
    /// Maps a Structure entity to a StructurePrivilegeResponseDto
    /// </summary>
    /// <param name="structure">The Structure entity to map</param>
    /// <returns>A StructurePrivilegeResponseDto representing the Structure entity</returns>
    public static StructurePrivilegeResponseDto ToStructurePrivilegeResponseDto(this Structure structure)
    {
        return new StructurePrivilegeResponseDto
        {
            StructureId = structure.StructureUid.ToString(),
            StructureName = structure.StructureName,
            RolesPrivileges = structure.Roles
                    .Where(role => role is not null && role.IsDeleted == false && role.IsHidden == false)
                    .Select(role => new RolePrivilegeResponseDto
                    {
                        StructureId = structure.StructureUid.ToString(),
                        RoleId = role.RoleUid.ToString(),
                        RoleName = role.RoleName ?? string.Empty,
                        FunctionPrivileges = role.RoleFunctions?
                            .Where(rf => rf is not null && rf.Function is not null)
                            .OrderBy(rf => rf.Function.DisplayOrder)
                            .Select(rf => new FunctionPrivilegeResponseDto
                            {
                                FunctionId = rf.FunctionUid.ToString(),
                                FunctionName = rf.Function.FunctionName,
                                RoleId = rf.RoleUid.ToString(),
                                IsHeader = rf.Function.Children.Count > 0,
                                CanRead = rf.CanRead,
                                CanCreate = rf.CanCreate,
                                CanUpdate = rf.CanUpdate,
                                CanDelete = rf.CanDelete
                            })
                            .ToList() ?? []
                    })
                    .ToList()
        };
    }


    /// <summary>
    /// Maps a Role entity to a SimpleRolePrivilegeResponseDto
    /// </summary>
    /// <param name="functions">The list of functions</param>
    /// <param name="roleFunctions">The list of role functions</param>
    /// <returns>A SimpleRolePrivilegeResponseDto representing the Role entity</returns>
    public static SimpleFunctionPrivilegesResponseDto ToSimpleRolePrivilegeResponseDto(
        this IEnumerable<Function> functions,
        IEnumerable<RoleFunction> roleFunctions)
    {
        return new SimpleFunctionPrivilegesResponseDto
        {
            Items = functions
                .Select(function =>
                {
                    var roleFunction = roleFunctions
                        .Where(rf => rf.FunctionUid == function.FunctionUid)
                        .ToList();
                    var functionPrivilege = new SimpleFunctionPrivilegeResponseDto
                    {
                        FunctionId = function.FunctionUid.ToString(),
                        FunctionName = function.FunctionName,
                        CanRead = roleFunction.Any(rf => rf.CanRead == true),
                        CanCreate = roleFunction.Any(rf => rf.CanCreate == true),
                        CanUpdate = roleFunction.Any(rf => rf.CanUpdate == true),
                        CanDelete = roleFunction.Any(rf => rf.CanDelete == true),
                    };
                    return functionPrivilege;
                }).ToList()
        };
    }

    /// <summary>
    /// Maps a RoleFunction entity to a FunctionPrivilegeResponseDto
    /// </summary>
    /// <param name="role">The Role entity to map</param>
    /// <param name="roleFunctions">The list of role functions</param>
    /// <returns>A FunctionPrivilegeResponseDto representing the RoleFunction entity</returns>
    public static RolePrivilegeResponseDto ToRolePrivilegeResponseDto(
        this Role role,
        IEnumerable<RoleFunction> roleFunctions)
    {
        var lst = roleFunctions.Select(urf =>
        {
            var lstItem = new FunctionPrivilegeResponseDto
            {
                FunctionId = urf.FunctionUid.ToString(),
                FunctionName = urf.Function.FunctionName,
                IsHeader = urf.Function.Children.Count > 0,
                CanCreate = urf.CanCreate,
                CanDelete = urf.CanDelete,
                CanUpdate = urf.CanUpdate,
                CanRead = urf.CanRead,
            };
            return lstItem;
        });

        var result = new RolePrivilegeResponseDto
        {
            StructureId = role.Structure?.StructureUid.ToString(),
            RoleId = role.RoleUid.ToString(),
            RoleName = role.RoleName,
            FunctionPrivileges = lst
        };
        return result;
    }

    /// <summary>
    /// Maps a RoleFunction entity to a FunctionPrivilegeResponseDto
    /// </summary>
    /// <param name="roleFunction">The RoleFunction entity to map</param>
    /// <param name="function">The function to map</param>
    /// <returns>A FunctionPrivilegeResponseDto representing the RoleFunction entity</returns>
    public static FunctionPrivilegeResponseDto ToFunctionPrivilegeResponseDto(
        this RoleFunction roleFunction,
        Function function)
    {
        return new FunctionPrivilegeResponseDto
        {
            FunctionId = function.FunctionUid.ToString(),
            FunctionName = function.FunctionName,
            IsHeader = function.Children.Count > 0,
            CanCreate = roleFunction.CanCreate,
            CanDelete = roleFunction.CanDelete,
            CanUpdate = roleFunction.CanUpdate,
            CanRead = roleFunction.CanRead,
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Updates a RoleFunction entity from a FunctionPrivilegeResponseDto
    /// </summary>  
    /// <param name="roleFunction">The RoleFunction entity to update</param>
    /// <param name="dto">The FunctionPrivilegeResponseDto to update from</param>
    public static void UpdateFromDto(this RoleFunction roleFunction, UpdateMenuPrivilegeRequestDto dto)
    {
        if (roleFunction is null)
            return;
        if (dto.CanRead is not null)
            roleFunction.CanRead = dto.CanRead.Value;
        if (dto.CanCreate is not null)
            roleFunction.CanCreate = dto.CanCreate.Value;
        if (dto.CanUpdate is not null)
            roleFunction.CanUpdate = dto.CanUpdate.Value;
        if (dto.CanDelete is not null)
            roleFunction.CanDelete = dto.CanDelete.Value;
    }

    #endregion
}