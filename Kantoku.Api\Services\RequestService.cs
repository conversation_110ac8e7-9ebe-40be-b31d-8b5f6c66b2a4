using Kantoku.Api.Databases.Models;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.EmployeeRequest.Request;
using Kantoku.Api.Dtos.EmployeeRequest.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public interface IRequestService
{
    Task<ResultDto<RequestInfoResponseDto>> GetRequestById(Guid requestId);
    Task<ResultDto<RequestsInfoResponseDto>> GetRequestsByUser(RequestFilter filter);
    Task<ResultDto<RequestsInfoResponseDto>> GetRequestByApprover(RequestFilter filter);
    Task<ResultDto<RequestInfoResponseDto>> CreateRequestAsync(CreateEmployeeRequestRequestDto dto);
    Task<ResultDto<RequestInfoResponseDto>> UpdateRequestAsync(Guid requestId, UpdateEmployeeRequestRequestDto dto);
    Task<ResultDto<RequestInfoResponseDto>> ProcessRequest(Guid requestId, bool status, string? rejectReason = null);
    Task<ResultDto<RequestInfoResponseDto>> CancelRequest(Guid requestId);
    Task<ResultDto<bool>> DeleteRequest(Guid requestId);
}

[Service(ServiceLifetime.Scoped)]
public class RequestService : BaseService<RequestService>, IRequestService
{
    private readonly IRequestRepository requestRepository;
    private readonly IMonthlyReportRepository monthlyReportRepository;
    private readonly IEmployeeLeaveRepository employeeLeaveRepository;
    private readonly IEmployeeRepository employeeRepository;
    private readonly RequestQueryableOptions options = new()
    {
        IncludedAuthor = true,
        IncludedProject = true,
        IncludedApprover1 = true,
        IncludedApprover2 = true,
    };

    public RequestService(IRequestRepository requestRepository,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor,
        IEmployeeLeaveRepository employeeLeaveRepository,
        IMonthlyReportRepository monthlyReportRepository,
        IEmployeeRepository employeeRepository) :
            base(logger, httpContextAccessor)
    {
        this.requestRepository = requestRepository;
        this.employeeLeaveRepository = employeeLeaveRepository;
        this.monthlyReportRepository = monthlyReportRepository;
        this.employeeRepository = employeeRepository;
    }

    public async Task<ResultDto<RequestInfoResponseDto>> GetRequestById(Guid requestId)
    {
        var request = await requestRepository.GetById(requestId, options);
        if (request is null)
        {
            logger.Error("Request not found for ID: {Id}", requestId);
            return new ErrorResultDto<RequestInfoResponseDto>(ResponseCodeConstant.REQUEST_NOT_EXIST);
        }
        var result = request.ToRequestInfoResponseDto(GetCurrentLanguageCode());
        return new SuccessResultDto<RequestInfoResponseDto>(result);
    }

    public async Task<ResultDto<RequestsInfoResponseDto>> GetRequestByApprover(RequestFilter filter)
    {
        var (requests, total) = await requestRepository.GetByManager(GetCurrentEmployeeUid(), filter, options);
        if (requests is null || total == 0 || !requests.Any())
        {
            logger.Error("No requests found for filter: {Filter}", filter);
            return new ErrorResultDto<RequestsInfoResponseDto>(ResponseCodeConstant.REQUEST_NOT_EXIST);
        }
        var result = requests.ToRequestsInfoResponseDto(filter.PageNum, filter.PageSize, total, GetCurrentLanguageCode());
        return new SuccessResultDto<RequestsInfoResponseDto>(result);
    }

    public async Task<ResultDto<RequestsInfoResponseDto>> GetRequestsByUser(RequestFilter filter)
    {
        var (requests, total) = await requestRepository.GetByAuthor(GetCurrentEmployeeUid(), filter, options);
        if (requests is null || total == 0 || !requests.Any())
        {
            logger.Error("No requests found for filter: {Filter}", filter);
            return new ErrorResultDto<RequestsInfoResponseDto>(ResponseCodeConstant.REQUEST_NOT_EXIST);
        }
        var result = requests.ToRequestsInfoResponseDto(filter.PageNum, filter.PageSize, total, GetCurrentLanguageCode());
        return new SuccessResultDto<RequestsInfoResponseDto>(result);
    }

    public async Task<ResultDto<RequestInfoResponseDto>> ProcessRequest(Guid requestId, bool status, string? rejectReason = null)
    {
        var option = options.TrackingOptions();
        var request = await requestRepository.GetById(requestId, option);
        if (request is null)
        {
            logger.Error("Request with ID {RequestId} not found", requestId);
            return new ErrorResultDto<RequestInfoResponseDto>(ResponseCodeConstant.REQUEST_NOT_EXIST);
        }
        var (level1Approval, level2Approval, representativeAuthority)
            = await employeeRepository.HasApprovalAuthority(GetCurrentEmployeeUid());

        if (!level1Approval && !level2Approval && !representativeAuthority && !string.Equals(request.StatusCode, StatusConstants.PENDING))
        {
            logger.Error("Employee with ID {EmployeeUid} does not have approval authority", GetCurrentEmployeeUid());
            return new ErrorResultDto<RequestInfoResponseDto>(ResponseCodeConstant.REQUEST_NOT_EXIST);
        }
        if (level1Approval || representativeAuthority)
        {
            request.Approver1Uid = GetCurrentEmployeeUid();
            request.Approver1Status = status;
            request.Approver1Time = DateTime.Now;
            request.Approver1Notes = rejectReason;
        }
        if (level2Approval)
        {
            request.Approver2Uid = GetCurrentEmployeeUid();
            request.Approver2Status = status;
            request.Approver2Time = DateTime.Now;
            request.Approver2Notes = rejectReason;
        }
        request.StatusCode = GetRequestStatus(request);
        var updatedRequest = await requestRepository.Update(request, options);
        if (updatedRequest is null)
        {
            logger.Error("Failed to update request with ID: {RequestId}", requestId);
            return new ErrorResultDto<RequestInfoResponseDto>(ResponseCodeConstant.REQUEST_UPDATE_FAILED);
        }
        if (string.Equals(request.RequestTypeCode, RequestTypeConstants.LEAVE)
            && string.Equals(request.StatusCode, StatusConstants.APPROVED))
        {
            await UpdateEmployeeLeave(request.AuthorUid);
        }
        var result = updatedRequest.ToRequestInfoResponseDto(GetCurrentLanguageCode());
        return new SuccessResultDto<RequestInfoResponseDto>(result);
    }

    public async Task<ResultDto<RequestInfoResponseDto>> CancelRequest(Guid requestId)
    {
        var option = options.TrackingOptions();
        var request = await requestRepository.GetById(requestId, option);
        if (request is null)
        {
            logger.Error("Request not found for ID: {RequestId}", requestId);
            return new ErrorResultDto<RequestInfoResponseDto>(ResponseCodeConstant.REQUEST_NOT_EXIST);
        }

        request.StatusCode = StatusConstants.CANCELLED;

        var cancelledRequest = await requestRepository.Update(request, options);
        if (cancelledRequest is null)
        {
            logger.Error("Failed to cancel request with ID: {RequestId}", requestId);
            return new ErrorResultDto<RequestInfoResponseDto>(ResponseCodeConstant.CANCEL_REQUEST_FAILED);
        }
        var result = cancelledRequest.ToRequestInfoResponseDto(GetCurrentLanguageCode());
        return new SuccessResultDto<RequestInfoResponseDto>(result);
    }

    public async Task<ResultDto<RequestInfoResponseDto>> CreateRequestAsync(CreateEmployeeRequestRequestDto dto)
    {
        var currentEmployeeUid = GetCurrentEmployeeUid();
        var newRequest = dto.ToEntity(currentEmployeeUid);
        if (newRequest is null)
        {
            logger.Error("Failed to create request");
            return new ErrorResultDto<RequestInfoResponseDto>(ResponseCodeConstant.REQUEST_CREATE_FAILED);
        }

        var createdRequest = await requestRepository.Create(newRequest, options);
        if (createdRequest is null)
        {
            logger.Error("Failed to create request");
            return new ErrorResultDto<RequestInfoResponseDto>(ResponseCodeConstant.REQUEST_CREATE_FAILED);
        }
        var result = createdRequest.ToRequestInfoResponseDto(GetCurrentLanguageCode());
        return new SuccessResultDto<RequestInfoResponseDto>(result);
    }

    public async Task<ResultDto<RequestInfoResponseDto>> UpdateRequestAsync(Guid requestId, UpdateEmployeeRequestRequestDto dto)
    {
        var option = options.TrackingOptions();
        var existReq = await requestRepository.GetById(requestId, option);
        if (existReq is null)
        {
            logger.Error("Request not found for ID: {RequestId}", requestId);
            return new ErrorResultDto<RequestInfoResponseDto>(ResponseCodeConstant.REQUEST_NOT_EXIST);
        }

        if (string.Equals(existReq.StatusCode, StatusConstants.APPROVED))
        {
            logger.Error("Request with ID: {RequestId} is already approved", requestId);
            return new ErrorResultDto<RequestInfoResponseDto>(ResponseCodeConstant.REQUEST_ALREADY_APPROVED);
        }

        if (string.Equals(existReq.StatusCode, StatusConstants.CANCELLED))
        {
            logger.Error("Request with ID: {RequestId} is already cancelled", requestId);
            return new ErrorResultDto<RequestInfoResponseDto>(ResponseCodeConstant.REQUEST_ALREADY_CANCELLED);
        }
        existReq.UpdateFromDto(dto);

        if (GetCurrentAccountUid().Equals(existReq.CreatedBy))
        {
            logger.Information("Employee {EmployeeUid} is the creator of the request, set status to pending", GetCurrentEmployeeUid());
            existReq.StatusCode = StatusConstants.PENDING;
        }

        var updatedRequest = await requestRepository.Update(existReq, options);
        if (updatedRequest is null)
        {
            logger.Error("Failed to update request with ID: {RequestId}", requestId);
            return new ErrorResultDto<RequestInfoResponseDto>(ResponseCodeConstant.REQUEST_UPDATE_FAILED);
        }

        var result = updatedRequest.ToRequestInfoResponseDto(GetCurrentLanguageCode());
        return new SuccessResultDto<RequestInfoResponseDto>(result);
    }

    private static string? GetRequestStatus(Request request)
    {
        var requestType = request.RequestType;

        if (requestType.RequiredLevel2Approval == true
            && request.Approver2Status.HasValue)
        {
            return request.Approver2Status.Value ? StatusConstants.APPROVED : StatusConstants.REJECTED;
        }
        else if (requestType.RequiredLevel1Approval == true
            && request.Approver1Status.HasValue)
        {
            return request.Approver1Status.Value ? StatusConstants.APPROVED : StatusConstants.REJECTED;
        }
        return request.StatusCode;
    }

    private async Task UpdateEmployeeLeave(Guid employeeUid)
    {
        var employeeLeave = await employeeLeaveRepository.GetCurrentByEmployeeId(
            employeeUid,
            new EmployeeLeaveQueryableOptions());
        var currentAttendanceReport = await monthlyReportRepository.GetCurrentByEmployeeId(
            employeeUid,
            new MonthlyReportQueryableOptions());
        if (currentAttendanceReport is null || employeeLeave is null)
        {
            logger.Error("Current attendance report or employee leave not found for employee with ID: {EmployeeUid}", employeeUid);
            return;
        }

        var (leaveRequests, _) = await requestRepository.GetByAuthor(
            employeeUid,
            new RequestFilter
            {
                FromDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1).ToString("yyyy-MM-dd"),
                ToDate = DateTime.Now.ToString("yyyy-MM-dd"),
                StatusCode = StatusConstants.APPROVED,
                RequestTypeCode = RequestTypeConstants.LEAVE
            }, options);

        var selfUsedLeave = leaveRequests.Select(r => r.IsUserRequestedLeave == true);
        var orgUsedLeave = leaveRequests.Select(r => r.IsUserRequestedLeave == false);

        //TODO: update self used leave and org used leave
        currentAttendanceReport.SelfLeaveUsed = selfUsedLeave.Count();
        currentAttendanceReport.OrgLeaveUsed = orgUsedLeave.Count();
        await monthlyReportRepository.Update(currentAttendanceReport);
        await employeeLeaveRepository.Update(employeeLeave);
    }

    public async Task<ResultDto<bool>> DeleteRequest(Guid requestId)
    {
        var request = await requestRepository.GetById(requestId, options);
        if (request is null)
        {
            logger.Error("Request with ID {RequestId} not found", requestId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.REQUEST_NOT_EXIST);
        }

        request.IsDeleted = true;
        var isDeleted = await requestRepository.Update(request, options);
        if (isDeleted is null)
        {
            logger.Error("Failed to delete request with ID: {RequestId}", requestId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.REQUEST_DELETE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }
}