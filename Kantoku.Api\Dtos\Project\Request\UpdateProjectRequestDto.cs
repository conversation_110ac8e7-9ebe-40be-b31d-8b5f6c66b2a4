using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.Project.Request;

public class UpdateProjectRequestDto
{
    /// <summary>
    /// The unique code identifier for the project
    /// </summary>
    public string? ProjectCode { get; set; }

    /// <summary>
    /// The name of the project
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// The ID of the project type
    /// </summary>
    public string? ProjectTypeId { get; set; }

    /// <summary>
    /// The customer of the project
    /// </summary>
    public string? CustomerId { get; set; }

    /// <summary>
    /// The contractor of the project
    /// </summary>
    public string? ContractorId { get; set; }

    /// <summary>
    /// The physical address of the project location
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Indicates if this project is an office location
    /// </summary>
    public bool? IsOffice { get; set; }

    /// <summary>
    /// The initial budget allocated for the project
    /// </summary>
    public float? InitialBudget { get; set; }

    /// <summary>
    /// The actual budget spent on the project
    /// </summary>
    public float? ActualBudget { get; set; }

    /// <summary>
    /// Collection of work shift IDs associated with the project
    /// </summary>
    public IEnumerable<string>? WorkShiftIds { get; set; }

    /// <summary>
    /// Collection of work shift IDs associated with the project 
    /// with default presett
    /// </summary>
    public IEnumerable<ProjectWorkShiftRequestDto>? WorkShifts { get; set; }

    /// <summary>
    /// Collection of ranking cost IDs associated with the project
    /// </summary>
    public IEnumerable<ProjectRankingCostRequestDto>? ProjectRankingCosts { get; set; }

    /// <summary>
    /// Collection of employee IDs for primary project managers
    /// </summary>
    [JsonPropertyName("primaryManagerEmployeeIds")]
    public IEnumerable<string>? PrimaryManagerEmployeeIds { get; set; }

    /// <summary>
    /// Collection of employee IDs for sub project managers
    /// </summary>
    [JsonPropertyName("subManagerEmployeeIds")]
    public IEnumerable<string>? SubManagerEmployeeIds { get; set; }

    /// <summary>
    /// The expected start date of the project
    /// </summary>
    public string? ExpectedStartDate { get; set; }

    /// <summary>
    /// The expected end date of the project
    /// </summary>
    public string? ExpectedEndDate { get; set; }

    /// <summary>
    /// The actual start date of the project
    /// </summary>
    public string? ActualStartDate { get; set; }

    /// <summary>
    /// The actual end date of the project
    /// </summary>
    public string? ActualEndDate { get; set; }

    /// <summary>
    /// Detailed description of the project
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// The current status code of the project
    /// </summary>
    public string? StatusCode { get; set; }
}