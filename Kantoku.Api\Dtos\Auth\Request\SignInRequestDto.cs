using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.Auth.Request;

/// <summary>
/// Data transfer object for sign in request
/// </summary>
public class SignInRequestDto
{
    /// <summary>
    /// User's login ID (*)
    /// </summary>
    [Required]
    public required string LoginId { get; set; }

    /// <summary>
    /// User's password, minimum 8 characters (*)
    /// </summary>
    [Required]
    public required string Password { get; set; }
}