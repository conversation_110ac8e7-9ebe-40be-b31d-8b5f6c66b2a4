using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class OutSourceShiftConfiguration(string schema) : IEntityTypeConfiguration<OutSourceShift>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<OutSourceShift> builder)
    {
        builder.HasKey(e => e.OutSourceShiftUid).HasName("outsource_shift_pkey");

        builder.ToTable("outsource_shift", Schema);

        builder.Property(e => e.OutSourceShiftUid)
            .HasColumnName("outsource_shift_uid");
        builder.Property(e => e.ProjectScheduleUid)
            .HasColumnName("project_schedule_uid");
        builder.Property(e => e.OutSourceUid)
            .HasColumnName("outsource_uid");
        builder.Property(e => e.ScheduledStartTime)
            .HasColumnType("timestamp")
            .HasColumnName("scheduled_start_time");
        builder.Property(e => e.ScheduledEndTime)
            .HasColumnType("timestamp")
            .HasColumnName("scheduled_end_time");
        builder.Property(e => e.AssignedWorkload)
            .HasColumnName("assigned_workload");
        builder.Property(e => e.Role)
            .HasColumnName("role");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");

        builder.HasOne(d => d.OutSource).WithMany(p => p.OutSourceShifts)
            .HasForeignKey(d => d.OutSourceUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("outsource_shift_outsource_id_fkey");

        builder.HasOne(d => d.ProjectSchedule).WithMany(p => p.OutSourceShifts)
            .HasForeignKey(d => d.ProjectScheduleUid)
        .OnDelete(DeleteBehavior.ClientSetNull)
        .HasConstraintName("outsource_shift_project_schedule_id_fkey");
    }
}

