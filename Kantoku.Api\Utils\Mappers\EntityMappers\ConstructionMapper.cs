using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.CategorizedCost.Response;
using Kantoku.Api.Dtos.Construction.Request;
using Kantoku.Api.Dtos.Construction.Response;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class ConstructionMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a Construction entity to a ConstructionBaseResponseDto
    /// </summary>
    /// <param name="construction">The Construction entity to map</param>
    /// <returns>The mapped ConstructionBaseResponseDto</returns>
    public static ConstructionBaseResponseDto ToConstructionBaseResponseDto(this Construction construction)
    {
        if (construction == null)
            return new ConstructionBaseResponseDto();

        return new ConstructionBaseResponseDto
        {
            ConstructionId = construction.ConstructionUid.ToString(),
            ConstructionName = construction.ConstructionName,
            Description = construction.Description,
            IsPrimary = construction.IsPrimary,
        };
    }

    /// <summary>
    /// Maps a list of Construction entities to a list of ConstructionBaseResponseDto
    /// </summary>
    /// <param name="constructions">The list of Construction entities to map</param>
    /// <returns>The mapped list of ConstructionBaseResponseDto</returns>
    public static IEnumerable<ConstructionBaseResponseDto> ToConstructionBaseResponseDtos(
        this IEnumerable<Construction> constructions)
    {
        if (constructions == null || !constructions.Any())
            return [];

        return constructions.Select(c => c.ToConstructionBaseResponseDto());
    }

    /// <summary>
    /// Maps a list of Construction entities to a ConstructionSimpleResponseDto
    /// </summary>
    /// <param name="constructions">The list of Construction entities to map</param>
    /// <returns>The mapped ConstructionSimpleResponseDto</returns> 
    public static ConstructionSimpleResponseDto ToConstructionSimpleResponseDto(
        this IEnumerable<Construction> constructions)
    {
        if (constructions == null || !constructions.Any())
            return new ConstructionSimpleResponseDto();

        return new ConstructionSimpleResponseDto
        {
            Constructions = constructions.ToConstructionBaseResponseDtos()
        };
    }

    /// <summary>
    /// Maps a list of Construction entities to a ConstructionOverviewResponseDto
    /// </summary>
    /// <param name="constructions">The list of Construction entities to map</param>
    /// <param name="languageCode">The language code to use for the response</param>
    /// <returns>The mapped ConstructionOverviewResponseDto</returns>
    public static ConstructionOverviewResponseDto ToConstructionOverviewResponseDto(
        this IEnumerable<Construction> constructions,
         string languageCode)
    {
        if (constructions == null || !constructions.Any())
            return new ConstructionOverviewResponseDto();

        var contractCosts = constructions.ToConstructionsContractCostResponseDto();
        var estimateCosts = constructions.ToConstructionsEstimateCostResponseDto();
        var accumulatedCosts = constructions.ToConstructionsAccumulatedCostResponseDto(languageCode);

        var project = constructions.FirstOrDefault()?.Project;
        var constructionsResponseDto = new ConstructionOverviewResponseDto
        {
            ProjectId = project?.ProjectUid.ToString(),
            ProjectName = project?.ProjectName,
            ContractorName = project?.Contractor?.ContractorName,
            CustomerName = project?.Customer?.CustomerName,
            ContractualStartDate = project?.ExpectedStartDate?.ToString("yyyy-MM-dd"),
            ContractualEndDate = project?.ExpectedEndDate?.ToString("yyyy-MM-dd"),
            ContractCosts = contractCosts,
            EstimateCosts = estimateCosts,
            AccumulatedCosts = accumulatedCosts,
        };
        return constructionsResponseDto;
    }

    /// <summary>
    /// Maps a list of Construction entities to a ConstructionsContractCostResponseDto
    /// </summary>
    /// <param name="constructions">The list of Construction entities to map</param>
    /// <returns>The mapped ConstructionsContractCostResponseDto</returns>
    private static ConstructionsContractCostResponseDto ToConstructionsContractCostResponseDto(this IEnumerable<Construction> constructions)
    {
        if (constructions == null || !constructions.Any())
            return new ConstructionsContractCostResponseDto();

        var mainConstructionResponse = constructions
            .FirstOrDefault(c => c.IsPrimary)?
            .ToContractCostResponseDto() ?? new ContractCostResponseDto();
        var totalMainConstructionCost = (mainConstructionResponse?.InitialCostItems?.Sum(c => c.Amount) ?? 0)
            + (mainConstructionResponse?.ModifiedCostItems?.Sum(c => c.Amount) ?? 0);

        var subConstructionResponse = constructions
            .FirstOrDefault(c => !c.IsPrimary)?
            .ToContractCostResponseDto() ?? new ContractCostResponseDto();
        var totalSubConstructionCost = (subConstructionResponse?.InitialCostItems?.Sum(c => c.Amount) ?? 0)
            + (subConstructionResponse?.ModifiedCostItems?.Sum(c => c.Amount) ?? 0);

        var overallConstructionResponse = SumContractCosts(
            [mainConstructionResponse!, subConstructionResponse!]);
        var totalOverallConstructionCost = (overallConstructionResponse.InitialCostItems?.Sum(c => c.Amount) ?? 0)
            + (overallConstructionResponse.ModifiedCostItems?.Sum(c => c.Amount) ?? 0);

        var res = new ConstructionsContractCostResponseDto
        {
            MainConstructionContractCost = mainConstructionResponse,
            TotalMainConstructionCost = totalMainConstructionCost,
            SubConstructionContractCost = subConstructionResponse,
            TotalSubConstructionCost = totalSubConstructionCost,
            OverallConstructionContractCost = overallConstructionResponse,
            TotalOverallConstructionCost = totalOverallConstructionCost,
        };
        return res;
    }

    /// <summary>
    /// Maps a Construction entity to a ContractCostResponseDto
    /// </summary>
    /// <param name="construction">The Construction entity to map</param>
    /// <returns>The mapped ContractCostResponseDto</returns>
    private static ContractCostResponseDto ToContractCostResponseDto(this Construction construction)
    {
        if (construction is null)
            return new ContractCostResponseDto();

        var initialCostItems = construction.InitialContractualCosts?.Select(c => new ContractCostItemResponseDto
        {
            SequenceNumber = c.SequenceNumber,
            Amount = c.Amount,
        }).ToList() ?? [];

        var modifiedCostItems = construction.ModifiedContractualCosts?.Select(c => new ContractCostItemResponseDto
        {
            SequenceNumber = c.SequenceNumber,
            Amount = c.Amount,
        }).ToList() ?? [];

        var res = new ContractCostResponseDto
        {
            InitialCostItems = initialCostItems,
            ModifiedCostItems = modifiedCostItems,
            TotalInitialCost = initialCostItems.Sum(c => c.Amount),
            TotalModifiedCost = modifiedCostItems.Sum(c => c.Amount),
        };
        return res;
    }

    /// <summary>
    /// Sums a list of ContractCostResponseDto
    /// </summary>
    /// <param name="contractCosts">The list of ContractCostResponseDto to sum</param>
    /// <returns>The summed ContractCostResponseDto</returns>
    private static ContractCostResponseDto SumContractCosts(this IEnumerable<ContractCostResponseDto> contractCosts)
    {
        if (contractCosts is null || !contractCosts.Any())
            return new ContractCostResponseDto();

        var contractCostsList = contractCosts.ToList();

        var initialCostItems = contractCostsList
            .Where(c => c.InitialCostItems is not null)
            .SelectMany(c => c.InitialCostItems!)
            .GroupBy(item => new
            {
                item.SequenceNumber,
            })
            .Select(group => new ContractCostItemResponseDto
            {
                SequenceNumber = group.Key.SequenceNumber,
                Amount = group.Sum(item => item.Amount),
            }).ToList();

        var modifiedCostItems = contractCostsList
            .Where(c => c.ModifiedCostItems is not null)
            .SelectMany(c => c.ModifiedCostItems!)
            .GroupBy(item => new
            {
                item.SequenceNumber,
            })
            .Select(group => new ContractCostItemResponseDto
            {
                SequenceNumber = group.Key.SequenceNumber,
                Amount = group.Sum(item => item.Amount),
            }).ToList();

        return new ContractCostResponseDto
        {
            InitialCostItems = initialCostItems,
            ModifiedCostItems = modifiedCostItems,
            TotalInitialCost = initialCostItems.Sum(c => c.Amount),
            TotalModifiedCost = modifiedCostItems.Sum(c => c.Amount),
        };
    }

    /// <summary>
    /// Maps a list of Construction entities to a ConstructionsEstimateCostResponseDto
    /// </summary>
    /// <param name="constructions">The list of Construction entities to map</param>
    /// <returns>The mapped ConstructionsEstimateCostResponseDto</returns>
    private static ConstructionsEstimateCostResponseDto ToConstructionsEstimateCostResponseDto(this IEnumerable<Construction> constructions)
    {
        if (constructions == null || !constructions.Any())
            return new ConstructionsEstimateCostResponseDto();

        var mainConstructionResponse = constructions.FirstOrDefault(c => c.IsPrimary)?.ToEstimateCostResponseDto();
        var subConstructionResponse = constructions.FirstOrDefault(c => !c.IsPrimary)?.ToEstimateCostResponseDto();

        var overallConstructionResponse = SumEstimateCosts(
            [mainConstructionResponse!, subConstructionResponse!]);

        var res = new ConstructionsEstimateCostResponseDto
        {
            MainConstructionEstimateCost = mainConstructionResponse,
            SubConstructionEstimateCost = subConstructionResponse,
            OverallConstructionEstimateCost = overallConstructionResponse,
        };
        return res;
    }

    /// <summary>
    /// Maps a Construction entity to an EstimateCostResponseDto
    /// </summary>
    /// <param name="construction">The Construction entity to map</param>
    /// <returns>The mapped EstimateCostResponseDto</returns>
    private static EstimateCostResponseDto ToEstimateCostResponseDto(this Construction? construction)
    {

        if (construction is null)
        {
            return new EstimateCostResponseDto();
        }

        var initialCostItems = construction.InitialEstimatedCosts?.Select(c => new EstimateCostItemResponseDto
        {
            SequenceNumber = c.SequenceNumber,
            Amount = c.Amount,
        }).ToList() ?? [];

        var res = new EstimateCostResponseDto
        {
            EstimateCostItems = initialCostItems,
            TotalEstimateCost = construction.InitialEstimatedCosts?.Sum(c => c.Amount) ?? 0,
        };
        return res;
    }

    /// <summary>
    /// Sums a list of EstimateCostResponseDto
    /// </summary>
    /// <param name="estimateCosts">The list of EstimateCostResponseDto to sum</param>
    /// <returns>The summed EstimateCostResponseDto</returns>
    private static EstimateCostResponseDto SumEstimateCosts(this IEnumerable<EstimateCostResponseDto> estimateCosts)
    {
        if (estimateCosts is null || !estimateCosts.Any())
            return new EstimateCostResponseDto();

        var estimateCostsList = estimateCosts.ToList();

        var initialCostItems = estimateCostsList
            .Where(c => c.EstimateCostItems is not null)
            .SelectMany(c => c.EstimateCostItems!)
            .GroupBy(item => new
            {
                item.SequenceNumber,
            })
            .Select(group => new EstimateCostItemResponseDto
            {
                SequenceNumber = group.Key.SequenceNumber,
                Amount = group.Sum(item => item.Amount),
            }).ToList();

        return new EstimateCostResponseDto
        {
            EstimateCostItems = initialCostItems,
            TotalEstimateCost = initialCostItems.Sum(c => c.Amount),
        };
    }

    /// <summary>
    /// Maps a list of Construction entities to a ConstructionsAccumulatedCostResponseDto
    /// </summary>
    /// <param name="constructions">The list of Construction entities to map</param>
    /// <param name="languageCode">The language code to use for the response</param>
    /// <returns>The mapped ConstructionsAccumulatedCostResponseDto</returns>
    private static ConstructionsAccumulatedCostResponseDto ToConstructionsAccumulatedCostResponseDto(
        this IEnumerable<Construction> constructions, string languageCode)
    {
        var mainConstructionResponse = constructions.FirstOrDefault(c => c.IsPrimary)?.ToAccumulatedCostItemResponseDto(languageCode);
        var subConstructionResponse = constructions.FirstOrDefault(c => !c.IsPrimary)?.ToAccumulatedCostItemResponseDto(languageCode);

        var overallConstructionResponse = SumAccumulatedCosts(
            [mainConstructionResponse!, subConstructionResponse!]);

        var res = new ConstructionsAccumulatedCostResponseDto
        {
            MainConstructionAccumulatedCost = mainConstructionResponse,
            SubConstructionAccumulatedCost = subConstructionResponse,
            OverallConstructionAccumulatedCost = overallConstructionResponse,
        };
        return res;
    }

    /// <summary>
    /// Maps a Construction entity to an AccumulatedCostItemResponseDto
    /// </summary>
    /// <param name="construction">The Construction entity to map</param>
    /// <param name="languageCode">The language code to use for the response</param>
    /// <returns>The mapped AccumulatedCostItemResponseDto</returns>
    private static AccumulatedCostItemResponseDto ToAccumulatedCostItemResponseDto(
        this Construction? construction, string languageCode)
    {
        if (construction is null)
        {
            return new AccumulatedCostItemResponseDto();
        }

        var rootCategorizedCosts = construction.ConstructionCosts?
            .SelectMany(c => c.CategorizedCosts)
            .Where(c => c.Category.ParentUid is null)
            .Select(c => new
            {
                CategoryId = c.Category.CategoryUid.ToString(),
                c.Category.CategoryCode,
                c.Category.TranslatedCategory?.FirstOrDefault(t => t.LanguageCode.Equals(languageCode, StringComparison.CurrentCultureIgnoreCase))?.CategoryName,
                c.TotalAmount,
            }).ToList() ?? [];

        var childCategorizedCostsDictionary = construction.ConstructionCosts?
            .SelectMany(c => c.CategorizedCosts)
            .Where(c => c.Category.ParentUid is not null)
            .GroupBy(c => c.Category.RootCategoryCode)
            .ToDictionary(
                g => g.Key,
                g => g.Select(c => new CategorizedCostResponseDto
                {
                    CategoryId = c.Category.CategoryUid.ToString(),
                    CategoryCode = c.Category.CategoryCode,
                    CategoryName = c.Category.CategoryName,
                    TotalAmount = c.TotalAmount,
                }).ToList() ?? []
            ) ?? [];

        var hierarchicalCategories = rootCategorizedCosts?.Select(root => new CategorizedCostResponseDto
        {
            CategoryId = root.CategoryId,
            CategoryCode = root.CategoryCode,
            CategoryName = root.CategoryName,
            TotalAmount = root.TotalAmount,
        }).ToList() ?? [];

        var riskAmount = construction.ConstructionCosts?.Sum(c => c.RiskModifiedAmount) ?? 0;
        var totalAccumulatedCost = (rootCategorizedCosts?.Sum(c => (long)c.TotalAmount) ?? 0) + riskAmount;

        var res = new AccumulatedCostItemResponseDto
        {
            CategorizedCosts = hierarchicalCategories,
            RiskAmount = riskAmount,
            TotalAccumulatedCost = totalAccumulatedCost,
        };
        return res;
    }

    private static AccumulatedCostItemResponseDto SumAccumulatedCosts(
        this IEnumerable<AccumulatedCostItemResponseDto> accumulatedCosts)
    {
        if (accumulatedCosts is null || !accumulatedCosts.Any())
            return new AccumulatedCostItemResponseDto();

        var accumulatedCostsList = accumulatedCosts.ToList();

        var categorizedCosts = accumulatedCostsList
            .Where(c => c.CategorizedCosts is not null)
            .SelectMany(c => c.CategorizedCosts!)
            .GroupBy(item => new
            {
                item.CategoryId,
                item.CategoryCode,
                item.CategoryName,
            })
            .Select(group => new CategorizedCostResponseDto
            {
                CategoryId = group.Key.CategoryId,
                CategoryCode = group.Key.CategoryCode,
                CategoryName = group.Key.CategoryName,
                TotalAmount = group.Sum(item => item.TotalAmount),
            }).ToList();

        return new AccumulatedCostItemResponseDto
        {
            CategorizedCosts = categorizedCosts,
            RiskAmount = accumulatedCostsList.Sum(c => c.RiskAmount),
            TotalAccumulatedCost = accumulatedCostsList.Sum(c => c.TotalAccumulatedCost),
        };
    }

    /// <summary>
    /// Maps a list of Construction entities to a ConstructionsResponseDto
    /// </summary>
    /// <param name="constructions">The list of Construction entities to map</param>
    /// <param name="rootCategories">The list of root categories to use for the response</param>
    /// <param name="languageCode">The language code to use for the response</param>
    /// <returns>The mapped ConstructionsResponseDto</returns>  
    public static ConstructionsResponseDto ToConstructionsResponseDto(
        this IEnumerable<Construction> constructions,
        IEnumerable<Category> rootCategories,
        string languageCode)
    {
        if (constructions is null || !constructions.Any())
        {
            return new ConstructionsResponseDto();
        }

        var items = constructions
            .Select(c => c.ToConstructionResponseDto(rootCategories, languageCode))
            .Where(c => c.ConstructionId is not null)
            .ToList();

        var constructionsResponseDto = new ConstructionsResponseDto
        {
            Constructions = items,
        };
        return constructionsResponseDto;
    }

    /// <summary>
    /// Maps a Construction entity to a ConstructionResponseDto
    /// </summary>
    /// <param name="construction">The Construction entity to map</param>
    /// <param name="rootCategories">The list of root categories to use for the response</param>
    /// <param name="languageCode">The language code to use for the response</param>
    /// <returns>The mapped ConstructionResponseDto</returns>
    public static ConstructionResponseDto ToConstructionResponseDto(
        this Construction construction,
        IEnumerable<Category> rootCategories,
        string languageCode)
    {
        if (construction is null)
        {
            return new ConstructionResponseDto();
        }
        var constructionResponseDto = new ConstructionResponseDto
        {
            ConstructionId = construction.ConstructionUid.ToString(),
            ConstructionName = construction.ConstructionName,
            Description = construction.Description,
            IsPrimary = construction.IsPrimary,
            ContractualCosts = new ConstructionContractCostResponseDto
            {
                ConstructionId = construction.ConstructionUid.ToString(),
                IsPrimary = construction.IsPrimary,
                InitialCostItems = construction.InitialContractualCosts?.Select(c => new ContractCostItemResponseDto
                {
                    SequenceNumber = c.SequenceNumber,
                    Amount = c.Amount,
                }).ToList() ?? [],
                ModifiedCostItems = construction.ModifiedContractualCosts?.Select(c => new ContractCostItemResponseDto
                {
                    SequenceNumber = c.SequenceNumber,
                    Amount = c.Amount,
                }).ToList() ?? [],
            },
            EstimatedCosts = new ConstructionEstimateCostResponseDto
            {
                ConstructionId = construction.ConstructionUid.ToString(),
                IsPrimary = construction.IsPrimary,
                EstimateCostItems = construction.InitialEstimatedCosts?.Select(c => new EstimateCostItemResponseDto
                {
                    SequenceNumber = c.SequenceNumber,
                    Amount = c.Amount,
                }).ToList() ?? [],
            },
            AccumulatedCosts = new ConstructionAccumulatedCostResponseDto
            {
                ConstructionId = construction.ConstructionUid.ToString(),
                IsPrimary = construction.IsPrimary,
                CategorizedCosts = ToCategorizedCosts(construction.ConstructionCosts.SelectMany(c => c.CategorizedCosts), rootCategories, languageCode),
                RiskAmount = construction.ConstructionCosts.Sum(c => c.RiskModifiedAmount),
                TotalAccumulatedCost = construction.ConstructionCosts.Sum(c => c.TotalCostAmount),
            },
        };
        return constructionResponseDto;
    }

    /// <summary>
    /// Maps a list of CategorizedCost entities to a list of CategorizedCostResponseDto
    /// </summary>
    /// <param name="categorizedCosts">The list of CategorizedCost entities to map</param>
    /// <param name="rootCategories">The list of root categories to use for the response</param>
    /// <param name="languageCode">The language code to use for the response</param>
    /// <returns>The mapped list of CategorizedCostResponseDto</returns>
    private static IEnumerable<CategorizedCostResponseDto> ToCategorizedCosts(
        this IEnumerable<CategorizedCost> categorizedCosts,
        IEnumerable<Category> rootCategories,
        string languageCode)
    {
        if (rootCategories is null || !rootCategories.Any())
        {
            return [];
        }
        var res = rootCategories.Select(c =>
        {
            var subCategorizedCosts = categorizedCosts
                .Where(cc => cc.Category.ParentUid is not null)
                .Where(cc => cc.Category.ParentUid.Equals(c.CategoryUid) || cc.Category.RootCategoryCode.Equals(c.CategoryCode));

            var totalAmount = subCategorizedCosts.Sum(sc => sc.TotalAmount);

            var rootCategoryName = c.TranslatedCategory?
                .FirstOrDefault(t => t.LanguageCode.ToLower().Equals(languageCode.ToLower()))?.CategoryName;
            return new CategorizedCostResponseDto
            {
                CategoryId = c.CategoryUid.ToString(),
                CategoryCode = c.CategoryCode,
                CategoryName = rootCategoryName ?? c.CategoryName,
                TotalAmount = totalAmount,
            };
        });
        return res;
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateConstructionRequestDto to a Construction entity
    /// </summary>
    /// <param name="dto">The CreateConstructionRequestDto to map</param>
    /// <param name="projectUid">The project uid to use for the entity</param>
    /// <param name="orgUid">The org uid to use for the entity</param>
    /// <param name="isPrimary">Whether the construction is primary</param>
    /// <returns>The mapped Construction entity</returns>
    public static Construction? ToEntity(
        this CreateConstructionRequestDto dto,
        Guid projectUid,
        Guid orgUid,
        bool isPrimary = false)
    {
        if (dto == null)
            return null;

        var entity = new Construction
        {
            ConstructionName = dto.ConstructionName,
            ProjectUid = projectUid,
            OrgUid = orgUid,
            Description = dto.Description,
            IsPrimary = isPrimary,
            InitialContractualCosts = dto.InitialCost?.InitialContractualCosts?
                .Select(c => new InitalCost
                {
                    SequenceNumber = c.SequenceNumber,
                    Description = c.Description,
                    Amount = c.Amount,
                    RecordedDate = DateOnly.TryParse(c.RecordedDate, out var date) ? date : null,
                }).ToList(),
            ModifiedContractualCosts = dto.InitialCost?.ModifiedContractualCosts?
                .Select(c => new InitalCost
                {
                    SequenceNumber = c.SequenceNumber,
                    Description = c.Description,
                    Amount = c.Amount,
                    RecordedDate = DateOnly.TryParse(c.RecordedDate, out var date) ? date : null,
                }).ToList(),
            InitialEstimatedCosts = dto.InitialCost?.InitalEstimatedCosts?
                .Select(c => new InitalCost
                {
                    SequenceNumber = c.SequenceNumber,

                    Description = c.Description,
                    Amount = c.Amount,
                    RecordedDate = DateOnly.TryParse(c.RecordedDate, out var date) ? date : null,
                }).ToList(),
        };

        return entity;
    }

    /// <summary>
    /// Updates a Construction entity from an UpdateConstructionRequestDto
    /// </summary>
    /// <param name="entity">The Construction entity to update</param>
    /// <param name="dto">The UpdateConstructionRequestDto to update from</param>
    public static void UpdateFromDto(this Construction entity, UpdateConstructionRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        entity.ConstructionName = dto?.ConstructionName
           ?? entity.ConstructionName;
        entity.Description = dto?.Description
            ?? entity.Description;

        entity.InitialContractualCosts = dto?.InitialCost?.InitialContractualCosts?
                .Select(c => new InitalCost
                {
                    SequenceNumber = c.SequenceNumber,
                    Description = c.Description,
                    Amount = c.Amount,
                    RecordedDate = DateOnly.TryParse(c.RecordedDate, out var date) ? date : null,
                }).ToList() ?? entity.InitialContractualCosts;

        entity.ModifiedContractualCosts = dto?.InitialCost?.ModifiedContractualCosts?
           .Select(c => new InitalCost
           {
               SequenceNumber = c.SequenceNumber,
               Description = c.Description,
               Amount = c.Amount,
               RecordedDate = DateOnly.TryParse(c.RecordedDate, out var date) ? date : null,
           }).ToList() ?? entity.ModifiedContractualCosts;

        entity.InitialEstimatedCosts = dto?.InitialCost?.InitalEstimatedCosts?
                .Select(c => new InitalCost
                {
                    SequenceNumber = c.SequenceNumber,

                    Amount = c.Amount,
                    RecordedDate = DateOnly.TryParse(c.RecordedDate, out var date) ? date : null,
                }).ToList() ?? entity.InitialEstimatedCosts;
    }

    #endregion

    #region Helper methods


    #endregion
}
