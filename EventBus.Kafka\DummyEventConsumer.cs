using EventBus.Interfaces;

namespace EventBus.Kafka;

/// <summary>
/// Dummy event consumer that throws exceptions when used
/// Used for producer-only scenarios where consumer functionality should not be available
/// </summary>
public class DummyEventConsumer : IEventConsumer
{
    public void Subscribe<T, TH>()
        where T : class
        where TH : class, IEventHandler<T>
    {
        throw new InvalidOperationException("Consumer functionality is not available in producer-only mode. Use AddKafkaEventConsumer() instead of AddKafkaEventProducer() if you need consumer functionality.");
    }

    public void Unsubscribe<T, TH>()
        where T : class
        where TH : class, IEventHandler<T>
    {
        throw new InvalidOperationException("Consumer functionality is not available in producer-only mode. Use AddKafkaEventConsumer() instead of AddKafkaEventProducer() if you need consumer functionality.");
    }

    public Task StartAsync(CancellationToken cancellationToken = default)
    {
        throw new InvalidOperationException("Consumer functionality is not available in producer-only mode. Use AddKafkaEventConsumer() instead of AddKafkaEventProducer() if you need consumer functionality.");
    }

    public Task StopAsync(CancellationToken cancellationToken = default)
    {
        throw new InvalidOperationException("Consumer functionality is not available in producer-only mode. Use AddKafkaEventConsumer() instead of AddKafkaEventProducer() if you need consumer functionality.");
    }

    public void Dispose()
    {
        // Nothing to dispose
        GC.SuppressFinalize(this);
    }
}
