
using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.MonthlyAttReport;

public class EmployeeMonthlyDetailsResponseDto
{
    [JsonPropertyName("employeeId")]
    public string? EmployeeId { get; set; }
    
    [JsonPropertyName("employeeName")]
    public string? EmployeeName { get; set; }

    [JsonPropertyName("code")]
    public string? Code { get; set; }
    
    [JsonPropertyName("calendar")]
    public IEnumerable<AttendanceDetailDto> Calendar { get; set; } = [];
}

public class AttendanceDetailDto
{
    public required string Date { get; set; }
    public IEnumerable<ShiftInfoDto> ShiftInfos { get; set; } = [];
    public IEnumerable<RequestInfoDto> LeaveInfos { get; set; } = [];
}

public class ShiftInfoDto
{
    public string? ProjectName { get; set; }
    public string? WorkingLocation { get; set; }
    public string? CheckInTime { get; set; }
    public string? CheckOutTime { get; set; }
    public float? WorkHours { get; set; }
    public float? Overtime { get; set; }
    public string? Description { get; set; }
    public bool IsRequested { get; set; }
    public bool? IsApproved { get; set; }
}

public class RequestInfoDto
{
    public string? RequestType { get; set; }
    public string? LeaveType { get; set; }
    public string? RequestFrom { get; set; }
    public string? RequestTo { get; set; }
    public string? Status { get; set; }
    public string? Description { get; set; }
}
