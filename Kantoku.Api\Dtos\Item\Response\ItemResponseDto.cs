﻿using Kantoku.Api.Dtos.Base.Response;

namespace Kantoku.Api.Dtos.Item;

/// <summary>
/// Data transfer object for item details
/// </summary>
public class ItemResponseDto : BaseResponseDto
{
    /// <summary>
    ///  the item identifier
    /// </summary>
    public string? ItemId { get; set; }

    /// <summary>
    ///  the code of the item (*)
    /// </summary>
    public string? ItemCode { get; set; }

    /// <summary>
    ///  the name of the item (*)
    /// </summary>
    public string? ItemName { get; set; }

    /// <summary>
    ///  the sub name of the item
    /// </summary>
    public string? ItemSubName { get; set; }

    /// <summary>
    ///  the size of the item
    /// </summary>
    public string? Size { get; set; }

    /// <summary>
    ///  the serial number of the item
    /// </summary>
    public string? SerialNumber { get; set; }

    /// <summary>
    ///  the category ID of the item
    /// </summary>
    public string? CategoryId { get; set; }

    /// <summary>
    ///  the category code of the item
    /// </summary>
    public string? CategoryCode { get; set; }

    /// <summary>
    ///  the category name of the item
    /// </summary>
    public string? CategoryName { get; set; }

    /// <summary>
    ///  the manufacturer ID of the item
    /// </summary>
    public string? ManufacturerId { get; set; }

    /// <summary>
    ///  the manufacturer code of the item
    /// </summary>
    public string? ManufacturerCode { get; set; }

    /// <summary>
    ///  the manufacturer name of the item
    /// </summary>
    public string? ManufacturerName { get; set; }

    /// <summary>
    ///  additional description or notes about the item
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    ///  the image of the item
    /// </summary>
    public string? ImageUrl { get; set; }
}
