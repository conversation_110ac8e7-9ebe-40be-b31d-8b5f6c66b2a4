﻿using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Filters.Domains;
using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;

namespace Kantoku.Api.Databases.Repositories;

public interface IConstructionRepository
{
    Task<(int, IEnumerable<Construction>)> GetByFilter(ConstructionFilter filter, ConstructionQueryableOptions options);
    Task<IEnumerable<Construction>> GetByProjectId(Guid projectId, ConstructionQueryableOptions options);
    Task<Construction?> GetById(Guid constructionUid, ConstructionQueryableOptions options);
    Task<Construction?> Create(Construction construction);
    Task<Construction?> Update(Construction construction);
}

[Repository(ServiceLifetime.Scoped)]
public class ConstructionRepository : BaseRepository<Construction>, IConstructionRepository
{
    private readonly IConstructionQueryable constructionQueryable;

    public ConstructionRepository(
        PostgreDbContext context,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor,
        IConstructionQueryable constructionQueryable
    ) : base(context, logger, httpContextAccessor)
    {
        this.constructionQueryable = constructionQueryable;
    }

    public async Task<(int, IEnumerable<Construction>)> GetByFilter(ConstructionFilter filter, ConstructionQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = constructionQueryable.GetConstructionQueryFilter(filter, options);

            var total = await query
                .CountAsync();

            var result = await query
                .OrderBy(c => c.ConstructionUid)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            return (total, result);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all constructions");
            return (0, []);
        }
    }

    public async Task<IEnumerable<Construction>> GetByProjectId(Guid projectId, ConstructionQueryableOptions options)
    {
        try
        {
            var query = constructionQueryable.GetConstructionQueryIncluded(options)
                .Where(c => c.ProjectUid == projectId);

            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting constructions by project id");
            return [];
        }
    }

    public async Task<Construction?> GetById(Guid constructionUid, ConstructionQueryableOptions options)
    {
        try
        {
            var query = constructionQueryable.GetConstructionQueryIncluded(options)
                .Where(c => c.ConstructionUid == constructionUid);

            var result = await query
                .FirstOrDefaultAsync();

            return result;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting construction by id: {constructionUid}", constructionUid);
            return null;
        }
    }

    public async Task<Construction?> Create(Construction construction)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Constructions.AddAsync(construction);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(construction.ConstructionUid, new ConstructionQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating construction");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Construction?> Update(Construction construction)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Constructions.Update(construction);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(construction.ConstructionUid, new ConstructionQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating construction");
            await transaction.RollbackAsync();
            return null;
        }
    }
}
