using Kantoku.Api.Dtos.Base.Response;

namespace Kantoku.Api.Dtos.EmployeeShift.Response;

public class SimpleShiftInfoResponseDto : BaseResponseDto
{
    public string? EmployeeShiftId { get; set; }

    public string? ProjectId { get; set; }

    public string? ProjectName { get; set; }

    public string? WorkingLocation { get; set; }

    public string? WorkingDate { get; set; }

    public string? ScheduledStartTime { get; set; }

    public string? CheckInTime { get; set; }

    public string? CheckInLocation { get; set; }

    public string? ScheduledEndTime { get; set; }

    public string? CheckOutTime { get; set; }

    public string? CheckOutLocation { get; set; }

    public IEnumerable<BreakItemResponseDto>? BreakList { get; set; }
}
