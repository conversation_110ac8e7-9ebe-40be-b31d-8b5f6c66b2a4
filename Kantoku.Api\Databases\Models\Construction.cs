using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Models;

public class Construction : AuditableEntity
{
    public Guid ConstructionUid { get; set; }

    public Guid ProjectUid { get; set; }

    [AuditProperty]
    public string? ConstructionName { get; set; }

    [AuditProperty]
    public string? Description { get; set; }

    public bool IsPrimary { get; set; }

    public ICollection<InitalCost>? InitialContractualCosts { get; set; }

    public ICollection<InitalCost>? ModifiedContractualCosts { get; set; }

    public ICollection<InitalCost>? InitialEstimatedCosts { get; set; }

    public bool IsDeleted { get; set; }

    public Guid OrgUid { get; set; }

    public virtual Project Project { get; set; } = null!;

    public virtual Org Org { get; set; } = null!;

    public virtual ICollection<InputCost> InputCosts { get; set; } = [];

    public virtual ICollection<InputCostItem> InputCostItems { get; set; } = [];

    public virtual ICollection<ConstructionCost> ConstructionCosts { get; set; } = [];
}

public class InitalCost
{
    public short SequenceNumber { get; set; } // for 1st, 2nd, 3rd, etc.

    public string? Description { get; set; } // for description of the amount (main cons, sub cons 9th month, ...)

    public long Amount { get; set; }

    public DateOnly? RecordedDate { get; set; } // date when the amount was recorded
}
