using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Item;
using Kantoku.Api.Dtos.Item.Request;
using Kantoku.Api.Dtos.Manufacturer.Response;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class ItemMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps an Item entity to an ItemResponseDto
    /// </summary>
    /// <param name="item">The Item entity to map</param>
    /// <returns>The mapped ItemResponseDto</returns>   
    public static ItemResponseDto ToItemResponseDto(this Item item)
    {
        if (item == null)
            return new ItemResponseDto();

        return new ItemResponseDto
        {
            ItemId = item.ItemUid.ToString(),
            ItemCode = item.ItemCode,
            ItemName = item.ItemName,
            ItemSubName = item.ItemSubName,
            Size = item.Size,
            SerialNumber = item.SerialNumber,
            CategoryId = item.CategoryUid.ToString(),
            CategoryCode = item.Category?.CategoryCode,
            CategoryName = item.Category?.CategoryName,
            ManufacturerId = item.ManufacturerUid?.ToString(),
            ManufacturerCode = item.Manufacturer?.ManufacturerCode,
            ManufacturerName = item.Manufacturer?.ManufacturerName,
            Description = item.Description,
            ImageUrl = item.ImageUrl,
            CreateTime = item.CreatedTime?.ToString("yyyy-MM-dd HH:mm:ss"),
            UpdateTime = item.LastModifiedTime?.ToString("yyyy-MM-dd HH:mm:ss")
        };
    }

    /// <summary>
    /// Maps a collection of Item entities to an ItemsResponseDto
    /// </summary>
    /// <param name="items">The collection of Item entities to map</param>
    /// <param name="pageNum">The page number</param>
    /// <param name="pageSize">The page size</param>
    /// <param name="totalRecords">The total number of records</param>
    public static ItemsResponseDto ToItemsResponseDto(this IEnumerable<Item> items, int pageNum, int pageSize, int totalRecords)
    {
        if (items == null)
            return new ItemsResponseDto();

        return new ItemsResponseDto
        {
            Items = items.Select(i => i.ToItemResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    /// <summary>
    /// Maps a collection of Item entities to an EquipmentResponseDto collection
    /// </summary>
    /// <param name="items">The collection of Item entities to map</param>
    /// <returns>The mapped EquipmentResponseDto collection</returns>
    public static IEnumerable<EquipmentResponseDto> ToEquipmentResponseDto(this IEnumerable<Item> items)
    {
        if (items == null || !items.Any())
            return [];

        return items
            .Where(item => item.IsDeleted == false)
            .Where(item => item.Category.CategoryCode.Equals(CategoryConstant.EQUIPMENT))
            .Select(item => new EquipmentResponseDto
            {
                EquipmentId = item.ItemUid.ToString(),
                EquipmentCode = item.ItemCode,
                EquipmentName = item.ItemName,
                EquipmentSubName = item.ItemSubName,
                CategoryName = item.Category.CategoryName,
                Size = item.Size,
                SerialNumber = item.SerialNumber,
                Model = item.EquipmentAttribute?.Model,
                FuelConsumption = item.EquipmentAttribute?.FuelConsumption,
                EquipmentStatus = item.EquipmentAttribute?.EquipmentStatus,
                LastMaintenanceDate = item.EquipmentAttribute?.LastMaintenanceDate?.ToString("yyyy-MM-dd"),
                Description = item.Description
            });
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateItemRequestDto to an Item entity
    /// </summary>
    /// <param name="dto">The CreateItemRequestDto to map</param>
    /// <param name="orgUid">The organization UID</param>
    /// <returns>The mapped Item entity</returns>
    public static Item? ToEntity(this CreateItemRequestDto dto, Guid orgUid)
    {
        if (dto == null)
            return null;

        var entity = new Item
        {
            ItemUid = GuidHelper.GenerateUUIDv7(),
            OrgUid = orgUid,
            ItemCode = dto.ItemCode,
            ItemName = dto.ItemName,
            ItemSubName = dto.ItemSubName,
            Size = dto.Size,
            SerialNumber = dto.SerialNumber,
            CategoryUid = Guid.Parse(dto.CategoryId),
            ManufacturerUid = !string.IsNullOrEmpty(dto.ManufacturerId) ? Guid.Parse(dto.ManufacturerId) : null,
            Description = dto.Description,
            IsDeleted = false
            // Image handling (upload, storing URL) typically done in service layer
        };

        return entity;
    }

    /// <summary>
    /// Maps an UpdateItemRequestDto to an Item entity
    /// </summary>
    /// <param name="entity">The Item entity to update</param>
    /// <param name="dto">The UpdateItemRequestDto to map</param>
    public static void UpdateFromDto(this Item entity, UpdateItemRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        entity.ItemCode = dto.ItemCode ?? entity.ItemCode;
        entity.ItemName = dto.ItemName ?? entity.ItemName;
        entity.ItemSubName = dto.ItemSubName ?? entity.ItemSubName;
        entity.Size = dto.Size ?? entity.Size;
        entity.SerialNumber = dto.SerialNumber ?? entity.SerialNumber;
        entity.CategoryUid = Guid.TryParse(dto.CategoryId, out var categoryUid)
            ? categoryUid
            : entity.CategoryUid;
        entity.ManufacturerUid = Guid.TryParse(dto.ManufacturerId, out var manufacturerUid)
            ? manufacturerUid
            : entity.ManufacturerUid;
        entity.Description = dto.Description ?? entity.Description;
    }

    #endregion
}