using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class WorkShiftQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedProjectWorkShift { get; set; } = false;

    public WorkShiftQueryableOptions TrackingOptions()
    {
        IsTracking = true;
        return this;
    }

    public WorkShiftQueryableOptions SplitQueryOptions()
    {
        IsSplitQuery = true;
        return this;
    }
}

public interface IWorkShiftQueryable
{
    IQueryable<WorkShift> GetWorkShiftQuery(
        WorkShiftQueryableOptions options
    );
    IQueryable<WorkShift> GetWorkShiftQueryIncluded(
        WorkShiftQueryableOptions options,
        IQueryable<WorkShift>? query = null
    );

    IQueryable<WorkShift> GetWorkShiftQueryFiltered(
        WorkShiftFilter filter,
        WorkShiftQueryableOptions options,
        IQueryable<WorkShift>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class WorkShiftQueryable(PostgreDbContext context) :
    BaseQueryable<WorkShift>(context), IWorkShiftQueryable
{
    public IQueryable<WorkShift> GetWorkShiftQuery(
        WorkShiftQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(s => s.IsDeleted == false);
        return query;
    }

    public IQueryable<WorkShift> GetWorkShiftQueryIncluded(
        WorkShiftQueryableOptions options,
        IQueryable<WorkShift>? query = null
    )
    {
        query ??= GetWorkShiftQuery(options);
        if (options.IncludedProjectWorkShift || options.IncludedAll)
        {
            query = query.Include(s => s.ProjectWorkShifts)
                .ThenInclude(pws => pws.Project);
        }

        return query;
    }

    public IQueryable<WorkShift> GetWorkShiftQueryFiltered(
        WorkShiftFilter filter,
        WorkShiftQueryableOptions options,
        IQueryable<WorkShift>? query = null
    )
    {
        query ??= GetWorkShiftQueryIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(s => s.OrgUid == filter.OrgId);
        }

        if (filter.Keyword is not null)
        {
            query = query.Where(s => s.WorkShiftName.Contains(filter.Keyword));
        }

        if (filter.ProjectId is not null && filter.ProjectId != Guid.Empty)
        {
            query = query.Where(s => s.ProjectWorkShifts.Any(pws => pws.ProjectUid == filter.ProjectId));
        }

        return query;
    }
}
