namespace Kantoku.Api.Dtos.EmployeeCost.Response;

public class EmployeeCostsResponseDto
{
    public IEnumerable<EmployeeCostResponseDto> Items { get; set; } = [];
    public int PageNum { get; set; }
    public int PageSize { get; set; }
    public int TotalRecords { get; set; }
}

public class EmployeeCostResponseDto
{
    public Guid? EmployeeId { get; set; }
    public string? EmployeeName { get; set; }
    public Guid? RankId { get; set; }
    public string? RankName { get; set; }

    public int? CostAmount { get; set; }
    public int? AverageCostAmount { get; set; }
}
