using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class PaymentTypeConfiguration(string schema) : IEntityTypeConfiguration<PaymentType>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<PaymentType> builder)
    {
        builder.HasKey(e => e.PaymentTypeUid).HasName("payment_type_pkey");

        builder.ToTable("payment_type", Schema, tb => tb.HasComment("Phương thức thanh toán"));

        builder.Property(e => e.PaymentTypeUid)
            .HasColumnName("payment_type_uid");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.PaymentTypeName)
            .HasColumnName("payment_type_name");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");

        builder.HasOne(d => d.Org).WithMany(p => p.PaymentTypes)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("payment_type_org_id_fkey");
    }
}

