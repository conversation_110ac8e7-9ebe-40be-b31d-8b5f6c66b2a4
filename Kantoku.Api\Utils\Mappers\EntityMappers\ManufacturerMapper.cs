using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Manufacturer.Request;
using Kantoku.Api.Dtos.Manufacturer.Response;
using Kantoku.Api.Utils.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class ManufacturerMapper
{
    #region Entity to DTO mappings

    public static ManufacturerResponseDto ToManufacturerResponseDto(this Manufacturer manufacturer)
    {
        if (manufacturer == null)
            return new ManufacturerResponseDto();

        return new ManufacturerResponseDto
        {
            ManufacturerId = manufacturer.ManufacturerUid.ToString(),
            ManufacturerCode = manufacturer.ManufacturerCode,
            ManufacturerName = manufacturer.ManufacturerName,
            ManufacturerSubName = manufacturer.ManufacturerSubName,
            Description = manufacturer.Description,
            CorporateNumber = manufacturer.CorporateNumber,
            Address = manufacturer.Address,
            PhoneNumber = manufacturer.PhoneNumber,
            Email = manufacturer.Email,
            ContactPerson = manufacturer.ContactPerson,
            LogoUrl = manufacturer.LogoUrl
        };
    }

    // Map collection of Manufacturer entities to ManufacturersResponseDto
    public static ManufacturersResponseDto ToManufacturersResponseDto(this IEnumerable<Manufacturer> manufacturers, int pageNum, int pageSize, int totalRecords)
    {
        if (manufacturers == null)
            return new ManufacturersResponseDto();

        return new ManufacturersResponseDto
        {
            Items = manufacturers.Select(m => m.ToManufacturerResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    // Map Manufacturer entity to ManufacturerDetailResponseDto
    public static ManufacturerDetailResponseDto ToManufacturerDetailResponseDto(this Manufacturer manufacturer, 
        IEnumerable<Item>? items, 
        int itemListPageNum = 0, 
        int itemListPageSize = 0, 
        int itemListTotalRecords = 0)
    {
        if (manufacturer == null)
            return new ManufacturerDetailResponseDto();

        var detailDto = new ManufacturerDetailResponseDto
        {
            ManufacturerId = manufacturer.ManufacturerUid.ToString(),
            ManufacturerCode = manufacturer.ManufacturerCode,
            ManufacturerName = manufacturer.ManufacturerName,
            ManufacturerSubName = manufacturer.ManufacturerSubName,
            Description = manufacturer.Description,
            CorporateNumber = manufacturer.CorporateNumber,
            Address = manufacturer.Address,
            PhoneNumber = manufacturer.PhoneNumber,
            Email = manufacturer.Email,
            ContactPerson = manufacturer.ContactPerson,
            LogoUrl = manufacturer.LogoUrl,
            Items = items?.ToEquipmentResponseDto(),
            ItemListPageNum = itemListPageNum,
            ItemListPageSize = itemListPageSize,
            ItemListTotalRecords = itemListTotalRecords
        };

        return detailDto;
    }

    #endregion

    #region DTO to Entity mappings

    // Map CreateManufacturerRequestDto to Manufacturer entity
    public static Manufacturer? ToEntity(this CreateManufacturerRequestDto dto, Guid orgUid)
    {
        if (dto == null)
            return null;

        var entity = new Manufacturer
        {
            ManufacturerUid = GuidHelper.GenerateUUIDv7(),
            OrgUid = orgUid,
            ManufacturerCode = dto.ManufacturerCode,
            ManufacturerName = dto.ManufacturerName,
            ManufacturerSubName = dto.ManufacturerSubName,
            Description = dto.Description,
            CorporateNumber = dto.CorporateNumber,
            Address = dto.Address,
            PhoneNumber = dto.PhoneNumber,
            Email = dto.Email,
            ContactPerson = dto.ContactPerson,
            IsDeleted = false
            // LogoUrl is handled separately during file upload in the service
        };

        return entity;
    }

    // Map UpdateManufacturerRequestDto to Manufacturer entity
    public static void UpdateFromDto(this Manufacturer entity, UpdateManufacturerRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (dto.ManufacturerCode != null)
            entity.ManufacturerCode = dto.ManufacturerCode;

        if (dto.ManufacturerName != null)
            entity.ManufacturerName = dto.ManufacturerName;

        if (dto.ManufacturerSubName != null)
            entity.ManufacturerSubName = dto.ManufacturerSubName;

        if (dto.Description != null)
            entity.Description = dto.Description;

        if (dto.CorporateNumber != null)
            entity.CorporateNumber = dto.CorporateNumber;

        if (dto.Address != null)
            entity.Address = dto.Address;

        if (dto.PhoneNumber != null)
            entity.PhoneNumber = dto.PhoneNumber;

        if (dto.Email != null)
            entity.Email = dto.Email;

        if (dto.ContactPerson != null)
            entity.ContactPerson = dto.ContactPerson;
        
        // LogoUrl is handled separately during file upload in the service
    }

    #endregion
} 