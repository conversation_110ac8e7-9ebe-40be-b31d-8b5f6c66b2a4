using Kantoku.Processor.Models.BatchJobManager;
using Microsoft.EntityFrameworkCore;
using Kantoku.Processor.Data.Contexts;
namespace Kantoku.Processor.Data.Repositories;  

/// <summary>
/// Implementation of the batch job repository
/// </summary>
public class BatchJobRepository : IBatchJobRepository
{

    private readonly IServiceScopeFactory _serviceScopeFactory;
    private readonly ILogger<BatchJobRepository> _logger;

    public BatchJobRepository(
        IServiceScopeFactory serviceScopeFactory, 
        ILogger<BatchJobRepository> logger)
    {
        _serviceScopeFactory = serviceScopeFactory;
        _logger = logger;
    }

    public async Task<IEnumerable<BatchJob>> GetAllJobsAsync()
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<JobDbContext>();
        return await context.BatchJobs.ToListAsync();
    }

    public async Task<IEnumerable<BatchJob>> GetEnabledJobsAsync()
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<JobDbContext>();
        return await context.BatchJobs
            .Where(j => j.IsEnabled)
            .ToListAsync();
    }

    public async Task<BatchJob?> GetJobByIdAsync(Guid id)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<JobDbContext>();
        return await context.BatchJobs.FindAsync(id);
    }

    public async Task<BatchJob?> GetJobByNameAsync(string name)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<JobDbContext>();
        return await context.BatchJobs
            .FirstOrDefaultAsync(j => j.JobName == name);
    }

    public async Task<BatchJob> CreateJobAsync(BatchJob job)
    {
        job.JobId = Guid.NewGuid();

        using var scope = _serviceScopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<JobDbContext>();
        await context.BatchJobs.AddAsync(job);
        await context.SaveChangesAsync();

        _logger.LogInformation("Created new batch job: {JobName} with ID {JobId}", job.JobName, job.JobId);
        return job;
    }

    public async Task<BatchJob> UpdateJobAsync(BatchJob job)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<JobDbContext>();
        var existingJob = await context.BatchJobs.FindAsync(job.JobId);
        
        if (existingJob == null)
        {
            throw new KeyNotFoundException($"Batch job with ID {job.JobId} not found");
        }

        // Update only allowed properties
        existingJob.JobName = job.JobName;
        existingJob.JobDescription = job.JobDescription;
        existingJob.IsEnabled = job.IsEnabled;
        existingJob.Interval = job.Interval;
        existingJob.JobType = job.JobType;
        existingJob.Configuration = job.Configuration;

        context.BatchJobs.Update(existingJob);
        await context.SaveChangesAsync();

        _logger.LogInformation("Updated batch job: {JobName} with ID {JobId}", existingJob.JobName, existingJob.JobId);
        return existingJob;
    }

    public async Task<bool> ExistsAsync(string name)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<JobDbContext>();
        return await context.BatchJobs.AnyAsync(j => j.JobName == name);
    }

    public async Task<bool> SetJobStatusAsync(Guid jobId, bool isEnabled)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<JobDbContext>();
        var job = await context.BatchJobs.FindAsync(jobId);
        
        if (job == null)
        {
            return false;
        }

        job.IsEnabled = isEnabled;

        context.BatchJobs.Update(job);
        await context.SaveChangesAsync();

        _logger.LogInformation("Set batch job {JobName} (ID: {JobId}) status to {Status}", 
            job.JobName, job.JobId, isEnabled ? "Enabled" : "Disabled");
            
        return true;
    }

    public async Task<bool> DeleteJobAsync(Guid jobId)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<JobDbContext>();
        var job = await context.BatchJobs.FindAsync(jobId);
        
        if (job == null)
        {
            return false;
        }

        context.BatchJobs.Remove(job);
        await context.SaveChangesAsync();

        _logger.LogInformation("Deleted batch job: {JobName} with ID {JobId}", job.JobName, job.JobId);
        return true;
    }

    public async Task UpdateJobLastRunTimeAsync(Guid jobId, DateTime lastRunTime)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<JobDbContext>();
        var job = await context.BatchJobs.FindAsync(jobId);
        
        if (job == null)
        {
            throw new KeyNotFoundException($"Batch job with ID {jobId} not found");
        }

        job.LastRunAt = lastRunTime;

        context.BatchJobs.Update(job);
        await context.SaveChangesAsync();

        _logger.LogInformation("Updated last run time for job {JobName} (ID: {JobId}) to {LastRunTime}", 
            job.JobName, job.JobId, lastRunTime);
    }

    public async Task<BatchJobHistory> CreateJobHistoryAsync(BatchJobHistory history)
    {
        history.FiredId = Guid.NewGuid();
        using var scope = _serviceScopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<JobDbContext>();
        await context.BatchJobHistory.AddAsync(history);
        await context.SaveChangesAsync();

        _logger.LogInformation("Created job history entry for job ID {JobId} with history ID {HistoryId}", 
            history.JobId, history.FiredId);
        return history;
    }

    public async Task<BatchJobHistory> UpdateJobHistoryAsync(BatchJobHistory history)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<JobDbContext>();
        var existingHistory = await context.BatchJobHistory.FindAsync(history.FiredId);
        
        if (existingHistory == null)
        {
            throw new KeyNotFoundException($"Job history with ID {history.FiredId} not found");
        }

        existingHistory.CompletedAt = history.CompletedAt;
        existingHistory.Status = history.Status;
        existingHistory.ErrorMessage = history.ErrorMessage;
        existingHistory.ExecutionLog = history.ExecutionLog;

        context.BatchJobHistory.Update(existingHistory);
        await context.SaveChangesAsync();

        _logger.LogInformation("Updated job history entry {HistoryId} with status {Status}", 
            existingHistory.FiredId, existingHistory.Status);
            
        return existingHistory;
    }

    public async Task<IEnumerable<BatchJobHistory>> GetJobHistoryAsync(Guid jobId, int limit = 10)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<JobDbContext>();
        return await context.BatchJobHistory
            .Where(h => h.JobId == jobId)
            .OrderByDescending(h => h.StartedAt)
            .Take(limit)
            .ToListAsync();
    }

    public async Task<BatchJobHistory?> GetJobHistoryByIdAsync(Guid historyId)
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<JobDbContext>();
        return await context.BatchJobHistory.FindAsync(historyId);
    }
}
