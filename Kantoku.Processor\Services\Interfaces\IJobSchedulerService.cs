using Kantoku.Processor.Models.BatchJobManager;

namespace Kantoku.Processor.Services.Interfaces
{
    /// <summary>
    /// Interface for the service that schedules and triggers batch jobs
    /// </summary>
    public interface IJobSchedulerService
    {
        /// <summary>
        /// Starts the scheduler service
        /// </summary>
        Task StartAsync(CancellationToken cancellationToken);
        
        /// <summary>
        /// Stops the scheduler service
        /// </summary>
        Task StopAsync(CancellationToken cancellationToken);
        
        /// <summary>
        /// Gets the next scheduled execution time for a job
        /// </summary>
        DateTime? GetNextExecutionTime(BatchJob job);
        
        /// <summary>
        /// Manually triggers a job to run immediately
        /// </summary>
        Task<bool> TriggerJobAsync(Guid jobId);
        
        /// <summary>
        /// Reschedules all jobs (e.g., after configuration changes)
        /// </summary>
        Task RescheduleAllJobsAsync();
    }
} 