using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class RoleFunctionConfiguration(string schema) : IEntityTypeConfiguration<RoleFunction>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<RoleFunction> builder)
    {
        builder.ToTable("role_function", Schema);

        builder.HasKey(e => new { e.FunctionUid, e.RoleUid }).HasName("role_function_pkey");

        builder.Property(e => e.FunctionUid)
            .HasColumnName("function_uid");
        builder.Property(e => e.RoleUid)
            .HasColumnName("role_uid");
        builder.Property(e => e.CanCreate)
            .HasDefaultValue(false)
            .HasColumnName("can_create");
        builder.Property(e => e.CanDelete)
            .HasDefaultValue(false)
            .HasColumnName("can_delete");
        builder.Property(e => e.CanRead)
            .HasDefaultValue(true)
            .HasColumnName("can_read");
        builder.Property(e => e.CanUpdate)
            .HasDefaultValue(false)
            .HasColumnName("can_update");

        builder.HasOne(d => d.Function)
            .WithMany(p => p.RoleFunctions)
            .HasForeignKey(d => d.FunctionUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("role_function_function_id_fkey");

        builder.HasOne(d => d.Role)
            .WithMany(p => p.RoleFunctions)
            .HasForeignKey(d => d.RoleUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("role_function_role_id_fkey");
    }
} 