using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Dtos.Schedule;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Dtos.Schedule.Request;
using Kantoku.Api.Dtos.Schedule.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Databases.Queryables;

using iText.Kernel.Pdf;
using iText.Layout;
using iText.Layout.Element;
using iText.Layout.Properties;
using System.Text;
using iText.IO.Font;
using iText.Kernel.Font;
using iText.IO.Font.Constants;
using iText.Kernel.Colors;
using System.Globalization;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public partial interface IProjectScheduleService
{
    Task<byte[]> ExportProjectSchedules(DateOnly fromDate, DateOnly toDate);

    Task<ResultDto<ProjectScheduleResponsesDto>> GetProjectScheduleByProjectIdAndDate(Guid projectId, DateOnly dateFrom, DateOnly dateTo);
    Task<ResultDto<ProjectSchedulesResponseDto>> GetProjectScheduleByFilter(ProjectScheduleFilter filter);
    Task<ResultDto<ProjectScheduleResponseDto>> GetProjectScheduleById(Guid projectScheduleId);

    Task<ResultDto<ProjectScheduleResponseDto>> CreateProjectSchedule(CreateProjectScheduleRequestDto dto);
    Task<ResultDto<ProjectScheduleResponseDto>> UpdateProjectSchedule(Guid projectScheduleId, UpdateProjectScheduleRequestDto dto);
    Task<ResultDto<ProjectScheduleResponseDto>> DeleteProjectSchedule(Guid projectScheduleId);

    Task<ResultDto<ProjectScheduleResponsesDto>> DuplicateProjectSchedule(Guid projectScheduleId, DuplicateProjectScheduleRequestDto dto);
    Task<ResultDto<ProjectScheduleResponsesDto>> DuplicateProjectSchedules(DuplicateProjectSchedulesRequestDto dto);
}

[Service(ServiceLifetime.Scoped)]
public partial class ProjectScheduleService : BaseService<ProjectScheduleService>, IProjectScheduleService
{
    private readonly IProjectScheduleRepository projectScheduleRepository;
    private readonly IEmployeeShiftRepository employeeShiftRepository;
    private readonly IProjectRepository projectRepository;
    private readonly IOutSourceShiftRepository outSourceShiftRepository;
    private readonly IMonthlyReportRepository monthlyReportRepository;

    public ProjectScheduleService(
        IProjectScheduleRepository projectScheduleRepository,
        IEmployeeShiftRepository employeeShiftRepository,
        IProjectRepository projectRepository,
        IOutSourceShiftRepository outSourceShiftRepository,
        IMonthlyReportRepository monthlyReportRepository,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor
        ) : base(logger, httpContextAccessor)
    {
        this.projectScheduleRepository = projectScheduleRepository;
        this.employeeShiftRepository = employeeShiftRepository;
        this.projectRepository = projectRepository;
        this.outSourceShiftRepository = outSourceShiftRepository;
        this.monthlyReportRepository = monthlyReportRepository;
    }

    public async Task<byte[]> ExportProjectSchedules(DateOnly fromDate, DateOnly toDate)
    {
        var schedules = await projectScheduleRepository.GetByFilters(new ProjectScheduleFilter
        {
            FromDate = fromDate,
            ToDate = toDate,
        }, new ScheduleQueryableOptions
        {
            IncludedProject = true,
            IncludedEmployeeShifts = true,
            IncludedOutSourceShifts = true,
        });

        var printableSchedule = schedules.ToPrintableScheduleDto();

        return await ExportSchedulesToPdf(printableSchedule);
    }

    public async Task<ResultDto<ProjectScheduleResponsesDto>> GetProjectScheduleByProjectIdAndDate(Guid projectId, DateOnly dateFrom, DateOnly dateTo)
    {
        var schedules = await projectScheduleRepository.GetByProjectIdAndDateRange(projectId, dateFrom, dateTo, new ScheduleQueryableOptions
        {
            IncludedEmployeeShifts = true,
            IncludedOutSourceShifts = true,
        });

        var res = new ProjectScheduleResponsesDto
        {
            Items = schedules.Select(s => s.ToProjectScheduleResponseDto(
                s.EmployeeShifts.ToList(),
                s.OutSourceShifts.ToList()
            )).OrderBy(s => s.WorkingDate)
        };

        return new SuccessResultDto<ProjectScheduleResponsesDto>(res);
    }

    public async Task<ResultDto<ProjectSchedulesResponseDto>> GetProjectScheduleByFilter(ProjectScheduleFilter filter)
    {
        var (projects, _) = await projectRepository.GetByFilter(new ProjectFilter
        {
            Keyword = filter.SearchKeyword,
            StatusCode = StatusConstants.STARTED
        }, new ProjectQueryableOptions());

        var schedules = await projectScheduleRepository.GetByFilters(filter, new ScheduleQueryableOptions
        {
            IncludedProject = true,
            IncludedEmployeeShifts = true,
            IncludedOutSourceShifts = true,
        });
        var res = schedules.ToProjectSchedulesResponseDto(
            projects,
            filter.FromDate,
            filter.ToDate,
            filter.PageNum,
            filter.PageSize
        );

        return new SuccessResultDto<ProjectSchedulesResponseDto>(res);
    }
    public async Task<ResultDto<ProjectScheduleResponseDto>> GetProjectScheduleById(Guid projectScheduleId)
    {
        var projectSchedule = await projectScheduleRepository.GetById(projectScheduleId, new ScheduleQueryableOptions());
        if (projectSchedule is null)
        {
            logger.Error("Project schedule not found for id {ProjectScheduleId}", projectScheduleId);
            return new ErrorResultDto<ProjectScheduleResponseDto>(ResponseCodeConstant.SCHEDULE_NOT_EXIST);
        }
        var employeeShifts = await employeeShiftRepository.GetByProjectScheduleId(projectScheduleId, new EmployeeShiftQueryableOptions
        {
            IncludedEmployee = true,
        });
        var outSourceShifts = await outSourceShiftRepository.GetByProjectScheduleId(projectScheduleId, new OutSourceShiftQueryableOptions
        {
            IncludedOutSource = true,
        });

        var res = projectSchedule.ToProjectScheduleResponseDto(
            employeeShifts,
            outSourceShifts
        );
        return new SuccessResultDto<ProjectScheduleResponseDto>(res);
    }

    public async Task<ResultDto<ProjectScheduleResponseDto>> CreateProjectSchedule(CreateProjectScheduleRequestDto dto)
    {
        var existProjectSchedule = await projectScheduleRepository.GetByProjectIdAndDate(
            dto.ProjectId, dto.WorkingDate, new ScheduleQueryableOptions());
        if (existProjectSchedule is not null)
        {
            logger.Error("Project schedule already exists for id {ProjectScheduleId}", dto.ProjectId);
            return new ErrorResultDto<ProjectScheduleResponseDto>(ResponseCodeConstant.ALREADY_HAS_SCHEDULED);
        }

        var newProjectSchedule = dto.ToEntity(dto.ProjectId);
        if (newProjectSchedule is null)
        {
            logger.Error("Failed to create project schedule for id {ProjectScheduleId}", dto.ProjectId);
            return new ErrorResultDto<ProjectScheduleResponseDto>(ResponseCodeConstant.SCHEDULE_CREATE_FAILED);
        }
        var createdProjectSchedule = await projectScheduleRepository.Create(newProjectSchedule);
        if (createdProjectSchedule is null)
        {
            logger.Error("Failed to create project schedule for id {ProjectScheduleId}", dto.ProjectId);
            return new ErrorResultDto<ProjectScheduleResponseDto>(ResponseCodeConstant.SCHEDULE_CREATE_FAILED);
        }

        var res = createdProjectSchedule.ToProjectScheduleResponseDto(
            createdProjectSchedule.EmployeeShifts.ToList(),
            createdProjectSchedule.OutSourceShifts.ToList()
        );
        return new SuccessResultDto<ProjectScheduleResponseDto>(res);
    }

    public async Task<ResultDto<ProjectScheduleResponseDto>> UpdateProjectSchedule(Guid projectScheduleId, UpdateProjectScheduleRequestDto dto)
    {
        var options = new ScheduleQueryableOptions().TrackingOptions();
        var projectSchedule = await projectScheduleRepository.GetById(projectScheduleId, options);
        if (projectSchedule is null)
        {
            logger.Error("Project schedule not found for id {ProjectScheduleId}", projectScheduleId);
            return new ErrorResultDto<ProjectScheduleResponseDto>(ResponseCodeConstant.SCHEDULE_NOT_EXIST);
        }

        projectSchedule.UpdateFromDto(dto);
        var updatedProjectSchedule = await projectScheduleRepository.Update(projectSchedule);
        if (updatedProjectSchedule is null)
        {
            logger.Error("Failed to update project schedule for id {ProjectScheduleId}", projectScheduleId);
            return new ErrorResultDto<ProjectScheduleResponseDto>(ResponseCodeConstant.SCHEDULE_UPDATE_FAILED);
        }

        var res = updatedProjectSchedule.ToProjectScheduleResponseDto(
            updatedProjectSchedule.EmployeeShifts.ToList(),
            updatedProjectSchedule.OutSourceShifts.ToList()
        );
        return new SuccessResultDto<ProjectScheduleResponseDto>(res);
    }

    public async Task<ResultDto<ProjectScheduleResponseDto>> DeleteProjectSchedule(Guid projectScheduleId)
    {
        var projectSchedule = await projectScheduleRepository.GetById(projectScheduleId, new ScheduleQueryableOptions());
        if (projectSchedule is null)
        {
            logger.Error("Project schedule not found for id {ProjectScheduleId}", projectScheduleId);
            return new ErrorResultDto<ProjectScheduleResponseDto>(ResponseCodeConstant.SCHEDULE_NOT_EXIST);
        }

        projectSchedule.Delete();
        var deletedProjectSchedule = await projectScheduleRepository.Update(projectSchedule);
        if (deletedProjectSchedule is null)
        {
            logger.Error("Failed to delete project schedule for id {ProjectScheduleId}", projectScheduleId);
            return new ErrorResultDto<ProjectScheduleResponseDto>(ResponseCodeConstant.SCHEDULE_REMOVE_FAILED);
        }

        return new SuccessResultDto<ProjectScheduleResponseDto>(deletedProjectSchedule.ToProjectScheduleResponseDto(
            deletedProjectSchedule.EmployeeShifts.ToList(),
            deletedProjectSchedule.OutSourceShifts.ToList()
        ));
    }

    public async Task<ResultDto<ProjectScheduleResponsesDto>> DuplicateProjectSchedule(Guid projectScheduleId, DuplicateProjectScheduleRequestDto dto)
    {
        var options = new ScheduleQueryableOptions
        {
            IncludedEmployeeShifts = true,
            IncludedOutSourceShifts = true,
        }.TrackingOptions();
        var sourceProjectSchedule = await projectScheduleRepository.GetById(projectScheduleId, options);
        if (sourceProjectSchedule is null)
        {
            logger.Error("Project schedule not found for id {ProjectScheduleId}", projectScheduleId);
            return new ErrorResultDto<ProjectScheduleResponsesDto>(ResponseCodeConstant.SCHEDULE_NOT_EXIST);
        }

        var items = new List<ProjectScheduleResponseDto>();
        for (var date = dto.TargetDateFrom; date <= dto.TargetDateTo; date = date.AddDays(1))
        {
            var targetProjectSchedule = await projectScheduleRepository.GetByProjectIdAndDate(
                sourceProjectSchedule.ProjectUid, date, options);

            if (targetProjectSchedule is null)
            {
                var newProjectSchedule = sourceProjectSchedule.Clone(sourceProjectSchedule.ProjectUid, date);
                if (newProjectSchedule is null)
                {
                    logger.Error("Failed to duplicate project schedule for id {ProjectScheduleId}", projectScheduleId);
                    continue;
                }
                targetProjectSchedule = await projectScheduleRepository.Create(newProjectSchedule, options);
                if (targetProjectSchedule is null)
                {
                    logger.Error("Failed to duplicate project schedule for id {ProjectScheduleId}", projectScheduleId);
                    continue;
                }
            }

            var employeeShifts = sourceProjectSchedule
                .EmployeeShifts.Select(e => e.Clone(targetProjectSchedule?.ProjectScheduleUid, date))
                .Where(e => e is not null)
                .ToList();
            var outSourceShifts = sourceProjectSchedule
                .OutSourceShifts.Select(e => e.Clone(targetProjectSchedule?.ProjectScheduleUid, date))
                .Where(e => e is not null)
                .ToList();

            await employeeShiftRepository.CreateMultiple(employeeShifts!, new EmployeeShiftQueryableOptions
            {
                IncludedEmployee = true,
            });
            await outSourceShiftRepository.CreateMultiple(outSourceShifts!, new OutSourceShiftQueryableOptions
            {
                IncludedOutSource = true,
            });
            foreach (var employeeShift in employeeShifts)
            {
                targetProjectSchedule.EmployeeShifts.Add(employeeShift!);
            }
            foreach (var outSourceShift in outSourceShifts)
            {
                targetProjectSchedule.OutSourceShifts.Add(outSourceShift!);
            }
            items.Add(targetProjectSchedule.ToProjectScheduleResponseDto(
                targetProjectSchedule.EmployeeShifts.ToList(),
                targetProjectSchedule.OutSourceShifts.ToList()
            ));
        }

        var res = new ProjectScheduleResponsesDto
        {
            Items = items
        };

        return new SuccessResultDto<ProjectScheduleResponsesDto>(res);
    }

    public async Task<ResultDto<ProjectScheduleResponsesDto>> DuplicateProjectSchedules(DuplicateProjectSchedulesRequestDto dto)
    {
        var options = new ScheduleQueryableOptions
        {
            IncludedEmployeeShifts = true,
            IncludedOutSourceShifts = true,
        };
        var schedules = new List<ProjectScheduleResponseDto>();
        foreach (var projectScheduleId in dto.ProjectScheduleIds)
        {
            var sourceProjectSchedule = await projectScheduleRepository.GetById(projectScheduleId, options);
            if (sourceProjectSchedule is null)
            {
                logger.Error("Project schedule not found for id {ProjectScheduleId}", projectScheduleId);
                return new ErrorResultDto<ProjectScheduleResponsesDto>(ResponseCodeConstant.SCHEDULE_NOT_EXIST);
            }

            var items = new List<ProjectScheduleResponseDto>();
            for (var date = dto.TargetDateFrom; date <= dto.TargetDateTo; date = date.AddDays(1))
            {
                var targetProjectSchedule = await projectScheduleRepository.GetByProjectIdAndDate(
                    sourceProjectSchedule.ProjectUid, date, options);

                if (targetProjectSchedule is null)
                {
                    var newProjectSchedule = sourceProjectSchedule.Clone(sourceProjectSchedule.ProjectUid, date);
                    if (newProjectSchedule is null)
                    {
                        logger.Error("Failed to duplicate project schedule for id {ProjectScheduleId}", projectScheduleId);
                        continue;
                    }
                    targetProjectSchedule = await projectScheduleRepository.Create(newProjectSchedule, options);
                    if (targetProjectSchedule is null)
                    {
                        logger.Error("Failed to duplicate project schedule for id {ProjectScheduleId}", projectScheduleId);
                        continue;
                    }
                }

                var employeeShifts = sourceProjectSchedule
                    .EmployeeShifts.Select(e => e.Clone(targetProjectSchedule?.ProjectScheduleUid, date))
                    .Where(e => e is not null)
                    .ToList();
                var outSourceShifts = sourceProjectSchedule
                    .OutSourceShifts.Select(e => e.Clone(targetProjectSchedule?.ProjectScheduleUid, date))
                    .Where(e => e is not null)
                    .ToList();

                var createdEmployeeShifts = await employeeShiftRepository.CreateMultiple(employeeShifts!, new EmployeeShiftQueryableOptions
                {
                    IncludedEmployee = true,
                });
                var createdOutSourceShifts = await outSourceShiftRepository.CreateMultiple(outSourceShifts!, new OutSourceShiftQueryableOptions
                {
                    IncludedOutSource = true,
                });
                foreach (var employeeShift in createdEmployeeShifts)
                {
                    targetProjectSchedule.EmployeeShifts.Add(employeeShift!);
                }
                foreach (var outSourceShift in createdOutSourceShifts)
                {
                    targetProjectSchedule.OutSourceShifts.Add(outSourceShift!);
                }
                items.Add(targetProjectSchedule.ToProjectScheduleResponseDto(
                    targetProjectSchedule.EmployeeShifts.ToList(),
                    targetProjectSchedule.OutSourceShifts.ToList()
                ));
            }

            schedules.AddRange(items);
        }

        var res = new ProjectScheduleResponsesDto
        {
            Items = schedules
        };

        return new SuccessResultDto<ProjectScheduleResponsesDto>(res);
    }

    public async Task<byte[]> ExportSchedulesToPdf(IEnumerable<PrintableScheduleDto> schedules)
    {
        using var stream = new MemoryStream();
        var writer = new PdfWriter(stream);
        var pdf = new PdfDocument(writer);
        var document = new Document(pdf);

        // Embed font from resources
        PdfFont font;
        try
        {
            var fontPath = Path.Combine(AppContext.BaseDirectory, "Resources", "Fonts", "ArialUnicodeMS.TTF");
            var fontBytes = await System.IO.File.ReadAllBytesAsync(fontPath);
            var fontProgram = FontProgramFactory.CreateFont(fontBytes, true);
            font = PdfFontFactory.CreateFont(fontProgram, "Identity-H", PdfFontFactory.EmbeddingStrategy.FORCE_EMBEDDED);
        }
        catch (Exception)
        {
            // Fallback to default font if custom font loading fails
            logger.Error("Failed to load custom font, using fallback font");
            font = PdfFontFactory.CreateFont(StandardFonts.HELVETICA);
        }

        var orderedSchedules = schedules.OrderBy(s => s.WorkingDate).ToList();
        for (int i = 0; i < orderedSchedules.Count; i++)
        {
            var schedule = orderedSchedules[i];

            var cultureInfo = httpContextAccessor.HttpContext?.GetCurrentCulture() ?? new CultureInfo("ja-JP");

            var date = FormatDate(schedule.WorkingDate, cultureInfo.Name);

            var culture = httpContextAccessor.HttpContext?.GetCurrentCulture();

            // Add date header with Japanese font
            document.Add(new Paragraph($"スケジュール: {date}")
                .SetFont(font)
                .SetFontSize(16)
                .SetTextAlignment(TextAlignment.CENTER));

            // Create a single table for all projects
            var table = new Table(2).UseAllAvailableWidth();

            // Define grey color for background
            var greyColor = new DeviceRgb(240, 240, 240);

            // Add table headers with Japanese font and grey background
            table.AddHeaderCell(new Cell()
                .SetBackgroundColor(greyColor)
                .Add(new Paragraph("プロジェクト")
                    .SetFont(font)
                    .SetTextAlignment(TextAlignment.CENTER)));
            table.AddHeaderCell(new Cell()
                .SetBackgroundColor(greyColor)
                .Add(new Paragraph("従業員")
                    .SetFont(font)
                    .SetTextAlignment(TextAlignment.CENTER)));

            foreach (var projectSchedule in schedule.Schedules)
            {
                // Add cells with Japanese font - first column with grey background
                table.AddCell(new Cell()
                    .SetBackgroundColor(greyColor)
                    .Add(new Paragraph(projectSchedule.ProjectName ?? "データなし")
                        .SetFont(font)
                        .SetTextAlignment(TextAlignment.CENTER)));

                var employeeInfo = new StringBuilder();
                foreach (var emp in projectSchedule.Employees)
                {
                    var isOutSource = emp.IsOutSource == true ? "（外注）" : "";
                    employeeInfo.AppendLine($"{emp.EmployeeName} {isOutSource} ");
                }
                table.AddCell(new Cell().Add(new Paragraph(employeeInfo.ToString()).SetFont(font)));
            }

            document.Add(table);
            document.Add(new Paragraph("\n"));

            // Add page break only if not the last schedule
            if (i < orderedSchedules.Count - 1)
            {
                document.Add(new AreaBreak(AreaBreakType.NEXT_PAGE));
            }
        }

        document.Close();
        return stream.ToArray();
    }

    private static string GetDateFormat(string culture)
    {
        return culture.ToLower() switch
        {
            "ja-jp" => "MM月dd日(ddd)",
            "ja" => "MM月dd日(ddd)",
            _ => "MM/dd (ddd)"  // default English format
        };
    }

    private static string FormatDate(DateOnly date, string culture)
    {
        var cultureInfo = new CultureInfo(culture);
        var format = GetDateFormat(culture);

        // Convert day of week to appropriate language
        var dateString = date.ToString(format, cultureInfo);

        return dateString;
    }
}