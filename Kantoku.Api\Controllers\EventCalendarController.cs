using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Services;
using Kantoku.Api.Dtos.EventCalendar.Request;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Dtos.EventCalendar.Response;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class EventCalendarController : BaseController
{
    private readonly IEventCalendarService eventCalendarService;
    private readonly IAuditLogService auditLogService;

    public EventCalendarController(
        ITResponseFactory responseFactory,
        IEventCalendarService eventCalendarService,
        IAuditLogService auditLogService
    ) : base(responseFactory)
    {
        this.eventCalendarService = eventCalendarService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get event calendar by ID
    /// </summary>
    /// <param name="id">Event calendar ID</param>
    /// <returns>Event calendar details</returns>
    [HttpGet("{id}")]
    public async Task<GeneralResponse<EventCalendarRuleDto>> GetEventCalendar([FromRoute] Guid id)
    {
        try
        {
            var result = await eventCalendarService.GetEventCalendarRuleByIdAsync(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException ex)
        {
            return Fail<EventCalendarRuleDto>(ex.ErrorCode);
        }
    }

    /// <summary>
    /// Get events within a date range
    /// </summary>
    /// <param name="from">Start date</param>
    /// <param name="to">End date</param>
    /// <returns>List of events in the date range</returns>
    [HttpGet("date")]
    public async Task<GeneralResponse<EventCalendarsResponseDto>> GetEventsByDateRange(
        [FromQuery] string from,
        [FromQuery] string to)
    {
        try
        {
            if (!DateOnly.TryParse(from, out DateOnly fromDate) || !DateOnly.TryParse(to, out DateOnly toDate))
                return BadRequest<EventCalendarsResponseDto>();

            var result = await eventCalendarService.GetEventCalendarsAsync(fromDate, toDate);
            return SuccessFromResult(result);
        }
        catch (BusinessException ex)
        {
            return Fail<EventCalendarsResponseDto>(ex.ErrorCode);
        }
    }

    /// <summary>
    /// Get event calendar rules with filtering
    /// </summary>
    /// <param name="filter">Filter parameters for event calendar rules</param>
    /// <returns>Filtered list of event calendar rules</returns>
    [HttpGet]
    public async Task<GeneralResponse<EventCalendarRulesResponseDto>> GetEventCalendarRules(
        [FromQuery] EventCalendarFilter filter)
    {
        try
        {
            var result = await eventCalendarService.GetEventCalendarRulesAsync(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException ex)
        {
            return Fail<EventCalendarRulesResponseDto>(ex.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new event calendar
    /// </summary>
    /// <param name="request">Event calendar creation details</param>
    /// <returns>Created event calendar</returns>
    [HttpPost]
    public async Task<GeneralResponse<EventCalendarRuleDto>> CreateEventCalendar([FromBody] CreateEventCalendarRequestDto request)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<EventCalendarRuleDto>();

            var result = await eventCalendarService.CreateEventCalendarAsync(request);
            return SuccessFromResult(result);
        }
        catch (BusinessException ex)
        {
            return Fail<EventCalendarRuleDto>(ex.ErrorCode);
        }
    }

    /// <summary>
    /// Update an existing event calendar
    /// </summary>
    /// <param name="id">Event calendar ID to update</param>
    /// <param name="request">Updated event calendar details</param>
    /// <returns>Updated event calendar</returns>
    [HttpPut("{id}")]
    public async Task<GeneralResponse<EventCalendarRuleDto>> UpdateEventCalendar([FromRoute] Guid id, [FromBody] UpdateEventCalendarRequestDto request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest<EventCalendarRuleDto>();
            }
            var result = await eventCalendarService.UpdateEventCalendarAsync(id, request);
            return SuccessFromResult(result);
        }
        catch (BusinessException ex)
        {
            return Fail<EventCalendarRuleDto>(ex.ErrorCode);
        }
    }

    /// <summary>
    /// Delete an event calendar
    /// </summary>
    /// <param name="id">Event calendar ID to delete</param>
    /// <returns>Success response</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> DeleteEventCalendar([FromRoute] Guid id)
    {
        try
        {
            var result = await eventCalendarService.DeleteEventCalendarAsync(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException ex)
        {
            return Fail<bool>(ex.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for an event calendar
    /// </summary>
    /// <param name="id">Event calendar ID</param>
    /// <param name="filter">Filter parameters for audit logs</param>
    /// <returns>Filtered audit log entries</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetEventCalendarLogs([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<EventCalendar>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
