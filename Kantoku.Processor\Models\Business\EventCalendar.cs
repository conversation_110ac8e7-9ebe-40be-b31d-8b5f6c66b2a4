namespace Kantoku.Processor.Models.Business;

public class EventCalendar
{
    public Guid EventUid { get; set; }
    // event start date for non-recurring event
    public DateOnly? EventStartDate { get; set; }
    // event end date for non-recurring event
    public DateOnly? EventEndDate { get; set; }

    // true: recurring, false: not recurring
    public bool IsRecurring { get; set; }

    // recurring from date
    public DateOnly? RecurringFrom { get; set; }
    // recurring to date
    public DateOnly? RecurringTo { get; set; }

    // only if RecurringType is weekly
    // for example: [1, 2, 3, 4, 5, 6, 7] as day of week 
    // 1: Monday, 2: Tuesday, 3: Wednesday, 4: Thursday, 5: Friday, 6: Saturday, 7: Sunday
    public List<int>? RecurringDay { get; set; }

    // only if RecurringType is weekly
    // for example: [1, 2, 3, 4] as week of month
    // 1: first week, 2: second week, 3: third week, 4: fourth week
    public List<int>? RecurringWeek { get; set; }

    // only if RecurringType is monthly
    // for example: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12] as month of year
    // 1: January, 2: February, 3: March, 4: April, 5: May, 6: June, 7: July, 8: August, 9: September, 10: October, 11: November, 12: December
    public List<int>? RecurringMonth { get; set; }

    public bool IsDayOff { get; set; } = true;
    public bool IsDeleted { get; set; } = false;
    public Guid OrgUid { get; set; }
}
