using Kantoku.Api.Dtos.Leave.Request;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Dtos.Leave.Response;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class EmployeeLeaveController : BaseController
{
    private readonly IEmployeeLeaveService employeeLeaveService;
    private readonly IAuditLogService auditLogService;

    public EmployeeLeaveController(
        ITResponseFactory responseFactory,
        IEmployeeLeaveService employeeLeaveService,
        IAuditLogService auditLogService
    ) : base(responseFactory)
    {
        this.employeeLeaveService = employeeLeaveService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get employee leave information for an organization with filtering
    /// </summary>
    /// <param name="filterDto">Filter parameters for employee leave</param>
    /// <returns>Filtered list of employee leave records</returns>
    [HttpGet]
    public async Task<GeneralResponse<LeavesResponseDto>> GetEmployeeLeaveInOrg([FromQuery] EmployeeLeaveFilter filterDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<LeavesResponseDto>();

            var res = await employeeLeaveService.GetEmployeeLeaveByOrg(filterDto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<LeavesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get leave usage statistics for a specific employee within a date range
    /// </summary>
    /// <param name="employeeId">Employee ID</param>
    /// <param name="dateFrom">Start date</param>
    /// <param name="dateTo">End date</param>
    /// <returns>Leave usage details</returns>
    [HttpGet("usage/{employeeId}")]
    public async Task<GeneralResponse<UsedLeavesResponseDto>> GetLeaveUsage([FromRoute] Guid employeeId, [FromQuery] string dateFrom, [FromQuery] string dateTo)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<UsedLeavesResponseDto>();

            if (DateTime.TryParse(dateFrom, out var dateFromParsed) && DateTime.TryParse(dateTo, out var dateToParsed))
            {
                var fromDate = DateOnly.FromDateTime(dateFromParsed);
                var toDate = DateOnly.FromDateTime(dateToParsed);
                var res = await employeeLeaveService.GetLeaveUsage(employeeId, fromDate, toDate);
                return SuccessFromResult(res);
            }
            return BadRequest<UsedLeavesResponseDto>();
        }
        catch (BusinessException e)
        {
            return Fail<UsedLeavesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get employee leave records for a specific employee
    /// </summary>
    /// <param name="employeeId">Employee ID</param>
    /// <param name="dateFrom"> start date filter</param>
    /// <param name="dateTo"> end date filter</param>
    /// <returns>Employee leave records</returns>
    [HttpGet("{employeeId}")]
    public async Task<GeneralResponse<EmployeeLeavesResponseDto>> GetEmployeeLeave(
        [FromRoute] Guid employeeId,
        [FromQuery] DateOnly dateFrom,
        [FromQuery] DateOnly dateTo
    )
    {
        try
        {
            var res = await employeeLeaveService.GetEmployeeLeaveByUser(employeeId, dateFrom, dateTo);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<EmployeeLeavesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new employee leave record
    /// </summary>
    /// <param name="employeeId">Employee ID</param>
    /// <param name="dto">Employee leave details</param>
    /// <returns>Created employee leave record</returns>
    [HttpPost("{employeeId}")]
    public async Task<GeneralResponse<LeaveResponseDto>> CreateEmployeeLeave([FromRoute] Guid employeeId, [FromBody] UpdateEmployeeLeaveRequestDto dto)
    {
        try
        {
            if (dto.BaseLeave is null || dto.BaseLeaveExpire is null)
                return BadRequest<LeaveResponseDto>();

            var expireDate = DateOnly.FromDateTime(DateTime.Parse(dto.BaseLeaveExpire));

            var res = await employeeLeaveService.CreateEmployeeLeave(employeeId, dto.BaseLeave.Value, expireDate);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<LeaveResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update an existing employee leave record
    /// </summary>
    /// <param name="employeeLeaveId">Employee leave ID to update</param>
    /// <param name="dto">Updated employee leave details</param>
    /// <returns>Updated employee leave record</returns>
    [HttpPut("{employeeLeaveId}")]
    public async Task<GeneralResponse<LeaveResponseDto>> UpdateEmployeeLeave([FromRoute] Guid employeeLeaveId, [FromBody] UpdateEmployeeLeaveRequestDto dto)
    {
        try
        {
            var res = await employeeLeaveService.UpdateEmployeeLeave(employeeLeaveId, dto);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<LeaveResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete an employee leave record
    /// </summary>
    /// <param name="employeeLeaveId">Employee leave ID to delete</param>
    /// <returns>Success response</returns>
    [HttpDelete("{employeeLeaveId}")]
    public async Task<GeneralResponse<bool>> DeleteEmployeeLeave([FromRoute] Guid employeeLeaveId)
    {
        try
        {
            await employeeLeaveService.DeleteEmployeeLeave(employeeLeaveId);
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for an employee leave record
    /// </summary>
    /// <param name="id">Employee leave ID</param>
    /// <param name="filter">Filter parameters for audit logs</param>
    /// <returns>Filtered audit log entries</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetEmployeeLeaveLogs([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<EmployeeLeave>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}