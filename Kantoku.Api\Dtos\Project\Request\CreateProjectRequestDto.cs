using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.Project.Request;

public class CreateProjectRequestDto
{
    /// <summary>
    /// (*) The unique code identifier for the project
    /// </summary>
    [Required]
    public string ProjectCode { get; set; } = null!;

    /// <summary>
    /// (*) The name of the project
    /// </summary>
    [Required]
    public string ProjectName { get; set; } = null!;

    /// <summary>
    /// The type identifier of the project
    /// </summary>
    public string? ProjectTypeId { get; set; }

    /// <summary>
    /// The customer of the project
    /// </summary>
    public string? CustomerId { get; set; }

    /// <summary>
    /// The contractor of the project
    /// </summary>
    public string? ContractorId { get; set; }

    /// <summary>
    /// The physical address of the project location
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Indicates if this project is an office
    /// </summary>
    public bool IsOffice { get; set; }

    /// <summary>
    /// (*) Collection of work shift IDs associated with the project
    /// </summary>
    [Required]
    public IEnumerable<string> WorkShiftIds { get; set; } = [];

    /// <summary>
    /// (*) Collection of work shift IDs associated with the project 
    /// with default presett
    /// </summary>
    [Required]
    public IEnumerable<ProjectWorkShiftRequestDto> WorkShifts { get; set; } = [];

    /// <summary>
    /// Collection of ranking cost IDs associated with the project
    /// </summary>
    public IEnumerable<ProjectRankingCostRequestDto> ProjectRankingCosts { get; set; } = [];

    /// <summary>
    /// (*) Collection of employee IDs for primary project managers
    /// </summary>
    [Required]
    [JsonPropertyName("primaryManagerEmployeeIds")]
    public IEnumerable<string> PrimaryManagerEmployeeIds { get; set; } = [];

    /// <summary>
    /// Collection of employee IDs for sub project managers
    /// </summary>
    [JsonPropertyName("subManagerEmployeeIds")]
    public IEnumerable<string>? SubManagerEmployeeIds { get; set; }

    /// <summary>
    /// The expected start date of the project
    /// </summary>
    public string? ExpectedStartDate { get; set; }

    /// <summary>
    /// The expected end date of the project
    /// </summary>
    public string? ExpectedEndDate { get; set; }

    /// <summary>
    /// The actual start date of the project
    /// </summary>
    public string? ActualStartDate { get; set; }

    /// <summary>
    /// The actual end date of the project
    /// </summary>
    public string? ActualEndDate { get; set; }

    /// <summary>
    /// Additional description about the project
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// (*) The status code of the project
    /// </summary>
    [Required]
    public string StatusCode { get; set; } = null!;

    /// <summary>
    /// The initial budget allocated for the project
    /// </summary>
    public float? InitialBudget { get; set; }

    /// <summary>
    /// The actual budget spent on the project
    /// </summary>
    public float? ActualBudget { get; set; }
}