﻿using Kantoku.Api.Databases.Models;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.CategorizedCost.Response;
using Kantoku.Api.Dtos.ConstructionCost.Request;
using Kantoku.Api.Dtos.ConstructionCost.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Services;

public interface IConstructionCostService
{
    Task<ResultDto<ConstructionCostSummaryResponseDto>> GetConstructionCostByConstructionId(Guid constructionId, string dateFrom, string dateTo);
    Task<ResultDto<ConstructionCostSummariesResponseDto>> GetConstructionCostsByProjectId(Guid projectId, string dateFrom, string dateTo);
    Task<ResultDto<bool>> CreateConstructionCost(ConstructionCostCreateRequestDto request);
    Task<ResultDto<bool>> UpdateConstructionCost(Guid constructionCostId, ConstructionCostUpdateRequestDto request);
}

[Service(ServiceLifetime.Scoped)]
public class ConstructionCostService : BaseService<ConstructionCostService>, IConstructionCostService
{
    private readonly IConstructionRepository constructionRepository;
    private readonly IConstructionCostRepository constructionCostRepository;
    private readonly IProjectDailyReportRepository projectDailyReportRepository;
    private readonly ICategoryRepository categoryRepository;
    private readonly IOutSourceRepository outSourceRepository;
    private readonly IInputCostItemRepository inputCostItemRepository;
    private readonly IRequestRepository requestRepository;
    private readonly IOrgRepository orgRepository;
    public ConstructionCostService(
        IConstructionRepository constructionRepository,
        IConstructionCostRepository constructionCostRepository,
        IProjectDailyReportRepository projectDailyReportRepository,
        ICategoryRepository categoryRepository,
        IOutSourceRepository outSourceRepository,
        IInputCostItemRepository inputCostItemRepository,
        IRequestRepository requestRepository,
        IOrgRepository orgRepository,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor)
        : base(logger, httpContextAccessor)
    {
        this.constructionRepository = constructionRepository;
        this.constructionCostRepository = constructionCostRepository;
        this.projectDailyReportRepository = projectDailyReportRepository;
        this.categoryRepository = categoryRepository;
        this.outSourceRepository = outSourceRepository;
        this.inputCostItemRepository = inputCostItemRepository;
        this.requestRepository = requestRepository;
        this.orgRepository = orgRepository;
    }

    public async Task<ResultDto<ConstructionCostSummariesResponseDto>> GetConstructionCostsByProjectId(Guid projectId, string dateFrom, string dateTo)
    {
        var options = new ConstructionQueryableOptions
        {
            IncludedConstructionCosts = true,
            IncludedProject = true,
        };
        var constructions = await constructionRepository.GetByProjectId(projectId, options);
        if (constructions is null || !constructions.Any())
        {
            logger.Error("no construction found for project {id}", projectId);
            return new ErrorResultDto<ConstructionCostSummariesResponseDto>(ResponseCodeConstant.CONSTRUCTION_NOT_EXIST);
        }

        var org = await orgRepository.GetById(GetCurrentOrgUid());
        if (org is null)
        {
            logger.Error("org not found");
            return new ErrorResultDto<ConstructionCostSummariesResponseDto>(ResponseCodeConstant.ORG_NOT_EXIST);
        }
        var items = await ConstructionCostSummariesResponseDtoParser(constructions, dateFrom, dateTo, org.EmployeeRankingDefinitionType);

        var result = new ConstructionCostSummariesResponseDto
        {
            Items = items
        };
        return new SuccessResultDto<ConstructionCostSummariesResponseDto>(result);
    }

    public async Task<ResultDto<ConstructionCostSummaryResponseDto>> GetConstructionCostByConstructionId(Guid constructionId, string dateFrom, string dateTo)
    {
        var options = new ConstructionQueryableOptions
        {
            IncludedConstructionCosts = true,
            IncludedProject = true,
        };
        var construction = await constructionRepository.GetById(constructionId, options);
        if (construction is null)
        {
            logger.Error("construction {id} not found", constructionId);
            return new ErrorResultDto<ConstructionCostSummaryResponseDto>(ResponseCodeConstant.CONSTRUCTION_NOT_EXIST);
        }
        var org = await orgRepository.GetById(GetCurrentOrgUid());
        if (org is null)
        {
            logger.Error("org not found");
            return new ErrorResultDto<ConstructionCostSummaryResponseDto>(ResponseCodeConstant.ORG_NOT_EXIST);
        }
        var item = await ConstructionCostSummaryResponseDtoParser(
            construction.Project.ProjectUid,
            construction.ConstructionUid,
            construction.IsPrimary,
            dateFrom,
            dateTo,
            org.EmployeeRankingDefinitionType);
        return new SuccessResultDto<ConstructionCostSummaryResponseDto>(item);
    }

    public async Task<ResultDto<bool>> CreateConstructionCost(ConstructionCostCreateRequestDto request)
    {
        var construction = await constructionRepository.GetById(request.ConstructionId, new ConstructionQueryableOptions
        {
            IncludedProject = true,
        });
        if (construction is null)
        {
            logger.Error("construction {id} not found", request.ConstructionId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.CONSTRUCTION_NOT_EXIST);
        }
        var org = await orgRepository.GetById(GetCurrentOrgUid());
        if (org is null)
        {
            logger.Error("org not found");
            return new ErrorResultDto<bool>(ResponseCodeConstant.ORG_NOT_EXIST);
        }

        var newConstructionCost = new ConstructionCost
        {
            ConstructionUid = construction.ConstructionUid,
            StartDate = DateOnly.Parse(request.StartDate),
            EndDate = DateOnly.Parse(request.EndDate),
            RiskModifiedAmount = request.RiskAmount ?? 0,
        };

        var paymentRequest = new ConstructionPaymentRequest
        {
            IssueDateFrom = DateOnly.Parse(request.StartDate),
            IssueDateTo = DateOnly.Parse(request.EndDate),
            RequestAmount = request.RequestAmount ?? 0,
            RetentionAmount = request.RetentionAmount ?? 0,
            ReleasedAmount = request.ReleaseAmount ?? 0,
            ConstructionCostUid = newConstructionCost.ConstructionCostUid,
            ContractorUid = construction.Project?.ContractorUid ?? Guid.Empty,
        };

        newConstructionCost.ConstructionPaymentRequest = paymentRequest;

        var createdConstructionCost = await constructionCostRepository.Create(newConstructionCost);
        if (createdConstructionCost is null)
        {
            logger.Error("failed to create construction cost");
            return new ErrorResultDto<bool>(ResponseCodeConstant.CONSTRUCTION_COST_CREATE_FAILED);
        }

        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> UpdateConstructionCost(Guid constructionCostId, ConstructionCostUpdateRequestDto request)
    {
        var constructionCost = await constructionCostRepository.GetById(constructionCostId, new ConstructionCostQueryableOptions());
        if (constructionCost is null)
        {
            logger.Error("construction cost {id} not found", constructionCostId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.CONSTRUCTION_COST_NOT_EXIST);
        }

        if (request.RiskAmount != null)
        {
            constructionCost.RiskModifiedAmount = request.RiskAmount ?? constructionCost.RiskModifiedAmount;
            constructionCost.TotalCostAmount = (long)(constructionCost.TotalCostAmount - constructionCost.RiskModifiedAmount + request.RiskAmount!);
        }

        var paymentRequest = constructionCost.ConstructionPaymentRequest;
        paymentRequest.RequestAmount = request.RequestAmount ?? paymentRequest.RequestAmount;
        paymentRequest.RetentionAmount = request.RetentionAmount ?? paymentRequest.RetentionAmount;
        paymentRequest.ReleasedAmount = request.ReleaseAmount ?? paymentRequest.ReleasedAmount;

        var updatedConstructionCost = await constructionCostRepository.Update(constructionCost);
        if (updatedConstructionCost is null)
        {
            return new ErrorResultDto<bool>(ResponseCodeConstant.CONSTRUCTION_COST_UPDATE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }

    private async Task<IEnumerable<ConstructionCostSummaryResponseDto>> ConstructionCostSummariesResponseDtoParser(
        IEnumerable<Construction> constructions,
        string dateFrom,
        string dateTo,
        string employeeRankingDefinitionType
    )
    {
        try
        {
            var items = new List<ConstructionCostSummaryResponseDto>();
            foreach (var construction in constructions)
            {
                if (construction.ConstructionCosts is null || construction.ConstructionCosts.Count == 0)
                {
                    var item = await ConstructionCostSummaryResponseDtoParser(
                        construction.Project.ProjectUid,
                        construction.ConstructionUid,
                        construction.IsPrimary,
                        dateFrom,
                        dateTo,
                        employeeRankingDefinitionType);
                    if (item.ConstructionId is not null)
                    {
                        items.Add(item);
                    }
                }
                else
                {
                    var item = ConstructionCostSummaryResponseDtoParser(
                        construction.ConstructionUid,
                        construction.IsPrimary,
                        construction.ConstructionCosts,
                        dateFrom,
                        dateTo);
                    if (item.ConstructionId is not null)
                    {
                        items.Add(item);
                    }
                }
            }
            return items;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error parsing constructionCost response");
            return [];
        }
    }

    private ConstructionCostSummaryResponseDto ConstructionCostSummaryResponseDtoParser(
        Guid construnctionId,
        bool IsPrimaryConstruction,
        IEnumerable<ConstructionCost> constructionCosts,
        string dateFrom,
        string dateTo)
    {
        try
        {
            var reportFrom = DateOnly.Parse(dateFrom);
            var reportTo = DateOnly.Parse(dateTo);

            var currentCost = GetCalculatedCost(
                constructionCosts,
                reportFrom,
                reportTo);

            var lastAccumulateCost = GetCalculatedCost(
                constructionCosts,
                null,
                reportFrom);

            var currentAccumulateCost = GetCalculatedCost(
                constructionCosts,
                null,
                reportTo);

            var result = new ConstructionCostSummaryResponseDto
            {
                ConstructionId = construnctionId,
                IsPrimary = IsPrimaryConstruction,
                CurrentCost = currentCost,
                CurrentAccumulateCost = currentAccumulateCost,
                LastAccumulateCost = lastAccumulateCost
            };
            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error parsing constructionCost response");
            return new ConstructionCostSummaryResponseDto();
        }
    }

    private async Task<ConstructionCostSummaryResponseDto> ConstructionCostSummaryResponseDtoParser(
        Guid projectId,
        Guid construnctionId,
        bool IsPrimaryConstruction,
        string dateFrom,
        string dateTo,
        string employeeRankingDefinitionType
    )
    {
        try
        {
            var reportFrom = DateOnly.Parse(dateFrom);
            var reportTo = DateOnly.Parse(dateTo);

            var currentCost = await GetCalculatedCost(
                projectId,
                construnctionId,
                IsPrimaryConstruction,
                reportFrom,
                reportTo);

            var lastAccumulateCost = await GetCalculatedCost(
                projectId,
                construnctionId,
                IsPrimaryConstruction,
                null,
                reportFrom);

            var currentAccumulateCost = await GetCalculatedCost(
                projectId,
                construnctionId,
                IsPrimaryConstruction,
                null,
                reportTo);

            var result = new ConstructionCostSummaryResponseDto
            {
                ConstructionId = construnctionId,
                IsPrimary = IsPrimaryConstruction,
                CurrentCost = currentCost,
                CurrentAccumulateCost = currentAccumulateCost,
                LastAccumulateCost = lastAccumulateCost
            };
            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error parsing constructionCost response");
            return new ConstructionCostSummaryResponseDto();
        }
    }

    private ConstructionPartialCostResponseDto GetCalculatedCost(
        IEnumerable<ConstructionCost> constructionCosts,
        DateOnly? reportFrom,
        DateOnly? reportTo)
    {
        try
        {
            if (reportFrom is not null)
            {
                constructionCosts = constructionCosts.Where(c => c.StartDate >= reportFrom).ToList();
            }
            if (reportTo is not null)
            {
                constructionCosts = constructionCosts.Where(c => c.EndDate <= reportTo).ToList();
            }

            var categorizedCosts = CategorizedCostResponseDtoParser(constructionCosts);

            var res = new ConstructionPartialCostResponseDto
            {
                ReportFrom = reportFrom?.ToString(),
                ReportTo = reportTo?.ToString(),
                ConstructionPaymentRequest = ConstructionPaymentResponseDtoParser(constructionCosts),
                TotalCost = constructionCosts.Sum(c => c.TotalCostAmount),
                RiskAmount = constructionCosts.Sum(c => c.RiskModifiedAmount),
                // CategorizedCosts = categorizedCosts
            };

            if (reportFrom is not null && reportTo is not null)
            {
                res.ConstructionCostId = constructionCosts.FirstOrDefault()?.ConstructionCostUid.ToString();
            }

            return res;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error parsing constructionCost response");
            return new ConstructionPartialCostResponseDto();
        }
    }

    private async Task<ConstructionPartialCostResponseDto> GetCalculatedCost(
        Guid projectId,
        Guid construnctionId,
        bool IsPrimaryConstruction,
        DateOnly? reportFrom,
        DateOnly? reportTo
    )
    {
        try
        {
            var categorizedCosts = await CategorizedCostResponseDtoParser(
                projectId,
                construnctionId,
                IsPrimaryConstruction,
                reportFrom,
                reportTo);

            var res = new ConstructionPartialCostResponseDto
            {
                ReportFrom = reportFrom?.ToString(),
                ReportTo = reportTo?.ToString(),
                TotalCost = categorizedCosts.Sum(c => c.TotalAmount),
                // CategorizedCosts = categorizedCosts
            };

            return res;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error parsing constructionCost response");
            return new ConstructionPartialCostResponseDto();
        }
    }

    private ConstructionPaymentResponseDto? ConstructionPaymentResponseDtoParser(IEnumerable<ConstructionCost> constructionCosts)
    {
        try
        {
            if (constructionCosts is null || !constructionCosts.Any())
            {
                logger.Error("current paymentClaim is not exist");
                return null;
            }

            var res = new ConstructionPaymentResponseDto();
            foreach (var constructionCost in constructionCosts)
            {
                var paymentClaim = constructionCost.ConstructionPaymentRequest;

                res.ConstructionId = paymentClaim.ConstructionCost.ConstructionUid;
                res.RequestAmount += paymentClaim.RequestAmount;
                res.RetentionAmount += paymentClaim.RetentionAmount;
                res.ReleasedAmount += paymentClaim.ReleasedAmount;
            }
            return res;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error parsing claim response");
            return null;
        }
    }

    private async Task<IEnumerable<CategorizedCostResponseDto>> CategorizedCostResponseDtoParser(
        Guid projectId,
        Guid construnctionId,
        bool IsPrimaryConstruction,
        DateOnly? reportFrom,
        DateOnly? reportTo
    )
    {
        try
        {
            var rootCategories = await categoryRepository.GetRoot(new CategoryQueryableOptions());

            var (dailyReports, _) = await projectDailyReportRepository.GetByProjectInDateRange(
                projectId, new ProjectDailyReportFilter
                {
                    ReportFrom = reportFrom?.ToString("yyyy-MM-dd"),
                    ReportTo = reportTo?.ToString("yyyy-MM-dd"),
                }, new ProjectDailyReportQueryableOptions());

            var employeeWorkloads = dailyReports
                .Where(d => d.EmployeeWorkload is not null)
                .SelectMany(d => d.EmployeeWorkload ?? [])
                .ToList();

            var outsourceWorkloads = dailyReports
                .Where(d => d.OutSourceWorkload is not null)
                .SelectMany(d => d.OutSourceWorkload ?? [])
                .ToList();

            var employeeUids = employeeWorkloads.Select(e => e.EmployeeUid).Distinct().ToList();
            var outSourceIds = outsourceWorkloads.Select(e => e.OutSourceUid).Distinct().ToList();
            var outSources = await outSourceRepository.GetByIds(outSourceIds, new OutSourceQueryableOptions
            {
                IncludedOutSourcePrice = true,
            });

            var (_, inputCostItems) = await inputCostItemRepository.GetByFilter(new InputCostItemCategoryFilter
            {
                ConstructionId = construnctionId,
                DateFrom = reportFrom?.ToString(),
                DateTo = reportTo?.ToString(),
            }, new InputCostItemQueryableOptions
            {
                IncludedItem = true,
            });

            var categorizedCosts = new List<CategorizedCostResponseDto>();
            foreach (var rootCategory in rootCategories)
            {
                var languageCode = GetCurrentLanguageCode();
                var categoryName = rootCategory.IsDefault
                    ? rootCategory.TranslatedCategory?.Where(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase)).FirstOrDefault()?.CategoryName
                    : rootCategory.CategoryName;

                if (rootCategory.CategoryCode.Equals(CategoryConstant.EMPLOYEE))
                {
                    //daily cost
                    var employeeSubCategorizedCosts = employeeWorkloads
                        .Select(e =>
                        {
                            return new CategorizedCostResponseDto
                            {
                                CategoryId = rootCategory.CategoryUid.ToString(),
                                CategoryCode = rootCategory.CategoryCode,
                                CategoryName = categoryName,
                                // TotalAmount = (long)(quantity * (employeeRank?.ApproximateCostAmount ?? 0)),
                            };
                        })
                        .ToList();

                    //overtime cost
                    var (otRequests, _) = await requestRepository.GetByProject(projectId, new RequestFilter
                    {
                        RequestTypeCode = RequestTypeConstants.OVERTIME,
                        FromDate = reportFrom?.ToDateTime(TimeOnly.MinValue).ToString("yyyy-MM-dd HH:mm:ss"),
                        ToDate = reportTo?.ToDateTime(TimeOnly.MaxValue).ToString("yyyy-MM-dd HH:mm:ss"),
                        StatusCode = StatusConstants.APPROVED,
                    }, new RequestQueryableOptions
                    {
                        IncludedAuthor = true,
                    });

                    // if (otRequests is not null && otRequests.Any())
                    // {
                    //     var otRequestAmount = otRequests.Sum(GetOvertimeDuration);
                    //     var (fixedOvertimeCostAmount, approximateOvertimeCostAmount) = GetOvertimeCostAmount(otRequests, employeeRanks);
                    //     var otCategory = new CategorizedCostResponseDto
                    //     {
                    //         CategoryId = rootCategory.CategoryUid.ToString(),
                    //         CategoryCode = rootCategory.CategoryCode,
                    //         CategoryName = categoryName + " (OT)",
                    //         Quantity = otRequestAmount,
                    //         TotalAmount = (long)approximateOvertimeCostAmount,
                    //         FixedAmount = (long)fixedOvertimeCostAmount,
                    //     };
                    //     employeeSubCategorizedCosts.Add(otCategory);
                    // }

                    var employeeRootCategorizedCost = new CategorizedCostResponseDto
                    {
                        CategoryId = rootCategory.CategoryUid.ToString(),
                        CategoryCode = rootCategory.CategoryCode,
                        CategoryName = categoryName,
                        TotalAmount = employeeSubCategorizedCosts.Sum(c => c.TotalAmount),
                    };
                    categorizedCosts.Add(employeeRootCategorizedCost);
                    continue;
                }
                if (rootCategory.CategoryCode.Equals(CategoryConstant.OUTSOURCE_DAILY))
                {
                    var outsourceSubCategorizedCosts = outsourceWorkloads
                        .Select(e =>
                        {
                            var outSource = outSources
                                .Where(r => r.OutSourceUid == e.OutSourceUid)
                                .FirstOrDefault();
                            var outSourcePrice = outSource?.OutSourcePrices
                                .OrderByDescending(o => o.EffectiveDate)
                                ?.FirstOrDefault();
                            var quantity = IsPrimaryConstruction ? (float)e.MainConsWorkload : (float)e.SubConsWorkload;
                            return new CategorizedCostResponseDto
                            {
                                CategoryId = rootCategory.CategoryUid.ToString(),
                                CategoryCode = rootCategory.CategoryCode,
                                CategoryName = categoryName + " " + outSource?.OutSourceName,
                                TotalAmount = (long)(quantity * (outSourcePrice?.PricePerDay ?? 0)),
                            };
                        })
                        .ToList();
                    var outsourceRootCategorizedCost = new CategorizedCostResponseDto
                    {
                        CategoryId = rootCategory.CategoryUid.ToString(),
                        CategoryCode = rootCategory.CategoryCode,
                        CategoryName = categoryName,
                        TotalAmount = outsourceSubCategorizedCosts.Sum(c => c.TotalAmount),
                    };
                    categorizedCosts.Add(outsourceRootCategorizedCost);
                    continue;
                }

                // non-labor cost
                var nonLaborRootCategorizedCost = new CategorizedCostResponseDto
                {
                    CategoryId = rootCategory.CategoryUid.ToString(),
                    CategoryCode = rootCategory.CategoryCode,
                    CategoryName = categoryName,
                    TotalAmount = inputCostItems.Sum(ici => ici.TotalTaxed ?? ici.TotalNonTaxed ?? 0),
                };
                categorizedCosts.Add(nonLaborRootCategorizedCost);
            }
            return categorizedCosts;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error parsing categorizedCosts response");
            return [];
        }
    }

    private float GetOvertimeDuration(Request request)
    {
        var duration = request.RequestTo - request.RequestFrom;
        return (float)Math.Round(duration.TotalMinutes / 60, 2);
    }

    private (float fixedOvertimeCostAmount, float approximateOvertimeCostAmount) GetOvertimeCostAmount(
        IEnumerable<Request> requests
    )
    {
        var overtimeRequestsGroup = requests.GroupBy(r => r.Author);
        var fixedOvertimeCostAmount = 0f;
        var approximateOvertimeCostAmount = 0f;
        foreach (var overtimeRequests in overtimeRequestsGroup)
        {
            var overtimeDuration = overtimeRequests.Sum(GetOvertimeDuration);
        }
        return (fixedOvertimeCostAmount, approximateOvertimeCostAmount);
    }

    private IEnumerable<CategorizedCostResponseDto> CategorizedCostResponseDtoParser(IEnumerable<ConstructionCost> constructionCosts)
    {
        try
        {
            var categorizedCosts = constructionCosts
                .Where(c => c.CategorizedCosts is not null)
                .SelectMany(c => c.CategorizedCosts)
                .ToList();

            var languageCode = GetCurrentLanguageCode();

            var categorizedCostResponseDtos = categorizedCosts
                .Where(c => c.Category.ParentUid is null)
                .Select(c => new CategorizedCostResponseDto
                {
                    CategoryId = c.Category.CategoryUid.ToString(),
                    CategoryCode = c.Category.CategoryCode,
                    CategoryName = c.Category.IsDefault
                        ? c.Category.CategoryName
                        : c.Category.TranslatedCategory?.Where(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase)).FirstOrDefault()?.CategoryName,
                    TotalAmount = categorizedCosts.Where(x => x.Category.ParentUid == c.CategoryUid).Sum(x => x.TotalAmount),
                });
            return categorizedCostResponseDtos;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error parsing categorizedCosts response");
            return [];
        }
    }
}
