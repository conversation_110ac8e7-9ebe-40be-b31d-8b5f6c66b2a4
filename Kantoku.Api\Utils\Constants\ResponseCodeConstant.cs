namespace Kantoku.Api.Utils.Constants
{
    public class ResponseCodeConstant
    {
        #region "common"
        public const string INTERNAL_SERVER_ERROR = "KAN500";
        public const string SUCCESS = "KAN00";
        public const string NOT_EXIST = "KAN01";
        public const string NOT_CHANGED = "KAN02";
        public const string BAD_REQUEST = "KAN03";
        public const string UNAUTHORIZED = "KAN04";
        public const string FORBIDDEN = "KAN05";
        #endregion

        #region "auth"
        public const string PASSWORD_INCORRECT = "AUTH01";
        public const string PASSWORD_CHANGES_FAILED = "AUTH02";
        public const string CONFIRM_PASSWORD_MISMATCH = "AUTH03";
        public const string PASSWORD_DOES_NOT_MEET_REQUIREMENT = "AUTH04";
        public const string PASSWORD_RESET_FAILED = "AUTH05";
        public const string OUTSOURCE_BLOCKED = "AUTH06";
        public const string PASSWORD_GENERATE_FAILED = "AUTH07";
        public const string LOGIN_FAILED = "AUTH08";
        public const string SIGN_UP_FAILED = "AUTH09";
        public const string SIGN_ON_FAILED = "AUTH10";
        public const string OTP_GENERATE_FAILED = "AUTH11";
        public const string OTP_CODE_NOT_EXIST = "AUTH12";
        public const string OTP_CODE_INCORRECT = "AUTH13";
        #endregion

        #region "account"
        public const string ACCOUNT_ALREADY_EXISTS = "AC01";
        public const string ACCOUNT_NOT_EXIST = "AC02";
        public const string ACCOUNT_CREATE_FAILED = "AC03";
        public const string ACCOUNT_UPDATE_FAILED = "AC04";
        public const string ACCOUNT_DELETE_FAILED = "AC05";
        public const string ACCOUNT_LOCK_FAILED = "AC06";
        public const string ACCOUNT_UNLOCK_FAILED = "AC07";
        public const string ACCOUNT_INFO_ALREADY_EXISTS = "AC08";
        #endregion

        #region "audit log"
        public const string NO_AUDIT_LOG = "AUD01";
        #endregion

        #region "token"
        public const string TOKEN_EXPIRED = "TKN01";
        public const string TOKEN_INVALID = "TKN02";
        public const string TOKEN_VALIDATE_FAILED = "TKN03";
        public const string TOKEN_GENERATE_FAILED = "TKN04";
        #endregion

        #region "user info"
        public const string USER_INFO_NOT_EXIST = "USI01";
        public const string USER_INFO_ALREADY_EXIST = "USI02";
        public const string USER_INFO_CREATE_FAILED = "USI03";
        public const string USER_INFO_UPDATE_FAILED = "USI04";
        public const string USER_INFO_DELETE_FAILED = "USI05";
        public const string USER_INFO_AVATAR_NOT_SET = "USI06";
        public const string USER_INFO_AVATAR_UPDATE_FAILED = "USI07";
        public const string USER_INFO_AVATAR_DELETE_FAILED = "USI08";
        #endregion

        #region "employee"
        public const string EMPLOYEE_NOT_EXIST = "EMP01";
        public const string EMPLOYEE_ALREADY_EXIST = "EMP02";
        public const string EMPLOYEE_CREATE_FAILED = "EMP03";
        public const string EMPLOYEE_UPDATE_FAILED = "EMP04";
        public const string EMPLOYEE_DELETE_FAILED = "EMP05";
        public const string EMPLOYEE_INVITATION_FAILED = "EMP06";
        public const string EMPLOYEE_INVITATION_NOT_EXIST = "EMP07";
        public const string EMPLOYEE_INVITATION_DELETE_FAILED = "EMP08";
        public const string EMPLOYEE_INVITATION_ACCEPT_FAILED = "EMP09";
        public const string EMPLOYEE_INVITATION_REJECT_FAILED = "EMP10";
        public const string EMPLOYEE_INVITATION_EXIST = "EMP11";
        public const string LINK_EMPLOYEE_FAILED = "EMP12";
        #endregion

        #region "employee leave"
        public const string EMPLOYEE_LEAVE_NOT_EXIST = "EML01";
        public const string INITIAL_EMPLOYEE_LEAVE_FAILED = "EML02";
        public const string UPDATE_EMPLOYEE_LEAVE_FAILED = "EML03";
        public const string DELETE_EMPLOYEE_LEAVE_FAILED = "EML04";
        public const string EMPLOYEE_LEAVE_ALREADY_EXIST = "EML05";
        public const string EMPLOYEE_LEAVE_EXPIRE_DATE_NOT_VALID = "EML06";
        #endregion

        #region "contract"
        public const string CONTRACT_CREATE_FAILED = "CRT01";
        public const string CONTRACT_UPDATE_FAILED = "CRT02";
        #endregion

        #region "contractor"
        public const string CONTRACTOR_NOT_EXIST = "CTR01";
        public const string CONTRACTOR_CREATE_FAILED = "CTR02";
        public const string CONTRACTOR_UPDATE_FAILED = "CTR03";
        public const string CONTRACTOR_DELETE_FAILED = "CTR04";
        public const string CONTRACTOR_LOGO_NOT_EXIST = "CTR05";
        #endregion

        #region "org"
        public const string ORG_NOT_EXIST = "ORG01";
        public const string ORG_CREATE_FAILED = "ORG02";
        public const string ORG_UPDATE_FAILED = "ORG03";
        public const string ORG_DELETE_FAILED = "ORG04";
        public const string NOT_ORG_OWNER = "ORG05";
        public const string NOT_ORG_ADMIN = "ORG06";
        #endregion

        #region "structure"
        public const string STRUCTURE_NOT_EXIST = "STR01";
        public const string STRUCTURE_CREATE_FAILED = "STR02";
        public const string STRUCTURE_UPDATE_FAILED = "STR03";
        public const string STRUCTURE_DELETE_FAILED = "STR04";
        public const string STRUCTURE_INPUT_DATA_INVALID = "STR05";
        #endregion

        #region "position"
        public const string POSITION_NOT_EXIST = "POS01";
        public const string POSITION_CREATE_FAILED = "POS02";
        public const string POSITION_UPDATE_FAILED = "POS03";
        public const string POSITION_DELETE_FAILED = "POS04";
        #endregion

        #region "outsource"
        public const string OUTSOURCE_NOT_EXIST = "OS01";
        public const string OUTSOURCE_CREATE_FAILED = "OS02";
        public const string OUTSOURCE_UPDATE_FAILED = "OS03";
        public const string OUTSOURCE_DELETE_FAILED = "OS04";
        public const string OUTSOURCE_PRICE_NOT_EXIST = "OS05"; 
        public const string OUTSOURCE_PRICE_CREATE_FAILED = "OS06";
        public const string OUTSOURCE_PRICE_UPDATE_FAILED = "OS07";
        public const string OUTSOURCE_LOGO_NOT_EXIST = "OS08";
        #endregion

        #region "ranking"
        public const string RANKING_NOT_EXIST = "RNK01";
        public const string RANKING_CREATE_FAILED = "RNK02";
        public const string RANKING_UPDATE_FAILED = "RNK03";
        public const string RANKING_DELETE_FAILED = "RNK04";
        #endregion

        #region "workplace"
        public const string WORKPLACE_NOT_EXIST = "WPL01";
        public const string WORKPLACE_CREATE_FAILED = "WPL02";
        public const string WORKPLACE_UPDATE_FAILED = "WPL03";
        #endregion

        #region "global config"
        public const string GLOBALCONFIG_NOT_EXIST = "GLC01";

        #endregion

        #region "email service"
        public const string EMAIL_TEMPLATE_NOT_EXIST = "MAIL01";
        public const string EMAIL_SENT_FAILED = "MAIL02";
        #endregion

        #region  "role"
        public const string ROLE_NOT_EXIST = "ROLE01";
        public const string ROLE_CREATE_FAILED = "ROLE02";
        public const string ROLE_UPDATE_FAILED = "ROLE03";
        public const string ROLE_DELETE_FAILED = "ROLE04";
        #endregion

        #region "common"
        public const string REQUEST_TYPE_NOT_EXIST = "COMN02";
        public const string LEAVE_TYPE_NOT_EXIST = "COMN03";
        public const string STATUS_NOT_EXIST = "COMN04";
        #endregion

        #region "request"
        public const string REQUEST_NOT_EXIST = "REQ01";
        public const string REQUEST_CREATE_FAILED = "REQ02";
        public const string REQUEST_UPDATE_FAILED = "REQ03";
        public const string APPROVE_ATTENDANCE_FAILED = "REQ04";
        public const string APPROVE_REQUEST_FAILED = "REQ05";
        public const string REJECT_REQUEST_FAILED = "REQ06";
        public const string CANCEL_REQUEST_FAILED = "REQ07";
        public const string DAILY_ATT_REQUEST_HAS_NO_SHIFT = "REQ08";
        public const string NO_APPROVAL_PERMISSION = "REQ09";
        public const string ALREADY_PROCESSED_OR_CANCELLED = "REQ10";
        public const string REQUEST_HAS_NO_PROJECT = "REQ11";
        public const string REQUEST_LEAVE_ON_DAYOFF = "REQ12";
        public const string REQUEST_ATTENDANCE_DAILY_NOT_CHECKOUT = "REQ13";
        public const string REQUEST_UPDATE_STATUS_FAILED = "REQ14";
        public const string REQUEST_ATTENDANCE_DAILY_CREATE_FAILED = "REQ15";
        public const string REQUEST_ATTENDANCE_DAILY_IS_REQUESTED = "REQ16";
        public const string REQUEST_INOUT_ON_DAYOFF = "REQ17";
        public const string REQUEST_ATTENDANCE_DAILY_INVALID_DATE = "REQ18";
        public const string REQUEST_ALREADY_APPROVED = "REQ19";
        public const string REQUEST_ALREADY_CANCELLED = "REQ20";
        public const string REQUEST_DELETE_FAILED = "REQ21";
        #endregion

        #region "translator"
        public const string TRANSLATOR_NOT_EXIST = "TRL01";

        #endregion

        #region "shift"
        public const string SHIFT_NOT_EXIST = "SFT01";
        public const string SHIFT_CREATE_FAILED = "SFT02";
        public const string SHIFT_UPDATE_FAILED = "SFT03";
        public const string SHIFT_REMOVE_FAILED = "SFT04";
        public const string CHECKIN_FAILED = "SFT05";
        public const string CHECKOUT_FAILED = "SFT06";
        public const string BREAKIN_FAILED = "SFT07";
        public const string BREAKOUT_FAILED = "SFT08";
        public const string ALREADY_CHECKED_IN = "SFT09";
        public const string ALREADY_IN_A_BREAK = "SFT10";
        public const string NOT_CHECKIN_YET = "SFT11";
        public const string MORE_THAN_ONE_BREAK = "SFT12";
        public const string NOT_IN_A_BREAK = "SFT13";
        public const string NOT_IN_A_SHIFT = "SFT14";
        public const string BREAK_TIME_EXCEEDED = "SFT15";
        public const string CREATE_ADDITIONAL_SHIFT_FAILED = "SFT16";
        public const string CANNOT_UPDATE_APPROVED_SHIFT = "SFT17";
        public const string ALREADY_CHECKED_OUT = "SFT18";
        public const string DELETE_SHIFT_FAILED = "SFT19";
        #endregion

        #region "schedule"
        public const string SCHEDULE_NOT_EXIST = "SCH01";
        public const string SCHEDULE_CREATE_FAILED = "SCH02";
        public const string SCHEDULE_UPDATE_FAILED = "SCH03";
        public const string SCHEDULE_REMOVE_FAILED = "SCH04";
        public const string SCHEDULE_EXPORT_FAILED = "SCH05";
        public const string ALREADY_HAS_SCHEDULED = "SCH06";
        public const string UPDATE_ON_ATTENDANCE_SHIFT = "SCH07";
        #endregion

        #region  "project"
        public const string PROJECT_NOT_EXIST = "PRJ01";
        public const string PROJECT_CREATE_FAILED = "PRJ02";
        public const string PROJECT_UPDATE_FAILED = "PRJ03";
        public const string PROJECT_DELETE_FAILED = "PRJ04";
        public const string PROJECT_SUMMARY_NOT_EXIST = "PRJ05";
        #endregion

        #region "project type"
        public const string PROJECT_TYPE_NOT_EXIST = "PRT01";
        public const string PROJECT_TYPE_CREATE_FAILED = "PRT02";
        public const string PROJECT_TYPE_UPDATE_FAILED = "PRT03";
        #endregion

        #region "customer"
        public const string CUSTOMER_NOT_EXIST = "CUS01";
        public const string CUSTOMER_CREATE_FAILED = "CUS02";
        public const string CUSTOMER_UPDATE_FAILED = "CUS03";
        public const string CUSTOMER_DELETE_FAILED = "CUS04";
        public const string CUSTOMER_LOGO_NOT_EXIST = "CUS05";
        #endregion

        #region "customer type"
        public const string CUSTOMER_TYPE_NOT_EXIST = "CT01";
        #endregion


        #region "partner"
        public const string LOCATION_NOT_EXIST = "GPS01";
        public const string LOCATION_INFO_INVALID = "GPS02";
        #endregion

        #region "joliday"
        public const string HOLIDAY_NOT_EXIST = "HOL01";
        public const string HOLIDAY_CREATE_FAILED = "HOL02";
        public const string HOLIDAY_UPDATE_FAILED = "HOL03";
        #endregion

        #region "event calendar"
        public const string EVENT_CALENDAR_NOT_EXIST = "EVC01";
        public const string EVENT_CALENDAR_CREATE_FAILED = "EVC02";
        public const string EVENT_CALENDAR_UPDATE_FAILED = "EVC03";
        public const string EVENT_CALENDAR_DELETE_FAILED = "EVC04";
        public const string EVENT_CALENDAR_DATE_INVALID = "EVC05";
        public const string EVENT_CALENDAR_EXISTS = "EVC06";
        #endregion

        #region "function"
        public const string FUNCTIONAL_ROLE_NOT_EXIST = "URF01";
        public const string FUNCTIONAL_ROLE_CREATE_FAILED = "URF02";
        public const string FUNCTIONAL_ROLE_UPDATE_FAILED = "URF03";

        public const string FUNCTION_NOT_EXIST = "FNC01";
        public const string FUNCTION_CREATE_FAILED = "FNC02";
        public const string FUNCTION_UPDATE_FAILED = "FNC03";

        #endregion

        #region "history Tracking"
        public const string TRACKING_HISTORY_RETRIEVE_FAILED = "HST01";
        public const string TRACKING_HISTORY_CREATE_FAILED = "HST02";

        #endregion

        #region "storage"
        public const string BUCKET_NOT_EXIST = "MIO01";
        public const string OBJECT_NOT_EXIST = "MIO02";
        public const string OBJECT_METADATA_NOT_EXIST = "MIO03";
        public const string OBJECT_UPLOAD_FAILED = "MIO04";
        public const string OBJECT_DOWNLOAD_FAILED = "MIO05";
        public const string BUCKET_CREATE_FAILED = "MIO06";
        public const string OBJECT_DELETE_FAILED = "MIO07";
        #endregion

        #region "file"
        public const string FILE_UPLOAD_FAILED = "FIL01";
        public const string FILE_DOWNLOAD_FAILED = "FIL02";
        public const string FILE_DELETE_FAILED = "FIL03";
        public const string FILE_METADATA_NOT_EXIST = "FIL04";
        public const string FILE_METADATA_CREATE_FAILED = "FIL05";
        public const string FILE_METADATA_UPDATE_FAILED = "FIL06";
        #endregion

        #region "report"
        public const string ATTENDANCE_REPORT_CREATE_FAILED = "RPT01";
        public const string ATTENDANCE_REPORT_UPDATE_FAILED = "RPT02";
        public const string ATTENDANCE_REPORT_NOT_EXIST = "RPT03";
        public const string ATTENDANCE_REPORT_ALREADY_APPROVED = "RPT04";
        public const string PROJECT_DAILY_REPORT_EXIST = "RPT05";
        public const string PROJECT_DAILY_REPORT_NOT_EXIST = "RPT06";
        public const string PROJECT_DAILY_REPORT_CREATE_FAILED = "RPT07";
        public const string PROJECT_DAILY_REPORT_UPDATE_FAILED = "RPT08";
        public const string PROJECT_DAILY_REPORT_DELETE_FAILED = "RPT09";
        #endregion

        #region "workshift"
        public const string WORK_SHIFT_NOT_EXIST = "WOS01";
        public const string WORK_SHIFT_CREATE_FAILED = "WOS02";
        public const string WORK_SHIFT_UPDATE_FAILED = "WOS03";
        public const string WORK_SHIFT_DELETE_FAILED = "WOS04";
        public const string WORK_SHIFT_ASSIGN_FAILED = "WOS05";
        public const string WORK_SHIFT_BREAK_TIME_INVALID = "WOS06";
        public const string WORK_SHIFT_NOT_ASSIGNED = "WOS07";
        public const string WORK_SHIFT_INPUT_DATA_INVALID = "WOS08";
        #endregion

        #region "Cost"

        #region "structure"
        public const string CATEGORY_NOT_EXIST = "CTG01";
        public const string CATEGORY_CREATE_FAILED = "CTG02";
        public const string CATEGORY_UPDATE_FAILED = "CTG03";
        public const string CATEGORY_DELETE_FAILED = "CTG04";
        #endregion

        #region "vendor"
        public const string VENDOR_NOT_EXIST = "VDR01";
        public const string VENDOR_CREATE_FAILED = "VDR02";
        public const string VENDOR_UPDATE_FAILED = "VDR03";
        public const string VENDOR_DELETE_FAILED = "VDR04";
        public const string VENDOR_LOGO_NOT_EXIST = "VDR05";
        public const string VENDOR_INPUT_DATA_INVALID = "VDR06";
        #endregion

        #region "process"
        public const string PROCESS_NOT_EXIST = "PRS01";
        public const string PROCESS_CREATE_FAILED = "PRS02";
        public const string PROCESS_UPDATE_FAILED = "PRS03";
        #endregion

        #region "item"
        public const string ITEM_NOT_EXIST = "IT01";
        public const string ITEM_CREATE_FAILED = "IT02";
        public const string ITEM_UPDATE_FAILED = "IT03";
        public const string ITEM_DELETE_FAILED = "IT04";
        public const string ITEM_ILLUSTRATION_NOT_EXIST = "IT05";
        #endregion

        #region "item price"
        public const string ITEM_PRICE_NOT_EXIST = "ITP01";
        public const string ITEM_PRICE_CREATE_FAILED = "ITP02";
        public const string ITEM_PRICE_UPDATE_FAILED = "ITP03";
        #endregion

        #region "input cost"
        public const string INPUTCOST_NOT_EXIST = "IPC01";
        public const string INPUTCOST_CREATE_FAILED = "IPC02";
        public const string INPUTCOST_UPDATE_FAILED = "IPC03";
        public const string INPUTCOST_DELETE_FAILED = "IPC04";
        #endregion

        #region "EntryType"
        public const string ENTRYTYPE_NOT_EXIST = "ET01";
        public const string ENTRYTYPE_CREATE_FAILED = "ET02";
        public const string ENTRYTYPE_UPDATE_FAILED = "ET03";
        public const string ENTRYTYPE_DELETE_FAILED = "ET04";
        #endregion

        #region "Manufacturer"
        public const string MANUFACTURER_NOT_EXIST = "MFTR01";
        public const string MANUFACTURER_CREATE_FAILED = "MFTR02";
        public const string MANUFACTURER_UPDATE_FAILED = "MFTR03";
        public const string MANUFACTURER_DELETE_FAILED = "MFTR04";
        public const string MANUFACTURER_LOGO_NOT_EXIST = "MFTR05";
        #endregion

        #region "input cost item"
        public const string INPUTCOSTITEM_NOT_EXIST = "IPCI01";
        public const string INPUTCOSTITEM_CREATE_FAILED = "IPCI02";
        public const string INPUTCOSTITEM_UPDATE_FAILED = "IPCI03";
        public const string INPUTCOSTITEM_DELETE_FAILED = "IPCI04";
        #endregion

        #region "PaymentType"
        public const string PAYMENTTYPE_NOT_EXIST = "PMT01";
        public const string PAYMENTTYPE_CREATE_FAILED = "PMT02";
        public const string PAYMENTTYPE_UPDATE_FAILED = "PMT03";
        public const string PAYMENTTYPE_DELETE_FAILED = "PMT04";
        #endregion

        #region "unit"
        public const string UNIT_NOT_EXIST = "UNT01";
        public const string UNIT_CREATE_FAILED = "UNT02";
        public const string UNIT_UPDATE_FAILED = "UNT03";
        public const string UNIT_DELETE_FAILED = "UNT04";
        #endregion

        #region "construction"
        public const string CONSTRUCTION_NOT_EXIST = "CON01";
        public const string CONSTRUCTION_CREATE_FAILED = "CON02";
        public const string CONSTRUCTION_UPDATE_FAILED = "CON03";
        public const string CONSTRUCTION_DELETE_FAILED = "CON04";
        public const string SUB_CONSTRUCTION_EXIST = "CON05";
        #endregion

        #region "construction cost"
        public const string CONSTRUCTION_COST_NOT_EXIST = "CONC01";
        public const string CONSTRUCTION_COST_CREATE_FAILED = "CONC02";
        public const string CONSTRUCTION_COST_UPDATE_FAILED = "CONC03";
        public const string CONSTRUCTION_COST_DELETE_FAILED = "CONC04";
        #endregion
        
        #endregion
    }
}
