﻿// <auto-generated />
using System;
using Kantoku.Processor.Data.Contexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Kantoku.Processor.Data.Migrations
{
    [DbContext(typeof(JobDbContext))]
    [Migration("20250326175858_InitialJobDbMigration")]
    partial class InitialJobDbMigration
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Kantoku.Processor.Models.BatchJobManager.BatchJob", b =>
                {
                    b.Property<Guid>("JobId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("job_id");

                    b.Property<string>("Configuration")
                        .HasColumnType("text")
                        .HasColumnName("configuration");

                    b.Property<string>("Interval")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("interval");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("is_enabled");

                    b.Property<string>("JobDescription")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("job_description");

                    b.Property<string>("JobName")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("job_name");

                    b.Property<string>("JobType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("job_type");

                    b.Property<DateTime?>("LastRunAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("last_run_at");

                    b.HasKey("JobId")
                        .HasName("batch_job_pkey");

                    b.ToTable("batch_job", "dev");
                });

            modelBuilder.Entity("Kantoku.Processor.Models.BatchJobManager.BatchJobHistory", b =>
                {
                    b.Property<Guid>("FiredId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("fired_id");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("text")
                        .HasColumnName("error_message");

                    b.Property<string>("ExecutionLog")
                        .HasColumnType("text")
                        .HasColumnName("execution_log");

                    b.Property<Guid>("JobId")
                        .HasColumnType("uuid")
                        .HasColumnName("job_id");

                    b.Property<DateTime>("StartedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("FiredId")
                        .HasName("batch_job_history_pkey");

                    b.ToTable("batch_job_history", "dev");
                });
#pragma warning restore 612, 618
        }
    }
}
