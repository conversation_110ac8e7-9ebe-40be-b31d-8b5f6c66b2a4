using Microsoft.IdentityModel.Tokens;
using System.Globalization;

namespace Kantoku.Api.Middlewares.Locale
{
    public class LocaleMiddleware : IMiddleware
    {
        public const string CultureKey = "CurrentCulture";

        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            var acceptLanguage = context.Request.Headers.AcceptLanguage;
            CultureInfo culture;

            if (acceptLanguage.IsNullOrEmpty())
            {
                RequestLocalizationOptions requestLocalizationOptions = new();
                culture = requestLocalizationOptions.DefaultRequestCulture.Culture;
            }
            else if (CultureExists(acceptLanguage!))
            {
                culture = new CultureInfo(acceptLanguage!);
            }
            else
            {
                RequestLocalizationOptions requestLocalizationOptions = new();
                culture = requestLocalizationOptions.DefaultRequestCulture.Culture;
            }

            // Store culture in HttpContext.Items
            context.Items[CultureKey] = culture;

            // Set current culture
            CultureInfo.CurrentCulture = culture;
            CultureInfo.CurrentUICulture = culture;

            context.Response.Headers.AcceptLanguage = culture.Name;

            await next(context);
        }

        private static bool CultureExists(string cultureName) =>
            CultureInfo.GetCultures(CultureTypes.AllCultures)
                .Any(culture => string.Equals(culture.Name, cultureName));
    }
}