namespace EventBus.Kafka.Configuration;

/// <summary>
/// Kafka configuration settings
/// </summary>
public class KafkaConfig
{
    /// <summary>
    /// Configuration section name
    /// </summary>
    public const string SectionName = "KafkaConfig";

    /// <summary>
    /// Kafka bootstrap servers
    /// </summary>
    public string BootstrapServers { get; set; } = "localhost:9092";

    /// <summary>
    /// Consumer group ID
    /// </summary>
    public string GroupId { get; set; } = "kantoku-service";

    /// <summary>
    /// Auto offset reset policy
    /// </summary>
    public string AutoOffsetReset { get; set; } = "earliest";

    /// <summary>
    /// Enable auto commit
    /// </summary>
    public bool EnableAutoCommit { get; set; } = true;

    /// <summary>
    /// Session timeout in milliseconds
    /// </summary>
    public int SessionTimeoutMs { get; set; } = 30000;

    /// <summary>
    /// Default topic prefix
    /// </summary>
    public string TopicPrefix { get; set; } = "kantoku";

    /// <summary>
    /// Number of partitions for new topics
    /// </summary>
    public int DefaultPartitions { get; set; } = 3;

    /// <summary>
    /// Replication factor for new topics
    /// </summary>
    public short DefaultReplicationFactor { get; set; } = 1;

    /// <summary>
    /// Producer configuration
    /// </summary>
    public KafkaProducerConfig Producer { get; set; } = new();

    /// <summary>
    /// Consumer configuration
    /// </summary>
    public KafkaConsumerConfig Consumer { get; set; } = new();
}

/// <summary>
/// Kafka producer specific configuration
/// </summary>
public class KafkaProducerConfig
{
    /// <summary>
    /// Acknowledgment level
    /// </summary>
    public string Acks { get; set; } = "all";

    /// <summary>
    /// Batch size
    /// </summary>
    public int BatchSize { get; set; } = 16384;

    /// <summary>
    /// Linger time in milliseconds
    /// </summary>
    public int LingerMs { get; set; } = 5;
}

/// <summary>
/// Kafka consumer specific configuration
/// </summary>
public class KafkaConsumerConfig
{
    /// <summary>
    /// Fetch minimum bytes
    /// </summary>
    public int FetchMinBytes { get; set; } = 1;
}
