using System.Globalization;
using System.Text.Json;
using Kantoku.Api.Configurations;
using Kantoku.Api.Dtos.Partner;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.Extensions.Options;

namespace Kantoku.Api.Services;

public interface IPartnerService
{
    Task<string> GetLocation(string? latitude, string? longtitude);
}

[Service(ServiceLifetime.Scoped)]
public class PartnerService : IPartnerService
{
    private readonly AppConfig appConfig;
    private readonly Serilog.ILogger logger;

    public PartnerService(IOptions<AppConfig> appConfig, Serilog.ILogger logger)
    {
        this.appConfig = appConfig.Value;
        this.logger = logger.ForContext<PartnerService>();
    }

    public async Task<string> GetLocation(string? latitude, string? longtitude)
    {
        try
        {
            if (latitude is null || longtitude is null)
            {
                logger.Warning("Invalid location info: {Latitude}, {Longtitude}", latitude, longtitude);
                return string.Empty;
            }
            logger.Information("Getting location with latitude: {Latitude}, longtitude: {Longtitude}", latitude, longtitude);
            string url = $"https://maps.googleapis.com/maps/api/geocode/json?latlng={latitude},{longtitude}&key={appConfig.GoogleApiKey}&language={CultureInfo.CurrentCulture.TwoLetterISOLanguageName}";
            var client = new HttpClient();
            var responseMessage = await client.GetAsync(url);
            logger.Information("Response status: {Status}", responseMessage.StatusCode);
            if (!responseMessage.IsSuccessStatusCode)
                return string.Empty;
            var responseBytes = await responseMessage.Content.ReadAsByteArrayAsync();
            var responseData = JsonSerializer.Deserialize<GeoCodeResponseDto>(responseBytes);
            logger.Information("Response data: {ResponseData}", responseData?.Results?.Count());
            if (responseData is null || responseData.Results is null || !responseData.Results.Any())
            {
                logger.Error("Invalid location info: {Latitude}, {Longtitude}", latitude, longtitude);
                return string.Empty;
            }
            logger.Information("Successfully got location: {Location}", responseData.Results.First().FormattedAddress);
            return AddressExtractor(responseData.Results.First().FormattedAddress ?? string.Empty);
        }
        catch (Exception ex)
        {
            logger.Error("Error getting location: {Error}", ex.Message, ex.StackTrace);
            return string.Empty;
        }
    }

    public string AddressExtractor(string address)
    {
        if (string.IsNullOrWhiteSpace(address))
        {
            logger.Warning("Empty address provided to AddressExtractor");
            return string.Empty;
        }

        logger.Information("Extracting address from: {Address}", address);

        int indexOfTown = address.IndexOf('都');
        if (indexOfTown == -1)
        {
            logger.Warning("Unable to extract address: '都' not found in {Address}", address);
            return address;
        }

        string extractedAddress = address[(indexOfTown + 1)..].Trim();
        logger.Information("Extracted address: {ExtractedAddress}", extractedAddress);

        return extractedAddress;
    }
}