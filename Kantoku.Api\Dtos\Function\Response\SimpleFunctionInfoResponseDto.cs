using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.Function.Response;

public class SimpleFunctionsResponseDto
{
    public IEnumerable<SimpleFunctionResponseDto> Items { get; set; } = [];
    public int PageNum { get; set; }
    public int PageSize { get; set; }
    public int TotalRecords { get; set; }
}

public class SimpleFunctionResponseDto
{
    [JsonPropertyName("id")]
    public string? FunctionId { get; set; }

    [JsonPropertyName("title")]
    public string? FunctionName { get; set; }

    [JsonPropertyName("hideInMenu")]
    public bool? HideInMenu { get; set; }
}