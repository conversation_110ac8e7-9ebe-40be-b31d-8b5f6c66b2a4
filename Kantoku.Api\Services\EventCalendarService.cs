using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.EventCalendar.Request;
using Kantoku.Api.Dtos.EventCalendar.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public interface IEventCalendarService
{
    Task<ResultDto<EventCalendarRuleDto>> GetEventCalendarRuleByIdAsync(Guid eventCalendarId);
    Task<ResultDto<EventCalendarsResponseDto>> GetEventCalendarsAsync(DateOnly from, DateOnly to);
    Task<ResultDto<EventCalendarRulesResponseDto>> GetEventCalendarRulesAsync(EventCalendarFilter filter);
    Task<ResultDto<EventCalendarRuleDto>> CreateEventCalendarAsync(CreateEventCalendarRequestDto request);
    Task<ResultDto<EventCalendarRuleDto>> UpdateEventCalendarAsync(Guid eventCalendarId, UpdateEventCalendarRequestDto request);
    Task<ResultDto<bool>> DeleteEventCalendarAsync(Guid eventCalendarId);
}

[Service(ServiceLifetime.Scoped)]
public class EventCalendarService : BaseService<EventCalendarService>, IEventCalendarService
{
    private readonly IEventCalendarRepository eventCalendarRepository;

    public EventCalendarService(
        IEventCalendarRepository eventCalendarRepository,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor)
        : base(logger, httpContextAccessor)
    {
        this.eventCalendarRepository = eventCalendarRepository;
    }

    public async Task<ResultDto<EventCalendarRuleDto>> GetEventCalendarRuleByIdAsync(Guid eventCalendarId)
    {
        var eventCalendar = await eventCalendarRepository.GetById(eventCalendarId, new EventCalendarQueryableOptions());
        if (eventCalendar is null)
        {
            logger.Error("EventCalendar Id {id} not found", eventCalendarId);
            return new ErrorResultDto<EventCalendarRuleDto>(ResponseCodeConstant.EVENT_CALENDAR_NOT_EXIST);
        }
        var result = eventCalendar.ToEventCalendarRuleDto();
        return new SuccessResultDto<EventCalendarRuleDto>(result);
    }

    public async Task<ResultDto<EventCalendarsResponseDto>> GetEventCalendarsAsync(DateOnly from, DateOnly to)
    {
        var eventCalendars = await eventCalendarRepository.GetByDate(from, to, new EventCalendarQueryableOptions());
        if (eventCalendars is null || !eventCalendars.Any())
        {
            logger.Error("EventCalendar not found");
            return new ErrorResultDto<EventCalendarsResponseDto>(ResponseCodeConstant.EVENT_CALENDAR_NOT_EXIST);
        }
        var result = eventCalendars.ToEventCalendarsResponseDto(from, to);
        return new SuccessResultDto<EventCalendarsResponseDto>(result);
    }

    public async Task<ResultDto<EventCalendarRulesResponseDto>> GetEventCalendarRulesAsync(EventCalendarFilter filter)
    {
        var (eventCalendars, totalRecords) = await eventCalendarRepository.GetByFilter(filter, new EventCalendarQueryableOptions());
        if (eventCalendars is null || !eventCalendars.Any() || totalRecords == 0)
        {
            logger.Error("EventCalendar not found");
            return new ErrorResultDto<EventCalendarRulesResponseDto>(ResponseCodeConstant.EVENT_CALENDAR_NOT_EXIST);
        }
        var result = eventCalendars.ToEventCalendarRulesResponseDto(filter.PageNum, filter.PageSize, totalRecords);
        return new SuccessResultDto<EventCalendarRulesResponseDto>(result);
    }


    public async Task<ResultDto<EventCalendarRuleDto>> CreateEventCalendarAsync(CreateEventCalendarRequestDto request)
    {
        if (await eventCalendarRepository.IsExists(request.EventName))
        {
            logger.Error("Event name {eventName} already exists", request.EventName);
            return new ErrorResultDto<EventCalendarRuleDto>(ResponseCodeConstant.EVENT_CALENDAR_EXISTS);
        }
        if (!GetCurrentOrgGuid(out var orgUid))
        {
            logger.Error("Organization not found");
            return new ErrorResultDto<EventCalendarRuleDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }

        var eventCalendar = request.ToEntity(orgUid);

        var createdEventCalendar = await eventCalendarRepository.Create(eventCalendar, new EventCalendarQueryableOptions());
        if (createdEventCalendar is null)
        {
            logger.Error("EventCalendar not created");
            return new ErrorResultDto<EventCalendarRuleDto>(ResponseCodeConstant.EVENT_CALENDAR_CREATE_FAILED);
        }
        var result = createdEventCalendar.ToEventCalendarRuleDto();
        return new SuccessResultDto<EventCalendarRuleDto>(result);
    }

    public async Task<ResultDto<EventCalendarRuleDto>> UpdateEventCalendarAsync(Guid eventCalendarId, UpdateEventCalendarRequestDto request)
    {
        var existEventCalendar = await eventCalendarRepository.GetById(eventCalendarId, new EventCalendarQueryableOptions());
        if (existEventCalendar is null)
        {
            logger.Error("EventCalendar {eventCalendarId} not found", eventCalendarId);
            return new ErrorResultDto<EventCalendarRuleDto>(ResponseCodeConstant.EVENT_CALENDAR_NOT_EXIST);
        }
        existEventCalendar.UpdateFromDto(request);

        var updatedEventCalendar = await eventCalendarRepository.Update(existEventCalendar, new EventCalendarQueryableOptions());
        if (updatedEventCalendar is null)
        {
            logger.Error("EventCalendar {eventCalendarId} not updated", eventCalendarId);
            return new ErrorResultDto<EventCalendarRuleDto>(ResponseCodeConstant.EVENT_CALENDAR_UPDATE_FAILED);
        }

        var result = existEventCalendar.ToEventCalendarRuleDto();
        return new SuccessResultDto<EventCalendarRuleDto>(result);
    }

    public async Task<ResultDto<bool>> DeleteEventCalendarAsync(Guid eventCalendarId)
    {
        var eventCalendar = await eventCalendarRepository.GetById(eventCalendarId, new EventCalendarQueryableOptions());
        if (eventCalendar is null)
        {
            logger.Error("EventCalendar {eventCalendarId} not found", eventCalendarId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.EVENT_CALENDAR_NOT_EXIST);
        }

        eventCalendar.IsDeleted = true;
        var isDeleted = await eventCalendarRepository.Update(eventCalendar);
        if (!isDeleted)
        {
            logger.Error("EventCalendar not deleted");
            return new ErrorResultDto<bool>(ResponseCodeConstant.EVENT_CALENDAR_DELETE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }
}
