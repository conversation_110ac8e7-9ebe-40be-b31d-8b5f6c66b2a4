﻿using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Filters.Domains;

public class InputCostItemCategoryFilter : InputCostItemFilter
{
    [FromQuery(Name = "categoryId")]
    public Guid? CategoryId { get; set; }
}

public class InputCostItemFilter : BaseFilter
{
    [FromQuery(Name = "projectId")]
    public Guid? ProjectId { get; set; }

    [FromQuery(Name = "constructionId")]
    public Guid? ConstructionId { get; set; }

    [FromQuery(Name = "inputCostId")]
    public Guid? InputCostId { get; set; }

    [FromQuery(Name = "vendorId")]
    public Guid? VendorId { get; set; }

    [FromQuery(Name = "dateFrom")]
    public string? DateFrom { get; set; }

    [FromQuery(Name = "dateTo")]
    public string? DateTo { get; set; }
}
