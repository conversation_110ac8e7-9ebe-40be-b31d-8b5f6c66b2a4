using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Models;

public class EventCalendar : AuditableEntity
{
    public Guid EventUid { get; set; }

    [AuditProperty]
    public string EventName { get; set; } = null!;

    [AuditProperty]
    public DateOnly? EventStartDate { get; set; }

    [AuditProperty]
    public DateOnly? EventEndDate { get; set; }

    [AuditProperty]
    public TimeOnly? EventStartTime { get; set; }

    [AuditProperty]
    public TimeOnly? EventEndTime { get; set; }

    // true: recurring, false: not recurring
    [AuditProperty]
    public bool IsRecurring { get; set; }

    [AuditProperty]
    public DateOnly? RecurringFrom { get; set; }

    [AuditProperty]
    public DateOnly? RecurringTo { get; set; }

    // Recurring type: daily, weekly, monthly, yearly 
    [AuditProperty]
    public string? RecurringType { get; set; }

    // only if RecurringType is weekly
    // for example: [1, 2, 3, 4, 5, 6, 7] as day of week 
    // 1: Monday, 2: Tuesday, 3: Wednesday, 4: Thursday, 5: Friday, 6: Saturday, 7: Sunday
    [AuditProperty]
    public List<int>? RecurringDay { get; set; }

    // only if RecurringType is weekly
    // for example: [1, 2, 3, 4] as week of month
    // 1: first week, 2: second week, 3: third week, 4: fourth week
    [AuditProperty]
    public List<int>? RecurringWeek { get; set; }

    // only if RecurringType is monthly
    // for example: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12] as month of year
    // 1: January, 2: February, 3: March, 4: April, 5: May, 6: June, 7: July, 8: August, 9: September, 10: October, 11: November, 12: December
    [AuditProperty]
    public List<int>? RecurringMonth { get; set; }

    [AuditProperty]
    public string? Description { get; set; }

    [AuditProperty]
    public bool IsDayOff { get; set; } = true;

    public bool IsDeleted { get; set; } = false;
    public Guid OrgUid { get; set; }

    public virtual Org? Org { get; set; }
}
