using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.ConstructionCost.Request;

public class ConstructionCostCreateRequestDto
{
    /// <summary>
    /// Construction uid
    /// </summary>
    [Required]
    public required Guid ConstructionId { get; set; }

    /// <summary>
    /// Start date of the construction cost
    /// </summary>
    [Required]
    public string StartDate { get; set; } = null!;

    /// <summary>
    /// End date of the construction cost
    /// </summary>
    [Required]
    public string EndDate { get; set; } = null!;

    /// <summary>
    /// Risk amount of the construction cost
    /// </summary>  
    public int? RiskAmount { get; set; }

    /// <summary>
    /// Request amount of the construction cost
    /// </summary>
    public int? RequestAmount { get; set; }

    /// <summary>
    /// Retention amount of the construction cost
    /// </summary>
    public int? RetentionAmount { get; set; }

    /// <summary>
    /// Release amount of the construction cost
    /// </summary>
    public int? ReleaseAmount { get; set; }
}
