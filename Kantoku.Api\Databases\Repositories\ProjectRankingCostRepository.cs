using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;

namespace Kantoku.Api.Databases.Repositories;

public interface IProjectRankingCostRepository
{
    Task<IEnumerable<ProjectRankingCost>> GetByProjectId(Guid projectId);
    Task<ProjectRankingCost?> GetById(Guid projectId, Guid rankId);
}

[Repository(ServiceLifetime.Scoped)]
public class ProjectRankingCostRepository : BaseRepository<ProjectRankingCost>, IProjectRankingCostRepository
{

    public ProjectRankingCostRepository(
        PostgreDbContext context,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor
    ) : base(context, logger, httpContextAccessor)
    {
    }

    public async Task<IEnumerable<ProjectRankingCost>> GetByProjectId(Guid projectId)
    {
        try
        {
            var query = context.ProjectRankingCosts
                .Where(c => c.ProjectUid == projectId);

            return await query
                .OrderBy(c => c.Min<PERSON>alue)
                .ThenBy(c => c.MaxValue)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all project ranking costs");
            return [];
        }
    }

    public async Task<ProjectRankingCost?> GetById(Guid projectId, Guid rankingId)
    {
        try
        {
            return await context.ProjectRankingCosts
                .Where(c => c.ProjectUid == projectId && c.RankingUid == rankingId)
                .FirstOrDefaultAsync();
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting project ranking cost by id: {projectId} and {rankingId}", projectId, rankingId);
            return null;
        }
    }
}
