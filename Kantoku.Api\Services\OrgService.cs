using Kantoku.Api.Dtos.Orgz.Request;
using Kantoku.Api.Dtos.Orgz.Response;
using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Helpers;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Dtos.File;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public interface IOrgService
{
    Task<ResultDto<OrgResponseDto>> GetOrgById(Guid orgUid);
    Task<ResultDto<OrgsResponseDto>> GetOrgsByLoginId(string loginId);
    Task<ResultDto<OrgsResponseDto>> GetOwnedOrgs();
    Task<ResultDto<OrgsResponseDto>> GetAssociatedOrgs();
    Task<ResultDto<OrgResponseDto>> CreateOrg(CreateOrgRequestDto requestDto);
    Task<ResultDto<OrgResponseDto>> UpdateOrg(Guid orgUid, UpdateOrgRequestDto requestDto);

    Task<ResultDto<AvatarResponseDto>> UploadLogo(Guid orgUid, IFormFile file);
    Task<ResultDto<AvatarResponseDto>> DownloadLogo(Guid orgUid);

}

[Service(ServiceLifetime.Scoped)]
public class OrgService : BaseService<OrgService>, IOrgService
{
    private readonly IOrgRepository orgRepository;
    private readonly IFileService fileService;
    public OrgService(
        IOrgRepository orgRepository,
        IFileService fileService,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor)
        : base(logger, httpContextAccessor)
    {
        this.orgRepository = orgRepository;
        this.fileService = fileService;
    }

    public async Task<ResultDto<OrgsResponseDto>> GetOrgsByLoginId(string loginId)
    {
        var orgs = await orgRepository.GetByLoginId(loginId);
        if (orgs is null || !orgs.Any())
        {
            logger.Error("No orgs found for login id: {LoginId}", loginId);
            return new ErrorResultDto<OrgsResponseDto>(ResponseCodeConstant.ORG_NOT_EXIST);
        }
        var result = orgs.ToOrgsResponseDto(1, orgs.Count(), orgs.Count());
        return new SuccessResultDto<OrgsResponseDto>(result);
    }

    public async Task<ResultDto<OrgsResponseDto>> GetOwnedOrgs()
    {
        var orgs = await orgRepository.GetOwned();
        if (orgs is null || !orgs.Any())
        {
            logger.Error("No owned orgs found");
            return new ErrorResultDto<OrgsResponseDto>(ResponseCodeConstant.ORG_NOT_EXIST);
        }
        var result = orgs.ToOrgsResponseDto(1, orgs.Count(), orgs.Count());
        return new SuccessResultDto<OrgsResponseDto>(result);
    }

    public async Task<ResultDto<OrgResponseDto>> GetOrgById(Guid orgUid)
    {
        var org = await orgRepository.GetById(orgUid);
        if (org is null)
        {
            logger.Error("Org not found: {OrgId}", orgUid);
            return new ErrorResultDto<OrgResponseDto>(ResponseCodeConstant.ORG_NOT_EXIST);
        }
        var result = org.ToOrgResponseDto();
        return new SuccessResultDto<OrgResponseDto>(result);
    }

    public async Task<ResultDto<OrgResponseDto>> CreateOrg(CreateOrgRequestDto requestDto)
    {
        var requestedOrg = requestDto.ToEntity();
        if (requestedOrg is null)
        {
            logger.Error("Invalid request");
            return new ErrorResultDto<OrgResponseDto>(ResponseCodeConstant.ORG_CREATE_FAILED);
        }
        var createdOrg = await orgRepository.Create(requestedOrg);
        if (createdOrg is null)
        {
            logger.Error("Org creation failed");
            return new ErrorResultDto<OrgResponseDto>(ResponseCodeConstant.ORG_CREATE_FAILED);
        }
        var result = createdOrg.ToOrgResponseDto();
        return new SuccessResultDto<OrgResponseDto>(result);
    }

    public async Task<ResultDto<OrgResponseDto>> UpdateOrg(Guid orgUid, UpdateOrgRequestDto requestDto)
    {
        var existOrg = await orgRepository.GetById(orgUid);
        if (existOrg is null)
        {
            logger.Error("Org not found: {OrgId}", orgUid);
            return new ErrorResultDto<OrgResponseDto>(ResponseCodeConstant.ORG_NOT_EXIST);
        }

        var currentAccountUid = GetCurrentAccountUid();
        var isOrgAdmin = existOrg.Employees.Any(
                e => e.AccountUid.ToString().Equals(currentAccountUid) && (e.IsOrgAdmin || e.IsOrgOwner));
        if (!isOrgAdmin)
        {
            logger.Error("Org is not owned by current account: {OrgId}", orgUid);
            return new ErrorResultDto<OrgResponseDto>(ResponseCodeConstant.NOT_ORG_ADMIN);
        }

        try
        {
            ObjectHelper.UpdateEntityFromDto(requestDto, existOrg);
        }
        catch (System.Exception ex)
        {
            logger.Error("Error updating org: {Error}", ex.Message);
            return new ErrorResultDto<OrgResponseDto>(ResponseCodeConstant.ORG_UPDATE_FAILED);
        }

        var updatedOrg = await orgRepository.Update(existOrg);
        if (updatedOrg is null)
        {
            logger.Error("Org update failed");
            return new ErrorResultDto<OrgResponseDto>(ResponseCodeConstant.ORG_UPDATE_FAILED);
        }
        logger.Information("Org updated: {OrgId}", updatedOrg.OrgUid);
        var result = updatedOrg.ToOrgResponseDto();
        return new SuccessResultDto<OrgResponseDto>(result);
    }

    public async Task<ResultDto<OrgsResponseDto>> GetAssociatedOrgs()
    {
        var orgs = await orgRepository.GetAssociated();
        if (!orgs.Any())
        {
            logger.Error("No orgs associated with account: {AccountId}", GetCurrentAccountUid());
            return new ErrorResultDto<OrgsResponseDto>(ResponseCodeConstant.ORG_NOT_EXIST);
        }
        var result = orgs.ToOrgsResponseDto(1, orgs.Count(), orgs.Count());
        return new SuccessResultDto<OrgsResponseDto>(result);
    }

    public async Task<ResultDto<AvatarResponseDto>> UploadLogo(Guid orgUid, IFormFile file)
    {
        var org = await orgRepository.GetById(orgUid);
        if (org is null)
        {
            logger.Error("Org not found: {OrgId}", orgUid);
            return new ErrorResultDto<AvatarResponseDto>(ResponseCodeConstant.ORG_NOT_EXIST);
        }
        var path = StorageConstant.OrgLogo();
        var fileName = "logo.jpg";
        var objectName = path + fileName;
        var uploadResult = await fileService.UploadFileAsync(file, objectName);
        if (uploadResult is null || uploadResult.Data is null)
        {
            logger.Error("Failed to upload logo: {OrgId}", orgUid);
            return new ErrorResultDto<AvatarResponseDto>(ResponseCodeConstant.ORG_UPDATE_FAILED);
        }
        org.LogoUrl = uploadResult.Data.FileUrl;
        await orgRepository.Update(org);
        var result = new AvatarResponseDto
        {
            AvatarUrl = uploadResult.Data.FileUrl,
        };
        return new SuccessResultDto<AvatarResponseDto>(result);
    }

    public async Task<ResultDto<AvatarResponseDto>> DownloadLogo(Guid orgUid)
    {
        var org = await orgRepository.GetById(orgUid);
        if (org is null)
        {
            logger.Error("Org not found: {OrgId}", orgUid);
            return new ErrorResultDto<AvatarResponseDto>(ResponseCodeConstant.ORG_NOT_EXIST);
        }
        var logoUrl = org.LogoUrl;
        if (logoUrl is null)
        {
            logger.Error("Logo not found: {OrgId}", orgUid);
            return new ErrorResultDto<AvatarResponseDto>(ResponseCodeConstant.ORG_NOT_EXIST);
        }
        var fileStat = await fileService.GetFileMetadataAsync(logoUrl);
        if (fileStat is null || fileStat.Data is null)
        {
            logger.Error("Logo not found: {OrgId}", orgUid);
            return new ErrorResultDto<AvatarResponseDto>(ResponseCodeConstant.ORG_NOT_EXIST);
        }
        var fileContent = await fileService.DownloadFile(logoUrl);
        if (fileContent is null || fileContent.Data is null)
        {
            logger.Error("Logo not found: {OrgId}", orgUid);
            return new ErrorResultDto<AvatarResponseDto>(ResponseCodeConstant.ORG_NOT_EXIST);
        }
        var result = new AvatarResponseDto
        {
            AvatarUrl = logoUrl,
            AvatarBase64 = fileContent.Data.DataAsBase64,
        };
        return new SuccessResultDto<AvatarResponseDto>(result);
    }
}
