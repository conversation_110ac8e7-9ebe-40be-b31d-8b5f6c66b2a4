using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.Structure.Request;

public class CreateStructureRequestDto
{
    /// <summary>
    /// Code of the structure (*)
    /// </summary>
    public string StructureCode { get; set; } = null!;

    /// <summary>
    /// Name of the structure (*)
    /// </summary>
    [Required]
    public string StructureName { get; set; } = null!;

    /// <summary>
    /// ID of the parent structure
    /// </summary>
    public string? StructureParentId { get; set; }

    /// <summary>
    /// Additional description for the structure
    /// </summary>
    public string? Description { get; set; }
}
