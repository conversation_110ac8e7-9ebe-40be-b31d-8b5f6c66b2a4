using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Mappers;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Attributes.Class;

namespace Kantoku.Api.Factories.Response;

public interface ITResponseFactory
{
    GeneralResponse<T> Success<T>(T data);
    GeneralResponse<T> BadRequest<T>();
    GeneralResponse<T> Unauthorized<T>();
    GeneralResponse<T> Forbidden<T>();
    GeneralResponse<T> Fail<T>(string code);
}

[Service(ServiceLifetime.Scoped)]
public class TResponseFactory : ITResponseFactory
{
    private readonly IErrorService errorService;
    private readonly MultiLanguageErrorService multiLanguageErrorService;
    public TResponseFactory(IErrorService errorService, MultiLanguageErrorService multiLanguageErrorService)
    {
        this.errorService = errorService;
        this.multiLanguageErrorService = multiLanguageErrorService;
    }

    private GeneralResponse<T> SucessBuilder<T>(T data)
    {
        var messageEntry = multiLanguageErrorService.GetErrorMessage(ResponseCodeConstant.SUCCESS);
        return new GeneralResponse<T>().WithStatus(messageEntry.ErrorStatus)
            .WithCode(messageEntry.ErrorCode)
            .WithMessage(messageEntry.ErrorMessage)
            .WithData(data);
    }

    private GeneralResponse<T> FailBuilder<T>(string code)
    {
        var messageEntry = multiLanguageErrorService.GetErrorMessage(code);
        return new GeneralResponse<T>().WithStatus(messageEntry.ErrorStatus)
            .WithCode(messageEntry.ErrorCode)
            .WithMessage(messageEntry.ErrorMessage);
    }

    public GeneralResponse<T> Success<T>(T data)
    {
        return SucessBuilder<T>(data);
    }

    public GeneralResponse<T> Fail<T>(string code)
    {
        return FailBuilder<T>(code);
    }

    public GeneralResponse<T> BadRequest<T>()
    {
        return FailBuilder<T>(ResponseCodeConstant.BAD_REQUEST);
    }

    public GeneralResponse<T> Unauthorized<T>()
    {
        return FailBuilder<T>(ResponseCodeConstant.UNAUTHORIZED);
    }

    public GeneralResponse<T> Forbidden<T>()
    {
        return FailBuilder<T>(ResponseCodeConstant.FORBIDDEN);
    }
}
