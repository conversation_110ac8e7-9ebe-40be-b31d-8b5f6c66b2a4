﻿using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.Process
{
    public class CreateProcessRequestDto
    {
        [Required]
        public string ProjectId { get; set; } = null!;

        [Required]
        public string ProcessCode { get; set; } = null!;

        [Required]
        public string ProcessName { get; set; } = null!;

        public string? Description { get; set; }

        public string? StartDate { get; set; }

        public string? EndDate { get; set; }

        public string? ParentId { get; set; }

        public float Workload { get; set; }

        [Required]
        public string WorkloadUnitId { get; set; } = null!;

        public float? InitialCost { get; set; }

        public string? EmployeeRequirement { get; set; }
    }
}
