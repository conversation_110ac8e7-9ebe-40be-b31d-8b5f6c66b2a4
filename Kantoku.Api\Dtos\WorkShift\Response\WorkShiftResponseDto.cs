using Kantoku.Api.Databases.Models;

namespace Kantoku.Api.Dtos.WorkShift.Response;

public class WorkShiftsResponseDto
{
    public IEnumerable<WorkShiftResponseDto> Items { get; set; } = [];

    public int PageNum { get; set; }

    public int PageSize { get; set; }

    public int TotalRecords { get; set; }
}

public class WorkShiftResponseDto
{
    public string? WorkShiftId { get; set; }

    public string? WorkShiftCode { get; set; }

    public string? WorkShiftName { get; set; }

    public string? CheckInTime { get; set; }

    public string? CheckOutTime { get; set; }

    public IEnumerable<WorkShiftBreakResponseDto>? WorkShiftBreaks { get; set; }

    public float? TotalRequiredTime { get; set; }

    public string? Description { get; set; }
}

public class WorkShiftBreakResponseDto
{
    public string? BreakInTime { get; set; }

    public string? BreakOutTime { get; set; }
}
