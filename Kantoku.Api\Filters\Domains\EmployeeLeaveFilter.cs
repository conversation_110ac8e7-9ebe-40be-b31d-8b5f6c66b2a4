using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Filters.Domains;

public class EmployeeLeaveFilter : BaseFilter
{
    [FromQuery(Name = "queryFrom")]
    public string? QueryFrom { get; set; }

    [FromQuery(Name = "queryTo")]
    public string? QueryTo { get; set; }

    [FromQuery(Name = "userInfo")]
    public string? UserInfo { get; set;  }

    [FromQuery(Name = "baseLeaveMin")]
    public float? BaseLeaveMin { get; set; }

    [FromQuery(Name = "baseLeaveMax")]
    public float? BaseLeaveMax { get; set; }

    [FromQuery(Name = "baseLeaveValidFrom")]
    public string? BaseLeaveValidFrom { get; set; }

    [FromQuery(Name = "baseLeaveValidTo")]
    public string? BaseLeaveValidTo { get; set; }

    [FromQuery(Name = "lastLeaveRemainingMin")]
    public float? LastLeaveRemainingMin { get; set; }

    [FromQuery(Name = "lastLeaveRemainingMax")]
    public float? LastLeaveRemainingMax { get; set; }

    [FromQuery(Name = "lastLeaveRemainingValidFrom")]
    public string? LastLeaveRemainingValidFrom { get; set; }

    [FromQuery(Name = "lastLeaveRemainingValidTo")]
    public string? LastLeaveRemainingValidTo { get; set; }

    [FromQuery(Name = "usedMin")]
    public float? UsedMin { get; set; }

    [FromQuery(Name = "usedMax")]
    public float? UsedMax { get; set; }

    [FromQuery(Name = "personalUsedMin")]
    public float? PersonalUsedMin { get; set; }

    [FromQuery(Name = "personalUsedMax")]
    public float? PersonalUsedMax { get; set; }

    [FromQuery(Name = "orgUsedMin")]
    public float? OrgUsedMin { get; set; }

    [FromQuery(Name = "orgUsedMax")]
    public float? OrgUsedMax { get; set; }
}
