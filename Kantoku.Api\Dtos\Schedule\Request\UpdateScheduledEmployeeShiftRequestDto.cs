using System.Text.Json.Serialization;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.Schedule.Request;

public class UpdateScheduledEmployeeShiftRequestDto
{
    /// <summary>
    /// new start time, format: HH:mm:ss 
    /// </summary>
    [JsonPropertyName("startTime")]
    [DateTimeValidator(typeof(TimeOnly))]
    public string StartTime { get; set; } = null!;

    /// <summary>
    /// new end time, format: HH:mm:ss
    /// </summary>
    [JsonPropertyName("endTime")]
    [DateTimeValidator(typeof(TimeOnly))]
    public string EndTime { get; set; } = null!;

    /// <summary>
    /// The required work time of the employee's shift (with no break time)
    /// used as metric to calculate actual workload
    /// </summary>
    [JsonPropertyName("totalScheduledWorkTime")]
    public float? TotalScheduledWorkTime { get; set; }

    /// <summary>
    /// Assigned role
    /// </summary>
    [JsonPropertyName("assignedRole")]
    public string? AssignedRole { get; set; }
}