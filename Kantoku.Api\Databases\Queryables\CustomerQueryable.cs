using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class CustomerQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedProject { get; set; } = false;
    public bool IncludedCustomerType { get; set; } = false;
}

public interface ICustomerQueryable
{
    IQueryable<Customer> GetCustomerQuery(
        CustomerQueryableOptions options
    );

    IQueryable<Customer> GetCustomerQueryIncluded(
        CustomerQueryableOptions options,
        IQueryable<Customer>? query = null
    );

    IQueryable<Customer> GetCustomerQueryFilter(
        CustomerFilter filter,
        CustomerQueryableOptions options,
        IQueryable<Customer>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class CustomerQueryable(PostgreDbContext context) :
    BaseQueryable<Customer>(context), ICustomerQueryable
{
    public IQueryable<Customer> GetCustomerQuery(
        CustomerQueryableOptions options
    )
    {
        var query = base.GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<Customer> GetCustomerQueryIncluded(
        CustomerQueryableOptions options,
        IQueryable<Customer>? query = null
    )
    {
        query ??= GetCustomerQuery(options);
        if (options.IncludedProject || options.IncludedAll)
        {
            query = query.Include(c => c.Projects);
        }
        if (options.IncludedCustomerType || options.IncludedAll)
        {
            query = query.Include(c => c.CustomerType);
        }
        return query;
    }

    public IQueryable<Customer> GetCustomerQueryFilter(
        CustomerFilter filter,
        CustomerQueryableOptions options,
        IQueryable<Customer>? query = null
    )
    {
        query ??= GetCustomerQueryIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(c => c.OrgUid == filter.OrgId);
        }
        if (string.IsNullOrEmpty(filter.Keyword) == false)
        {
            query = query.Where(c => c.CustomerName.Contains(filter.Keyword));
        }
        if (filter.CustomerType != null && filter.CustomerType.Any())
        {
            query = query.Where(c => filter.CustomerType.Contains(c.CustomerTypeCode));
        }
        return query;
    }
}

