# Employee Check-in Flow Documentation

This document describes the business flow for employee check-in functionality, including both scheduled and unscheduled shifts.

## API Endpoints

### 1. Scheduled Check-in
- **URL**: `/attendance/{shiftId}/checkin`
- **Method**: PUT
- **Consumes**: application/json
- **Produces**: application/json
- **Path Parameters**:
  - `shiftId` (GUID): Unique identifier of the pre-scheduled shift

### 2. Unscheduled Check-in
- **URL**: `/attendance/checkin`
- **Method**: POST
- **Consumes**: application/json
- **Produces**: application/json

## Request Payload

### Scheduled Check-in
```json
{
    "coordinates": {
        "latitude": number,    // Decimal value of latitude
        "longitude": number    // Decimal value of longitude
    },
    "deviceId": string        // Unique identifier of the device used for check-in
}
```

### Unscheduled Check-in
```json
{
    "projectId": string,      // GUID of the project
    "employeeId": string,     // GUID of the employee
    "coordinates": {
        "latitude": number,    // Decimal value of latitude
        "longitude": number    // Decimal value of longitude
    },
    "deviceId": string        // Unique identifier of the device used for check-in
}
```

## Business Flow

```mermaid
graph TD
    A[Employee] -->|Initiates Check-in| B{Scheduled Shift?}
    
    B -->|Yes| C["PUT /attendance/{shiftId}/checkin"]
    B -->|No| D["POST /attendance/checkin"]
    
    %% Input Data Section
    C -->|Input: shiftId, coordinates, deviceId| E[Check if shift exists]
    D -->|Input: projectId, employeeId, coordinates, deviceId| F[Validate project and employee]
    
    %% Validation and Error Handling
    E -->|Shift not found| E1[Return 404: Shift not found]
    E -->|Shift found| G{Already checked in?}
    G -->|Yes| H[Return 400: Already checked in]
    G -->|No| I[Process Check-in]
    
    F -->|Project not found| F1[Return 404: Project not found]
    F -->|Employee not found| F2[Return 404: Employee not found]
    F -->|Employee not assigned to project| F3[Return 403: Not authorized for project]
    F -->|Valid| I
    
    %% Processing
    I -->|Update| J[Set Check-in Time]
    J -->|Get Location| K[Geocode coordinates]
    K -->|Geocoding failed| K1[Log error, continue with coordinates only]
    K -->|Success| L[Update Check-in Location]
    K1 -->|Continue| L
    
    L -->|DB Operation| M[Return Shift Details]
```

## Data Processing Steps

1. **Input Validation**
   - Verify all required fields are present
   - Validate coordinate values (latitude: -90 to 90, longitude: -180 to 180)
   - Verify deviceId format
   - For unscheduled: Validate projectId and employeeId exist

2. **Authorization Check**
   - Verify employee is active in the system
   - For unscheduled: Verify employee is assigned to the specified project
   - For scheduled: Verify employee owns the shift

3. **Location Processing**
   - Geocode the provided coordinates to get address details
   - If geocoding fails, proceed with coordinates only
   - Store both coordinates and address (if available)

4. **Time Processing**
   - Record check-in time in UTC
   - Convert to project's timezone for display

5. **Database Operations**
   - Update the Shifts table with check-in details
   - Return the updated shift details

6. **Error Handling**
   - Handle database errors and log them
   - Return appropriate error messages and status codes

## Database Operations

### Shifts Table Updates

#### For Scheduled Shifts
```sql
UPDATE employee_shift
SET 
    check_in_time = @currentUtcTime,
    check_in_latitude = @latitude,
    check_in_longitude = @longitude,
    check_in_address = @geocodedAddress,
    device_id = @deviceId,
    last_modified_at = @currentUtcTime,
    last_modified_by = @employeeId
WHERE employee_shift_uid = @shiftId
```

#### For Unscheduled Shifts
```sql
INSERT INTO employee_shift (
    employee_shift_uid,
    employee_uid,
    project_uid,
    project_schedule_uid,
    check_in_time,
    check_in_latitude,
    check_in_longitude,
    check_in_address,
    device_id,
    created_at,
    created_by
)
VALUES (
    @newGuid,
    @employeeId,
    @projectId,
    @scheduleId,
    @currentUtcTime,
    @latitude,
    @longitude,
    @geocodedAddress,
    @deviceId,
    @currentUtcTime,
    @employeeId
)
```

## Response

### Success Response (200 OK)
```json
{
    "shiftId": "guid",
    "employeeId": "guid",
    "projectId": "guid",
    "checkInTime": "ISO8601 DateTime",
    "checkInLocation": {
        "latitude": number,
        "longitude": number,
        "address": "string"
    },
    "deviceId": "string"
}
```

### Error Responses

#### 400 Bad Request
```json
{
    "errorCode": "SFT09",
    "message": "Already Checked In",
    "status": 400
}
```
```json
{
    "errorCode": "GPS02",
    "message": "Location info invalid",
    "status": 400
}
```
```json
{
    "errorCode": "WOS07",
    "message": "There is no work shift have been assigned to the working project",
    "status": 400
}
```

#### 403 Forbidden
```json
{
    "errorCode": "KAN05",
    "message": "Forbidden",
    "status": 403
}
```

#### 404 Not Found
```json
{
    "errorCode": "SFT01",
    "message": "Shift Not Exist",
    "status": 404
}
```
```json
{
    "errorCode": "PRJ01",
    "message": "Project Not Exist",
    "status": 404
}
```
```json
{
    "errorCode": "EMP01",
    "message": "Employee Not Exist",
    "status": 404
}
```

#### 500 Internal Server Error
```json
{
    "errorCode": "SFT05",
    "message": "Check In Failed",
    "status": 500
}
```
```json
{
    "errorCode": "KAN500",
    "message": "Unknown Error",
    "status": 500
}
```
