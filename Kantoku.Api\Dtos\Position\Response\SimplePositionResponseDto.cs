using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.Position.Response;

public class SimplePositionResponseDto
{
    public IEnumerable<PositionInfoDto> Items { get; set; } = [];
    public int PageNum { get; set; }
    public int PageSize { get; set; }
    public int TotalRecords { get; set; }
}

public class PositionInfoDto
{
    [JsonPropertyName("id")]
    public string? PositionId { get; set; }

    [JsonPropertyName("name")]
    public string? PositionName { get; set; }
}
