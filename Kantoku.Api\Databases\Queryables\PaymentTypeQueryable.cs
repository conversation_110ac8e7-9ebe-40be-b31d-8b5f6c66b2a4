using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;

namespace Kantoku.Api.Databases.Queryables;

public class PaymentTypeQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
}

public interface IPaymentTypeQueryable
{
    IQueryable<PaymentType> GetPaymentTypesQuery(
        PaymentTypeQueryableOptions options
    );
    IQueryable<PaymentType> GetPaymentTypesQueryIncluded(
        PaymentTypeQueryableOptions options,
        IQueryable<PaymentType>? query = null
    );
    IQueryable<PaymentType> GetPaymentTypesQueryFiltered(
        PaymentTypeFilter filter,
        PaymentTypeQueryableOptions options,
        IQueryable<PaymentType>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class PaymentTypeQueryable(PostgreDbContext context) :
    BaseQueryable<PaymentType>(context), IPaymentTypeQueryable
{
    public IQueryable<PaymentType> GetPaymentTypesQuery(
        PaymentTypeQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<PaymentType> GetPaymentTypesQueryIncluded(
        PaymentTypeQueryableOptions options,
        IQueryable<PaymentType>? query = null
    )
    {
        query ??= GetPaymentTypesQuery(options);
        return query;
    }

    public IQueryable<PaymentType> GetPaymentTypesQueryFiltered(
        PaymentTypeFilter filter,
        PaymentTypeQueryableOptions options,
        IQueryable<PaymentType>? query = null
    )
    {
        query ??= GetPaymentTypesQueryIncluded(options);

        if (filter.Keyword is not null)
        {
            query = query.Where(p => p.PaymentTypeName.Contains(filter.Keyword));
        }
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(p => p.OrgUid == filter.OrgId);
        }
        return query;
    }
}

