using Kantoku.Api.Dtos.AuditLog;
using FileModel = Kantoku.Api.Databases.Models.File;
using Kantoku.Api.Dtos.File;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class FileController : BaseController
{
    private readonly IFileService fileService;
    private readonly IAuditLogService auditLogService;

    public FileController(
        ITResponseFactory responseFactory,
        IFileService fileService,
        IAuditLogService auditLogService
    ) : base(responseFactory)
    {
        this.fileService = fileService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Upload a single file with metadata
    /// </summary>
    /// <param name="requestForm">Form containing file, path and metadata</param>
    /// <returns>File upload result with file details</returns>
    [HttpPost("upload-file")]
    public async Task<GeneralResponse<FileMetadataResponseDto>> UploadFile(IFormCollection requestForm)
    {
        try
        {
            var file = requestForm.Files[0];
            if (file is null)
                return BadRequest<FileMetadataResponseDto>();

            if (!requestForm.TryGetValue("path", out var path))
            {
                if (path.ToString() is null || path.Count == 0)
                    return BadRequest<FileMetadataResponseDto>();
            }

            var metadata = new Dictionary<string, string?>();

            foreach (var key in requestForm.Keys)
            {
                if (!key.Equals("file") && !key.Equals("path"))
                    metadata.TryAdd(key, requestForm[key]);
            }

            var res = await fileService.UploadFileAsync(file, path.ToString(), userMetadata: metadata);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<FileMetadataResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Upload multiple files at once
    /// </summary>
    /// <param name="requestForm">Form containing files and path</param>
    /// <returns>List of file upload results</returns>
    [HttpPost("upload-files")]
    public async Task<GeneralResponse<FilesMetadataResponseDto>> UploadFiles(IFormCollection requestForm)
    {
        try
        {
            var file = requestForm.Files;
            if (file is null)
                return BadRequest<FilesMetadataResponseDto>();

            if (!requestForm.TryGetValue("path", out var path))
            {
                if (path.ToString() is null || path.Count == 0)
                    return BadRequest<FilesMetadataResponseDto>();
            }

            var res = await fileService.UploadFilesAsync(requestForm.Files, path.ToString());
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<FilesMetadataResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Download a file by its URL
    /// </summary>
    /// <param name="url">URL of the file to download</param>
    /// <returns>File stream result</returns>
    [HttpGet("download")]
    public async Task<dynamic> DownloadFile([FromQuery] string url)
    {
        try
        {
            var res = await fileService.DownloadFile(url);
            if (res.Data is null)
                return Fail<FileResponseDto>(res.ErrorCode ?? ResponseCodeConstant.FILE_DOWNLOAD_FAILED);
            return File(res.Data.DataAsBytes, res.Data.FileType, res.Data.FileName);
        }
        catch (BusinessException e)
        {
            return Fail<FileResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// List all files in a specified path
    /// </summary>
    /// <param name="path">Path to list files from</param>
    /// <param name="pageNum">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>List of file objects</returns>
    [HttpGet]
    public async Task<GeneralResponse<FilesMetadataResponseDto>> ListObjects([FromQuery] string path, [FromQuery] int pageNum, [FromQuery] int pageSize)
    {
        try
        {
            var res = await fileService.ListFilesAsync(path, pageNum, pageSize);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<FilesMetadataResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete a file by its URL
    /// </summary>
    /// <param name="url">URL of the file to delete</param>
    /// <returns>Success response</returns>
    [HttpDelete]
    public async Task<GeneralResponse<bool>> DeleteFile([FromQuery] string url)
    {
        try
        {
            await fileService.DeleteFileAsync(url);

            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }
}