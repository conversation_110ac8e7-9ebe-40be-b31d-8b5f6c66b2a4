<?xml version="1.0" encoding="utf-8"?>
<root>
	<!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader><resheader name="version">2.0</resheader><resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader><resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader><data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data><data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data><data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64"><value>[base64 mime encoded serialized .NET Framework object]</value></data><data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64"><value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value><comment>This is a comment</comment></data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
	<xsd:schema id="root"
		xmlns=""
		xmlns:xsd="http://www.w3.org/2001/XMLSchema"
		xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
		<xsd:import namespace="http://www.w3.org/XML/1998/namespace"/>
		<xsd:element name="root" msdata:IsDataSet="true">
			<xsd:complexType>
				<xsd:choice maxOccurs="unbounded">
					<xsd:element name="metadata">
						<xsd:complexType>
							<xsd:sequence>
								<xsd:element name="value" type="xsd:string" minOccurs="0"/>
							</xsd:sequence>
							<xsd:attribute name="name" use="required" type="xsd:string"/>
							<xsd:attribute name="type" type="xsd:string"/>
							<xsd:attribute name="mimetype" type="xsd:string"/>
							<xsd:attribute ref="xml:space"/>
						</xsd:complexType>
					</xsd:element>
					<xsd:element name="assembly">
						<xsd:complexType>
							<xsd:attribute name="alias" type="xsd:string"/>
							<xsd:attribute name="name" type="xsd:string"/>
						</xsd:complexType>
					</xsd:element>
					<xsd:element name="data">
						<xsd:complexType>
							<xsd:sequence>
								<xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1"/>
								<xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2"/>
							</xsd:sequence>
							<xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1"/>
							<xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3"/>
							<xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4"/>
							<xsd:attribute ref="xml:space"/>
						</xsd:complexType>
					</xsd:element>
					<xsd:element name="resheader">
						<xsd:complexType>
							<xsd:sequence>
								<xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1"/>
							</xsd:sequence>
							<xsd:attribute name="name" type="xsd:string" use="required"/>
						</xsd:complexType>
					</xsd:element>
				</xsd:choice>
			</xsd:complexType>
		</xsd:element>
	</xsd:schema>
	<resheader name="resmimetype">
		<value>text/microsoft-resx</value>
	</resheader>
	<resheader name="version">
		<value>2.0</value>
	</resheader>
	<resheader name="reader">
		<value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
	</resheader>
	<resheader name="writer">
		<value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
	</resheader>
	<data name="KAN00" xml:space="preserve">
		<value>成功</value>
	</data>
	<data name="KAN01" xml:space="preserve">
		<value>要求されたリソースが存在しません</value>
	</data>
	<data name="KAN02" xml:space="preserve">
		<value>リソースが変更されていません</value>
	</data>
	<data name="KAN03" xml:space="preserve">
		<value>リソースが変更されていません</value>
	</data>
	<data name="KAN04" xml:space="preserve">
		<value>認証されていません</value>
	</data>
	<data name="KAN05" xml:space="preserve">
		<value>アクセスが禁止されています</value>
	</data>
	<data name="KAN500" xml:space="preserve">
		<value>不明なエラー</value>
	</data>
	<data name="AUTH01" xml:space="preserve">
		<value>パスワードが正しくありません</value>
	</data>
	<data name="AUTH02" xml:space="preserve">
		<value>パスワードの変更に失敗しました</value>
	</data>
	<data name="AUTH03" xml:space="preserve">
		<value>確認用パスワードが一致しません</value>
	</data>
	<data name="AUTH04" xml:space="preserve">
		<value>パスワードが要件を満たしていません</value>
	</data>
	<data name="AUTH05" xml:space="preserve">
		<value>パスワードのリセットに失敗しました</value>
	</data>
	<data name="AUTH06" xml:space="preserve">
		<value>外部労働者はログインできません</value>
	</data>
	<data name="AUTH07" xml:space="preserve">
		<value>パスワードの生成に失敗しました</value>
	</data>
	<data name="AUTH08" xml:space="preserve">
		<value>ログインに失敗しました</value>
	</data>
	<data name="AUTH09" xml:space="preserve">
		<value>サインアップに失敗しました</value>
	</data>
	<data name="AUTH10" xml:space="preserve">
		<value>サインオンに失敗しました</value>
	</data>
	<data name="AUTH11" xml:space="preserve">
		<value>OTPコードの生成に失敗しました</value>
	</data>
	<data name="AUTH12" xml:space="preserve">
		<value>OTPコードが存在しません</value>
	</data>
	<data name="AUTH13" xml:space="preserve">
		<value>OTPコードが正しくありません</value>
	</data>
	<data name="TKN01" xml:space="preserve">
		<value>トークンの有効期限が切れています</value>
	</data>
	<data name="TKN02" xml:space="preserve">
		<value>トークンが無効です</value>
	</data>
	<data name="TKN03" xml:space="preserve">
		<value>トークンの検証に失敗しました</value>
	</data>
	<data name="TKN04" xml:space="preserve">
		<value>トークンの生成に失敗しました</value>
	</data>
	<data name="USR01" xml:space="preserve">
		<value>ユーザーが存在しません</value>
	</data>
	<data name="USR03" xml:space="preserve">
		<value>ユーザーの作成に失敗しました</value>
	</data>
	<data name="USR04" xml:space="preserve">
		<value>ユーザーの更新に失敗しました</value>
	</data>
	<data name="USR05" xml:space="preserve">
		<value>ユーザーの削除に失敗しました</value>
	</data>
	<data name="USR06" xml:space="preserve">
		<value>ロールの割り当てに失敗しました</value>
	</data>
	<data name="USR07" xml:space="preserve">
		<value>ログインIDが既に存在します</value>
	</data>
	<data name="USR09" xml:space="preserve">
		<value>ユーザーのアバターが設定されていません</value>
	</data>
	<data name="USR08" xml:space="preserve">
		<value>ユーザーのアバターの更新に失敗しました</value>
	</data>
	<data name="USR10" xml:space="preserve">
		<value>ユーザーのアバターの削除に失敗しました</value>
	</data>
	<data name="EML01" xml:space="preserve">
		<value>有休日数が存在しません</value>
	</data>
	<data name="EML02" xml:space="preserve">
		<value>有休日数が新規作成に失敗しました</value>
	</data>
	<data name="EML03" xml:space="preserve">
		<value>有休日数の更新に失敗しました</value>
	</data>
	<data name="EML04" xml:space="preserve">
		<value>有休日数の削除に失敗しました</value>
	</data>
	<data name="EML05" xml:space="preserve">
		<value>有休日数設定が既に存在します</value>
	</data>
	<data name="EML06" xml:space="preserve">
		<value>有休日数の有効期限が無効です</value>
	</data>
	<data name="CRT01" xml:space="preserve">
		<value>契約の作成に失敗しました</value>
	</data>
	<data name="CRT02" xml:space="preserve">
		<value>契約の更新に失敗しました</value>
	</data>
	<data name="ORG01" xml:space="preserve">
		<value>組織が存在しません</value>
	</data>
	<data name="ORG02" xml:space="preserve">
		<value>組織の作成に失敗しました</value>
	</data>
	<data name="ORG03" xml:space="preserve">
		<value>組織の更新に失敗しました</value>
	</data>
	<data name="ORG04" xml:space="preserve">
		<value>組織の削除に失敗しました</value>
	</data>
	<data name="ORG05" xml:space="preserve">
		<value>組織の所有者ではありません</value>
	</data>
	<data name="ORG06" xml:space="preserve">
		<value>組織の管理者ではありません</value>
	</data>
	<data name="STR01" xml:space="preserve">
		<value>構造が存在しません</value>
	</data>

	<data name="STR02" xml:space="preserve">
		<value>構造の作成に失敗しました</value>
	</data>
	<data name="STR03" xml:space="preserve">
		<value>構造の更新に失敗しました</value>
	</data>
	<data name="STR04" xml:space="preserve">
		<value>構造の削除に失敗しました</value>
	</data>
	<data name="POS01" xml:space="preserve">
		<value>役割が存在しません</value>
	</data>
	<data name="POS02" xml:space="preserve">
		<value>役割の作成に失敗しました</value>
	</data>
	<data name="POS03" xml:space="preserve">
		<value>役割の更新に失敗しました</value>
	</data>
	<data name="POS04" xml:space="preserve">
		<value>役割の削除に失敗しました</value>
	</data>
	<data name="RNK01" xml:space="preserve">
		<value>ランキングが存在しません</value>
	</data>
	<data name="RNK02" xml:space="preserve">
		<value>ランキングの作成に失敗しました</value>
	</data>
	<data name="RNK03" xml:space="preserve">
		<value>ランキングの更新に失敗しました</value>
	</data>
	<data name="RNK04" xml:space="preserve">
		<value>ランキングの削除に失敗しました</value>
	</data>
	<data name="WPL01" xml:space="preserve">
		<value>職場が存在しません</value>
	</data>
	<data name="WPL02" xml:space="preserve">
		<value>職場の作成に失敗しました</value>
	</data>
	<data name="WPL03" xml:space="preserve">
		<value>職場の更新に失敗しました</value>
	</data>
	<data name="GLC01" xml:space="preserve">
		<value>グローバル設定が存在しません</value>
	</data>
	<data name="MAIL01" xml:space="preserve">
		<value>メールテンプレートが存在しません</value>
	</data>
	<data name="MAIL02" xml:space="preserve">
		<value>メールの送信に失敗しました</value>
	</data>
	<data name="ROLE01" xml:space="preserve">
		<value>役割が存在しません</value>
	</data>
	<data name="ROLE02" xml:space="preserve">
		<value>役割の作成に失敗しました</value>
	</data>
	<data name="ROLE03" xml:space="preserve">
		<value>役割の更新に失敗しました</value>
	</data>
	<data name="ROLE04" xml:space="preserve">
		<value>役割の削除に失敗しました</value>
	</data>
	<data name="COMN01" xml:space="preserve">
		<value>単位が存在しません</value>
	</data>
	<data name="COMN02" xml:space="preserve">
		<value>申請種別が存在しません</value>
	</data>
	<data name="COMN03" xml:space="preserve">
		<value>休暇種別が存在しません</value>
	</data>
	<data name="COMN04" xml:space="preserve">
		<value>状態が存在しません</value>
	</data>
	<data name="REQ01" xml:space="preserve">
		<value>申請が存在しません</value>
	</data>
	<data name="REQ02" xml:space="preserve">
		<value>申請の作成に失敗しました</value>
	</data>
	<data name="REQ03" xml:space="preserve">
		<value>申請の更新に失敗しました</value>
	</data>
	<data name="REQ04" xml:space="preserve">
		<value>出勤の承認に失敗しました</value>
	</data>
	<data name="REQ05" xml:space="preserve">
		<value>申請の承認に失敗しました</value>
	</data>
	<data name="REQ06" xml:space="preserve">
		<value>申請の拒否に失敗しました</value>
	</data>
	<data name="REQ07" xml:space="preserve">
		<value>申請のキャンセルに失敗しました</value>
	</data>
	<data name="REQ08" xml:space="preserve">
		<value>申請の詳細が無効です</value>
	</data>
	<data name="REQ09" xml:space="preserve">
		<value>承認権限がありません</value>
	</data>
	<data name="REQ10" xml:space="preserve">
		<value>処理済みまたは取消済み申請が更新できません</value>
	</data>
	<data name="REQ11" xml:space="preserve">
		<value>申請には現場情報が必要です</value>
	</data>
	<data name="REQ12" xml:space="preserve">
		<value>休日に休暇申請ができません</value>
	</data>
	<data name="REQ13" xml:space="preserve">
		<value>日次出勤申請に退勤が必要です</value>
	</data>
	<data name="REQ14" xml:space="preserve">
		<value>申請承認に失敗しました</value>
	</data>
	<data name="REQ15" xml:space="preserve">
		<value>日次出勤申請の作成に失敗しました</value>
	</data>
	<data name="REQ16" xml:space="preserve">
		<value>日次勤怠が申請済みです</value>
	</data>
	<data name="REQ17" xml:space="preserve">
		<value>出勤申請が休日に申請できません</value>
	</data>
	<data name="REQ18" xml:space="preserve">
		<value>勤怠の日付が一日以上申請できません</value>
	</data>
	<data name="REQ19" xml:space="preserve">
		<value>申請はすでに承認されています</value>
	</data>
	<data name="REQ20" xml:space="preserve">
		<value>申請はすでにキャンセルされています</value>
	</data>
	<data name="REQ21" xml:space="preserve">
		<value>申請の削除に失敗しました</value>
	</data>
	<data name="TRL01" xml:space="preserve">
		<value>翻訳が存在しません</value>
	</data>
	<data name="SFT01" xml:space="preserve">
		<value>シフトが存在しません</value>
	</data>
	<data name="SFT02" xml:space="preserve">
		<value>シフトの作成に失敗しました</value>
	</data>
	<data name="SFT03" xml:space="preserve">
		<value>シフトの更新に失敗しました</value>
	</data>
	<data name="SFT04" xml:space="preserve">
		<value>シフトの削除に失敗しました</value>
	</data>
	<data name="SFT05" xml:space="preserve">
		<value>チェックインに失敗しました</value>
	</data>
	<data name="SFT06" xml:space="preserve">
		<value>チェックアウトに失敗しました</value>
	</data>
	<data name="SFT07" xml:space="preserve">
		<value>休憩開始に失敗しました</value>
	</data>
	<data name="SFT08" xml:space="preserve">
		<value>休憩終了に失敗しました</value>
	</data>
	<data name="SFT09" xml:space="preserve">
		<value>すでにチェックインしています</value>
	</data>
	<data name="SFT10" xml:space="preserve">
		<value>すでに休憩中です</value>
	</data>
	<data name="SFT11" xml:space="preserve">
		<value>まだチェックインしていません</value>
	</data>
	<data name="SFT12" xml:space="preserve">
		<value>シフトはすでに終了しています</value>
	</data>
	<data name="SFT13" xml:space="preserve">
		<value>休憩中ではありません</value>
	</data>
	<data name="SFT14" xml:space="preserve">
		<value>まだ休憩を終了していません</value>
	</data>
	<data name="SFT15" xml:space="preserve">
		<value>休憩時間を超過しています</value>
	</data>
	<data name="SFT16" xml:space="preserve">
		<value>追加シフトの作成に失敗しました</value>
	</data>
	<data name="SFT17" xml:space="preserve">
		<value>承認済みシフトを更新できません</value>
	</data>
	<data name="SFT18" xml:space="preserve">
		<value>すでにチェックアウトしています</value>
	</data>
	<data name="SCH01" xml:space="preserve">
		<value>スケジュールが存在しません</value>
	</data>
	<data name="SCH02" xml:space="preserve">
		<value>スケジュールの作成に失敗しました</value>
	</data>
	<data name="SCH03" xml:space="preserve">
		<value>スケジュールの更新に失敗しました</value>
	</data>
	<data name="SCH04" xml:space="preserve">
		<value>スケジュールの削除に失敗しました</value>
	</data>
	<data name="SCH05" xml:space="preserve">
		<value>スケジュールのエクスポートに失敗しました</value>
	</data>
	<data name="SCH06" xml:space="preserve">
		<value>すでにスケジュールが設定されています</value>
	</data>
	<data name="SCH07" xml:space="preserve">
		<value>スケジュールの解析に失敗しました</value>
	</data>
	<data name="PRJ01" xml:space="preserve">
		<value>プロジェクトが存在しません</value>
	</data>
	<data name="PRJ02" xml:space="preserve">
		<value>プロジェクトの作成に失敗しました</value>
	</data>
	<data name="PRJ03" xml:space="preserve">
		<value>プロジェクトの更新に失敗しました</value>
	</data>
	<data name="PRJ04" xml:space="preserve">
		<value>プロジェクトの削除に失敗しました</value>
	</data>
	<data name="PRJ05" xml:space="preserve">
		<value>プロジェクトのサマリーが存在しません</value>
	</data>
	<data name="GPS01" xml:space="preserve">
		<value>位置情報が存在しません</value>
	</data>
	<data name="GPS02" xml:space="preserve">
		<value>位置情報が無効です</value>
	</data>
	<data name="HOL01" xml:space="preserve">
		<value>休日が存在しません</value>
	</data>
	<data name="HOL02" xml:space="preserve">
		<value>休日の作成に失敗しました</value>
	</data>
	<data name="HOL03" xml:space="preserve">
		<value>休日の更新に失敗しました</value>
	</data>
	<data name="URF01" xml:space="preserve">
		<value>機能的役割が存在しません</value>
	</data>
	<data name="URF02" xml:space="preserve">
		<value>機能的役割の作成に失敗しました</value>
	</data>
	<data name="URF03" xml:space="preserve">
		<value>機能的役割の更新に失敗しました</value>
	</data>
	<data name="FNC01" xml:space="preserve">
		<value>機能が存在しません</value>
	</data>
	<data name="FNC02" xml:space="preserve">
		<value>機能の作成に失敗しました</value>
	</data>
	<data name="FNC03" xml:space="preserve">
		<value>機能の更新に失敗しました</value>
	</data>
	<data name="HST01" xml:space="preserve">
		<value>追跡履歴の取得に失敗しました</value>
	</data>
	<data name="HST02" xml:space="preserve">
		<value>追跡履歴の作成に失敗しました</value>
	</data>
	<data name="MIO01" xml:space="preserve">
		<value>バケットが存在しません</value>
	</data>
	<data name="MIO02" xml:space="preserve">
		<value>オブジェクトが存在しません</value>
	</data>
	<data name="MIO03" xml:space="preserve">
		<value>オブジェクトのメタデータが存在しません</value>
	</data>
	<data name="MIO04" xml:space="preserve">
		<value>オブジェクトのアップロードに失敗しました</value>
	</data>
	<data name="MIO05" xml:space="preserve">
		<value>オブジェクトのダウンロードに失敗しました</value>
	</data>
	<data name="MIO06" xml:space="preserve">
		<value>オブジェクトの作成に失敗しました</value>
	</data>
	<data name="MIO07" xml:space="preserve">
		<value>オブジェクトの削除に失敗しました</value>
	</data>
	<data name="RPT01" xml:space="preserve">
		<value>出勤レポートの作成に失敗しました</value>
	</data>
	<data name="RPT02" xml:space="preserve">
		<value>出勤レポートの更新に失敗しました</value>
	</data>
	<data name="RPT03" xml:space="preserve">
		<value>出勤レポートが存在しません</value>
	</data>
	<data name="RPT04" xml:space="preserve">
		<value>出勤レポートは既に承認されています、更新できません</value>
	</data>
	<data name="RPT05" xml:space="preserve">
		<value>プロジェクトの日報がすでに存在します</value>
	</data>
	<data name="RPT06" xml:space="preserve">
		<value>プロジェクトの日報が存在しません</value>
	</data>
	<data name="RPT07" xml:space="preserve">
		<value>プロジェクトの日報の作成に失敗しました</value>
	</data>
	<data name="RPT08" xml:space="preserve">
		<value>プロジェクトの日報の更新に失敗しました</value>
	</data>
	<data name="RPT09" xml:space="preserve">
		<value>プロジェクトの日報の削除に失敗しました</value>
	</data>
	<data name="CTG01" xml:space="preserve">
		<value>カテゴリが存在しません</value>
	</data>
	<data name="CTG02" xml:space="preserve">
		<value>カテゴリの作成に失敗しました</value>
	</data>
	<data name="CTG03" xml:space="preserve">
		<value>カテゴリの更新に失敗しました</value>
	</data>
	<data name="CTG04" xml:space="preserve">
		<value>カテゴリの削除に失敗しました</value>
	</data>
	<data name="VDR01" xml:space="preserve">
		<value>ベンダーが存在しません</value>
	</data>
	<data name="VDR02" xml:space="preserve">
		<value>ベンダーの作成に失敗しました</value>
	</data>
	<data name="VDR03" xml:space="preserve">
		<value>ベンダーの更新に失敗しました</value>
	</data>
	<data name="VDR04" xml:space="preserve">
		<value>ベンダーの削除に失敗しました</value>
	</data>
	<data name="VDR05" xml:space="preserve">
		<value>ベンダーのロゴが存在しません</value>
	</data>
	<data name="PRS01" xml:space="preserve">
		<value>プロセスが存在しません</value>
	</data>
	<data name="PRS02" xml:space="preserve">
		<value>プロセスの作成に失敗しました</value>
	</data>
	<data name="PRS03" xml:space="preserve">
		<value>プロセスの更新に失敗しました</value>
	</data>
	<data name="IT01" xml:space="preserve">
		<value>アイテムが存在しません</value>
	</data>
	<data name="IT02" xml:space="preserve">
		<value>アイテムの作成に失敗しました</value>
	</data>
	<data name="IT03" xml:space="preserve">
		<value>アイテムの更新に失敗しました</value>
	</data>
	<data name="IT04" xml:space="preserve">
		<value>アイテムの削除に失敗しました</value>
	</data>
	<data name="IT05" xml:space="preserve">
		<value>アイテムのイメージが存在しません</value>
	</data>
	<data name="ITP01" xml:space="preserve">
		<value>アイテム価格が存在しません</value>
	</data>
	<data name="ITP02" xml:space="preserve">
		<value>アイテム価格の作成に失敗しました</value>
	</data>
	<data name="ITP03" xml:space="preserve">
		<value>アイテム価格の更新に失敗しました</value>
	</data>
	<data name="IPC01" xml:space="preserve">
		<value>入力コストが存在しません</value>
	</data>
	<data name="IPC02" xml:space="preserve">
		<value>入力コストの作成に失敗しました</value>
	</data>
	<data name="IPC03" xml:space="preserve">
		<value>入力コストの更新に失敗しました</value>
	</data>
	<data name="IPC04" xml:space="preserve">
		<value>入力コストの削除に失敗しました</value>
	</data>
	<data name="ET01" xml:space="preserve">
		<value>エントリ種別が存在しません</value>
	</data>
	<data name="ET02" xml:space="preserve">
		<value>エントリ種別の作成に失敗しました</value>
	</data>
	<data name="ET03" xml:space="preserve">
		<value>エントリ種別の更新に失敗しました</value>
	</data>
	<data name="ET04" xml:space="preserve">
		<value>エントリ種別の削除に失敗しました</value>
	</data>
	<data name="MFTR01" xml:space="preserve">
		<value>メーカーが存在しません</value>
	</data>
	<data name="MFTR02" xml:space="preserve">
		<value>メーカーの作成が失敗しました</value>
	</data>
	<data name="MFTR03" xml:space="preserve">
		<value>メーカーの更新に失敗しました</value>
	</data>
	<data name="MFTR04" xml:space="preserve">
		<value>メーカーの削除に失敗しました</value>
	</data>
	<data name="MFTR05" xml:space="preserve">
		<value>メーカーのロゴが存在しません</value>
	</data>
	<data name="IPCI01" xml:space="preserve">
		<value>入力コストアイテムが存在しません</value>
	</data>
	<data name="IPCI02" xml:space="preserve">
		<value>入力コストアイテムの作成が失敗しました</value>
	</data>
	<data name="IPCI03" xml:space="preserve">
		<value>入力コストアイテムの更新に失敗しました</value>
	</data>
	<data name="IPCI04" xml:space="preserve">
		<value>入力コストアイテムの削除に失敗しました</value>
	</data>
	<data name="PMT01" xml:space="preserve">
		<value>支払いタイプが存在しません</value>
	</data>
	<data name="PMT02" xml:space="preserve">
		<value>支払いタイプの作成に失敗しました</value>
	</data>
	<data name="PMT03" xml:space="preserve">
		<value>支払いタイプの更新に失敗しました</value>
	</data>
	<data name="PMT04" xml:space="preserve">
		<value>支払いタイプの削除に失敗しました</value>
	</data>
	<data name="AC01" xml:space="preserve">
		<value>アカウントは既に存在します</value>
	</data>
	<data name="AC02" xml:space="preserve">
		<value>アカウントが存在しません</value>
	</data>
	<data name="AC03" xml:space="preserve">
		<value>アカウントの作成に失敗しました</value>
	</data>
	<data name="AC04" xml:space="preserve">
		<value>アカウントの更新に失敗しました</value>
	</data>
	<data name="AC05" xml:space="preserve">
		<value>アカウントの削除に失敗しました</value>
	</data>
	<data name="AC06" xml:space="preserve">
		<value>アカウントのロックに失敗しました</value>
	</data>
	<data name="AC07" xml:space="preserve">
		<value>アカウントのロック解除に失敗しました</value>
	</data>
	<data name="AC08" xml:space="preserve">
		<value>メールアドレスまたはログインIDは既に存在します</value>
	</data>
	<data name="USI01" xml:space="preserve">
		<value>ユーザー情報が存在しません</value>
	</data>
	<data name="USI02" xml:space="preserve">
		<value>ユーザー情報は既に存在します</value>
	</data>
	<data name="USI03" xml:space="preserve">
		<value>ユーザー情報の作成に失敗しました</value>
	</data>
	<data name="USI04" xml:space="preserve">
		<value>ユーザー情報の更新に失敗しました</value>
	</data>
	<data name="USI05" xml:space="preserve">
		<value>ユーザー情報の削除に失敗しました</value>
	</data>
	<data name="USI06" xml:space="preserve">
		<value>ユーザー情報のアバターが設定されていません</value>
	</data>
	<data name="USI07" xml:space="preserve">
		<value>ユーザー情報のアバターの更新に失敗しました</value>
	</data>
	<data name="USI08" xml:space="preserve">
		<value>ユーザー情報のアバターの削除に失敗しました</value>
	</data>
	<data name="EMP01" xml:space="preserve">
		<value>従業員が存在しません</value>
	</data>
	<data name="EMP02" xml:space="preserve">
		<value>従業員は既に存在します</value>
	</data>
	<data name="EMP03" xml:space="preserve">
		<value>従業員の作成に失敗しました</value>
	</data>
	<data name="EMP04" xml:space="preserve">
		<value>従業員の更新に失敗しました</value>
	</data>
	<data name="EMP05" xml:space="preserve">
		<value>従業員の削除に失敗しました</value>
	</data>
	<data name="EMP06" xml:space="preserve">
		<value>従業員の招待に失敗しました</value>
	</data>
	<data name="EMP07" xml:space="preserve">
		<value>従業員の招待が存在しません</value>
	</data>
	<data name="EMP08" xml:space="preserve">
		<value>従業員の招待の削除に失敗しました</value>
	</data>
	<data name="EMP09" xml:space="preserve">
		<value>従業員の招待の承認に失敗しました</value>
	</data>
	<data name="EMP10" xml:space="preserve">
		<value>従業員の招待の拒否に失敗しました</value>
	</data>
	<data name="EMP11" xml:space="preserve">
		<value>従業員の招待は既に存在します</value>
	</data>
	<data name="EMP12" xml:space="preserve">
		<value>組織へのアカウントのリンクに失敗しました</value>
	</data>
	<data name="FIL01" xml:space="preserve">
		<value>ファイルのアップロードに失敗しました</value>
	</data>
	<data name="FIL02" xml:space="preserve">
		<value>ファイルのダウンロードに失敗しました</value>
	</data>
	<data name="FIL03" xml:space="preserve">
		<value>ファイルの削除に失敗しました</value>
	</data>
	<data name="FIL04" xml:space="preserve">
		<value>ファイルのメタデータが存在しません</value>
	</data>
	<data name="FIL05" xml:space="preserve">
		<value>ファイルのメタデータの作成に失敗しました</value>
	</data>
	<data name="FIL06" xml:space="preserve">
		<value>ファイルのメタデータの更新に失敗しました</value>
	</data>
	<data name="WOS01" xml:space="preserve">
		<value>勤務シフトが存在しません</value>
	</data>
	<data name="WOS02" xml:space="preserve">
		<value>勤務シフトの作成に失敗しました</value>
	</data>
	<data name="WOS03" xml:space="preserve">
		<value>勤務シフトの更新に失敗しました</value>
	</data>
	<data name="WOS04" xml:space="preserve">
		<value>勤務シフトの削除に失敗しました</value>
	</data>
	<data name="WOS05" xml:space="preserve">
		<value>勤務シフトの割り当てに失敗しました</value>
	</data>
	<data name="WOS06" xml:space="preserve">
		<value>休憩時間は勤務時間内でなければなりません</value>
	</data>
	<data name="WOS07" xml:space="preserve">
		<value>勤務シフトが割り当てられていません</value>
	</data>
	<data name="EVC01" xml:space="preserve">
		<value>イベントカレンダーが存在しません</value>
	</data>
	<data name="EVC02" xml:space="preserve">
		<value>イベントカレンダーの作成に失敗しました</value>
	</data>
	<data name="EVC03" xml:space="preserve">
		<value>イベントカレンダーの更新に失敗しました</value>
	</data>
	<data name="EVC04" xml:space="preserve">
		<value>イベントカレンダーの削除に失敗しました</value>
	</data>
	<data name="EVC05" xml:space="preserve">
		<value>イベントカレンダーの日付が無効です</value>
	</data>
	<data name="EVC06" xml:space="preserve">
		<value>イベントカレンダーは既に存在します</value>
	</data>
	<data name="UNT01" xml:space="preserve">
		<value>単位が存在しません</value>
	</data>
	<data name="UNT02" xml:space="preserve">
		<value>単位の作成に失敗しました</value>
	</data>
	<data name="UNT03" xml:space="preserve">
		<value>単位の更新に失敗しました</value>
	</data>
	<data name="UNT04" xml:space="preserve">
		<value>単位の削除に失敗しました</value>
	</data>
	<data name="OS01" xml:space="preserve">
		<value>外部委託が存在しません</value>
	</data>
	<data name="OS02" xml:space="preserve">
		<value>外部委託の作成に失敗しました</value>
	</data>
	<data name="OS03" xml:space="preserve">
		<value>外部委託の更新に失敗しました</value>
	</data>
	<data name="OS04" xml:space="preserve">
		<value>外部委託の削除に失敗しました</value>
	</data>
	<data name="OS05" xml:space="preserve">
		<value>外部委託の価格が存在しません</value>
	</data>
	<data name="OS06" xml:space="preserve">
		<value>外部委託の価格の作成に失敗しました</value>
	</data>
	<data name="OS07" xml:space="preserve">
		<value>外部委託の価格の更新に失敗しました</value>
	</data>
	<data name="OS08" xml:space="preserve">
		<value>外部委託のロゴが見つかりません</value>
	</data>
	<data name="PRT01" xml:space="preserve">
		<value>プロジェクトタイプが存在しません</value>
	</data>
	<data name="PRT02" xml:space="preserve">
		<value>プロジェクトタイプの作成に失敗しました</value>
	</data>
	<data name="PRT03" xml:space="preserve">
		<value>プロジェクトタイプの更新に失敗しました</value>
	</data>
	<data name="CON01" xml:space="preserve">
		<value>工事が存在しません</value>
	</data>
	<data name="CON02" xml:space="preserve">
		<value>工事の作成に失敗しました</value>
	</data>
	<data name="CON03" xml:space="preserve">
		<value>工事の更新に失敗しました</value>
	</data>
	<data name="CON04" xml:space="preserve">
		<value>工事の削除に失敗しました</value>
	</data>
	<data name="CON05" xml:space="preserve">
		<value>別工事が存在します</value>
	</data>
	<data name="CUS01" xml:space="preserve">
		<value>顧客が存在しません</value>
	</data>
	<data name="CUS02" xml:space="preserve">
		<value>顧客の作成に失敗しました</value>
	</data>
	<data name="CUS03" xml:space="preserve">
		<value>顧客の更新に失敗しました</value>
	</data>
	<data name="CUS04" xml:space="preserve">
		<value>顧客の削除に失敗しました</value>
	</data>
	<data name="CUS05" xml:space="preserve">
		<value>顧客のロゴが見つかりません</value>
	</data>	
	<data name="CTR01" xml:space="preserve">
		<value>業者が存在しません</value>
	</data>
	<data name="CTR02" xml:space="preserve">
		<value>業者の作成に失敗しました</value>
	</data>
	<data name="CTR03" xml:space="preserve">
		<value>業者の更新に失敗しました</value>
	</data>
	<data name="CTR04" xml:space="preserve">
		<value>業者の削除に失敗しました</value>
	</data>
	<data name="CTR05" xml:space="preserve">
		<value>業者のロゴが見つかりません</value>
	</data>
	<data name="CT01" xml:space="preserve">
		<value>顧客種別が存在しません</value>
	</data>
	<data name="CONC01" xml:space="preserve">
		<value>工事費用が存在しません</value>
	</data>
	<data name="CONC02" xml:space="preserve">
		<value>工事費用の作成に失敗しました</value>
	</data>
	<data name="CONC03" xml:space="preserve">
		<value>工事費用の更新に失敗しました</value>
	</data>
	<data name="CONC04" xml:space="preserve">
		<value>工事費用の削除に失敗しました</value>
	</data>
	<data name="AUD01" xml:space="preserve">
		<value>変更履歴が存在しません</value>
	</data>
</root>