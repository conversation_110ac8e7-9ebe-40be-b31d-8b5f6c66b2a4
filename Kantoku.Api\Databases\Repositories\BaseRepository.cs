using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Constants;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Repositories;

public interface IBaseRepository<T>
{
    Guid GetCurrentAccountUid();
    Guid GetCurrentEmployeeUid();
    Guid GetCurrentOrgUid();
    dynamic ValidateFilter(BaseFilter filter);
    Task<bool> IsSuperUser();
    Task<bool> IsOrgSuperUser();
}

public abstract class BaseRepository<T> : IBaseRepository<T>
{
    protected readonly PostgreDbContext context;
    protected readonly Serilog.ILogger logger;
    protected readonly IHttpContextAccessor httpContextAccessor;
    public BaseRepository(PostgreDbContext context,
    Serilog.ILogger logger, IHttpContextAccessor httpContextAccessor)
    {
        this.context = context;
        this.logger = logger.ForContext<T>();
        this.httpContextAccessor = httpContextAccessor;
    }

    public Guid GetCurrentAccountUid()
    {
        try
        {
            var accountUidClaim = httpContextAccessor.HttpContext?.Items[ClaimConstant.ACCOUNT_UID];
            return accountUidClaim is not null && Guid.TryParse(accountUidClaim.ToString(), out Guid accountUid) ? accountUid : Guid.Empty;
        }
        catch (System.Exception)
        {
            logger.Error("Error occurred while getting current account uid");
            return Guid.Empty;
        }
    }

    public Guid GetCurrentEmployeeUid()
    {
        try
        {
            var employeeUidClaim = httpContextAccessor.HttpContext?.Items[ClaimConstant.EMPLOYEE_UID];
            return employeeUidClaim is not null && Guid.TryParse(employeeUidClaim.ToString(), out Guid employeeUid) ? employeeUid : Guid.Empty;
        }
        catch (System.Exception)
        {
            logger.Error("Error occurred while getting current employee uid");
            return Guid.Empty;
        }
    }

    public Guid GetCurrentOrgUid()
    {
        try
        {
            var orgUidClaim = httpContextAccessor.HttpContext?.Items[ClaimConstant.ORG_UID];
            return orgUidClaim is not null && Guid.TryParse(orgUidClaim.ToString(), out Guid orgUid) ? orgUid : Guid.Empty;
        }
        catch (System.Exception)
        {
            logger.Error("Error occurred while getting current org uid");
            return Guid.Empty;
        }
    }

    public dynamic ValidateFilter(BaseFilter filter)
    {
        filter.OrgId = GetCurrentOrgUid();
        return filter;
    }

    public async Task<bool> IsSuperUser()
    {
        var accountUid = GetCurrentAccountUid();
        var account = await context.Accounts
            .Where(a => a.AccountUid == accountUid)
            .FirstOrDefaultAsync();
        if (account is null)
        {
            return false;
        }
        if (account.AccountType.Equals(AccountTypeConstant.SUPER_USER))
        {
            return true;
        }
        return false;
    }

    public async Task<bool> IsOrgSuperUser()
    {
        var accountUid = GetCurrentAccountUid();
        var orgUid = GetCurrentOrgUid();
        var employee = await context.Employees
            .Include(e => e.EmployeeRoles)
            .ThenInclude(er => er.Role)
            .Where(e => e.OrgUid == orgUid && e.AccountUid == accountUid)
            .FirstOrDefaultAsync();
        if (employee is null)
        {
            return false;
        }
        if (employee.IsOrgAdmin || employee.IsOrgOwner || employee.IsHidden || employee.EmployeeRoles.Any(er => er.Role.IsHidden))
        {
            return true;
        }
        return false;
    }
}