using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Models;

public class ItemPrice : AuditableEntity
{
    public Guid ItemPriceUid { get; set; }
    public Guid ItemUid { get; set; }
    public Guid VendorUid { get; set; }
    public Guid OrgUid { get; set; }

    [AuditProperty]
    public string? Unit { get; set; }

    [AuditProperty]
    public DateOnly ValidFrom { get; set; }

    [AuditProperty]
    public DateOnly? ValidTo { get; set; }

    [AuditProperty]
    public int Price { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual Item Item { get; set; } = null!;

    public virtual Vendor Vendor { get; set; } = null!;
}
