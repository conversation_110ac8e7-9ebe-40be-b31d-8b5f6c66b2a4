<?xml version="1.0" encoding="utf-8"?>
<root>
	<!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader><resheader name="version">2.0</resheader><resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader><resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader><data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data><data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data><data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64"><value>[base64 mime encoded serialized .NET Framework object]</value></data><data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64"><value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value><comment>This is a comment</comment></data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
	<xsd:schema id="root"
		xmlns=""
		xmlns:xsd="http://www.w3.org/2001/XMLSchema"
		xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
		<xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
		<xsd:element name="root" msdata:IsDataSet="true">
			<xsd:complexType>
				<xsd:choice maxOccurs="unbounded">
					<xsd:element name="metadata">
						<xsd:complexType>
							<xsd:sequence>
								<xsd:element name="value" type="xsd:string" minOccurs="0" />
							</xsd:sequence>
							<xsd:attribute name="name" use="required" type="xsd:string" />
							<xsd:attribute name="type" type="xsd:string" />
							<xsd:attribute name="mimetype" type="xsd:string" />
							<xsd:attribute ref="xml:space" />
						</xsd:complexType>
					</xsd:element>
					<xsd:element name="assembly">
						<xsd:complexType>
							<xsd:attribute name="alias" type="xsd:string" />
							<xsd:attribute name="name" type="xsd:string" />
						</xsd:complexType>
					</xsd:element>
					<xsd:element name="data">
						<xsd:complexType>
							<xsd:sequence>
								<xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
								<xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
							</xsd:sequence>
							<xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
							<xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
							<xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
							<xsd:attribute ref="xml:space" />
						</xsd:complexType>
					</xsd:element>
					<xsd:element name="resheader">
						<xsd:complexType>
							<xsd:sequence>
								<xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
							</xsd:sequence>
							<xsd:attribute name="name" type="xsd:string" use="required" />
						</xsd:complexType>
					</xsd:element>
				</xsd:choice>
			</xsd:complexType>
		</xsd:element>
	</xsd:schema>
	<resheader name="resmimetype">
		<value>text/microsoft-resx</value>
	</resheader>
	<resheader name="version">
		<value>2.0</value>
	</resheader>
	<resheader name="reader">
		<value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
	</resheader>
	<resheader name="writer">
		<value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
	</resheader>
	<data name="KAN00" xml:space="preserve">
		<value>Success</value>
	</data>
	<data name="KAN01" xml:space="preserve">
		<value>Requested resources Not Exist</value>
	</data>
	<data name="KAN02" xml:space="preserve">
		<value>Resource Not Changed</value>
	</data>
	<data name="KAN03" xml:space="preserve">
		<value>Resources not changed</value>
	</data>
	<data name="KAN04" xml:space="preserve">
		<value>Unauthorized</value>
	</data>
	<data name="KAN05" xml:space="preserve">
		<value>Forbidden</value>
	</data>
	<data name="KAN500" xml:space="preserve">
		<value>Unknown Error</value>
	</data>
	<data name="AUTH01" xml:space="preserve">
		<value>Password Incorrect</value>
	</data>
	<data name="AUTH02" xml:space="preserve">
		<value>Password Changes Failed</value>
	</data>
	<data name="AUTH03" xml:space="preserve">
		<value>Confirm Password Mismatch</value>
	</data>
	<data name="AUTH04" xml:space="preserve">
		<value>Password Does Not Meet Requirement</value>
	</data>
	<data name="AUTH05" xml:space="preserve">
		<value>Password Reset Failed</value>
	</data>
	<data name="AUTH06" xml:space="preserve">
		<value>User is outsource labors, cannot login</value>
	</data>
	<data name="AUTH07" xml:space="preserve">
		<value>Password Generate Failed</value>
	</data>
	<data name="AUTH08" xml:space="preserve">
		<value>Login Failed</value>
	</data>
	<data name="AUTH09" xml:space="preserve">
		<value>Sign Up Failed</value>
	</data>
	<data name="AUTH10" xml:space="preserve">
		<value>Sign On Failed</value>
	</data>
	<data name="AUTH11" xml:space="preserve">
		<value>OTP Generation Failed</value>
	</data>
	<data name="AUTH12" xml:space="preserve">
		<value>OTP Code Does Not Exist</value>
	</data>
	<data name="AUTH13" xml:space="preserve">
		<value>OTP Code Incorrect</value>
	</data>
	<data name="TKN01" xml:space="preserve">
		<value>Token Expired</value>
	</data>
	<data name="TKN02" xml:space="preserve">
		<value>Token Invalid</value>
	</data>
	<data name="TKN03" xml:space="preserve">
		<value>Token Validate Failed</value>
	</data>
	<data name="TKN04" xml:space="preserve">
		<value>Token Generate Failed</value>
	</data>
	<data name="USR01" xml:space="preserve">
		<value>User Not Exist</value>
	</data>
	<data name="USR03" xml:space="preserve">
		<value>User Create Failed</value>
	</data>
	<data name="USR04" xml:space="preserve">
		<value>User Update Failed</value>
	</data>
	<data name="USR05" xml:space="preserve">
		<value>User Delete Failed</value>
	</data>
	<data name="USR06" xml:space="preserve">
		<value>Assign Role Failed</value>
	</data>
	<data name="USR07" xml:space="preserve">
		<value>Login ID Already Exist</value>
	</data>
	<data name="USR09" xml:space="preserve">
		<value>User Avatar Not Set</value>
	</data>
	<data name="USR08" xml:space="preserve">
		<value>User Avatar Update Failed</value>
	</data>
	<data name="USR10" xml:space="preserve">
		<value>User Avatar Delete Failed</value>
	</data>
	<data name="EML01" xml:space="preserve">
		<value>Employee Leave Not Exist</value>
	</data>
	<data name="EML02" xml:space="preserve">
		<value>Initial Employee Leave Failed</value>
	</data>
	<data name="EML03" xml:space="preserve">
		<value>Update Employee Leave Failed</value>
	</data>
	<data name="EML04" xml:space="preserve">
		<value>Delete Employee Leave Failed</value>
	</data>
	<data name="EML05" xml:space="preserve">
		<value>Employee Leave Already Exist</value>
	</data>
	<data name="EML06" xml:space="preserve">
		<value>Employee Leave Expire Date Not Valid</value>
	</data>
	<data name="CRT01" xml:space="preserve">
		<value>Contract create failed</value>
	</data>
	<data name="CRT02" xml:space="preserve">
		<value>Contract update failed</value>
	</data>
	<data name="ORG01" xml:space="preserve">
		<value>Organization Not Exist</value>
	</data>
	<data name="ORG02" xml:space="preserve">
		<value>Organization Create Failed</value>
	</data>
	<data name="ORG03" xml:space="preserve">
		<value>Organization Update Failed</value>
	</data>
	<data name="ORG04" xml:space="preserve">
		<value>Organization Delete Failed</value>
	</data>
	<data name="ORG05" xml:space="preserve">
		<value>Not Organization Owner</value>
	</data>
	<data name="ORG06" xml:space="preserve">
		<value>Not Organization Admin</value>
	</data>
	<data name="STR01" xml:space="preserve">
		<value>Structure Not Exist</value>
	</data>
	<data name="STR02" xml:space="preserve">
		<value>Structure Create Failed</value>
	</data>
	<data name="STR03" xml:space="preserve">
		<value>Structure Update Failed</value>
	</data>
	<data name="STR04" xml:space="preserve">
		<value>Structure Delete Failed</value>
	</data>
	<data name="POS01" xml:space="preserve">
		<value>Position Not Exist</value>
	</data>
	<data name="POS02" xml:space="preserve">
		<value>Position Create Failed</value>
	</data>
	<data name="POS03" xml:space="preserve">
		<value>Position Update Failed</value>
	</data>
	<data name="POS04" xml:space="preserve">
		<value>Position Delete Failed</value>
	</data>
	<data name="RNK01" xml:space="preserve">
		<value>Ranking Not Exist</value>
	</data>
	<data name="RNK02" xml:space="preserve">
		<value>Ranking Create Failed</value>
	</data>
	<data name="RNK03" xml:space="preserve">
		<value>Ranking Update Failed</value>
	</data>
	<data name="RNK04" xml:space="preserve">
		<value>Ranking Delete Failed</value>
	</data>
	<data name="WPL01" xml:space="preserve">
		<value>Workplace Not Exist</value>
	</data>
	<data name="WPL02" xml:space="preserve">
		<value>Workplace Create Failed</value>
	</data>
	<data name="WPL03" xml:space="preserve">
		<value>Workplace Update Failed</value>
	</data>
	<data name="GLC01" xml:space="preserve">
		<value>Global Config Not Exist</value>
	</data>
	<data name="MAIL01" xml:space="preserve">
		<value>Email Template Not Exist</value>
	</data>
	<data name="MAIL02" xml:space="preserve">
		<value>Email sent failed</value>
	</data>
	<data name="ROLE01" xml:space="preserve">
		<value>Role Not Exist</value>
	</data>
	<data name="ROLE02" xml:space="preserve">
		<value>Role create failed</value>
	</data>
	<data name="ROLE03" xml:space="preserve">
		<value>Role update failed</value>
	</data>
	<data name="ROLE04" xml:space="preserve">
		<value>Role delete failed</value>
	</data>
	<data name="COMN01" xml:space="preserve">
		<value>Unit Not Exist</value>
	</data>
	<data name="COMN02" xml:space="preserve">
		<value>Request Type Not Exist</value>
	</data>
	<data name="COMN03" xml:space="preserve">
		<value>Leave Type Not Exist</value>
	</data>
	<data name="COMN04" xml:space="preserve">
		<value>Status Not Exist</value>
	</data>
	<data name="REQ01" xml:space="preserve">
		<value>Request Not Exist</value>
	</data>
	<data name="REQ02" xml:space="preserve">
		<value>Request Create Failed</value>
	</data>
	<data name="REQ03" xml:space="preserve">
		<value>Request Update Failed</value>
	</data>
	<data name="REQ04" xml:space="preserve">
		<value>Approve Attendance Failed</value>
	</data>
	<data name="REQ05" xml:space="preserve">
		<value>Approve Request Failed</value>
	</data>
	<data name="REQ06" xml:space="preserve">
		<value>Reject Request Failed</value>
	</data>
	<data name="REQ07" xml:space="preserve">
		<value>Cancel request failed</value>
	</data>
	<data name="REQ08" xml:space="preserve">
		<value>Request Detail Invalid</value>
	</data>
	<data name="REQ09" xml:space="preserve">
		<value>No Approval Permission</value>
	</data>
	<data name="REQ10" xml:space="preserve">
		<value>Already Processed Or Cancelled</value>
	</data>
	<data name="REQ11" xml:space="preserve">
		<value>No Project Assigned with this request</value>
	</data>
	<data name="REQ12" xml:space="preserve">
		<value>Request Leave On Day Off</value>
	</data>
	<data name="REQ13" xml:space="preserve">
		<value>Request Attendance Daily Not Checkout</value>
	</data>
	<data name="REQ14" xml:space="preserve">
		<value>Request Update Status Failed</value>
	</data>
	<data name="REQ15" xml:space="preserve">
		<value>Request Attendance Daily Create Failed</value>
	</data>
	<data name="REQ16" xml:space="preserve">
		<value>All shifts for this desired day is requested</value>
	</data>
	<data name="REQ17" xml:space="preserve">
		<value>Request In/Out On Day Off</value>
	</data>
	<data name="REQ18" xml:space="preserve">
		<value>Request Attendance Daily Invalid Date</value>
	</data>
	<data name="REQ19" xml:space="preserve">
		<value>Request Already Approved</value>
	</data>
	<data name="REQ20" xml:space="preserve">
		<value>Request Already Cancelled</value>
	</data>
	<data name="REQ21" xml:space="preserve">
		<value>Request Delete Failed</value>
	</data>
	<data name="TRL01" xml:space="preserve">
		<value>Translator Not Exist</value>
	</data>
	<data name="SFT01" xml:space="preserve">
		<value>Shift Not Exist</value>
	</data>
	<data name="SFT02" xml:space="preserve">
		<value>Shift Create Failed</value>
	</data>
	<data name="SFT03" xml:space="preserve">
		<value>Shift Update Failed</value>
	</data>
	<data name="SFT04" xml:space="preserve">
		<value>Shift remove failed</value>
	</data>
	<data name="SFT05" xml:space="preserve">
		<value>Check In Failed</value>
	</data>
	<data name="SFT06" xml:space="preserve">
		<value>Check Out Failed</value>
	</data>
	<data name="SFT07" xml:space="preserve">
		<value>Break In Failed</value>
	</data>
	<data name="SFT08" xml:space="preserve">
		<value>Break Out Failed</value>
	</data>
	<data name="SFT09" xml:space="preserve">
		<value>Already Checked In</value>
	</data>
	<data name="SFT10" xml:space="preserve">
		<value>Already In A Break</value>
	</data>
	<data name="SFT11" xml:space="preserve">
		<value>Not Checked In Yet</value>
	</data>
	<data name="SFT12" xml:space="preserve">
		<value>Shift Already Ended</value>
	</data>
	<data name="SFT13" xml:space="preserve">
		<value>Not In A Break</value>
	</data>
	<data name="SFT14" xml:space="preserve">
		<value>Not Broken Out Yet</value>
	</data>
	<data name="SFT15" xml:space="preserve">
		<value>Break time exceeded</value>
	</data>
	<data name="SFT16" xml:space="preserve">
		<value>Create additional shift failed</value>
	</data>
	<data name="SFT17" xml:space="preserve">
		<value>Cannot Update Approved Shift</value>
	</data>
	<data name="SFT18" xml:space="preserve">
		<value>Already Checked Out</value>
	</data>
	<data name="SCH01" xml:space="preserve">
		<value>Schedule Not Exist</value>
	</data>
	<data name="SCH02" xml:space="preserve">
		<value>Schedule Create Failed</value>
	</data>
	<data name="SCH03" xml:space="preserve">
		<value>Schedule Update Failed</value>
	</data>
	<data name="SCH04" xml:space="preserve">
		<value>Schedule Remove Failed</value>
	</data>
	<data name="SCH05" xml:space="preserve">
		<value>Export Schedule Failed</value>
	</data>
	<data name="SCH06" xml:space="preserve">
		<value>Already has scheduled</value>
	</data>
	<data name="SCH07" xml:space="preserve">
		<value>Schedule Parse Failed</value>
	</data>
	<data name="PRJ01" xml:space="preserve">
		<value>Project Not Exist</value>
	</data>
	<data name="PRJ02" xml:space="preserve">
		<value>Project Create Failed</value>
	</data>
	<data name="PRJ03" xml:space="preserve">
		<value>Project Update Failed</value>
	</data>
	<data name="PRJ04" xml:space="preserve">
		<value>Project Delete Failed</value>
	</data>
	<data name="PRJ05" xml:space="preserve">
		<value>Project Summary Not Exist</value>
	</data>
	<data name="GPS01" xml:space="preserve">
		<value>Location Not Exist</value>
	</data>
	<data name="GPS02" xml:space="preserve">
		<value>Location info invalid</value>
	</data>
	<data name="HOL01" xml:space="preserve">
		<value>Holiday Not Exist</value>
	</data>
	<data name="HOL02" xml:space="preserve">
		<value>Holiday create failed</value>
	</data>
	<data name="HOL03" xml:space="preserve">
		<value>Holiday update failed</value>
	</data>
	<data name="URF01" xml:space="preserve">
		<value>Functional Role Not Exist</value>
	</data>
	<data name="URF02" xml:space="preserve">
		<value>Functional role create failed</value>
	</data>
	<data name="URF03" xml:space="preserve">
		<value>Functional role update failed</value>
	</data>
	<data name="FNC01" xml:space="preserve">
		<value>Function Not Exist</value>
	</data>
	<data name="FNC02" xml:space="preserve">
		<value>Function create failed</value>
	</data>
	<data name="FNC03" xml:space="preserve">
		<value>Function update failed</value>
	</data>
	<data name="HST01" xml:space="preserve">
		<value>Tracking history retrieve failed</value>
	</data>
	<data name="HST02" xml:space="preserve">
		<value>Tracking history create failed</value>
	</data>
	<data name="MIO01" xml:space="preserve">
		<value>Bucket Not Exist</value>
	</data>
	<data name="MIO02" xml:space="preserve">
		<value>Object Not Exist</value>
	</data>
	<data name="MIO03" xml:space="preserve">
		<value>Object Metadata Not Exist</value>
	</data>
	<data name="MIO04" xml:space="preserve">
		<value>Object upload failed</value>
	</data>
	<data name="MIO05" xml:space="preserve">
		<value>Object download failed</value>
	</data>
	<data name="MIO06" xml:space="preserve">
		<value>Object create failed</value>
	</data>
	<data name="MIO07" xml:space="preserve">
		<value>Object delete failed</value>
	</data>
	<data name="RPT01" xml:space="preserve">
		<value>Attendance report create failed</value>
	</data>
	<data name="RPT02" xml:space="preserve">
		<value>Attendance report update failed</value>
	</data>
	<data name="RPT03" xml:space="preserve">
		<value>Attendance report delete failed</value>
	</data>
	<data name="RPT04" xml:space="preserve">
		<value>Attendance report has already been approved, cannot update</value>
	</data>
	<data name="RPT05" xml:space="preserve">
		<value>Project Daily Report Already Exists</value>
	</data>
	<data name="RPT06" xml:space="preserve">
		<value>Project Daily Report Not Exist</value>
	</data>
	<data name="RPT07" xml:space="preserve">
		<value>Project Daily Report Create Failed</value>
	</data>
	<data name="RPT08" xml:space="preserve">
		<value>Project Daily Report Update Failed</value>
	</data>
	<data name="RPT09" xml:space="preserve">
		<value>Project Daily Report Delete Failed</value>
	</data>
	<data name="CTG01" xml:space="preserve">
		<value>Category Not Exist</value>
	</data>
	<data name="CTG02" xml:space="preserve">
		<value>Category create failed</value>
	</data>
	<data name="CTG03" xml:space="preserve">
		<value>Category update failed</value>
	</data>
	<data name="CTG04" xml:space="preserve">
		<value>Category Delete Failed</value>
	</data>
	<data name="VDR01" xml:space="preserve">
		<value>Vendor Not Exist</value>
	</data>
	<data name="VDR02" xml:space="preserve">
		<value>Vendor create failed</value>
	</data>
	<data name="VDR03" xml:space="preserve">
		<value>Vendor update failed</value>
	</data>
	<data name="VDR04" xml:space="preserve">
		<value>Vendor Delete Failed</value>
	</data>
	<data name="VDR05" xml:space="preserve">
		<value>Vendor Logo Not Found</value>
	</data>
	<data name="PRS01" xml:space="preserve">
		<value>Process Not Exist</value>
	</data>
	<data name="PRS02" xml:space="preserve">
		<value>Process create failed</value>
	</data>
	<data name="PRS03" xml:space="preserve">
		<value>Process update failed</value>
	</data>
	<data name="IT01" xml:space="preserve">
		<value>Item Not Exist</value>
	</data>
	<data name="IT02" xml:space="preserve">
		<value>Item create failed</value>
	</data>
	<data name="IT03" xml:space="preserve">
		<value>Item update failed</value>
	</data>
	<data name="IT04" xml:space="preserve">
		<value>Item Delete Failed</value>
	</data>
	<data name="IT05" xml:space="preserve">
		<value>Item Illustration Not Exist</value>
	</data>
	<data name="ITP01" xml:space="preserve">
		<value>Item Price Not Exist</value>
	</data>
	<data name="ITP02" xml:space="preserve">
		<value>Item Price Create Failed</value>
	</data>
	<data name="ITP03" xml:space="preserve">
		<value>Item Price Update Failed</value>
	</data>
	<data name="IPC01" xml:space="preserve">
		<value>Input Cost Not Exist</value>
	</data>
	<data name="IPC02" xml:space="preserve">
		<value>Input cost create failed</value>
	</data>
	<data name="IPC03" xml:space="preserve">
		<value>Input cost update failed</value>
	</data>
	<data name="IPC04" xml:space="preserve">
		<value>Input cost delete failed</value>
	</data>
	<data name="ET01" xml:space="preserve">
		<value>Entry Type Not Exist</value>
	</data>
	<data name="ET02" xml:space="preserve">
		<value>Entry type create failed</value>
	</data>
	<data name="ET03" xml:space="preserve">
		<value>Entry type update failed</value>
	</data>
	<data name="ET04" xml:space="preserve">
		<value>Entry Type Delete Failed</value>
	</data>
	<data name="MFTR01" xml:space="preserve">
		<value>Manufacturer Not Exist</value>
	</data>
	<data name="MFTR02" xml:space="preserve">
		<value>Manufacturer create failed</value>
	</data>
	<data name="MFTR03" xml:space="preserve">
		<value>Manufacturer update failed</value>
	</data>
	<data name="MFTR04" xml:space="preserve">
		<value>Manufacturer Delete Failed</value>
	</data>
	<data name="MFTR05" xml:space="preserve">
		<value>Manufacturer Logo Not Found</value>
	</data>
	<data name="IPCI01" xml:space="preserve">
		<value>Input Cost Item Not Exist</value>
	</data>
	<data name="IPCI02" xml:space="preserve">
		<value>Input cost item create failed</value>
	</data>
	<data name="IPCI03" xml:space="preserve">
		<value>Input cost item update failed</value>
	</data>
	<data name="IPCI04" xml:space="preserve">
		<value>Input cost item delete failed</value>
	</data>
	<data name="PMT01" xml:space="preserve">
		<value>Payment Type Not Found</value>
	</data>
	<data name="PMT02" xml:space="preserve">
		<value>Payment Type Create Failed</value>
	</data>
	<data name="PMT03" xml:space="preserve">
		<value>Payment Type Update Failed</value>
	</data>
	<data name="PMT04" xml:space="preserve">
		<value>Payment Type Delete Failed</value>
	</data>
	<data name="AC01" xml:space="preserve">
		<value>Account Already Exists</value>
	</data>
	<data name="AC02" xml:space="preserve">
		<value>Account Not Exist</value>
	</data>
	<data name="AC03" xml:space="preserve">
		<value>Account Create Failed</value>
	</data>
	<data name="AC04" xml:space="preserve">
		<value>Account Update Failed</value>
	</data>
	<data name="AC05" xml:space="preserve">
		<value>Account Delete Failed</value>
	</data>
	<data name="AC06" xml:space="preserve">
		<value>Account Lock Failed</value>
	</data>
	<data name="AC07" xml:space="preserve">
		<value>Account Unlock Failed</value>
	</data>
	<data name="AC08" xml:space="preserve">
		<value>Email or Login ID Already Exists</value>
	</data>
	<data name="USI01" xml:space="preserve">
		<value>User Info Not Exist</value>
	</data>
	<data name="USI02" xml:space="preserve">
		<value>User Info Already Exist</value>
	</data>
	<data name="USI03" xml:space="preserve">
		<value>User Info Create Failed</value>
	</data>
	<data name="USI04" xml:space="preserve">
		<value>User Info Update Failed</value>
	</data>
	<data name="USI05" xml:space="preserve">
		<value>User Info Delete Failed</value>
	</data>
	<data name="USI06" xml:space="preserve">
		<value>User Info Avatar Not Set</value>
	</data>
	<data name="USI07" xml:space="preserve">
		<value>User Info Avatar Update Failed</value>
	</data>
	<data name="USI08" xml:space="preserve">
		<value>User Info Avatar Delete Failed</value>
	</data>
	<data name="EMP01" xml:space="preserve">
		<value>Employee Not Exist</value>
	</data>
	<data name="EMP02" xml:space="preserve">
		<value>Employee Already Exist</value>
	</data>
	<data name="EMP03" xml:space="preserve">
		<value>Employee Create Failed</value>
	</data>
	<data name="EMP04" xml:space="preserve">
		<value>Employee Update Failed</value>
	</data>
	<data name="EMP05" xml:space="preserve">
		<value>Employee Delete Failed</value>
	</data>
	<data name="EMP06" xml:space="preserve">
		<value>Employee Invitation Failed</value>
	</data>
	<data name="EMP07" xml:space="preserve">
		<value>Employee Invitation Not Exist</value>
	</data>
	<data name="EMP08" xml:space="preserve">
		<value>Employee Invitation Delete Failed</value>
	</data>
	<data name="EMP09" xml:space="preserve">
		<value>Employee Invitation Accept Failed</value>
	</data>
	<data name="EMP10" xml:space="preserve">
		<value>Employee Invitation Reject Failed</value>
	</data>
	<data name="EMP11" xml:space="preserve">
		<value>Employee Invitation Already Exist</value>
	</data>
	<data name="EMP12" xml:space="preserve">
		<value>Link account with organization failed</value>
	</data>
	<data name="FIL01" xml:space="preserve">
		<value>File Upload Failed</value>
	</data>
	<data name="FIL02" xml:space="preserve">
		<value>File Download Failed</value>
	</data>
	<data name="FIL03" xml:space="preserve">
		<value>File Delete Failed</value>
	</data>
	<data name="FIL04" xml:space="preserve">
		<value>File Metadata Not Exist</value>
	</data>
	<data name="FIL05" xml:space="preserve">
		<value>File Metadata Create Failed</value>
	</data>
	<data name="FIL06" xml:space="preserve">
		<value>File Metadata Update Failed</value>
	</data>
	<data name="WOS01" xml:space="preserve">
		<value>Work Shift Not Exist</value>
	</data>
	<data name="WOS02" xml:space="preserve">
		<value>Work Shift Create Failed</value>
	</data>
	<data name="WOS03" xml:space="preserve">
		<value>Work Shift Update Failed</value>
	</data>
	<data name="WOS04" xml:space="preserve">
		<value>Work Shift Delete Failed</value>
	</data>
	<data name="WOS05" xml:space="preserve">
		<value>Work Shift Assign Failed</value>
	</data>
	<data name="WOS06" xml:space="preserve">
		<value>Work Shift Break Time Invalid</value>
	</data>
	<data name="WOS07" xml:space="preserve">
		<value>There is no work shift have been assigned to the working project</value>
	</data>
	<data name="EVC01" xml:space="preserve">
		<value>Event calendar does not exist</value>
	</data>
	<data name="EVC02" xml:space="preserve">
		<value>Event calendar creation failed</value>
	</data>
	<data name="EVC03" xml:space="preserve">
		<value>Event calendar update failed</value>
	</data>
	<data name="EVC04" xml:space="preserve">
		<value>Event calendar deletion failed</value>
	</data>
	<data name="EVC05" xml:space="preserve">
		<value>Event calendar date is invalid</value>
	</data>
	<data name="EVC06" xml:space="preserve">
		<value>Event calendar already exists</value>
	</data>
	<data name="UNT01" xml:space="preserve">
		<value>Unit Not Exist</value>
	</data>
	<data name="UNT02" xml:space="preserve">
		<value>Unit Create Failed</value>
	</data>
	<data name="UNT03" xml:space="preserve">
		<value>Unit Update Failed</value>
	</data>
	<data name="UNT04" xml:space="preserve">
		<value>Unit Delete Failed</value>
	</data>
	<data name="OS01" xml:space="preserve">
		<value>Outsource Not Exist</value>
	</data>
	<data name="OS02" xml:space="preserve">
		<value>Outsource Create Failed</value>
	</data>
	<data name="OS03" xml:space="preserve">
		<value>Outsource Update Failed</value>
	</data>
	<data name="OS04" xml:space="preserve">
		<value>Outsource Delete Failed</value>
	</data>
	<data name="OS05" xml:space="preserve">
		<value>Outsource Price Not Exist</value>
	</data>
	<data name="OS06" xml:space="preserve">
		<value>Outsource Price Create Failed</value>
	</data>
	<data name="OS07" xml:space="preserve">
		<value>Outsource Price Update Failed</value>
	</data>
	<data name="OS08" xml:space="preserve">
		<value>Outsource Logo Not Found</value>
	</data>
	<data name="PRT01" xml:space="preserve">
		<value>Project Type Not Exist</value>
	</data>
	<data name="PRT02" xml:space="preserve">
		<value>Project Type Create Failed</value>
	</data>
	<data name="PRT03" xml:space="preserve">
		<value>Project Type Update Failed</value>
	</data>
	<data name="CON01" xml:space="preserve">
		<value>Construction Not Exist</value>
	</data>
	<data name="CON02" xml:space="preserve">
		<value>Construction Create Failed</value>
	</data>
	<data name="CON03" xml:space="preserve">
		<value>Construction Update Failed</value>
	</data>
	<data name="CON04" xml:space="preserve">
		<value>Construction Delete Failed</value>
	</data>
	<data name="CON05" xml:space="preserve">
		<value>Sub Construction Already Exist</value>
	</data>
	<data name="CONC01" xml:space="preserve">
		<value>Construction Cost Not Exist</value>
	</data>
	<data name="CONC02" xml:space="preserve">
		<value>Construction Cost Create Failed</value>
	</data>
	<data name="CONC03" xml:space="preserve">
		<value>Construction Cost Update Failed</value>
	</data>
	<data name="CONC04" xml:space="preserve">
		<value>Construction Cost Delete Failed</value>
	</data>
	<data name="CUS01" xml:space="preserve">
		<value>Customer Not Exist</value>
	</data>
	<data name="CUS02" xml:space="preserve">
		<value>Customer Create Failed</value>
	</data>
	<data name="CUS03" xml:space="preserve">
		<value>Customer Update Failed</value>
	</data>
	<data name="CUS04" xml:space="preserve">
		<value>Customer Delete Failed</value>
	</data>
	<data name="CUS05" xml:space="preserve">
		<value>Customer Logo Not Found</value>
	</data>	
	<data name="CTR01" xml:space="preserve">
		<value>Contractor Not Exist</value>
	</data>
	<data name="CTR02" xml:space="preserve">
		<value>Contractor Create Failed</value>
	</data>
	<data name="CTR03" xml:space="preserve">
		<value>Contractor Update Failed</value>
	</data>
	<data name="CTR04" xml:space="preserve">
		<value>Contractor Delete Failed</value>
	</data>
	<data name="CTR05" xml:space="preserve">
		<value>Contractor Logo Not Found</value>
	</data>
	<data name="CT01" xml:space="preserve">
		<value>Customer Type Not Exist</value>
	</data>
	<data name="AUD01" xml:space="preserve">
		<value>No audit log found</value>
	</data>
</root>