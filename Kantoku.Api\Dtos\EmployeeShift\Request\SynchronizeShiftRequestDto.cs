using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.EmployeeShift.Request;

/// <summary>
/// Request DTO for synchronizing shifts
/// </summary>
public class SynchronizeShiftRequestDto
{
    /// <summary>
    /// Collection of shift items to synchronize
    /// </summary>
    [Required]
    public IEnumerable<SynchronizeShiftRequestItem> Items { get; set; } = [];
}

/// <summary>
/// Individual shift item for synchronization
/// </summary>
public class SynchronizeShiftRequestItem
{
    /// <summary>
    /// (*) ID of the employee this shift belongs to
    /// </summary>
    public Guid? EmployeeShiftId { get; set; }

    /// <summary>
    /// (*) ID of the project this shift belongs to
    /// </summary>
    public required Guid ProjectId { get; set; }

    /// <summary>
    /// Location where the work was performed
    /// </summary>
    public string? WorkingLocation { get; set; }

    /// <summary>
    /// Time when the shift started
    /// </summary>
    [DateTimeValidator(typeof(DateTime))]
    public string? CheckInTime { get; set; }

    /// <summary>
    /// Gps coordinate of the work location when the shift started
    /// </summary>
    public GpsCoordinate? CheckInCoordinate { get; set; }

    /// <summary>
    /// Time when the shift ended
    /// </summary>
    [DateTimeValidator(typeof(DateTime))]
    public string? CheckOutTime { get; set; }

    /// <summary>
    /// Gps coordinate of the work location when the shift ended
    /// </summary>
    public GpsCoordinate? CheckOutCoordinate { get; set; }

    /// <summary>
    /// Collection of break periods during the shift
    /// </summary>
    public IEnumerable<CreateBreakItem>? Breaks { get; set; }

    /// <summary>
    /// Additional notes or description about the shift
    /// </summary>
    public string? Description { get; set; }
}

/// <summary>
/// Break period during a shift
/// </summary>
public class CreateBreakItem
{
    /// <summary>
    /// Time when the break started
    /// </summary>
    [DateTimeValidator(typeof(DateTime))]
    public string? BreakInTime { get; set; }

    /// <summary>
    /// Time when the break ended
    /// </summary>
    [DateTimeValidator(typeof(DateTime))]
    public string? BreakOutTime { get; set; }
}

public class GpsCoordinate
{
    /// <summary>
    /// Latitude coordinate of the work location
    /// </summary>
    public string? Latitude { get; set; }

    /// <summary>
    /// Longitude coordinate of the work location
    /// </summary>
    public string? Longitude { get; set; }
}