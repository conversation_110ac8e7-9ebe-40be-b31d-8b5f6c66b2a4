﻿using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Dtos.InputCost.Request;
using Kantoku.Api.Dtos.InputCost.Response;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Controllers;

[Route("api/v1/cost/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class InputCostController : BaseController
{
    private readonly IInputCostService inputCostService;
    private readonly IAuditLogService auditLogService;
    public InputCostController(IInputCostService inputCostService,
        ITResponseFactory responseFactory,
        IAuditLogService auditLogService)
        : base(responseFactory)
    {
        this.inputCostService = inputCostService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get input costs by filter
    /// </summary>
    /// <param name="filter">Filter parameters</param>
    /// <returns>List of input costs</returns>
    [HttpGet]
    public async Task<GeneralResponse<InputCostsResponseDto>> GetByFilter([FromQuery] InputCostFilter filter)
    {
        try
        {
            var result = await inputCostService.GetByFilter(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<InputCostsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get input cost by ID
    /// </summary>
    /// <param name="id">Input cost ID</param>
    /// <returns>Input cost details</returns>
    [HttpGet("{id}")]
    public async Task<GeneralResponse<DetailedInputCostResponseDto>> GetById([FromRoute] Guid id)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<DetailedInputCostResponseDto>();

            var result = await inputCostService.GetById(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<DetailedInputCostResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Download images for input cost
    /// </summary>
    /// <param name="imageUrl">Image URL</param>
    /// <param name="orgId">Organization ID</param>
    /// <returns>List of uploaded image URLs</returns>
    [AllowAnonymous]
    [HttpGet("images")]
    public async Task<dynamic> DownloadImages([FromQuery] string imageUrl, [FromQuery] string orgId)
    {
        try
        {
            var result = await inputCostService.GetImage(imageUrl, orgId);
            if (result.Data is null || result.Data.Length == 0)
            {
                return Fail<bool>(result.ErrorCode ?? ResponseCodeConstant.NOT_EXIST);
            }
            return File(result.Data, "image/jpeg");
        }
        catch (BusinessException)
        {
            return Fail<bool>(ResponseCodeConstant.NOT_EXIST);
        }
    }

    /// <summary>
    /// Create new input cost
    /// </summary>
    /// <param name="requestDto">Input cost creation data</param>
    /// <returns>Created input cost details</returns>
    [HttpPost]
    [Consumes("multipart/form-data")]
    public async Task<GeneralResponse<DetailedInputCostResponseDto>> Create([FromForm] CreateInputCostRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<DetailedInputCostResponseDto>();

            var result = await inputCostService.Create(requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<DetailedInputCostResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update existing input cost
    /// </summary>
    /// <param name="id">Input cost ID</param>
    /// <param name="requestDto">Input cost update data</param>
    /// <returns>Updated input cost details</returns>
    [HttpPut("{id}")]
    [Consumes("multipart/form-data")]
    public async Task<GeneralResponse<DetailedInputCostResponseDto>> Update([FromRoute] Guid id, [FromForm] UpdateInputCostRequestDto requestDto)
    {
        try
        {
            var result = await inputCostService.Update(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<DetailedInputCostResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete input cost
    /// </summary>
    /// <param name="id">Input cost ID</param>
    /// <returns>Success response</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> Delete([FromRoute] Guid id)
    {
        try
        {
            await inputCostService.Delete(id);
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Download images for input cost
    /// </summary>
    /// <param name="id">Input cost ID</param>
    /// <param name="imageUrl">Image URL</param>
    /// <returns>List of uploaded image URLs</returns>
    [HttpDelete("{id}/image")]
    public async Task<dynamic> DeleteImage([FromRoute] Guid id, [FromQuery] string imageUrl)
    {
        try
        {
            var result = await inputCostService.DeleteImage(id, imageUrl);
            return SuccessFromResult(result);
        }
        catch (BusinessException)
        {
            return Fail<bool>(ResponseCodeConstant.NOT_EXIST);
        }
    }

    /// <summary>
    /// Get audit logs for input cost
    /// </summary>
    /// <param name="id">Input cost ID</param>
    /// <param name="filter">Filter parameters</param>
    /// <returns>List of audit logs</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetAuditLogByEntity([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<InputCost>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
