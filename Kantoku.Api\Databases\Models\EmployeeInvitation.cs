using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Models;

public class EmployeeInvitation : AuditableEntity
{
    public Guid? EmployeeInvitationUid { get; set; }
    public Guid OrgUid { get; set; }

    public string Email { get; set; } = null!;

    [AuditProperty]
    public string PreassignedEmployeeCode { get; set; } = null!;

    [AuditProperty]
    public ICollection<Guid>? PreassignedRoleUid { get; set; }

    [AuditProperty]
    public string? InvitationDescription { get; set; }

    [AuditProperty]
    public DateTime? ExpiredTime { get; set; }

    public bool IsAccepted { get; set; }
    public DateTime? AcceptedTime { get; set; }

    public bool IsDeleted { get; set; }
    public virtual Org Org { get; set; } = null!;
}
