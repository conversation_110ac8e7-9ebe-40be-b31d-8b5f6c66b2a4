using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.Schedule.Response;

public class ProjectScheduleResponsesDto
{
    [JsonPropertyName("items")]
    public IEnumerable<ProjectScheduleResponseDto> Items { get; set; } = [];
}

public class ProjectScheduleResponseDto
{
    [JsonPropertyName("scheduleId")]
    public string? ProjectScheduleId { get; set; }

    [JsonPropertyName("projectId")]
    public string? ProjectId { get; set; }

    [JsonPropertyName("workingDate")]
    public string? WorkingDate { get; set; }

    [JsonPropertyName("totalPlannedWorkload")]
    public float? PlannedWorkload { get; set; }

    [JsonPropertyName("totalEstimatedWorkload")]
    public float? EstimatedWorkload { get; set; }

    [JsonPropertyName("totalActualWorkload")]
    public float? ActualWorkload { get; set; }

    [JsonPropertyName("employeeShifts")]
    public IEnumerable<ScheduledEmployeeShiftResponseDto> EmployeeShifts { get; set; } = [];

    [JsonPropertyName("outSourceShifts")]
    public IEnumerable<ScheduledOutSourceShiftResponseDto> OutSourceShifts { get; set; } = [];
}

