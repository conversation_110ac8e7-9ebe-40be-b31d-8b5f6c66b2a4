﻿using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.Item.Request;

/// <summary>
/// Data transfer object for creating a new item
/// </summary>
public class CreateItemRequestDto
{
    /// <summary>
    /// The code of the item (*)
    /// </summary>
    [Required]
    public string ItemCode { get; set; } = null!;

    /// <summary>
    /// The name of the item (*)
    /// </summary>
    [Required]
    public string ItemName { get; set; } = null!;

    /// <summary>
    /// The sub name of the item
    /// </summary>
    public string? ItemSubName { get; set; }

    /// <summary>
    /// The size of the item
    /// </summary>
    public string? Size { get; set; }

    /// <summary>
    /// The serial number of the item
    /// </summary>
    public string? SerialNumber { get; set; }

    /// <summary>
    /// The category ID of the item (*)
    /// </summary>
    [Required]
    public string CategoryId { get; set; } = null!;

    /// <summary>
    /// The manufacturer ID of the item
    /// </summary>
    public string? ManufacturerId { get; set; }

    /// <summary>
    /// Additional description or notes about the item
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// The image of the item, if applicable.
    /// </summary>
    public IFormFile? Image { get; set; }
}
