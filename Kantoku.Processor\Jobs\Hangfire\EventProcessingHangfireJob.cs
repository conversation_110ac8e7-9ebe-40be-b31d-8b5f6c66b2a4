using EventBus.Events;
using EventBus.Interfaces;
using Kantoku.Processor.Jobs.Hangfire;
using Kantoku.Processor.Services.EventHandlers;

namespace Kantoku.Processor.Jobs.Hangfire;

/// <summary>
/// Hangfire job that processes events from Kafka
/// This replaces the EventBusHostedService for event processing
/// </summary>
public class EventProcessingHangfireJob : HangfireJobBase
{
    public EventProcessingHangfireJob(ILogger<EventProcessingHangfireJob> logger, IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public override async Task ExecuteAsync(CancellationToken cancellationToken = default)
    {
        LogInformation("Starting event processing job");

        using var scope = CreateScope();
        var eventConsumer = scope.ServiceProvider.GetRequiredService<IEventConsumer>();

        try
        {
            // Subscribe to events
            eventConsumer.Subscribe<AttendanceEvent, AttendanceEventHandler>();
            
            LogInformation("Subscribed to AttendanceEvent");

            // Start the consumer
            await eventConsumer.StartAsync(cancellationToken);
            
            LogInformation("Event consumer started successfully");

            // Keep processing until cancellation is requested
            // This job will run continuously to process events
            try
            {
                await Task.Delay(Timeout.Infinite, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                LogInformation("Event processing job cancellation requested");
            }
        }
        catch (OperationCanceledException)
        {
            LogWarning("Event processing job was cancelled");
            throw;
        }
        catch (Exception ex)
        {
            LogError("Error in event processing job: {ErrorMessage}", ex, ex.Message);
            throw;
        }
        finally
        {
            try
            {
                // Stop the consumer
                await eventConsumer.StopAsync(CancellationToken.None);
                LogInformation("Event consumer stopped");
            }
            catch (Exception ex)
            {
                LogError("Error stopping event consumer: {ErrorMessage}", ex, ex.Message);
            }
        }
    }
}
