using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.FunctionAccessibility.Response;

public class SimpleFunctionPrivilegesResponseDto
{
    [JsonPropertyName("Items")]
    public IEnumerable<SimpleFunctionPrivilegeResponseDto>? Items { get; set; }
}

public class SimpleFunctionPrivilegeResponseDto
{
    [JsonPropertyName("Id")]
    public string? FunctionId { get; set; }

    [Json<PERSON>ropertyName("Title")]
    public string? FunctionName { get; set; }

    [JsonPropertyName("CanRead")]
    public bool? CanRead { get; set; }

    [Json<PERSON>ropertyName("CanCreate")]
    public bool? CanCreate { get; set; }

    [J<PERSON><PERSON>ropertyName("CanUpdate")]
    public bool? CanUpdate { get; set; }

    [Json<PERSON>ropertyName("CanDelete")]
    public bool? CanDelete { get; set; }
}
