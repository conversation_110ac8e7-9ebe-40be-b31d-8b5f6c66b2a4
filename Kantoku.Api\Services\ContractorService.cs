using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Dtos.Contractor.Request;
using Kantoku.Api.Dtos.Contractor.Response;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Dtos.File;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public interface IContractorService
{
    Task<ResultDto<ContractorsResponseDto>> GetContractorByFilter(ContractorFilter filter);
    Task<ResultDto<byte[]>> GetLogo(Guid contractorId, string orgId);
    Task<ResultDto<ContractorDetailResponseDto>> GetContractorById(Guid contractorId, string? keyword, int pageNum, int pageSize);
    Task<ResultDto<ContractorResponseDto>> CreateContractor(CreateContractorRequestDto requestDto);
    Task<ResultDto<ContractorResponseDto>> UpdateContractor(Guid contractorId, UpdateContractorRequestDto requestDto);
    Task<ResultDto<bool>> DeleteContractor(Guid contractorId);
}

[Service(ServiceLifetime.Scoped)]
public class ContractorService : BaseService<ContractorService>, IContractorService
{
    private readonly IContractorRepository contractorRepository;
    private readonly IProjectRepository projectRepository;
    private readonly IFileService fileService;
    public ContractorService(
        IContractorRepository contractorRepository,
        IProjectRepository projectRepository,
        IFileService fileService,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor) : base(logger, httpContextAccessor)
    {
        this.contractorRepository = contractorRepository;
        this.projectRepository = projectRepository;
        this.fileService = fileService;
    }

    public async Task<ResultDto<ContractorsResponseDto>> GetContractorByFilter(ContractorFilter filter)
    {
        var options = new ContractorQueryableOptions();
        var (contractors, total) = await contractorRepository.GetByFilter(filter, options);
        if (contractors is null || !contractors.Any() || total == 0)
        {
            logger.Information("No contractors found");
            return new ErrorResultDto<ContractorsResponseDto>(ResponseCodeConstant.CONTRACTOR_NOT_EXIST);
        }

        var res = contractors.ToContractorsResponseDto(filter.PageNum, filter.PageSize, total);
        return new SuccessResultDto<ContractorsResponseDto>(res);
    }

    public async Task<ResultDto<ContractorDetailResponseDto>> GetContractorById(Guid contractorId, string? keyword, int pageNum, int pageSize)
    {
        var contractor = await contractorRepository.GetById(contractorId, new ContractorQueryableOptions
        {
            IncludedProject = true,
        });
        if (contractor is null)
        {
            logger.Error("Contractor {ContractorId} not found", contractorId);
            return new ErrorResultDto<ContractorDetailResponseDto>(ResponseCodeConstant.CONTRACTOR_NOT_EXIST);
        }
        var (projects, total) = await projectRepository.GetByFilter(new ProjectFilter
        {
            ContractorUid = contractorId,
            Keyword = keyword,
            PageNum = pageNum,
            PageSize = pageSize
        }, new ProjectQueryableOptions());
        var res = contractor.ToContractorDetailResponseDto(projects, pageNum, pageSize, total);
        return new SuccessResultDto<ContractorDetailResponseDto>(res);
    }

    public async Task<ResultDto<byte[]>> GetLogo(Guid contractorId, string orgId)
    {
        var contractor = await contractorRepository.GetById(contractorId, new ContractorQueryableOptions());
        if (contractor is null)
        {
            logger.Error("Contractor {ContractorId} not found", contractorId);
            return new ErrorResultDto<byte[]>(ResponseCodeConstant.CONTRACTOR_NOT_EXIST);
        }
        if (contractor.LogoUrl is null)
        {
            logger.Error("Contractor {ContractorId} has no logo", contractorId);
            return new ErrorResultDto<byte[]>(ResponseCodeConstant.CONTRACTOR_LOGO_NOT_EXIST);
        }
        var logo = await DownloadLogo(contractor.LogoUrl, orgId);
        if (logo is null || logo.Length == 0)
        {
            logger.Error("Contractor {ContractorId} logo not found", contractorId);
            return new ErrorResultDto<byte[]>(ResponseCodeConstant.CONTRACTOR_LOGO_NOT_EXIST);
        }
        return new SuccessResultDto<byte[]>(logo);
    }

    public async Task<ResultDto<ContractorResponseDto>> CreateContractor(CreateContractorRequestDto requestDto)
    {
        if (!GetCurrentOrgGuid(out var orgUid))
        {
            logger.Error("Failed to get current org uid");
            return new ErrorResultDto<ContractorResponseDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        var newContractor = requestDto.ToEntity(orgUid);
        if (newContractor is null)
        {
            logger.Error("Failed to create contractor");
            return new ErrorResultDto<ContractorResponseDto>(ResponseCodeConstant.CONTRACTOR_CREATE_FAILED);
        }
        if (requestDto.Logo is not null)
        {
            var fileMetadata = await UploadLogo(newContractor.ContractorUid.ToString(), requestDto.Logo);
            if (fileMetadata is not null)
            {
                newContractor.LogoUrl = fileMetadata.FileUrl;
            }
        }
        var createdContractor = await contractorRepository.Create(newContractor);
        if (createdContractor is null)
        {
            logger.Error("Failed to create contractor");
            return new ErrorResultDto<ContractorResponseDto>(ResponseCodeConstant.CONTRACTOR_CREATE_FAILED);
        }

        var result = createdContractor.ToContractorResponseDto();
        return new SuccessResultDto<ContractorResponseDto>(result);
    }

    public async Task<ResultDto<ContractorResponseDto>> UpdateContractor(Guid contractorId, UpdateContractorRequestDto requestDto)
    {
        var existContractor = await contractorRepository.GetById(contractorId, new ContractorQueryableOptions());
        if (existContractor is null)
        {
            logger.Error("Contractor {ContractorId} not found", contractorId);
            return new ErrorResultDto<ContractorResponseDto>(ResponseCodeConstant.CONTRACTOR_NOT_EXIST);
        }

        existContractor.UpdateFromDto(requestDto);

        if (requestDto.Logo is not null)
        {
            var fileMetadata = await UploadLogo(existContractor.ContractorUid.ToString(), requestDto.Logo);
            if (fileMetadata is not null)
            {
                existContractor.LogoUrl = fileMetadata.FileUrl;
            }
        }
        var updatedContractor = await contractorRepository.Update(existContractor);
        if (updatedContractor is null)
        {
            logger.Error("Failed to update contractor {ContractorId}", contractorId);
            return new ErrorResultDto<ContractorResponseDto>(ResponseCodeConstant.CONTRACTOR_UPDATE_FAILED);
        }
        var result = updatedContractor.ToContractorResponseDto();
        return new SuccessResultDto<ContractorResponseDto>(result);
    }

    public async Task<ResultDto<bool>> DeleteContractor(Guid contractorId)
    {
        var contractor = await contractorRepository.GetById(contractorId, new ContractorQueryableOptions());
        if (contractor is null)
        {
            logger.Error("Contractor {ContractorId} not found", contractorId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.CONTRACTOR_NOT_EXIST, false);
        }
        contractor.IsDeleted = true;
        var isDeleted = await contractorRepository.Update(contractor);
        if (isDeleted is null)
        {
            logger.Error("Failed to delete contractor {ContractorId}", contractorId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.CONTRACTOR_DELETE_FAILED, false);
        }
        return new SuccessResultDto<bool>(true);
    }

    private async Task<FileMetadataResponseDto?> UploadLogo(string contractorUid, IFormFile logo)
    {
        try
        {
            var path = StorageConstant.ContractorLogo(contractorUid);
            var fileName = "logo.jpg";
            var objectName = path + fileName;
            var fileMetadata = await fileService.UploadFileAsync(logo, objectName);
            if (fileMetadata is null || fileMetadata.Data is null)
            {
                logger.Error("Upload logo failed");
                return null;
            }
            return fileMetadata.Data;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error uploading logo");
            return new FileMetadataResponseDto();
        }
    }

    private async Task<byte[]?> DownloadLogo(string logoUrl, string orgId)
    {
        try
        {
            var file = await fileService.DownloadFile(logoUrl, orgId);
            if (file is null || file.Data is null)
            {
                logger.Error("Logo not found");
                return null;
            }
            return file.Data.DataAsBytes;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting logo as base64");
            return null;
        }
    }
}
