﻿namespace Kantoku.Api.Dtos.Item
{
    /// <summary>
    /// Response DTO for items with pagination
    /// </summary>
    public class ItemsResponseDto
    {
        /// <summary>
        /// Collection of item response DTOs
        /// </summary>
        public IEnumerable<ItemResponseDto> Items { get; set; } = [];

        /// <summary>
        /// Current page number (*)
        /// </summary>
        public int PageNum { get; set; }

        /// <summary>
        /// Number of items per page (*)
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// Total number of records available (*)
        /// </summary>
        public int TotalRecords { get; set; }
    }
}
