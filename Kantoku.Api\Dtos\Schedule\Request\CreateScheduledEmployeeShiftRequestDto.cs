using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.Schedule.Request;

public class CreateScheduledEmployeeShiftRequestDto
{
    /// <summary>
    /// (*) The unique identifier of the employee to assign the shift to
    /// </summary>
    [Required]
    [JsonPropertyName("employeeId")]
    public required Guid EmployeeId { get; set; }

    /// <summary>
    /// (*) The start time of the employee's shift
    /// Format: HH:mm:ss
    /// </summary>
    [Required]
    [JsonPropertyName("startTime")]
    public required TimeOnly StartTime { get; set; }

    /// <summary>
    /// (*) The end time of the employee's shift
    /// Format: HH:mm:ss
    /// </summary>
    [Required]
    [JsonPropertyName("endTime")]
    public required TimeOnly EndTime { get; set; }

    /// <summary>
    /// The required work time of the employee's shift (with no break time)
    /// used as metric to calculate actual workload
    /// </summary>
    [Required]
    [JsonPropertyName("totalScheduledWorkTime")]
    public float TotalScheduledWorkTime { get; set; }

    /// <summary>
    /// The role assigned to the employee for this shift
    /// The role has been assigned based on employee's ability, use as string code
    /// </summary>
    [JsonPropertyName("assignedRole")]
    public string? AssignedRole { get; set; }
}

public class CreateScheduledEmployeeShiftRequestDto2 : CreateScheduledEmployeeShiftRequestDto
{
    /// <summary>
    /// (*) The unique identifier of the project schedule to assign the shift to
    /// </summary>
    [Required]
    [JsonPropertyName("projectId")]
    public required Guid ProjectId { get; set; }

    /// <summary>
    /// (*) The working date of the shift
    /// Format: YYYY-MM-DD
    /// </summary>
    [Required]
    [JsonPropertyName("workingDate")]
    public required DateOnly WorkingDate { get; set; }
}

