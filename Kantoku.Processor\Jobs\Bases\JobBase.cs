using System.Text;
using Kantoku.Processor.Models.BatchJobManager;

namespace Kantoku.Processor.Jobs.Bases;

/// <summary>
/// Base class for all batch job implementations
/// </summary>
public abstract class JobBase : IJob
{
    private readonly StringBuilder _logBuilder = new StringBuilder();
    private ILogger? _logger;

    /// <summary>
    /// The batch job configuration from the database
    /// </summary>
    protected BatchJob JobConfiguration { get; private set; } = null!;

    /// <summary>
    /// The current job history record
    /// </summary>
    protected BatchJobHistory JobHistory { get; private set; } = null!;

    /// <summary>
    /// Cancellation token to check if the job should be cancelled
    /// </summary>
    protected CancellationToken CancellationToken { get; private set; }

    // New empty constructor for factory pattern
    protected JobBase()
    {
    }

    // Original constructor, kept for backward compatibility
    protected JobBase(BatchJob jobConfiguration, BatchJobHistory jobHistory, ILogger logger, CancellationToken cancellationToken)
    {
        JobConfiguration = jobConfiguration;
        JobHistory = jobHistory;
        _logger = logger;
        CancellationToken = cancellationToken;
    }

    /// <summary>
    /// Initializes the job with necessary context after construction
    /// </summary>
    public void Initialize(BatchJobHistory jobHistory, CancellationToken cancellationToken)
    {
        JobHistory = jobHistory;
        CancellationToken = cancellationToken;
    }

    /// <summary>
    /// Sets the logger for the job
    /// </summary>
    public void SetLogger(ILogger logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Main execution method for the job that needs to be implemented by all job classes
    /// </summary>
    public abstract Task ExecuteAsync();

    /// <summary>
    /// Adds a log entry to the job execution log
    /// </summary>
    protected void LogInformation(string message)
    {
        var timestampedMessage = $"[{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss.fff}] INFO: {message}";
        _logBuilder.AppendLine(timestampedMessage);
        _logger?.LogInformation("{JobName}: {Message}", JobConfiguration.JobName, message);
    }

    /// <summary>
    /// Adds a warning log entry to the job execution log
    /// </summary>
    protected void LogWarning(string message)
    {
        var timestampedMessage = $"[{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss.fff}] WARN: {message}";
        _logBuilder.AppendLine(timestampedMessage);
        _logger?.LogWarning("{JobName}: {Message}", JobConfiguration.JobName, message);
    }

    /// <summary>
    /// Adds an error log entry to the job execution log
    /// </summary>
    protected void LogError(string message, Exception? ex = null)
    {
        var timestampedMessage = $"[{DateTime.UtcNow:yyyy-MM-dd HH:mm:ss.fff}] ERROR: {message}";

        if (ex != null)
        {
            timestampedMessage += $"{Environment.NewLine}Exception: {ex.Message}{Environment.NewLine}Stack trace: {ex.StackTrace}";
        }

        _logBuilder.AppendLine(timestampedMessage);

        if (ex != null)
        {
            _logger?.LogError(ex, "{JobName}: {Message}", JobConfiguration.JobName, message);
        }
        else
        {
            _logger?.LogError("{JobName}: {Message}", JobConfiguration.JobName, message);
        }
    }

    /// <summary>
    /// Gets the complete execution log
    /// </summary>
    public string GetExecutionLog()
    {
        return _logBuilder.ToString();
    }

    /// <summary>
    /// Checks if the cancellation has been requested and throws an OperationCanceledException if it has
    /// </summary>
    protected void ThrowIfCancellationRequested()
    {
        CancellationToken.ThrowIfCancellationRequested();
    }

    /// <summary>
    /// Gets a typed configuration object from the job's Configuration property
    /// </summary>
    protected T? GetTypedConfiguration<T>() where T : class
    {
        return JobConfiguration.GetConfiguration<T>();
    }

    /// <summary>
    /// Sets the job configuration context
    /// </summary>
    public void SetJobContext(BatchJob jobConfiguration)
    {
        JobConfiguration = jobConfiguration;
    }
}
