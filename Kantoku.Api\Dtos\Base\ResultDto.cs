using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Dtos.Base;

public class ResultDto
{
    public string? ErrorCode { get; set; }
    public string? ErrorMessage { get; set; }
}

public class ResultDto<T> : ResultDto
{
    public T? Data { get; set; }
}

public class SuccessResultDto<T> : ResultDto<T>
{
    public SuccessResultDto(T data)
    {
        Data = data;
        ErrorCode = ResponseCodeConstant.SUCCESS;
    }
}

public class ErrorResultDto<T> : ResultDto<T>
{
    public ErrorResultDto(string errorCode)
    {
        ErrorCode = errorCode;
    }

    public ErrorResultDto(string errorCode, string errorMessage)
    {
        ErrorCode = errorCode;
        ErrorMessage = errorMessage;
    }

    public ErrorResultDto(string errorCode, T data)
    {
        ErrorCode = errorCode;
        Data = data;
    }
}


public class ReadResultDto<T> : ResultDto
{
    public T? Data { get; set; }

    public ReadResultDto(T data)
    {
        Data = data;
        ErrorCode = ResponseCodeConstant.SUCCESS;
    }

    public ReadResultDto(string errorCode)
    {
        Data = default;
        ErrorCode = errorCode;
    }

    public ReadResultDto(string errorCode, string errorMessage)
    {
        Data = default;
        ErrorCode = errorCode;
        ErrorMessage = errorMessage;
    }
}

public class WriteResultDto<T> : ResultDto
{
    public bool IsSuccess { get; set; }
}
