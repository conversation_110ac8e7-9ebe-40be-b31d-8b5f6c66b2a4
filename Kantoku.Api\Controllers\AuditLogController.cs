using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize]
public class AuditLogController : BaseController
{
    private readonly IAuditLogService auditLogService;

    public AuditLogController(
        ITResponseFactory responseFactory,
        IAuditLogService auditLogService
    ) : base(responseFactory)
    {
        this.auditLogService = auditLogService;
    }

    [HttpGet("account")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetAuditLogByAccount([FromQuery] string accountId, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<AuditLogResponseDto>();

            var result = await auditLogService.GetAuditLogsByAccount(accountId, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}