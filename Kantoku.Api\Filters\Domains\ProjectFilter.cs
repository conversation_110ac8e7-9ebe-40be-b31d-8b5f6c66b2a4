using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Filters.Domains;

public class ProjectFilter : BaseFilter
{
    [FromQuery(Name = "typeId")]
    public Guid? TypeId { get; set; }

    [FromQuery(Name = "statusCode")]
    public string? StatusCode { get; set; }

    [FromQuery(Name = "projectCode")]
    public string? ProjectCode { get; set; }

    [FromQuery(Name = "projectName")]
    public string? ProjectName { get; set; }

    [FromQuery(Name = "keyword")]
    public string? Keyword { get; set; }

    [FromQuery(Name = "exStartDate")]
    public string? ExStartDate { get; set; }

    [FromQuery(Name = "exEndDate")]
    public string? ExEndDate { get; set; }

    [FromQuery(Name = "actStartDate")]
    public string? ActStartDate { get; set; }

    [FromQuery(Name = "actEndDate")]
    public string? ActEndDate { get; set; }

    [FromQuery(Name = "budgetMin")]
    public float? BudgetMin { get; set; }

    [FromQuery(Name = "budgetMax")]
    public float? BudgetMax { get; set; }

    [FromQuery(Name = "costMin")]
    public float? CostMin { get; set; }

    [FromQuery(Name = "costMax")]
    public float? CostMax { get; set; }

    [FromQuery(Name = "contractorUid")]
    public Guid? ContractorUid { get; set; }

    [FromQuery(Name = "customerUid")]
    public Guid? CustomerUid { get; set; }
}
