﻿using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.Manufacturer.Request;
using Kantoku.Api.Dtos.Manufacturer.Response;
using Kantoku.Api.Dtos.File;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public interface IManufacturerService
{
    Task<ResultDto<ManufacturerDetailResponseDto>> GetById(Guid manufacturerId, string? keyword, int pageNum, int pageSize);
    Task<ResultDto<byte[]>> GetLogo(Guid manufacturerId, string orgId);
    Task<ResultDto<ManufacturersResponseDto>> GetByFilter(ManufacturerFilter filter);
    Task<ResultDto<ManufacturerResponseDto>> Create(CreateManufacturerRequestDto requestDto);
    Task<ResultDto<ManufacturerResponseDto>> Update(Guid manufacturerId, UpdateManufacturerRequestDto requestDto);
    Task<ResultDto<bool>> Delete(Guid manufacturerId);
}

[Service(ServiceLifetime.Scoped)]
public class ManufacturerService : BaseService<ManufacturerService>, IManufacturerService
{
    private readonly IManufacturerRepository manufacturerRepository;
    private readonly IItemRepository itemRepository;
    private readonly IFileService fileService;
    public ManufacturerService(
        IManufacturerRepository manufacturerRepository,
        IItemRepository itemRepository,
        IFileService fileService,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor
)
        : base(logger, httpContextAccessor)
    {
        this.manufacturerRepository = manufacturerRepository;
        this.itemRepository = itemRepository;
        this.fileService = fileService;
    }

    public async Task<ResultDto<ManufacturersResponseDto>> GetByFilter(ManufacturerFilter filter)
    {
        var (manufacturers, total) = await manufacturerRepository.GetByFilter(filter, new ManufacturerQueryableOptions());
        if (manufacturers is null || !manufacturers.Any() || total == 0)
        {
            logger.Error("Manufacturer not found");
            return new ErrorResultDto<ManufacturersResponseDto>(ResponseCodeConstant.MANUFACTURER_NOT_EXIST);
        }
        var res = manufacturers.ToManufacturersResponseDto(filter.PageNum, filter.PageSize, total);
        return new SuccessResultDto<ManufacturersResponseDto>(res);
    }

    public async Task<ResultDto<ManufacturerDetailResponseDto>> GetById(Guid manufacturerId, string? keyword, int pageNum, int pageSize)
    {
        var manufacturer = await manufacturerRepository.GetById(manufacturerId, new ManufacturerQueryableOptions());
        if (manufacturer is null)
        {
            logger.Error("Manufacturer not found");
            return new ErrorResultDto<ManufacturerDetailResponseDto>(ResponseCodeConstant.MANUFACTURER_NOT_EXIST);
        }
        var (items, total) = await itemRepository.GetByFilter(new ItemFilter
        {
            ManufacturerId = manufacturerId,
            Keyword = keyword,
            PageNum = pageNum,
            PageSize = pageSize
        }, new ItemQueryableOptions());
        var res = manufacturer.ToManufacturerDetailResponseDto(items, pageNum, pageSize, total);
        return new SuccessResultDto<ManufacturerDetailResponseDto>(res);
    }

    public async Task<ResultDto<byte[]>> GetLogo(Guid manufacturerId, string orgId)
    {
        var manufacturer = await manufacturerRepository.GetById(manufacturerId, new ManufacturerQueryableOptions());
        if (manufacturer == null)
        {
            logger.Error("Manufacturer not found");
            return new ErrorResultDto<byte[]>(ResponseCodeConstant.MANUFACTURER_NOT_EXIST);
        }
        if (manufacturer.LogoUrl == null)
        {
            logger.Error("Logo not found");
            return new ErrorResultDto<byte[]>(ResponseCodeConstant.MANUFACTURER_LOGO_NOT_EXIST);
        }
        var logo = await DownloadLogo(manufacturer.LogoUrl, orgId);
        if (logo == null || logo.Length == 0)
        {
            logger.Error("Logo not found");
            return new ErrorResultDto<byte[]>(ResponseCodeConstant.MANUFACTURER_LOGO_NOT_EXIST);
        }
        return new SuccessResultDto<byte[]>(logo);
    }

    public async Task<ResultDto<ManufacturerResponseDto>> Create(CreateManufacturerRequestDto requestDto)
    {
        if (!GetCurrentOrgGuid(out var orgUid))
        {
            logger.Error("Organization not found");
            return new ErrorResultDto<ManufacturerResponseDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        var newManufacturer = requestDto.ToEntity(orgUid);
        if (newManufacturer is null)
        {
            logger.Error("Manufacturer create failed");
            return new ErrorResultDto<ManufacturerResponseDto>(ResponseCodeConstant.MANUFACTURER_CREATE_FAILED);
        }
        var createdManufacturer = await manufacturerRepository.Create(newManufacturer);
        if (createdManufacturer is null)
        {
            logger.Error("Manufacturer create failed");
            return new ErrorResultDto<ManufacturerResponseDto>(ResponseCodeConstant.MANUFACTURER_CREATE_FAILED);
        }
        if (requestDto.Logo is not null)
        {
            var fileMetadata = await UploadLogo(createdManufacturer.ManufacturerUid.ToString(), requestDto.Logo);
            if (fileMetadata is not null)
            {
                createdManufacturer.LogoUrl = fileMetadata.FileUrl;
            }
            await manufacturerRepository.Update(createdManufacturer);
        }
        var result = createdManufacturer.ToManufacturerResponseDto();
        return new SuccessResultDto<ManufacturerResponseDto>(result);
    }

    public async Task<ResultDto<ManufacturerResponseDto>> Update(Guid manufacturerId, UpdateManufacturerRequestDto requestDto)
    {
        var existManufacturer = await manufacturerRepository.GetById(manufacturerId, new ManufacturerQueryableOptions());
        if (existManufacturer is null)
        {
            logger.Error("Manufacturer not found");
            return new ErrorResultDto<ManufacturerResponseDto>(ResponseCodeConstant.MANUFACTURER_NOT_EXIST);
        }
        existManufacturer.UpdateFromDto(requestDto);
        if (requestDto.Logo is not null)
        {
            _ = await UploadLogo(existManufacturer.ManufacturerUid.ToString(), requestDto.Logo);
        }

        var updatedManufacturer = await manufacturerRepository.Update(existManufacturer);
        if (updatedManufacturer is null)
        {
            logger.Error("Manufacturer update failed");
            return new ErrorResultDto<ManufacturerResponseDto>(ResponseCodeConstant.MANUFACTURER_UPDATE_FAILED);
        }
        var result = updatedManufacturer.ToManufacturerResponseDto();
        return new SuccessResultDto<ManufacturerResponseDto>(result);
    }

    public async Task<ResultDto<bool>> Delete(Guid manufacturerId)
    {
        var manufacturer = await manufacturerRepository.GetById(manufacturerId, new ManufacturerQueryableOptions());
        if (manufacturer is null)
        {
            logger.Error("Manufacturer not found");
            return new ErrorResultDto<bool>(ResponseCodeConstant.MANUFACTURER_NOT_EXIST, false);
        }
        var isDeleted = await manufacturerRepository.Delete(manufacturer);
        if (isDeleted == false)
        {
            logger.Error("Manufacturer delete failed");
            return new ErrorResultDto<bool>(ResponseCodeConstant.MANUFACTURER_DELETE_FAILED, false);
        }
        return new SuccessResultDto<bool>(true);
    }

    private async Task<FileMetadataResponseDto?> UploadLogo(string manufacturerId, IFormFile logo)
    {
        var path = StorageConstant.ManufacturerLogo(manufacturerId);
        var fileName = "logo.jpg";
        var objectName = path + fileName;
        var fileMetadata = await fileService.UploadFileAsync(logo, objectName);
        if (fileMetadata is null || fileMetadata.Data is null)
        {
            logger.Error("Upload logo failed");
            return null;
        }
        return fileMetadata.Data;
    }

    private async Task<byte[]> DownloadLogo(string logoUrl, string orgId)
    {
        try
        {
            var file = await fileService.DownloadFile(logoUrl, orgId);
            if (file is null || file.Data is null)
            {
                logger.Error("Logo not found");
                return [];
            }
            return file.Data.DataAsBytes;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting logo as base64");
            return [];
        }
    }
}
