using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.Project.Request;
using Kantoku.Api.Dtos.Project.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public partial interface IProjectService
{
    Task<ResultDto<ProjectResponseDto>> GetProjectById(Guid projectId);
    Task<ResultDto<ProjectsResponseDto>> GetProjectsPaginated(ProjectFilter filter);
    Task<ResultDto<SimpleProjectsResponseDto>> GetSimpleProjectInfo(int? pageNum, int? pageSize);
    Task<ResultDto<SimpleProjectsResponseDto>> GetProjectByManager();

    Task<ResultDto<ProjectResponseDto>> CreateProject(CreateProjectRequestDto dto);
    Task<ResultDto<ProjectResponseDto>> UpdateProject(Guid projectId, UpdateProjectRequestDto dto);
    Task<ResultDto<bool>> DeleteProject(Guid projectId);
}

[Service(ServiceLifetime.Scoped)]
public partial class ProjectService : BaseService<ProjectService>, IProjectService
{
    private readonly IProjectRepository projectRepository;
    private readonly IRequestRepository requestRepository;
    private readonly IInputCostItemRepository inputCostItemRepository;
    private readonly ICategoryRepository categoryRepository;
    private readonly IProjectDailyReportRepository projectDailyReportRepository;
    private readonly IOutSourceRepository outSourceRepository;
    private readonly IRankingRepository rankingRepository;
    private readonly IEmployeeRepository employeeRepository;
    private readonly IConstructionCostRepository constructionCostRepository;
    private readonly IEmployeeShiftRepository employeeShiftRepository;
    private readonly IProjectScheduleRepository projectScheduleRepository;
    private readonly ProjectQueryableOptions options = new()
    {
        IncludedProjectType = true,
        IncludedCustomer = true,
        IncludedContractor = true,
        IncludedManagers = true,
        IncludedProjectWorkShifts = true,
    };

    public ProjectService(
        IRequestRepository requestRepository,
        IInputCostItemRepository inputCostItemRepository,
        ICategoryRepository categoryRepository,
        IProjectRepository projectRepository,
        IProjectDailyReportRepository projectDailyReportRepository,
        IOutSourceRepository outSourceRepository,
        IRankingRepository rankingRepository,
        IEmployeeRepository employeeRepository,
        IConstructionCostRepository constructionCostRepository,
        IEmployeeShiftRepository employeeShiftRepository,
        IProjectScheduleRepository projectScheduleRepository,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor)
        : base(logger, httpContextAccessor)
    {
        this.inputCostItemRepository = inputCostItemRepository;
        this.projectRepository = projectRepository;
        this.categoryRepository = categoryRepository;
        this.requestRepository = requestRepository;
        this.projectDailyReportRepository = projectDailyReportRepository;
        this.outSourceRepository = outSourceRepository;
        this.rankingRepository = rankingRepository;
        this.employeeRepository = employeeRepository;
        this.constructionCostRepository = constructionCostRepository;
        this.employeeShiftRepository = employeeShiftRepository;
        this.projectScheduleRepository = projectScheduleRepository;
    }

    public async Task<ResultDto<ProjectResponseDto>> GetProjectById(Guid projectId)
    {
        var project = await projectRepository.GetById(projectId, options);
        if (project is null)
        {
            logger.Error("Project with ID {ProjectId} not found", projectId);
            return new ErrorResultDto<ProjectResponseDto>(ResponseCodeConstant.PROJECT_NOT_EXIST);
        }
        var result = project.ToProjectResponseDto(GetCurrentLanguageCode());
        return new SuccessResultDto<ProjectResponseDto>(result);
    }

    public async Task<ResultDto<ProjectsResponseDto>> GetProjectsPaginated(ProjectFilter filter)
    {
        var (projects, total) = await projectRepository.GetByFilter(filter, options);
        if (projects is null || !projects.Any() || total == 0)
        {
            logger.Error("No projects found for filter {Filter}", filter);
            return new ErrorResultDto<ProjectsResponseDto>(ResponseCodeConstant.PROJECT_NOT_EXIST);
        }
        var result = projects.ToProjectsResponseDto(filter.PageNum, filter.PageSize, total, GetCurrentLanguageCode());
        return new SuccessResultDto<ProjectsResponseDto>(result);
    }

    public async Task<ResultDto<SimpleProjectsResponseDto>> GetSimpleProjectInfo(int? pageNum, int? pageSize)
    {

        var filter = new ProjectFilter
        {
            PageNum = pageNum ?? 1,
            PageSize = pageSize ?? int.MaxValue,
            StatusCode = StatusConstants.STARTED
        };
        var options = new ProjectQueryableOptions
        {
            IncludedConstructions = true,
            IncludedManagers = true
        };
        var (projects, total) = await projectRepository.GetByFilter(filter, options);
        if (projects is null || !projects.Any() || total == 0)
        {
            logger.Error("No projects found for filter {Filter}", filter);
            return new ErrorResultDto<SimpleProjectsResponseDto>(ResponseCodeConstant.PROJECT_NOT_EXIST);
        }
        var result = projects.ToSimpleProjectsResponseDto(filter.PageNum, filter.PageSize, total);
        return new SuccessResultDto<SimpleProjectsResponseDto>(result);
    }

    public async Task<ResultDto<SimpleProjectsResponseDto>> GetProjectByManager()
    {
        var options = new ProjectQueryableOptions
        {
            IncludedConstructions = true,
            IncludedManagers = true
        };
        var managedProject = await projectRepository.GetSimpleProjectsByManagerId(GetCurrentEmployeeUid(), options);
        if (managedProject is null || !managedProject.Any())
        {
            logger.Error("No projects found for manager ID {EmployeeId}", GetCurrentEmployeeUid());
            return new ErrorResultDto<SimpleProjectsResponseDto>(ResponseCodeConstant.PROJECT_NOT_EXIST);
        }
        var result = managedProject.ToSimpleProjectsResponseDto(1, managedProject.Count(), managedProject.Count());
        return new SuccessResultDto<SimpleProjectsResponseDto>(result);
    }

    public async Task<ResultDto<ProjectResponseDto>> CreateProject(CreateProjectRequestDto dto)
    {
        if (!GetCurrentOrgGuid(out var orgUid))
        {
            logger.Error("Current organization not found");
            return new ErrorResultDto<ProjectResponseDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        var (rankings, _) = await rankingRepository.GetByFilter(new RankingFilter(), new RankingQueryableOptions());

        var newProject = dto.ToEntity(orgUid, rankings);
        if (newProject is null)
        {
            logger.Error("Failed to create project");
            return new ErrorResultDto<ProjectResponseDto>(ResponseCodeConstant.PROJECT_CREATE_FAILED);
        }

        var createdProject = await projectRepository.Create(newProject, options);
        if (createdProject is null)
        {
            logger.Error("Failed to create project");
            return new ErrorResultDto<ProjectResponseDto>(ResponseCodeConstant.PROJECT_CREATE_FAILED);
        }

        var result = createdProject.ToProjectResponseDto(GetCurrentLanguageCode());
        return new SuccessResultDto<ProjectResponseDto>(result);
    }

    public async Task<ResultDto<ProjectResponseDto>> UpdateProject(Guid projectId, UpdateProjectRequestDto dto)
    {
        var option = options.TrackingOptions();
        var existProject = await projectRepository.GetById(projectId, option);
        if (existProject is null)
        {
            logger.Error("Project with ID {ProjectId} not found", projectId);
            return new ErrorResultDto<ProjectResponseDto>(ResponseCodeConstant.PROJECT_NOT_EXIST);
        }
        existProject.UpdateFromDto(dto);

        var updatedProject = await projectRepository.Update(existProject, option);
        if (updatedProject is null)
        {
            logger.Error("Failed to update project with ID {ProjectId}", existProject.ProjectUid);
            return new ErrorResultDto<ProjectResponseDto>(ResponseCodeConstant.PROJECT_UPDATE_FAILED);
        }

        var result = updatedProject.ToProjectResponseDto(GetCurrentLanguageCode());
        return new SuccessResultDto<ProjectResponseDto>(result);
    }

    public async Task<ResultDto<bool>> DeleteProject(Guid projectId)
    {
        var project = await projectRepository.GetById(projectId, new ProjectQueryableOptions());
        if (project is null)
        {
            logger.Error("Project with ID {ProjectId} not found", projectId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.PROJECT_NOT_EXIST);
        }
        if (project.StatusCode != StatusConstants.STARTED)
        {
            logger.Error("Project with ID {ProjectId} is not in started status", projectId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.PROJECT_DELETE_FAILED);
        }

        project.IsDeleted = true;
        var isDeleted = await projectRepository.Update(project, new ProjectQueryableOptions());
        if (isDeleted is null)
        {
            logger.Error("Project with ID {ProjectId} not found", projectId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.PROJECT_DELETE_FAILED);
        }

        return new SuccessResultDto<bool>(true);
    }
}