using FirebaseAdmin.Messaging;

namespace Kantoku.Api.Dtos.Notification;

public class FirebaseBatchResponseDto
{
    public ICollection<SendResponse> Responses { get; set; } = [];

    //
    // Summary:
    //     Gets a count of how many of the responses in FirebaseAdmin.Messaging.BatchResponse.Responses
    //     were successful.
    public int SuccessCount { get; set; }

    //
    // Summary:
    //     Gets a count of how many of the responses in FirebaseAdmin.Messaging.BatchResponse.Responses
    //     were unsuccessful.
    public int FailureCount => Responses.Count - SuccessCount;
}
