using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class UserInfoQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedAccount { get; set; } = false;
}

public interface IUserInfoQueryable
{
    IQueryable<UserInfo> GetUserInfoQuery(
        UserInfoQueryableOptions options
    );
    IQueryable<UserInfo> GetUserInfoQueryIncluded(
        UserInfoQueryableOptions options,
        IQueryable<UserInfo>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class UserInfoQueryable(PostgreDbContext context) :
    BaseQueryable<UserInfo>(context), IUserInfoQueryable
{
    public IQueryable<UserInfo> GetUserInfoQuery(
        UserInfoQueryableOptions options
    )
    {
        var query = GetQuery(options);
        return query;
    }

    public IQueryable<UserInfo> GetUserInfoQueryIncluded(
        UserInfoQueryableOptions options,
        IQueryable<UserInfo>? query = null
    )
    {
        query ??= GetUserInfoQuery(options);

        if (options.IncludedAccount || options.IncludedAll)
        {
            query = query.Include(s => s.Account);
        }

        return query;
    }
}
