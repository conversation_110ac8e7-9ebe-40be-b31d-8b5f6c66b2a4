using Kantoku.Api.Dtos.Structure.Request;
using Kantoku.Api.Dtos.Structure.Response;
using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public interface IStructureService
{
    Task<ResultDto<StructureResponseDto>> GetStructureById(Guid structureId);
    Task<ResultDto<StructuresResponseDto>> GetAllStructure(StructureFilter filter);
    Task<ResultDto<SimpleStructureResponseDto>> GetSimpleStructureInfo(int? pageNum, int? pageSize);
    Task<ResultDto<StructureResponseDto>> CreateStructure(CreateStructureRequestDto requestDto);
    Task<ResultDto<StructureResponseDto>> UpdateStructure(Guid structureId, UpdateStructureRequestDto requestDto);
    Task<ResultDto<bool>> DeleteStructure(Guid structureId);
}

[Service(ServiceLifetime.Scoped)]
public class StructureService : BaseService<StructureService>, IStructureService
{
    private readonly IStructureRepository structureRepository;
    private readonly StructureQueryableOptions options = new()
    {
        IncludedRoles = true,
        IncludedEmployees = true,
        IncludedChildren = true,
    };

    public StructureService(IStructureRepository structureRepository,
    Serilog.ILogger logger, IHttpContextAccessor httpContextAccessor)
    : base(logger, httpContextAccessor)
    {
        this.structureRepository = structureRepository;
    }

    public async Task<ResultDto<StructuresResponseDto>> GetAllStructure(StructureFilter filter)
    {
        var option = new StructureQueryableOptions();
        var (structures, total) = await structureRepository.GetByFilter(filter, option);
        if (structures is null || total == 0 || !structures.Any())
        {
            logger.Error("No structures found for filter: {Filter}", filter);
            return new ErrorResultDto<StructuresResponseDto>(ResponseCodeConstant.STRUCTURE_NOT_EXIST);
        }
        var result = structures.ToStructuresResponseDto(filter.PageNum, filter.PageSize, total);
        return new SuccessResultDto<StructuresResponseDto>(result);
    }

    public async Task<ResultDto<StructureResponseDto>> GetStructureById(Guid structureId)
    {
        var structure = await structureRepository.GetById(structureId, options);
        if (structure is null)
        {
            logger.Error("Structure with id {Id} not found", structureId);
            return new ErrorResultDto<StructureResponseDto>(ResponseCodeConstant.STRUCTURE_NOT_EXIST);
        }
        var result = structure.ToStructureResponseDto();
        return new SuccessResultDto<StructureResponseDto>(result);
    }

    public async Task<ResultDto<StructureResponseDto>> CreateStructure(CreateStructureRequestDto requestDto)
    {
        if (!GetCurrentOrgGuid(out var orgUid))
        {
            logger.Error("Failed to get current org uid");
            return new ErrorResultDto<StructureResponseDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        var newStructure = requestDto.ToEntity(orgUid);
        if (newStructure is null)
        {
            logger.Error("Failed to create structure");
            return new ErrorResultDto<StructureResponseDto>(ResponseCodeConstant.STRUCTURE_INPUT_DATA_INVALID);
        }

        var createdStructure = await structureRepository.Create(newStructure, options);
        if (createdStructure is null)
        {
            logger.Error("Failed to create structure");
            return new ErrorResultDto<StructureResponseDto>(ResponseCodeConstant.STRUCTURE_CREATE_FAILED);
        }
        var result = createdStructure.ToStructureResponseDto();
        return new SuccessResultDto<StructureResponseDto>(result);
    }

    public async Task<ResultDto<StructureResponseDto>> UpdateStructure(Guid structureId, UpdateStructureRequestDto requestDto)
    {
        var option = options.TrackingOptions();
        var existStructure = await structureRepository.GetById(structureId, option);
        if (existStructure is null)
        {
            logger.Error("Structure with id {Id} not found", structureId);
            return new ErrorResultDto<StructureResponseDto>(ResponseCodeConstant.STRUCTURE_NOT_EXIST);
        }

        existStructure.UpdateFromDto(requestDto);

        var updatedStructure = await structureRepository.Update(existStructure, option);
        if (updatedStructure is null)
        {
            logger.Error("Failed to update structure with id {Id}", structureId);
            return new ErrorResultDto<StructureResponseDto>(ResponseCodeConstant.STRUCTURE_UPDATE_FAILED);
        }
        var result = updatedStructure.ToStructureResponseDto();
        return new SuccessResultDto<StructureResponseDto>(result);
    }

    public async Task<ResultDto<bool>> DeleteStructure(Guid structureId)
    {
        var existStructure = await structureRepository.GetById(structureId, new StructureQueryableOptions());
        if (existStructure is null)
        {
            logger.Error("Structure with id {Id} not found", structureId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.STRUCTURE_NOT_EXIST);
        }

        existStructure.IsDeleted = true;
        var isDeleted = await structureRepository.Update(existStructure);
        if (!isDeleted)
        {
            logger.Error("Failed to delete structure with id {Id}", structureId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.STRUCTURE_DELETE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<SimpleStructureResponseDto>> GetSimpleStructureInfo(int? pageNum, int? pageSize)
    {
        var filter = new StructureFilter
        {
            PageNum = pageNum ?? 1,
            PageSize = pageSize ?? 10,
        };
        var (structures, total) = await structureRepository.GetByFilter(filter, options);
        if (structures is null || total == 0 || !structures.Any())
        {
            logger.Error("No structures found for filter: {Filter}", filter);
            return new ErrorResultDto<SimpleStructureResponseDto>(ResponseCodeConstant.STRUCTURE_NOT_EXIST);
        }
        var result = structures.ToSimpleStructureResponseDto(filter.PageNum, filter.PageSize, total);
        return new SuccessResultDto<SimpleStructureResponseDto>(result);
    }
}
