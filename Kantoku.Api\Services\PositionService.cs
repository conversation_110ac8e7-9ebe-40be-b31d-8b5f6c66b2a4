using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Dtos.Position.Request;
using Kantoku.Api.Dtos.Position.Response;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public interface IPositionService
{
    Task<ResultDto<PositionResponseDto>> GetPositionById(Guid positionId);
    Task<ResultDto<PositionsResponseDto>> GetPositionByFilter(PositionFilter filter);
    Task<ResultDto<SimplePositionResponseDto>> GetSimplePositionInfo(PositionFilter filter);
    Task<ResultDto<PositionResponseDto>> CreatePosition(CreatePositionRequestDto requestDto);
    Task<ResultDto<PositionResponseDto>> UpdatePosition(Guid positionId, UpdatePositionRequestDto requestDto);
    Task<ResultDto<bool>> DeletePosition(Guid positionId);
}

[Service(ServiceLifetime.Scoped)]
public class PositionService : BaseService<PositionService>, IPositionService
{
    private readonly IPositionRepository positionRepository;

    public PositionService(
        IPositionRepository positionRepository,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor) : base(logger, httpContextAccessor)
    {
        this.positionRepository = positionRepository;
    }
    public async Task<ResultDto<PositionsResponseDto>> GetPositionByFilter(PositionFilter filter)
    {
        var (positions, total) = await positionRepository.GetByFilter(filter, new PositionQueryableOptions());
        if (positions is null || !positions.Any() || total == 0)
        {
            logger.Error("No positions found");
            return new ErrorResultDto<PositionsResponseDto>(ResponseCodeConstant.POSITION_NOT_EXIST);
        }

        var result = positions.ToPositionsResponseDto(filter.PageNum, filter.PageSize, total);
        return new SuccessResultDto<PositionsResponseDto>(result);
    }

    public async Task<ResultDto<PositionResponseDto>> GetPositionById(Guid positionId)
    {
        var position = await positionRepository.GetById(positionId, new PositionQueryableOptions());
        if (position is null)
        {
            logger.Error("Position {PositionId} not found", positionId);
            return new ErrorResultDto<PositionResponseDto>(ResponseCodeConstant.POSITION_NOT_EXIST);
        }
        var result = position.ToPositionResponseDto();
        return new SuccessResultDto<PositionResponseDto>(result);
    }

    public async Task<ResultDto<PositionResponseDto>> CreatePosition(CreatePositionRequestDto requestDto)
    {
        if (!GetCurrentOrgGuid(out var orgId))
        {
            logger.Error("No organization found");
            return new ErrorResultDto<PositionResponseDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        var requestedPosition = requestDto.ToEntity(orgId);
        if (requestedPosition is null)
        {
            logger.Error("Invalid request");
            return new ErrorResultDto<PositionResponseDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }

        var position = await positionRepository.Create(requestedPosition);
        if (position is null)
        {
            logger.Error("Failed to create position");
            return new ErrorResultDto<PositionResponseDto>(ResponseCodeConstant.POSITION_CREATE_FAILED);
        }
        var res = position.ToPositionResponseDto();
        return new SuccessResultDto<PositionResponseDto>(res);
    }

    public async Task<ResultDto<PositionResponseDto>> UpdatePosition(Guid positionId, UpdatePositionRequestDto requestDto)
    {
        var existPosition = await positionRepository.GetById(positionId, new PositionQueryableOptions());
        if (existPosition is null)
        {
            logger.Error("Position {PositionId} not found", positionId);
            return new ErrorResultDto<PositionResponseDto>(ResponseCodeConstant.POSITION_NOT_EXIST);
        }
        existPosition.UpdateFromDto(requestDto);
        var updatedPosition = await positionRepository.Update(existPosition);
        if (updatedPosition is null)
        {
            logger.Error("Failed to update position {PositionId}", positionId);
            return new ErrorResultDto<PositionResponseDto>(ResponseCodeConstant.POSITION_UPDATE_FAILED);
        }
        var result = updatedPosition.ToPositionResponseDto();
        return new SuccessResultDto<PositionResponseDto>(result);
    }

    public async Task<ResultDto<bool>> DeletePosition(Guid positionId)
    {
        var existPosition = await positionRepository.GetById(positionId, new PositionQueryableOptions());
        if (existPosition is null)
        {
            logger.Error("Position {PositionId} not found", positionId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.POSITION_NOT_EXIST);
        }
        existPosition.IsDeleted = true;
        var deletedPosition = await positionRepository.Update(existPosition);
        if (deletedPosition is null)
        {
            logger.Error("Failed to delete position {PositionId}", positionId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.POSITION_DELETE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<SimplePositionResponseDto>> GetSimplePositionInfo(PositionFilter filter)
    {
        var (positions, total) = await positionRepository.GetByFilter(filter, new PositionQueryableOptions());
        if (positions is null || !positions.Any() || total == 0)
        {
            logger.Error("No positions found");
            return new ErrorResultDto<SimplePositionResponseDto>(ResponseCodeConstant.POSITION_NOT_EXIST);
        }

        var res = positions.ToSimplePositionResponseDto(filter.PageNum, filter.PageSize, total);
        return new SuccessResultDto<SimplePositionResponseDto>(res);
    }
}
