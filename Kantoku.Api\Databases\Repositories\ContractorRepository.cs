﻿using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Filters.Domains;
using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Helpers;

namespace Kantoku.Api.Databases.Repositories;

public interface IContractorRepository
{
    Task<(IEnumerable<Contractor>, int)> GetByFilter(ContractorFilter filter, ContractorQueryableOptions options);
    Task<Contractor?> GetById(Guid contractorId, ContractorQueryableOptions options);
    Task<Contractor?> Create(Contractor contractor);
    Task<Contractor?> Update(Contractor contractor);
}

[Repository(ServiceLifetime.Scoped)]
public class ContractorRepository : BaseRepository<Contractor>, IContractorRepository
{
    private readonly IContractorQueryable ContractorQueryable;

    public ContractorRepository(
        PostgreDbContext context,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor,
        IContractorQueryable ContractorQueryable
    ) : base(context, logger, httpContextAccessor)
    {
        this.ContractorQueryable = ContractorQueryable;
    }

    public async Task<(IEnumerable<Contractor>, int)> GetByFilter(ContractorFilter filter, ContractorQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = ContractorQueryable.GetContractorQueryFilter(filter, options);

            var total = await query
                .CountAsync();

            var result = await query
                .OrderBy(c => c.ContractorUid)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            return (result, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all Contractors");
            return ([], 0);
        }
    }

    public async Task<Contractor?> GetById(Guid contractorId, ContractorQueryableOptions options)
    {
        try
        {
            var query = ContractorQueryable.GetContractorQueryIncluded(options)
                .Where(c => c.ContractorUid == contractorId);

            var result = await query
                .FirstOrDefaultAsync();

            return result;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting Contractor by id: {contractorId}", contractorId);
            return null;
        }
    }

    public async Task<Contractor?> Create(Contractor contractor)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            contractor.ContractorUid = GuidHelper.GenerateUUIDv7();
            contractor.OrgUid = GetCurrentOrgUid();
            contractor.IsDeleted = false;
            await context.Contractors.AddAsync(contractor);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(contractor.ContractorUid, new ContractorQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating Contractor");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Contractor?> Update(Contractor contractor)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Contractors.Update(contractor);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(contractor.ContractorUid, new ContractorQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating Contractor");
            await transaction.RollbackAsync();
            return null;
        }
    }
}
