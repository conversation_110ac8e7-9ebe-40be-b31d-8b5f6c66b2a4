﻿using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Dtos.PaymentType.Request;
using Kantoku.Api.Dtos.PaymentType.Response;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Controllers;

[Route("api/v1/cost/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class PaymentTypeController : BaseController
{
    private readonly IPaymentTypeService paymentTypeService;
    private readonly IAuditLogService auditLogService;
    public PaymentTypeController(IPaymentTypeService paymentTypeService,
        ITResponseFactory responseFactory,
        IAuditLogService auditLogService) : base(responseFactory)
    {
        this.paymentTypeService = paymentTypeService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get payment types by filter criteria
    /// </summary>
    /// <param name="filter">Filter parameters for payment types</param>
    /// <returns>List of payment types matching the filter criteria</returns>
    [HttpGet]
    public async Task<GeneralResponse<PaymentTypesResponseDto>> GetListPaginated([FromQuery] PaymentTypeFilter filter)
    {
        try
        {
            var res = await paymentTypeService.GetByFilter(filter);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<PaymentTypesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get payment type by ID
    /// </summary>
    /// <param name="id">Payment type ID</param>
    /// <returns>Payment type details if found</returns>
    [HttpGet("{id}")]
    public async Task<GeneralResponse<PaymentTypeResponseDto>> GetById([FromRoute] Guid id)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<PaymentTypeResponseDto>();

            var result = await paymentTypeService.GetById(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<PaymentTypeResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new payment type
    /// </summary>
    /// <param name="requestDto">Payment type creation data</param>
    /// <returns>Created payment type details</returns>
    [HttpPost]
    public async Task<GeneralResponse<PaymentTypeResponseDto>> Create([FromBody] CreatePaymentTypeRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<PaymentTypeResponseDto>();

            var result = await paymentTypeService.Create(requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<PaymentTypeResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update an existing payment type
    /// </summary>
    /// <param name="id">Payment type ID to update</param>
    /// <param name="requestDto">Updated payment type data</param>
    /// <returns>Updated payment type details</returns>
    [HttpPut("{id}")]
    public async Task<GeneralResponse<PaymentTypeResponseDto>> Update([FromRoute] Guid id, [FromBody] UpdatePaymentTypeRequestDto requestDto)
    {
        try
        {
            var result = await paymentTypeService.Update(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<PaymentTypeResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete a payment type
    /// </summary>
    /// <param name="id">Payment type ID to delete</param>
    /// <returns>Success response if deleted</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> Delete([FromRoute] Guid id)
    {
        try
        {
            await paymentTypeService.Delete(id);
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for a payment type
    /// </summary>
    /// <param name="id">Payment type ID</param>
    /// <param name="filter">Filter parameters for audit logs</param>
    /// <returns>Filtered audit logs for the payment type</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetAuditLogByEntity([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<PaymentType>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
