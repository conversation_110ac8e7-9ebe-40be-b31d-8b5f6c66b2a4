using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.Partner;

public class GeoCodeResponseDto
{
    [JsonPropertyName("plus_code")]
    public PlusCode? PlusCode { get; set; }

    [JsonPropertyName("results")]
    public IEnumerable<Result>? Results { get; set; }

    [JsonPropertyName("status")]
    public string? Status { get; set; }
}

public class PlusCode
{
    [JsonPropertyName("compound_code")]
    public string? CompoundCode { get; set; }

    [JsonPropertyName("global_code")]
    public string? GlobalCode { get; set; }
}

public class Result
{
    [JsonPropertyName("address_components")]
    public IEnumerable<AddressComponent>? AddressComponents { get; set; }

    [JsonPropertyName("formatted_address")]
    public string? FormattedAddress { get; set; }

    [JsonPropertyName("geometry")]
    public Geometry? Geometry { get; set; }

    [JsonPropertyName("place_id")]
    public string? PlaceId { get; set; }

    [JsonPropertyName("plus_code")]
    public PlusCode? PlusCode { get; set; }

    [JsonPropertyName("types")]
    public IEnumerable<string>? Types { get; set; }
}

public class AddressComponent
{
    [JsonPropertyName("long_name")]
    public string? LongName { get; set; }

    [JsonPropertyName("short_name")]
    public string? ShortName { get; set; }

    [JsonPropertyName("types")]
    public IEnumerable<string>? Types { get; set; }
}

public class Geometry
{
    [JsonPropertyName("location")]
    public Location? Location { get; set; }

    [JsonPropertyName("location_type")]
    public string? LocationType { get; set; }

    [JsonPropertyName("viewport")]
    public Viewport? Viewport { get; set; }

    [JsonPropertyName("bounds")]
    public Bounds? Bounds { get; set; }
}

public class Location
{
    [JsonPropertyName("lat")]
    public float Lat { get; set; }

    [JsonPropertyName("lng")]
    public float Lng { get; set; }
}

public class Viewport
{
    [JsonPropertyName("northeast")]
    public Location? Northeast { get; set; }

    [JsonPropertyName("southwest")]
    public Location? Southwest { get; set; }
}

public class Bounds
{
    [JsonPropertyName("northeast")]
    public Location? Northeast { get; set; }

    [JsonPropertyName("southwest")]
    public Location? Southwest { get; set; }
}
