using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.Construction.Request;

/// <summary>
/// Data transfer object for creating a new construction request
/// </summary>
public class CreateConstructionRequestDto
{
    /// <summary>
    /// The name of the construction project (*)
    /// </summary>
    [Required]
    public string ConstructionName { get; set; } = null!;

    /// <summary>
    /// Additional details or notes about the construction project
    /// </summary> 
    public string? Description { get; set; }

    /// <summary>
    /// Initial cost details for the construction project (*)
    /// </summary>
    [Required]
    public InitalCostRequestDto InitialCost { get; set; } = null!;
}
