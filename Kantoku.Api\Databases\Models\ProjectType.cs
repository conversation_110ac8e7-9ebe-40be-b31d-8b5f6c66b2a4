namespace Kantoku.Api.Databases.Models;

public class ProjectType
{
    public Guid ProjectTypeUid { get; set; }

    public string? ProjectTypeCode { get; set; }

    public IEnumerable<TranslatedProjectType> TranslatedProjectType { get; set; } = [];

    public virtual ICollection<Project> Projects { get; set; } = [];
}

public class TranslatedProjectType
{
    public string LanguageCode { get; set; } = null!;

    public string ProjectTypeName { get; set; } = null!;
}
