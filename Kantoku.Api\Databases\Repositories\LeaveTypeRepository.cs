using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;

namespace Kantoku.Api.Databases.Repositories;

public interface ILeaveTypeRepository
{
    Task<LeaveType?> GetByCode(string code);
    Task<IEnumerable<LeaveType>> GetAll();
}

[Repository(ServiceLifetime.Scoped)]
public class LeaveTypeRepository : BaseRepository<LeaveType>, ILeaveTypeRepository
{
    public LeaveTypeRepository(PostgreDbContext context, Serilog.ILogger logger, IHttpContextAccessor httpContextAccessor)
        : base(context, logger, httpContextAccessor)
    {
    }

    public async Task<LeaveType?> GetByCode(string code)
    {
        try
        {
            return await context.LeaveTypes
                .Where(l => l.LeaveTypeCode.Equals(code))
                .FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting leave type by code {Code}", code);
            return null;
        }
    }

    public async Task<IEnumerable<LeaveType>> GetAll()
    {
        try
        {
            return await context.LeaveTypes
               .ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all leave types");
            return [];
        }
    }
}