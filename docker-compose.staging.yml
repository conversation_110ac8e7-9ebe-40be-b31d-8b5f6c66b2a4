version: '3.4'

services:
  kantoku-api:
    image: kantoku-api-staging
    container_name: kantoku-api-staging
    build:
      context: ./Kantoku.Api
      dockerfile: Dockerfile
      args:
        - configuration=Debug
        - TZ=Asia/Tokyo
    ports:
      - 4869:4869
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
    volumes:
      - ./Logs:/app/Logs:rw

  kantoku-processor:
    image: kantoku-processor-staging
    container_name: kantoku-processor-staging
    build:
      context: ./Kantoku.Processor
      dockerfile: Dockerfile
      args:
        - configuration=Debug
        - TZ=Asia/Tokyo
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
    volumes:
      - ./Logs:/app/Logs:rw

