namespace Kantoku.Api.Dtos.Account.Request;

public class UpdateAccountRequestDto
{
    /// <summary>
    /// The email address used for the account. Optional field that can be updated.
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// The unique login identifier for the account. Optional field that can be updated.
    /// </summary>
    public string? LoginId { get; set; }
}