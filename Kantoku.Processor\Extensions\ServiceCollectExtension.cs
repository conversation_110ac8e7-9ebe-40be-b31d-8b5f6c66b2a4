using Kantoku.Processor.Data.Seeders;
using Kantoku.Processor.Data.Repositories;
using Kantoku.Processor.Jobs;
using Kantoku.Processor.Services;
using Kantoku.Processor.Services.Interfaces;

namespace Kantoku.Processor.Extensions;

public static class ServiceCollectExtension
{
    public static IServiceCollection ServiceCollect(this IServiceCollection services)
    {
        // Configure repositories - keep as scoped
        services.AddScoped<IBatchJobRepository, BatchJobRepository>();

        // Register job factory as singleton since it doesn't maintain state
        services.AddSingleton<JobFactory>();

        // Configure batch processing services as scoped to match repository lifetime
        services.AddScoped<IBatchExecutionService, BatchExecutionService>();
        services.AddScoped<IJobSchedulerService, JobSchedulerService>();
        services.AddScoped<IFirebaseService, FirebaseService>();

        // Register the scheduler hosted service
        services.AddHostedService<JobSchedulerHostedService>();

        // Configure seeder
        services.AddTransient<DatabaseSeeder>();

        // Register the AutoCheckoutJob as a transient service
        // This will ensure that a new instance is created each time it's requested
        services.AddTransient<AutoCheckoutJob>();
        services.AddTransient<CheckOutReminderJob>();
        services.AddTransient<EmployeeCostCalculateJob>();

        return services;
    }
}
