using Kantoku.Processor.Data.Seeders;
using Kantoku.Processor.Data.Repositories;
using Kantoku.Processor.Jobs;
using Kantoku.Processor.Services;
using Kantoku.Processor.Services.Interfaces;

namespace Kantoku.Processor.Extensions;

public static class ServiceCollectExtension
{
    public static IServiceCollection ServiceCollect(this IServiceCollection services)
    {
        // Configure repositories - keep as scoped for legacy support
        services.AddScoped<IBatchJobRepository, BatchJobRepository>();

        // Configure Firebase service for notifications
        services.AddScoped<IFirebaseService, FirebaseService>();

        // Configure seeder
        services.AddTransient<DatabaseSeeder>();

        // Legacy job support (can be removed after full migration)
        // services.AddSingleton<JobFactory>();
        // services.AddScoped<IBatchExecutionService, BatchExecutionService>();
        // services.AddScoped<IJobSchedulerService, JobSchedulerService>();
        // services.AddHostedService<JobSchedulerHostedService>();
        // services.AddTransient<AutoCheckoutJob>();
        // services.AddTransient<CheckOutReminderJob>();
        // services.AddTransient<EmployeeCostCalculateJob>();

        return services;
    }
}
