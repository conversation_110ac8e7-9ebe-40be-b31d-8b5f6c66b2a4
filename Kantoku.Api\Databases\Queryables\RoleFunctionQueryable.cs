using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Utils.Attributes.Class;

namespace Kantoku.Api.Databases.Queryables;

public class RoleFunctionQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedRole { get; set; } = false;
    public bool IncludedFunction { get; set; } = false;
}

public interface IRoleFunctionQueryable
{
    IQueryable<RoleFunction> GetRoleFunctionsQuery(
        RoleFunctionQueryableOptions options
    );
    IQueryable<RoleFunction> GetRoleFunctionsQueryIncluded(
        RoleFunctionQueryableOptions options,
        IQueryable<RoleFunction>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class RoleFunctionQueryable(PostgreDbContext context) :
    BaseQueryable<RoleFunction>(context), IRoleFunctionQueryable
{
    public IQueryable<RoleFunction> GetRoleFunctionsQuery(
        RoleFunctionQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(urf => urf.Function.OnlySuperUser == false);
        query = query.Where(urf => urf.Role.IsDeleted == false && urf.Role.IsHidden == false);
        return query;
    }

    public IQueryable<RoleFunction> GetRoleFunctionsQueryIncluded(
        RoleFunctionQueryableOptions options,
        IQueryable<RoleFunction>? query = null
    )
    {
        query ??= GetRoleFunctionsQuery(options);
        if (options.IncludedRole || options.IncludedAll)
        {
            query = query.Include(urf => urf.Role).ThenInclude(r => r.Structure);
            query = query.Include(urf => urf.Role).ThenInclude(r => r.EmployeeRoles);
        }
        if (options.IncludedFunction || options.IncludedAll)
        {
            query = query.Include(urf => urf.Function).ThenInclude(f => f.Parent);
            query = query.Include(urf => urf.Function).ThenInclude(f => f.Children);
        }
        return query;
    }
}
