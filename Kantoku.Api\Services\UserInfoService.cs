using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Dtos.UserInfo.Request;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Helpers;
using Kantoku.Api.Dtos.File;
using Kantoku.Api.Dtos.UserInfo.Response;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public interface IUserInfoService
{
    Task<ResultDto<UserInfoResponseDto>> GetUserInfo();
    Task<ResultDto<UserInfoResponseDto>> UpdateUserInfo(UpdateUserInfoRequestDto requestDto);
    Task<ResultDto<AvatarResponseDto>> GetUserAvatar();
    Task<ResultDto<bool>> UpdateUserAvatar(IFormFile file);
    Task<ResultDto<bool>> DeleteUserAvatar();
}

[Service(ServiceLifetime.Scoped)]
public class UserInfoService : BaseService<UserInfoService>, IUserInfoService
{
    private readonly IUserInfoRepository userInfoRepository;
    private readonly IFileService fileService;

    public UserInfoService(
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor,
        IUserInfoRepository userInfoRepository,
        IFileService fileService
    )
    : base(logger, httpContextAccessor)
    {
        this.userInfoRepository = userInfoRepository;
        this.fileService = fileService;
    }

    public async Task<ResultDto<UserInfoResponseDto>> GetUserInfo()
    {
        var accountId = GetCurrentAccountUid();

        var userInfo = await userInfoRepository.GetByAccountId(accountId, new UserInfoQueryableOptions
        {
            IncludedAccount = true,
        });
        if (userInfo is null)
        {
            logger.Error("User info not exist with account id {accountId}", accountId);
            return new ErrorResultDto<UserInfoResponseDto>(ResponseCodeConstant.USER_INFO_NOT_EXIST);
        }
        var result = userInfo.ToUserInfoResponseDto();
        return new SuccessResultDto<UserInfoResponseDto>(result);
    }

    public async Task<ResultDto<AvatarResponseDto>> GetUserAvatar()
    {
        var accountId = GetCurrentAccountUid();

        var userInfo = await userInfoRepository.GetByAccountId(accountId, new UserInfoQueryableOptions());
        if (userInfo is null || string.IsNullOrEmpty(userInfo.AvatarUrl))
        {
            logger.Error("Avatar is not set with account ID {AccountId}", accountId);
            return new ErrorResultDto<AvatarResponseDto>(ResponseCodeConstant.USER_INFO_AVATAR_NOT_SET);
        }

        var fileStat = await fileService.GetFileMetadataAsync(userInfo.AvatarUrl, bucketName: StorageConstant.DefaultBucket());
        if (fileStat is null || fileStat.Data is null || fileStat.Data.FileSize == 0)
        {
            logger.Error("Avatar is not exist with account ID {AccountId}", accountId);
            return new ErrorResultDto<AvatarResponseDto>(ResponseCodeConstant.USER_INFO_AVATAR_NOT_SET);
        }
        var fileContent = await fileService.DownloadFile(userInfo.AvatarUrl, bucketName: StorageConstant.DefaultBucket());
        if (fileContent is null || fileContent.Data is null)
        {
            logger.Error("Failed to download user avatar with account ID {AccountId}", accountId);
            return new ErrorResultDto<AvatarResponseDto>(ResponseCodeConstant.USER_INFO_AVATAR_NOT_SET);
        }
        var result = new AvatarResponseDto
        {
            AvatarUrl = fileContent.Data.FileName,
            AvatarBase64 = fileContent.Data.DataAsBase64,
            AvatarByteArr = fileContent.Data.DataAsBytes,
            Metadata = fileContent.Data.Metadata,
        };

        return new SuccessResultDto<AvatarResponseDto>(result);
    }

    public async Task<ResultDto<UserInfoResponseDto>> UpdateUserInfo(UpdateUserInfoRequestDto requestDto)
    {
        var accountId = GetCurrentAccountUid();

        var userInfo = await userInfoRepository.GetByAccountId(accountId, new UserInfoQueryableOptions());
        if (userInfo is null)
        {
            logger.Error("User info not found with account ID {AccountId}", accountId);
            return new ErrorResultDto<UserInfoResponseDto>(ResponseCodeConstant.USER_INFO_NOT_EXIST);
        }
        try
        {
            ObjectHelper.UpdateEntityFromDto(requestDto, userInfo);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error when updating user info with account ID {AccountId}", accountId);
            return new ErrorResultDto<UserInfoResponseDto>(ResponseCodeConstant.USER_INFO_UPDATE_FAILED);
        }

        var isUpdated = await userInfoRepository.Update(userInfo);
        if (isUpdated is null)
        {
            logger.Error("Failed to update user info with account ID {AccountId}", accountId);
            return new ErrorResultDto<UserInfoResponseDto>(ResponseCodeConstant.USER_INFO_UPDATE_FAILED);
        }

        var result = userInfo.ToUserInfoResponseDto();
        return new SuccessResultDto<UserInfoResponseDto>(result);
    }

    public async Task<ResultDto<bool>> UpdateUserAvatar(IFormFile file)
    {
        var accountId = GetCurrentAccountUid();
        var userInfo = await userInfoRepository.GetByAccountId(accountId, new UserInfoQueryableOptions());
        if (userInfo is null)
        {
            logger.Error("User info not exist with account id {accountId}", accountId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.USER_INFO_NOT_EXIST);
        }

        var fileName = string.Format("{0}_avatar.jpg", accountId);
        var path = StorageConstant.UserAvatar(accountId.ToString());
        var objectName = path + fileName;
        var res = await fileService.UploadFileAsync(file, objectName, bucketName: StorageConstant.DefaultBucket());
        if (res is null)
        {
            logger.Error("Failed to update user avatar with account id {accountId}", accountId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.USER_INFO_AVATAR_UPDATE_FAILED);
        }
        userInfo.AvatarUrl = objectName;

        var isUpdated = await userInfoRepository.Update(userInfo);
        if (isUpdated is null)
        {
            logger.Error("Failed to update user avatar with account id {accountId}", accountId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.USER_INFO_AVATAR_UPDATE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> DeleteUserAvatar()
    {
        var accountId = GetCurrentAccountUid();

        var userInfo = await userInfoRepository.GetByAccountId(accountId, new UserInfoQueryableOptions());
        if (userInfo is null || string.IsNullOrEmpty(userInfo.AvatarUrl))
        {
            logger.Error("Avatar not set with account id {accountId}", accountId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.USER_INFO_AVATAR_NOT_SET, false);
        }

        var isDeleted = await fileService.DeleteFileAsync(userInfo.AvatarUrl);
        if (!isDeleted.Data)
        {
            logger.Error("Failed to delete user avatar with account id {accountId}", accountId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.USER_INFO_AVATAR_DELETE_FAILED, false);
        }
        userInfo.AvatarUrl = null;
        var isDeletedAvatar = await userInfoRepository.Update(userInfo);
        if (isDeletedAvatar is null)
        {
            logger.Error("Failed to delete user avatar with account id {accountId}", accountId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.USER_INFO_AVATAR_DELETE_FAILED, false);
        }
        return new SuccessResultDto<bool>(true);
    }
}
