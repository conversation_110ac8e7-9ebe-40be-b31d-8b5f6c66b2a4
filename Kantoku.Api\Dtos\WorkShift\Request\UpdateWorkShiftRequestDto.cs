namespace Kantoku.Api.Dtos.WorkShift.Request;

public class UpdateWorkShiftRequestDto
{
    /// <summary>
    /// The code of the work shift
    /// </summary>
    public string? WorkShiftCode { get; set; }

    /// <summary>
    /// The name of the work shift
    /// </summary>
    public string? WorkShiftName { get; set; }

    /// <summary>
    /// The check-in time for the work shift
    /// </summary>
    public string? CheckInTime { get; set; }

    /// <summary>
    /// The check-out time for the work shift
    /// </summary>
    public string? CheckOutTime { get; set; }

    /// <summary>
    /// Description of the work shift
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Collection of break periods during the work shift
    /// </summary>
    public IEnumerable<WorkShiftBreakRequestDto>? WorkShiftBreaks { get; set; }
}