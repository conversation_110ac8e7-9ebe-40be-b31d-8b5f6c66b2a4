using Kantoku.Api.Dtos.Base.Response;

namespace Kantoku.Api.Dtos.Category.Response;

public class CategoryResponseDto : BaseResponseDto
{
    public string? CategoryId { get; set; }

    public string? CategoryCode { get; set; }

    public string? CategoryName { get; set; }

    public string? Description { get; set; }

    public int NumberOfItems { get; set; }

    public bool IsSystemCategory { get; set; }

    public string? ParentId { get; set; }

    public string? ParentCode { get; set; }

    public string? ParentName { get; set; }

    public bool HasChildren { get; set; }
}
