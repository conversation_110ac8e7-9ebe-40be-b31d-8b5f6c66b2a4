using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Kantoku.Api.Databases.Configurations;

public class NotificationTargetConfiguration(string schema) : IEntityTypeConfiguration<NotificationTarget>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<NotificationTarget> builder)
    {
        builder.HasKey(e => e.NotificationTargetUid)
            .HasName("notification_target_pkey");
        
        builder.ToTable("notification_target", Schema);

        builder.Property(e => e.NotificationTargetUid)
            .HasColumnName("notification_target_uid");
        builder.Property(e => e.NotificationUid)
            .HasColumnName("notification_uid");
        builder.Property(e => e.TargetType)
            .HasColumnName("target_type");
        builder.Property(e => e.TargetIds)
            .HasColumnName("target_ids")
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<ICollection<Guid>>(v) ?? new List<Guid>()
            ).Metadata.SetValueComparer(
                new ValueComparer<ICollection<Guid>>(
                    (c1, c2) => (c1 == null && c2 == null) || (c1 != null && c2 != null && c1.SequenceEqual(c2)),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()
                )
            );
        builder.Property(e => e.PublishStatus)
            .HasColumnName("publish_status");
        builder.Property(e => e.PublishAt)
            .HasColumnType("timestamp without time zone")
            .HasColumnName("publish_at");
        builder.Property(e => e.IsDeleted)
            .HasColumnName("is_deleted");

        builder.HasOne(d => d.Notification)
            .WithMany(p => p.NotificationTargets)
            .HasForeignKey(d => d.NotificationUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("notification_target_notification_id_fkey");
    }
} 