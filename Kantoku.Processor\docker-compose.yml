version: '3.8'

services:
  app:
    build:
      context: ..
      dockerfile: Kantoku.Processor/Dockerfile
    # No need to expose ports since we're not handling HTTP requests
    depends_on:
      - job-db
      - app-db
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__JobConnection=Host=job-db;Database=kantoku_jobs;Username=postgres;Password=postgres;Port=5432
      - ConnectionStrings__AppConnection=Host=app-db;Database=kantoku_app;Username=postgres;Password=postgres;Port=5432
    restart: unless-stopped

  job-db:
    image: postgres:16-alpine
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=kantoku_jobs
    ports:
      - "5432:5432"
    volumes:
      - job_postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  app-db:
    image: postgres:16-alpine
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=kantoku_app
    ports:
      - "5433:5432"
    volumes:
      - app_postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  job_postgres_data:
  app_postgres_data: 