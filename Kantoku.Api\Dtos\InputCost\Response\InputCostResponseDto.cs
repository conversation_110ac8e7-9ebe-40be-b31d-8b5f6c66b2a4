﻿using Kantoku.Api.Dtos.Base.Response;

namespace Kantoku.Api.Dtos.InputCost.Response;

/// <summary>
/// Response DTO for input cost data
/// </summary>
public class InputCostResponseDto : BaseResponseDto
{
    /// <summary>
    /// The unique identifier for the input cost
    /// </summary>
    public string? InputCostId { get; set; }

    /// <summary>
    /// Title of the input cost
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// Issue date of the input cost
    /// </summary>
    public string? IssueDate { get; set; }

    /// <summary>
    /// Payment date of the input cost
    /// </summary>
    public string? PaymentDate { get; set; }

    /// <summary>
    /// Original reference number
    /// </summary>
    public string? OriginalNumber { get; set; }

    /// <summary>
    /// ID of the associated construction
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// Name of the associated construction
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// ID of the associated construction
    /// </summary>
    public string? ConstructionId { get; set; }

    /// <summary>
    /// Name of the associated construction
    /// </summary>
    public string? ConstructionName { get; set; }

    /// <summary>
    /// ID of the entry type
    /// </summary>
    public string? EntryTypeId { get; set; }

    /// <summary>
    /// Code of the entry type
    /// </summary>
    public string? EntryTypeCode { get; set; }

    /// <summary>
    /// Name of the entry type
    /// </summary>
    public string? EntryTypeName { get; set; }

    /// <summary>
    /// ID of the vendor
    /// </summary>
    public string? VendorId { get; set; }

    /// <summary>
    /// Code of the vendor
    /// </summary>
    public string? VendorCode { get; set; }

    /// <summary>
    /// Name of the vendor
    /// </summary>
    public string? VendorName { get; set; }

    /// <summary>
    /// Presentative name of the vendor
    /// </summary>
    public string? VendorPresentativeName { get; set; }

    /// <summary>
    /// Address of the vendor
    /// </summary>
    public string? VendorAddress { get; set; }

    /// <summary>
    /// Phone number of the vendor
    /// </summary>
    public string? VendorPhoneNumber { get; set; }

    /// <summary>
    /// Email of the vendor
    /// </summary>
    public string? VendorEmail { get; set; }

    /// <summary>
    /// ID of the payment type
    /// </summary>
    public string? PaymentTypeId { get; set; }

    /// <summary>
    /// Code of the payment type
    /// </summary>
    public string? PaymentTypeCode { get; set; }

    /// <summary>
    /// Name of the payment type
    /// </summary>
    public string? PaymentTypeName { get; set; }

    /// <summary>
    /// Description of the input cost
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Total amount of the input cost
    /// </summary>
    public long? TotalAmount { get; set; }

    /// <summary>
    /// URLs of the associated images
    /// </summary>
    public IEnumerable<string>? ImageUrls { get; set; }
}
