using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.Schedule.Response;

public class ProjectSchedulesResponseDto
{
    [JsonPropertyName("items")]
    public IEnumerable<ProjectInfoDto> Projects { get; set; } = [];

    [JsonPropertyName("pageNum")]
    public int PageNum { get; set; }

    [JsonPropertyName("pageSize")]
    public int PageSize { get; set; }

    [JsonPropertyName("totalRecords")]
    public int TotalRecords { get; set; }
}

public class ProjectInfoDto
{
    [JsonPropertyName("projectId")]
    public string? ProjectId { get; set; }

    [JsonPropertyName("projectCode")]
    public string? ProjectCode { get; set; }

    [JsonPropertyName("projectName")]
    public string? ProjectName { get; set; }

    [JsonPropertyName("shifts")]
    public IEnumerable<ScheduledEmployeeShiftResponseDto> EmployeeShifts { get; set; } = [];

    [JsonPropertyName("outsourceShifts")]
    public IEnumerable<ScheduledOutSourceShiftResponseDto> OutsourceShifts { get; set; } = [];

    [JsonPropertyName("schedules")]
    public IEnumerable<ProjectScheduleInfoDto> Schedules { get; set; } = [];
}

public class ProjectScheduleInfoDto
{
    [JsonPropertyName("scheduleId")]
    public string? ProjectScheduleId { get; set; }

    [JsonPropertyName("workingDate")]
    public string? WorkingDate { get; set; }

    [JsonPropertyName("totalPlannedWorkload")]
    public float? PlannedWorkload { get; set; }

    [JsonPropertyName("totalEstimatedWorkload")]
    public float? EstimatedWorkload { get; set; }

    [JsonPropertyName("totalActualWorkload")]
    public float? ActualWorkload { get; set; }
}