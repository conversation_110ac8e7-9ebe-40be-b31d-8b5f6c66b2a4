using EventBus.Interfaces;
using EventBus.Kafka.Configuration;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace EventBus.Kafka.Extensions;

/// <summary>
/// Service collection extensions for Kafka event bus
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds Kafka event bus to the service collection
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Configuration</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddKafkaEventBus(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // Configure Kafka settings
        var kafkaSection = configuration.GetSection(KafkaConfig.SectionName);
        services.Configure<KafkaConfig>(kafkaSection);

        // Register event bus
        services.AddSingleton<IEventBus, KafkaEventBus>();

        // Register hosted service for consumer
        services.AddHostedService<KafkaEventBusHostedService>();

        return services;
    }

    /// <summary>
    /// Adds event handler to the service collection
    /// </summary>
    /// <typeparam name="TEvent">Event type</typeparam>
    /// <typeparam name="THandler">Handler type</typeparam>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection</returns>
    public static IServiceCollection AddEventHandler<TEvent, THandler>(this IServiceCollection services)
        where TEvent : class
        where THandler : class, IEventHandler<TEvent>
    {
        services.AddScoped<THandler>();
        return services;
    }
}

/// <summary>
/// Hosted service for managing Kafka event bus lifecycle
/// </summary>
internal class KafkaEventBusHostedService : IHostedService
{
    private readonly IEventBus _eventBus;

    public KafkaEventBusHostedService(IEventBus eventBus)
    {
        _eventBus = eventBus;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        await _eventBus.StartAsync(cancellationToken);
    }

    public async Task StopAsync(CancellationToken cancellationToken)
    {
        await _eventBus.StopAsync(cancellationToken);
    }
}
