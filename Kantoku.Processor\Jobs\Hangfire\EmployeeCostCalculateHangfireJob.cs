using Kantoku.Processor.Data.Contexts;
using Kantoku.Processor.Jobs.Hangfire;
using Kantoku.Processor.Models;
using Kantoku.Processor.Models.Business;
using Kantoku.Processor.Utils;
using Microsoft.EntityFrameworkCore;
using UUIDNext;

namespace Kantoku.Processor.Jobs.Hangfire;

/// <summary>
/// Hangfire job that calculates employee daily costs based on salary and working days
/// </summary>
public class EmployeeCostCalculateHangfireJob : HangfireJobBase
{
    public EmployeeCostCalculateHangfireJob(ILogger<EmployeeCostCalculateHangfireJob> logger, IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public override async Task ExecuteAsync(CancellationToken cancellationToken = default)
    {
        LogInformation("Starting employee cost calculation job execution");

        using var scope = CreateScope();
        var appDbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        try
        {
            // Get all employees
            var allEmployees = await appDbContext.Employees
                .Where(e => !e.IsDeleted)
                .Include(e => e.Ranking)
                .ToListAsync(cancellationToken);

            LogInformation("Processing cost calculation for {0} employees", allEmployees.Count);

            var allRankings = await appDbContext.Rankings
                .Where(r => !r.IsDeleted)
                .ToListAsync(cancellationToken);

            var allEventCalendars = await appDbContext.EventCalendars
                .Where(e => !e.IsDeleted)
                .ToListAsync(cancellationToken);

            // Group employees by organization
            var employeeGroups = allEmployees.GroupBy(e => e.OrgUid)
                .ToDictionary(g => g.Key, g => g.ToList());

            LogInformation("Processing {0} organizations", employeeGroups.Count);

            // Process each month of the current year
            foreach (var month in Enumerable.Range(1, 12))
            {
                ThrowIfCancellationRequested(cancellationToken);

                var effectiveDateFrom = new DateOnly(DateTime.Now.Year, month, 1);
                var effectiveDateTo = effectiveDateFrom.AddMonths(1).AddDays(-1);

                LogInformation("Processing month {0}: {1} to {2}",
                    month, effectiveDateFrom, effectiveDateTo);

                foreach (var orgUid in employeeGroups.Keys)
                {
                    ThrowIfCancellationRequested(cancellationToken);

                    var rankingsInOrg = allRankings
                        .Where(r => r.OrgUid == orgUid)
                        .ToList();

                    if (rankingsInOrg.Count == 0)
                    {
                        LogWarning("No rankings found for organization: {0}", orgUid);
                        continue;
                    }

                    var eventCalendarsInOrg = allEventCalendars
                        .Where(e => e.OrgUid == orgUid)
                        .ToList();

                    var standardWorkingDateCount = CalculateStandardWorkingDateCount(
                        effectiveDateFrom, effectiveDateTo, eventCalendarsInOrg);

                    LogInformation("Organization {0} has {1} working days in month {2}",
                        orgUid, standardWorkingDateCount, month);

                    var employees = employeeGroups[orgUid];
                    foreach (var employee in employees)
                    {
                        ThrowIfCancellationRequested(cancellationToken);

                        await ProcessEmployeeCost(appDbContext, employee, effectiveDateFrom,
                            effectiveDateTo, standardWorkingDateCount, cancellationToken);
                    }
                }
            }

            await appDbContext.SaveChangesAsync(cancellationToken);
            LogInformation("Successfully completed employee cost calculation");
        }
        catch (OperationCanceledException)
        {
            LogWarning("Employee cost calculation job was cancelled");
            throw;
        }
        catch (Exception ex)
        {
            LogError("Error executing employee cost calculation job: {0}", ex, ex.Message);
            throw;
        }
    }

    private async Task ProcessEmployeeCost(AppDbContext appDbContext, Employee employee,
        DateOnly effectiveDateFrom, DateOnly effectiveDateTo, int standardWorkingDateCount,
        CancellationToken cancellationToken)
    {
        var employeeCost = await appDbContext.EmployeeCosts
            .Where(er => er.EmployeeUid == employee.EmployeeUid)
            .Where(er => er.EffectiveDate >= effectiveDateFrom && er.EffectiveDate <= effectiveDateTo)
            .FirstOrDefaultAsync(cancellationToken);

        var dailyCost = employee.SalaryInMonth / standardWorkingDateCount;

        if (employeeCost != null)
        {
            employeeCost.DailyCostAmount = dailyCost;
            employeeCost.EffectiveDate = effectiveDateFrom;
            appDbContext.EmployeeCosts.Update(employeeCost);
        }
        else
        {
            var newEmployeeCost = new EmployeeCost
            {
                EmployeeCostUid = GuidHelper.GenerateUUIDv7(),
                EmployeeUid = employee.EmployeeUid,
                DailyCostAmount = dailyCost,
                EffectiveDate = effectiveDateFrom,
            };

            await appDbContext.EmployeeCosts.AddAsync(newEmployeeCost, cancellationToken);
        }
    }

    private int CalculateStandardWorkingDateCount(DateOnly dateFrom, DateOnly dateTo,
        List<EventCalendar> eventCalendars)
    {
        var dayCount = (dateTo.ToDateTime(TimeOnly.MinValue) - dateFrom.ToDateTime(TimeOnly.MinValue)).Days + 1;

        var nonRecurringEvents = eventCalendars.Where(e => !e.IsRecurring).ToList();
        var recurringEvents = eventCalendars.Where(e => e.IsRecurring).ToList();

        for (var date = dateFrom; date <= dateTo; date = date.AddDays(1))
        {
            try
            {
                if (nonRecurringEvents.Any(e => e.EventStartDate <= date && e.EventEndDate >= date && e.IsDayOff))
                {
                    dayCount--;
                    continue;
                }

                if (recurringEvents.Any(e =>
                    e.IsDayOff &&
                    e.RecurringFrom <= date && e.RecurringTo >= date &&
                    (
                        (e.RecurringDay != null && e.RecurringDay.Contains((int)date.DayOfWeek == 0 ? 7 : (int)date.DayOfWeek) &&
                            (e.RecurringWeek == null || e.RecurringWeek.Contains((date.Day - 1) / 7 + 1))
                        )
                        ||
                        (e.RecurringMonth != null && e.RecurringMonth.Contains(date.Month))
                    )
                ))
                {
                    dayCount--;
                }
            }
            catch (Exception ex)
            {
                LogError("Error calculating standard working date count on date: {0}, {1}",
                    ex, date.ToString("yyyy-MM-dd"), ex.Message);
            }
        }

        return dayCount;
    }
}
