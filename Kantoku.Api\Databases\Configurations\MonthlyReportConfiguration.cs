using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class MonthlyReportConfiguration(string schema) : IEntityTypeConfiguration<MonthlyReport>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<MonthlyReport> builder)
    {
        builder.HasKey(e => e.MonthlyReportUid).HasName("employee_attendance_report_pkey");

        builder.ToTable("monthly_report", Schema, tb => tb.HasComment("thông số chốt công"));

        builder.HasIndex(e => new { e.EmployeeUid, e.ReportFrom, e.ReportTo }, "monthly_report_ukey").IsUnique();

        builder.Property(e => e.MonthlyReportUid)
            .HasColumnName("monthly_report_uid");
        builder.Property(e => e.EmployeeUid)
            .HasColumnName("employee_uid");
        builder.Property(e => e.ReportFrom)
            .HasColumnName("report_from");
        builder.Property(e => e.ReportTo)
            .HasColumnName("report_to");
        builder.Property(e => e.TotalWorkDays)
            .HasColumnName("total_work_days");
        builder.Property(e => e.TotalOffDays)
            .HasColumnName("total_off_days");
        builder.Property(e => e.TotalWorkHours)
            .HasColumnName("total_work_hours");
        builder.Property(e => e.TotalOvertime)
            .HasColumnName("total_overtime");
        builder.Property(e => e.SelfLeaveUsed)
            .HasColumnName("self_leave_used");
        builder.Property(e => e.OrgLeaveUsed)
            .HasColumnName("org_leave_used");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.IsApproved)
            .HasColumnName("is_approved");
        builder.Property(e => e.IsRequested)
            .HasDefaultValue(false)
            .HasColumnName("is_requested");
        builder.Property(e => e.ApproverUid)
            .HasColumnName("approver_uid");
        builder.Property(e => e.ApprovedTime)
            .HasColumnName("approved_time");

        builder.HasOne(d => d.Employee).WithMany(p => p.MonthlyReportsAuthor)
            .HasForeignKey(d => d.EmployeeUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("employee_attendance_report_employee_id_fkey");

        builder.HasOne(d => d.Approver).WithMany(p => p.MonthlyReportsApprover)
            .HasForeignKey(d => d.ApproverUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("monthly_report_approver_id_fkey");
    }
}
