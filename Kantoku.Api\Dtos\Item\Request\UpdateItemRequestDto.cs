﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Price.Request;

namespace Kantoku.Api.Dtos.Item.Request;

/// <summary>
/// Data transfer object for updating an item
/// </summary>
public class UpdateItemRequestDto
{
    /// <summary>
    /// The code of the item (*)
    /// </summary>
    public string? ItemCode { get; set; }

    /// <summary>
    /// The name of the item (*)
    /// </summary>
    public string? ItemName { get; set; }

    /// <summary>
    /// The sub name of the item
    /// </summary>
    public string? ItemSubName { get; set; }

    /// <summary>
    /// The size of the item
    /// </summary>
    public string? Size { get; set; }

    /// <summary>
    /// The serial number of the item
    /// </summary>
    public string? SerialNumber { get; set; }

    /// <summary>
    /// The category ID of the item
    /// </summary>
    public string? CategoryId { get; set; }

    /// <summary>
    /// The manufacturer ID of the item
    /// </summary>
    public string? ManufacturerId { get; set; }

    /// <summary>
    /// Additional description or notes about the item
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// The image of the item, if applicable.
    /// </summary>
    public IFormFile? Image { get; set; }
}
