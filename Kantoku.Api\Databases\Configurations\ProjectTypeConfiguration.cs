using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Kantoku.Api.Databases.Configurations;

public class ProjectTypeConfiguration(string schema) : IEntityTypeConfiguration<ProjectType>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<ProjectType> builder)
    {
        builder.HasKey(e => e.ProjectTypeUid).HasName("project_type_pkey");

        builder.ToTable("project_type", Schema);

        builder.Property(e => e.ProjectTypeUid)
            .HasColumnName("project_type_uid");
        builder.Property(e => e.ProjectTypeCode)
            .HasColumnName("project_type_code");
        builder.Property(e => e.TranslatedProjectType)
            .HasColumnName("translated_project_type")
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<IEnumerable<TranslatedProjectType>>(v) ?? Enumerable.Empty<TranslatedProjectType>()
            ).Metadata.SetValueComparer(
                new ValueComparer<IEnumerable<TranslatedProjectType>>(
                    (c1, c2) => (c1 ?? Enumerable.Empty<TranslatedProjectType>()).SequenceEqual(c2 ?? Enumerable.Empty<TranslatedProjectType>()),
                    c => c.Aggregate(0, (a, v) => a ^ v.GetHashCode()),
                    c => c.ToList()
                )
            );
    }
}

