using Kantoku.Api.Filters.Domains;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Filters.Domains;

public class WorkShiftFilter : BaseFilter
{
    /// <summary>
    /// The keyword to search for work shifts (name or code)
    /// </summary>
    [FromQuery(Name = "keyword")]
    public string? Keyword { get; set; }

    /// <summary>
    /// The project ID of the project that the work shift have been assigned into
    /// </summary>
    [FromQuery(Name = "projectId")]
    public Guid? ProjectId { get; set; }
}

