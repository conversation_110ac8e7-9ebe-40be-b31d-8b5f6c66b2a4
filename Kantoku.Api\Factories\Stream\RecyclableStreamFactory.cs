using Microsoft.IO;

namespace Kantoku.Api.Factories.Stream
{
    public static class RecyclableStreamFactory
    {
        private static RecyclableMemoryStreamManager manager = new();

        /// <summary>
        /// ConfigureStaticManager completely rebuilds the <c>RecyclableMemoryStreamManager</c> so try to call it only once, and on startup.
        /// </summary>
        /// <param name="blockSize"></param>
        /// <param name="largeBufferMultiple"></param>
        /// <param name="maximumBufferSize"></param>
        /// <param name="useExponentialLargeBuffer"></param>
        /// <param name="maximumSmallPoolFreeBytes"></param>
        /// <param name="maximumLargePoolFreeBytes"></param>
        public static void ConfigureNewStaticManager(
            int blockSize,
            int largeBufferMultiple,
            int maximumBufferSize,
            bool useExponentialLargeBuffer,
            long maximumSmallPoolFreeBytes,
            long maximumLargePoolFreeBytes)
        {
            var options = new RecyclableMemoryStreamManager.Options()
            {
                BlockSize = blockSize,
                LargeBufferMultiple = largeBufferMultiple,
                MaximumBufferSize = maximumBufferSize,
                UseExponentialLargeBuffer = useExponentialLargeBuffer,
                GenerateCallStacks = true,
                AggressiveBufferReturn = true,
                MaximumLargePoolFreeBytes = maximumLargePoolFreeBytes,
                MaximumSmallPoolFreeBytes = maximumSmallPoolFreeBytes,
            };
            manager = new RecyclableMemoryStreamManager(options);
        }

        /// <summary>
        /// ConfigureStaticManagerWithDefaults completely rebuilds the <c>RecyclableMemoryStreamManager</c> so try to call it only once, and on startup.
        /// </summary>
        /// <param name="useExponentialLargeBuffer"></param>
        public static void ConfigureNewStaticManagerWithDefaults(bool useExponentialLargeBuffer = false)
        {
            var blockSize = 1024;
            var largeBufferMultiple = 4 * blockSize * blockSize;
            var maximumBufferSize = 2 * largeBufferMultiple;
            var maximumFreeLargePoolBytes = 32 * maximumBufferSize;
            var maximumFreeSmallPoolBytes = 256 * blockSize;

            var options = new RecyclableMemoryStreamManager.Options()
            {
                BlockSize = blockSize,
                LargeBufferMultiple = largeBufferMultiple,
                MaximumBufferSize = maximumBufferSize,
                UseExponentialLargeBuffer = useExponentialLargeBuffer,
                GenerateCallStacks = true,
                AggressiveBufferReturn = true,
                MaximumLargePoolFreeBytes = maximumFreeLargePoolBytes,
                MaximumSmallPoolFreeBytes = maximumFreeSmallPoolBytes,
            };

            manager = new RecyclableMemoryStreamManager(options);
        }

        public static RecyclableMemoryStream GetStream()
        {
            return manager.GetStream();
        }

        public static RecyclableMemoryStream GetStream(string tag)
        {
            return manager.GetStream(tag);
        }

        public static RecyclableMemoryStream GetStream(string tag, int desiredSize)
        {
            return manager.GetStream(tag, desiredSize);
        }

        public static RecyclableMemoryStream GetStream(byte[] buffer)
        {
            return manager.GetStream(buffer);
        }

        public static RecyclableMemoryStream GetStream(string tag, byte[] buffer)
        {
            return manager.GetStream(tag, buffer);
        }

        public static void ReturnStream(MemoryStream stream)
        {
            stream.Dispose();
        }
    }
}