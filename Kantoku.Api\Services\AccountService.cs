using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Dtos.Account.Response;
using Kantoku.Api.Dtos.Account.Request;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Utils.Helpers;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Dtos.Base;

namespace Kantoku.Api.Services;

public interface IAccountService
{
    Task<ResultDto<AccountResponseDto>> CreateAccount(CreateAccountRequestDto requestDto);
    Task<ResultDto<AccountResponseDto>> UpdateAccount(UpdateAccountRequestDto requestDto);
    Task<ResultDto<bool>> CheckAccountExist(string? email, string? loginId);

    Task<ResultDto<PendingInvitationsResponseDto>> GetPendingInvitation();
    Task<ResultDto<bool>> AcceptInvitation(InvitationAcceptRequestDto dto);
}

[Service(ServiceLifetime.Scoped)]
public class AccountService : BaseService<AccountService>, IAccountService
{
    private readonly IAccountRepository accountRepository;
    private readonly IEmployeeInvitationRepository employeeInvitationRepository;
    private readonly IEmployeeRepository employeeRepository;
    private readonly IOrgRepository orgRepository;
    private readonly IEmailService emailService;
    private readonly IRedisCacheService redisCacheService;
    public AccountService(
        IAccountRepository accountRepository,
        IEmailService emailService,
        IEmployeeInvitationRepository employeeInvitationRepository,
        IEmployeeRepository employeeRepository,
        IOrgRepository orgRepository,
        IRedisCacheService redisCacheService,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor)
        : base(logger, httpContextAccessor)
    {
        this.accountRepository = accountRepository;
        this.emailService = emailService;
        this.employeeRepository = employeeRepository;
        this.redisCacheService = redisCacheService;
        this.employeeInvitationRepository = employeeInvitationRepository;
        this.orgRepository = orgRepository;
    }

    public async Task<ResultDto<bool>> CheckAccountExist(string? email, string? loginId)
    {
        var isExist = await accountRepository.IsExist([email, loginId]);
        return new SuccessResultDto<bool>(isExist);
    }

    public async Task<ResultDto<PendingInvitationsResponseDto>> GetPendingInvitation()
    {
        var account = await accountRepository.GetById(GetCurrentAccountUid(), new AccountQueryableOptions());
        if (account is null)
        {
            logger.Error("Account not found");
            return new ErrorResultDto<PendingInvitationsResponseDto>(ResponseCodeConstant.ACCOUNT_NOT_EXIST);
        }
        var (employeeInvitations, _) = await employeeInvitationRepository.GetByFilter(new EmployeeInvitationFilter
        {
            InvitedEmail = account?.Email,
            IsAccepted = false,
        }, new EmployeeInvitationQueryableOptions());
        if (!employeeInvitations.Any())
        {
            logger.Warning("No pending invitation found");
            return new ErrorResultDto<PendingInvitationsResponseDto>(ResponseCodeConstant.EMPLOYEE_INVITATION_NOT_EXIST);
        }
        var pendingInvitations = new List<PendingInvitationResponseDto>();
        foreach (var employeeInvitation in employeeInvitations)
        {
            var org = await orgRepository.GetById(employeeInvitation.OrgUid);
            if (org is null)
            {
                logger.Error("Org not found with id: {OrgUid}", employeeInvitation.OrgUid);
                continue;
            }
            pendingInvitations.Add(new PendingInvitationResponseDto
            {
                InvitationId = employeeInvitation.EmployeeInvitationUid.ToString(),
                OrgId = org.OrgUid.ToString(),
                OrgName = org.OrgName,
                InvitationDescription = employeeInvitation.InvitationDescription,
                ExpireTime = employeeInvitation.ExpiredTime?.ToString("yyyy-MM-dd HH:mm:ss"),
            });
        }
        var result = new PendingInvitationsResponseDto
        {
            Items = pendingInvitations,
        };
        return new SuccessResultDto<PendingInvitationsResponseDto>(result);
    }

    public async Task<ResultDto<AccountResponseDto>> CreateAccount(CreateAccountRequestDto dto)
    {
        var redisKey = RedisKeyConst.OTP_CODE(dto.Email);
        var otpCode = await redisCacheService.GetAsync<string>(redisKey);
        if (otpCode is null)
        {
            logger.Warning("OTP code not found");
            return new ErrorResultDto<AccountResponseDto>(ResponseCodeConstant.OTP_CODE_NOT_EXIST);
        }
        if (otpCode != dto.OTP)
        {
            logger.Warning("OTP code is incorrect");
            return new ErrorResultDto<AccountResponseDto>(ResponseCodeConstant.OTP_CODE_INCORRECT);
        }
        var isExist = await accountRepository.IsExist([dto.LoginId, dto.Email]);
        if (isExist)
        {
            logger.Warning("Account {LoginId} already exists", dto.LoginId);
            return new ErrorResultDto<AccountResponseDto>(ResponseCodeConstant.ACCOUNT_ALREADY_EXISTS);
        }

        var account = new Account
        {
            AccountUid = GuidHelper.GenerateUUIDv7(),
            LoginId = dto.LoginId,
            Email = dto.Email,
            Password = StringHelper.HashPassword(dto.Password),
            HashedPassword = dto.HashedPassword is not null ? StringHelper.HashPassword(dto.HashedPassword) : null,
            AccountType = AccountTypeConstant.USER,
            IsDeleted = false,
            IsLocked = false,
        };
        var userInfo = new UserInfo
        {
            UserInfoUid = GuidHelper.GenerateUUIDv7(),
            AccountUid = account.AccountUid,
            Name = dto.UserInfo.Name,
            Address = dto.UserInfo.Address,
            Phone = dto.UserInfo.Phone,
            Gender = dto.UserInfo.Gender,
            Birthday = DateOnly.TryParse(dto.UserInfo.Birthday, out var birthday) ? birthday : null,
        };
        account.UserInfo = userInfo;
        var createdAccount = await accountRepository.Create(account);
        if (createdAccount is null)
        {
            logger.Error("Failed to create account for user {Email}", dto.Email);
            return new ErrorResultDto<AccountResponseDto>(ResponseCodeConstant.ACCOUNT_CREATE_FAILED);
        }

        var template = await emailService.GetTemplate(EmailTemplateConstant.ACCOUNT_CREATED_NOTI_TEMPLATE);
        if (template is null || template.Equals(string.Empty))
        {
            logger.Error("Template not found");
            return new ErrorResultDto<AccountResponseDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        template = template.Replace("{{UserName}}", account.UserInfo.Name);
        template = template.Replace("{{UserLoginID}}", account.LoginId);
        template = template.Replace("{{UserEmail}}", account.Email);
        await emailService.SendAsync(dto.Email, template);

        var result = new AccountResponseDto
        {
            AccountUid = createdAccount.ToString(),
            UserName = account.UserInfo.Name,
            Email = account.Email,
            LoginId = account.LoginId,
        };

        return new SuccessResultDto<AccountResponseDto>(result);
    }

    public async Task<ResultDto<AccountResponseDto>> UpdateAccount(UpdateAccountRequestDto requestDto)
    {
        var account = await accountRepository.GetById(GetCurrentAccountUid(), new AccountQueryableOptions());
        if (account is null)
        {
            logger.Error("Account not found with uid: {AccountUid}", GetCurrentAccountUid());
            return new ErrorResultDto<AccountResponseDto>(ResponseCodeConstant.ACCOUNT_NOT_EXIST);
        }

        var isExistAccount = await accountRepository.IsExist([requestDto.LoginId, requestDto.Email]);
        if (isExistAccount)
        {
            logger.Error("Account already exists with email: {Email}/ loginId: {LoginId}", requestDto.Email, requestDto.LoginId);
            return new ErrorResultDto<AccountResponseDto>(ResponseCodeConstant.ACCOUNT_INFO_ALREADY_EXISTS);
        }
        try
        {
            ObjectHelper.UpdateEntityFromDto(requestDto, account);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating account");
            return new ErrorResultDto<AccountResponseDto>(ResponseCodeConstant.ACCOUNT_UPDATE_FAILED);
        }
        var updatedAccount = await accountRepository.Update(account);
        if (!updatedAccount)
        {
            logger.Error("Account update failed with email: {Email} and loginId: {LoginId}", requestDto.Email, requestDto.LoginId);
            return new ErrorResultDto<AccountResponseDto>(ResponseCodeConstant.ACCOUNT_UPDATE_FAILED);
        }
        var result = new AccountResponseDto
        {
            AccountUid = updatedAccount.ToString(),
            // UserName = updatedAccount.UserInfo.Name,
            // Email = updatedAccount.Email,
            // LoginId = updatedAccount.LoginId,
        };
        return new SuccessResultDto<AccountResponseDto>(result);
    }

    public async Task<ResultDto<bool>> AcceptInvitation(InvitationAcceptRequestDto dto)
    {

        var employeeInvitation = await employeeInvitationRepository.GetById(dto.InvitationId, new EmployeeInvitationQueryableOptions());
        if (employeeInvitation is null)
        {
            logger.Error("Employee invitation not found with id: {InvitationId}", dto.InvitationId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.EMPLOYEE_INVITATION_NOT_EXIST);
        }
        if (dto.IsAccept)
        {
            var isLinked = await LinkEmployee(employeeInvitation);
            if (!isLinked)
            {
                logger.Error("Failed to link employee with invitation id: {InvitationId}", dto.InvitationId);
                return new ErrorResultDto<bool>(ResponseCodeConstant.LINK_EMPLOYEE_FAILED);
            }
            employeeInvitation.IsAccepted = true;
            employeeInvitation.AcceptedTime = DateTime.Now;
            await employeeInvitationRepository.Update(employeeInvitation);
            return new SuccessResultDto<bool>(true);
        }
        employeeInvitation.IsDeleted = true;
        await employeeInvitationRepository.Update(employeeInvitation);
        return new SuccessResultDto<bool>(true);
    }

    private async Task<bool> LinkEmployee(EmployeeInvitation employeeInvitation)
    {
        var account = await accountRepository.GetById(GetCurrentAccountUid(), new AccountQueryableOptions());
        if (account is null)
        {
            logger.Error("Account not found with uid: {AccountUid}", GetCurrentAccountUid());
            return false;
        }

        var newEmployee = new Employee
        {
            AccountUid = GetCurrentAccountUid(),
            OrgUid = employeeInvitation.OrgUid,
            EmployeeCode = employeeInvitation.PreassignedEmployeeCode,
            EmployeeName = account.UserInfo.Name,
            WorkingStatus = StatusConstants.INVITED,
            IsDeleted = false,
            IsOrgOwner = false,
            IsOrgAdmin = false,
            IsHidden = false,
        };

        if (employeeInvitation.PreassignedRoleUid?.Count > 0)
        {
            var employeeRoles = employeeInvitation.PreassignedRoleUid
                .Select(roleId => new EmployeeRole
                {
                    EmployeeUid = newEmployee.EmployeeUid,
                    RoleUid = roleId,
                }).ToList();
            newEmployee.EmployeeRoles = employeeRoles;
        }

        var createdEmployee = await employeeRepository.Create(newEmployee, new EmployeeQueryableOptions
        {
            IncludedUserInfo = true,
            IncludedOrg = true,
        });
        if (createdEmployee is null)
        {
            logger.Error("Failed to create employee with email: {Email}", employeeInvitation.Email);
            return false;
        }

        try
        {
            await Task.WhenAll(
                NotifyInvitedEmployee(employeeInvitation, createdEmployee),
                NotifyInvitor(employeeInvitation, createdEmployee)
            );
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error notifying invited employee and invitor");
        }
        return true;
    }

    private async Task NotifyInvitedEmployee(EmployeeInvitation employeeInvitation, Employee employee)
    {
        var employeeInvitationTemplate = await emailService.GetTemplate(EmailTemplateConstant.EMPLOYEE_LINKED_NOTI_TEMPLATE);
        if (employeeInvitationTemplate is null || employeeInvitationTemplate.Equals(string.Empty))
        {
            logger.Error("Employee invitation template not found");
            return;
        }
        employeeInvitationTemplate = employeeInvitationTemplate.Replace("{{UserName}}", employee.Account.UserInfo.Name);
        employeeInvitationTemplate = employeeInvitationTemplate.Replace("{{OrgName}}", employee.Org.OrgName);
        employeeInvitationTemplate = employeeInvitationTemplate.Replace("{{EmployeeId}}", employee.EmployeeCode);
        await emailService.SendAsync(employeeInvitation.Email, employeeInvitationTemplate);
    }


    private async Task NotifyInvitor(EmployeeInvitation employeeInvitation, Employee invitedEmployee)
    {
        if (employeeInvitation.CreatedBy is null)
        {
            logger.Error("Invitor not found with id: {AccountUid}", employeeInvitation.CreatedBy);
            return;
        }
        var invitor = await accountRepository.GetById(Guid.Parse(employeeInvitation.CreatedBy), new AccountQueryableOptions
        {
            IncludedUserInfo = true,
        });
        if (invitor is null)
        {
            logger.Error("Invitor not found with id: {AccountUid}", employeeInvitation.CreatedBy);
            return;
        }

        var template = await emailService.GetTemplate(EmailTemplateConstant.EMPLOYEE_INVITATION_ACCEPTED_NOTI_TEMPLATE);
        if (template is null || template.Equals(string.Empty))
        {
            logger.Error("Template not found");
            return;
        }
        template = template.Replace("{{InviterName}}", invitor.UserInfo.Name);
        template = template.Replace("{{InvitedUserName}}", invitedEmployee.Account.UserInfo.Name);
        template = template.Replace("{{EmployeeId}}", invitedEmployee.EmployeeCode);
        await emailService.SendAsync(invitor.Email, template);
    }
}