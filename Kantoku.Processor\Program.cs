using Kantoku.Processor.Configurations;
using Kantoku.Processor.Data.Contexts;
using Kantoku.Processor.Data.Seeders;
using Kantoku.Processor.Extensions;
using EventBus.Kafka.Extensions;
using EventBus.Kafka.Configuration;
using EventBus.Kafka;
using EventBus.Interfaces;
using EventBus.Events;
using Kantoku.Processor.Services.EventHandlers;
using Kantoku.Processor.Services;

var builder = WebApplication.CreateBuilder(args);

var env = builder.Environment;
builder.Configuration.AddEnvironmentVariables();
builder.Configuration.AddJsonFile($"appsettings.{env.EnvironmentName}.json", optional: false, reloadOnChange: false);

// Configure options
builder.Services.Configure<AppConfig>(builder.Configuration.GetSection("AppConfig"));
builder.Services.Configure<JobScheduleConfig>(builder.Configuration.GetSection("BatchProcessing"));
builder.Services.Configure<JobDbConfig>(builder.Configuration.GetSection("JobDbConfig"));
builder.Services.Configure<AppDbConfig>(builder.Configuration.GetSection("AppDbConfig"));

// Configure job database context
builder.Services.AddDbContext<JobDbContext>(options =>
{
    if (env.IsDevelopment())
    {
        options.EnableDetailedErrors();
        options.EnableSensitiveDataLogging();
    }
});

// Configure application database context
builder.Services.AddDbContext<AppDbContext>(options =>
{
    if (env.IsDevelopment())
    {
        options.EnableDetailedErrors();
        options.EnableSensitiveDataLogging();
    }
});

// Configure services
builder.Services.ServiceCollect();

// Configure EventBus - Consumer focused for processing events
// Register consumer without hosted service to avoid conflicts
var kafkaSection = builder.Configuration.GetSection("Kafka");
builder.Services.Configure<KafkaConfig>(kafkaSection);
builder.Services.AddSingleton<IEventConsumer, KafkaEventConsumer>();
builder.Services.AddEventHandler<AttendanceEvent, AttendanceEventHandler>();
// Use our custom hosted service that handles both subscription and starting
builder.Services.AddHostedService<EventBusHostedService>();

// Configure Caching
builder.Services.AddDistributedMemoryCache();

var app = builder.Build();

// Seed database
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        var seeder = services.GetRequiredService<DatabaseSeeder>();
        await seeder.SeedAsync();
    }
    catch (Exception ex)
    {
        var logger = services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "An error occurred while seeding the database");
    }
}

// Run as a service without handling HTTP requests
await app.RunAsync();
