using Kantoku.Processor.Configurations;
using Kantoku.Processor.Data.Contexts;
using Kantoku.Processor.Data.Seeders;
using Kantoku.Processor.Extensions;
using EventBus.Kafka.Extensions;
using EventBus.Events;
using Kantoku.Processor.Services.EventHandlers;
using Kantoku.Processor.Services;
using Hangfire;
using Hangfire.PostgreSql;
using Hangfire.Console;
using Kantoku.Processor.Jobs.Hangfire;

var builder = WebApplication.CreateBuilder(args);

var env = builder.Environment;
builder.Configuration.AddEnvironmentVariables();
builder.Configuration.AddJsonFile($"appsettings.{env.EnvironmentName}.json", optional: false, reloadOnChange: false);

// Configure options
builder.Services.Configure<AppConfig>(builder.Configuration.GetSection("AppConfig"));
builder.Services.Configure<JobScheduleConfig>(builder.Configuration.GetSection("BatchProcessing"));
builder.Services.Configure<JobDbConfig>(builder.Configuration.GetSection("JobDbConfig"));
builder.Services.Configure<AppDbConfig>(builder.Configuration.GetSection("AppDbConfig"));
builder.Services.Configure<HangfireConfig>(builder.Configuration.GetSection("HangfireConfig"));

// Configure job database context
builder.Services.AddDbContext<JobDbContext>(options =>
{
    if (env.IsDevelopment())
    {
        options.EnableDetailedErrors();
        options.EnableSensitiveDataLogging();
    }
});

// Configure application database context
builder.Services.AddDbContext<AppDbContext>(options =>
{
    if (env.IsDevelopment())
    {
        options.EnableDetailedErrors();
        options.EnableSensitiveDataLogging();
    }
});

// Configure services
builder.Services.ServiceCollect();

// Configure EventBus for Hangfire jobs
builder.Services.AddKafkaEventConsumer(builder.Configuration);
builder.Services.AddEventHandler<AttendanceEvent, AttendanceEventHandler>();

// Configure Hangfire
var hangfireConfig = builder.Configuration.GetSection("HangfireConfig").Get<HangfireConfig>() ?? new HangfireConfig();
builder.Services.AddHangfire(config => config
    .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
    .UseSimpleAssemblyNameTypeSerializer()
    .UseRecommendedSerializerSettings()
    .UseConsole() // Enable Hangfire Console for detailed logging
    .UsePostgreSqlStorage(c => c.UseNpgsqlConnection(hangfireConfig.BuildConnectionString()),
        new PostgreSqlStorageOptions
        {
            SchemaName = hangfireConfig.Schema
        }));

// Add Hangfire server
builder.Services.AddHangfireServer(options =>
{
    options.WorkerCount = hangfireConfig.WorkerCount;
});

// Register Hangfire jobs
builder.Services.AddScoped<AutoCheckoutHangfireJob>();
builder.Services.AddScoped<CheckOutReminderHangfireJob>();
builder.Services.AddScoped<EmployeeCostCalculateHangfireJob>();
builder.Services.AddScoped<EventProcessingHangfireJob>();

// Configure Caching
builder.Services.AddDistributedMemoryCache();

var app = builder.Build();

// Configure Hangfire dashboard
if (hangfireConfig.EnableDashboard)
{
    app.UseHangfireDashboard(hangfireConfig.DashboardPath);
}

// Seed database
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        var seeder = services.GetRequiredService<DatabaseSeeder>();
        await seeder.SeedAsync();
    }
    catch (Exception ex)
    {
        var logger = services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "An error occurred while seeding the database");
    }
}

// Schedule recurring jobs
RecurringJob.AddOrUpdate<AutoCheckoutHangfireJob>(
    "auto-checkout-job",
    job => job.Execute(null!, CancellationToken.None),
    "0 */1 * * *"); // Every hour

RecurringJob.AddOrUpdate<CheckOutReminderHangfireJob>(
    "checkout-reminder-job",
    job => job.Execute(null!, CancellationToken.None),
    "*/5 * * * *"); // Every 5 minutes

RecurringJob.AddOrUpdate<EmployeeCostCalculateHangfireJob>(
    "employee-cost-calculate-job",
    job => job.Execute(null!, CancellationToken.None),
    "0 2 1 * *"); // Monthly at 2 AM on the 1st

// Start continuous event processing job
BackgroundJob.Enqueue<EventProcessingHangfireJob>(job => job.Execute(null!, CancellationToken.None));

// Run as a service
await app.RunAsync();
