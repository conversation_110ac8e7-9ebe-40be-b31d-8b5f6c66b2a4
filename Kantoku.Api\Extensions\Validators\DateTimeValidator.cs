using System.ComponentModel.DataAnnotations;
using System.Globalization;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Exceptions;

namespace Kantoku.Api.Extensions.Validators
{
    [AttributeUsage(AttributeTargets.Property)]
    public class DateTimeValidator : ValidationAttribute
    {
        private readonly string[] _dateFormats = ["yyyy-MM-dd", "yyyy/MM/dd", "dd/MM/yyyy", "dd-MM-yyyy"];
        private readonly string[] _timeFormats = ["HH:mm:ss", "HH:mm", "HH:mm:ss.fff", "HH:mm:ss.ffffff", "HH:mm:ss.fffffffff"];
        private readonly string[] _dateTimeFormats = ["yyyy-MM-dd HH:mm:ss", "yyyy-MM-ddTHH:mm:ss", "yyyy-MM-dd HH:mm:ss.fff", "yyyy-MM-dd HH:mm", "yyyy-MM-dd HH:mm:ss.fff"];
        private readonly Type type;

        public DateTimeValidator(Type type)
        {
            this.type = type;
        }

        protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
        {
            if (value is null)
                return ValidationResult.Success;

            var valueString = value.ToString();
            if (string.IsNullOrEmpty(valueString))
                return new ValidationResult("The date/time value is required.");

            if (type == typeof(DateTime))
            {
                if (DateTime.TryParse(valueString, CultureInfo.InvariantCulture, DateTimeStyles.None, out _))
                {
                    return ValidationResult.Success;
                }
                return new ValidationResult($"Invalid {type.Name} format, expected format: {string.Join(", ", _dateTimeFormats)}");
            }

            if (type == typeof(DateOnly))
            {
                foreach (var format in _dateFormats)
                {
                    if (DateOnly.TryParseExact(valueString, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out _))
                    {
                        return ValidationResult.Success;
                    }
                }
                return new ValidationResult($"Invalid {type.Name} format, expected format: {string.Join(", ", _dateFormats)}");
            }

            if (type == typeof(TimeOnly))
            {
                foreach (var format in _timeFormats)
                {
                    if (TimeOnly.TryParseExact(valueString, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out _))
                    {
                        return ValidationResult.Success;
                    }
                }
                return new ValidationResult($"Invalid {type.Name} format, expected format: {string.Join(", ", _timeFormats)}");
            }

            return new ValidationResult($"Invalid {type.Name}");
        }
    }
}

