﻿using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.Category.Request;

/// <summary>
/// Data transfer object for creating a new category
/// </summary>
public class CreateCategoryRequestDto
{
    /// <summary>
    /// Unique code for the category (*)
    /// </summary>
    [Required]
    public string CategoryCode { get; set; } = null!;

    /// <summary>
    /// Name of the category (*)
    /// </summary>
    [Required]
    public string CategoryName { get; set; } = null!;

    /// <summary>
    /// Optional description of the category
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Optional ID of the parent category (*)
    /// </summary>
    [Required]
    public required Guid ParentId { get; set; }
}

