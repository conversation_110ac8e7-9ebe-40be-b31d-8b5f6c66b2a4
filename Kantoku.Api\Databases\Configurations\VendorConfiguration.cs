using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class VendorConfiguration(string schema) : IEntityTypeConfiguration<Vendor>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Vendor> builder)
    {
        builder.ToTable("vendor", Schema, tb => tb.HasComment("Lưu thông tin nhà cung cấp"));

        builder.HasKey(e => e.VendorUid).HasName("vendor_pkey");

        builder.Property(e => e.VendorUid)
            .HasColumnName("vendor_uid");
        builder.Property(e => e.VendorCode)
            .HasColumnName("vendor_code");
        builder.Property(e => e.VendorName)
            .HasColumnName("vendor_name");
        builder.Property(e => e.VendorSubName)
            .HasColumnName("vendor_sub_name");
        builder.Property(e => e.CorporateNumber)
            .HasColumnName("corporate_number");
        builder.Property(e => e.Address)
            .HasColumnName("address");
        builder.Property(e => e.PhoneNumber)
            .HasColumnName("phone_number");
        builder.Property(e => e.Email)
            .HasColumnName("email");
        builder.Property(e => e.ContactPerson)
            .HasColumnType("jsonb")
            .HasColumnName("contact_person")
            .HasConversion(
                v => Newtonsoft.Json.JsonConvert.SerializeObject(v),
                v => Newtonsoft.Json.JsonConvert.DeserializeObject<ContactPerson>(v)
            );
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.LogoUrl)
            .HasColumnName("logo_url");

        builder.HasOne(d => d.Org)
            .WithMany(p => p.Vendors)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("vendor_org_id_fkey");
    }
} 