using Kantoku.Processor.Dtos;

namespace Kantoku.Processor.Services.Interfaces;

public interface IFirebaseService
{
    Task SendToDevice(string deviceToken, FirebaseMessageDto notification);
    Task SendToDevice(string deviceTokens, IEnumerable<FirebaseMessageDto> notifications);

    Task SendToDevices(IEnumerable<string> deviceTokens, FirebaseMessageDto notification);
    Task SendToDevices(IEnumerable<string> deviceTokens, IEnumerable<FirebaseMessageDto> notifications);

    Task SendToTopic(string topic, FirebaseMessageDto notification);
    Task SendToTopic(string topic, IEnumerable<FirebaseMessageDto> notifications);
}

