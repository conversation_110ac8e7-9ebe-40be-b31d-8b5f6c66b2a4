using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.Position.Request;

public class UpdatePositionRequestDto
{
    /// <summary>
    /// The code assigned to the position
    /// </summary>
    public string? PositionCode { get; set; }

    /// <summary>
    /// The name of the position
    /// </summary>
    public string? PositionName { get; set; }

    /// <summary>
    /// Additional details about the position
    /// </summary>
    public string? Description { get; set; }
}
