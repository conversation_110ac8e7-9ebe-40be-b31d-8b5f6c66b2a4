using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.Position.Request;

/// <summary>
/// Data transfer object for creating a new position
/// </summary>
public class CreatePositionRequestDto
{
    /// <summary>
    /// The unique code identifier for the position (*)
    /// </summary>
    [Required]
    public string PositionCode { get; set; } = null!;

    /// <summary>
    /// The name of the position (*)
    /// </summary>
    [Required]
    public string PositionName { get; set; } = null!;

    /// <summary>
    /// Optional description of the position
    /// </summary>
    public string? Description { get; set; }
}
