﻿using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.InputCostItem.Request;

/// <summary>
/// Data transfer object for updating an input cost item
/// </summary>
public class UpdateInputCostItemRequestDto
{
    /// <summary>
    /// The ID of the construction
    /// </summary>
    public Guid? ConstructionId { get; set; }

    /// <summary>
    /// The transaction date of the cost item
    /// </summary>
    [DateTimeValidator(typeof(DateOnly))]
    public string? TransactionDate { get; set; }

    /// <summary>
    /// The ID of the item
    /// </summary>
    public Guid? ItemId { get; set; }

    /// <summary>
    /// The unit of measurement for the item
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// The ID of the vendor
    /// </summary>
    public Guid? VendorId { get; set; }

    /// <summary>
    /// The quantity of the item
    /// </summary>
    public float? Quantity { get; set; }

    /// <summary>
    /// The price per unit
    /// </summary>
    public int? Price { get; set; }

    /// <summary>
    /// The tax rate applied
    /// </summary>
    public float? TaxRate { get; set; }

    /// <summary>
    /// The total non-taxed amount
    /// </summary>
    public float? TotalNonTaxed { get; set; }

    /// <summary>
    /// The total taxed amount
    /// </summary>
    public float? TotalTaxed { get; set; }

    /// <summary>
    /// Additional description or notes
    /// </summary>
    public string? Description { get; set; }
}
