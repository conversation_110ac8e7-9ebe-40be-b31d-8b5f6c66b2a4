using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;

namespace Kantoku.Api.Databases.Repositories;

public interface IGlobalConfigRepository
{
    Task<GlobalConfig?> Get<PERSON>y<PERSON><PERSON>(string key);
}

[Repository(ServiceLifetime.Scoped)]
public class GlobalConfigRepository : BaseRepository<GlobalConfig>, IGlobalConfigRepository
{
    public GlobalConfigRepository(PostgreDbContext context, IHttpContextAccessor httpContextAccessor, Serilog.ILogger logger)
        : base(context, logger, httpContextAccessor)
    {
    }

    public async Task<GlobalConfig?> GetByKey(string key)
    {
        try
        {
            var orgUid = GetCurrentOrgUid();
            var globalConfig = await context.GlobalConfigs
                .Where(gc => gc.OrgUid.ToString().Equals(orgUid) && gc.Key.Equals(key))
                .FirstOrDefaultAsync();
            return globalConfig;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting global config with key: {Key}", key);
            return null;
        }
    }
}