using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.Structure.Request;

/// <summary>
/// DTO for updating structure information
/// </summary>
public class UpdateStructureRequestDto
{
    /// <summary>
    /// Code identifier for the structure
    /// </summary>
    public string? StructureCode { get; set; }

    /// <summary>
    /// Name of the structure
    /// </summary>
    public string? StructureName { get; set; }

    /// <summary>
    /// ID of the parent structure
    /// </summary>
    public string? StructureParentId { get; set; }

    /// <summary>
    /// Description of the structure
    /// </summary>
    public string? Description { get; set; }
}
