using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Databases.Models;

namespace Kantoku.Api.Dtos.Contractor.Request;
public class UpdateContractorRequestDto
{
    /// <summary>
    /// Contractor code (*)
    /// </summary>
    public string? ContractorCode { get; set; }

    /// <summary>
    /// Contractor name (*)
    /// </summary>
    public string? ContractorName { get; set; }

    /// <summary>
    /// Contractor sub name
    /// </summary>
    public string? ContractorSubName { get; set; }

    /// <summary>
    /// Contractor description
    /// </summary>
    public string? Description { get; set; }


    /// <summary>
    /// The corporate number of the contractor
    /// </summary>
    public string? CorporateNumber { get; set; }

    /// <summary>
    /// The address of the contractor
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// The phone number of the contractor
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// The email of the contractor
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// The contact person of the contractor
    /// </summary>
    public ContactPerson? ContactPerson { get; set; }

    /// <summary>
    /// The logo of the contractor
    /// </summary>
    public IFormFile? Logo { get; set; }
}
