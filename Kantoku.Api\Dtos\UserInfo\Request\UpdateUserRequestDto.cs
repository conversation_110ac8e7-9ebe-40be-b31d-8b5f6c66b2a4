using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.UserInfo.Request;

public class UpdateUserInfoRequestDto
{
    /// <summary>
    /// User's full name
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// User's physical address
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// User's phone number
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// User's gender (true for male, false for female)
    /// </summary>
    public bool? Gender { get; set; }

    /// <summary>
    /// User's date of birth in yyyy-MM-dd format
    /// </summary>
    [DateTimeValidator(typeof(DateOnly))]
    public string? Birthday { get; set; }
}