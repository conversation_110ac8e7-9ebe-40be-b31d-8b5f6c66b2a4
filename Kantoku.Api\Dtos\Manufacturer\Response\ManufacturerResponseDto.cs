﻿using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Base.Response;

namespace Kantoku.Api.Dtos.Manufacturer.Response;

/// <summary>
/// Data transfer object for manufacturer details
/// </summary>
public class ManufacturerResponseDto : BaseResponseDto
{
    /// <summary>
    ///  the manufacturer identifier
    /// </summary>
    public string? ManufacturerId { get; set; }

    /// <summary>
    ///  the code of the manufacturer (*)
    /// </summary>
    public string? ManufacturerCode { get; set; }

    /// <summary>
    ///  the name of the manufacturer (*)
    /// </summary>
    public string? ManufacturerName { get; set; }

    /// <summary>
    ///  the sub-name of the manufacturer
    /// </summary>
    public string? ManufacturerSubName { get; set; }

    /// <summary>
    ///  the description of the manufacturer
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Corporate number
    /// </summary>
    public string? CorporateNumber { get; set; }

    /// <summary>
    /// Address
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Phone number
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Email
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Contact person
    /// </summary>
    public ContactPerson? ContactPerson { get; set; }

    /// <summary>
    /// Logo URL
    /// </summary>
    public string? LogoUrl { get; set; }
}

