using Kantoku.Api.Dtos.Account.Request;
using Kantoku.Api.Dtos.Account.Response;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
public class AccountController : BaseController
{
    private readonly IAccountService accountService;

    public AccountController(
        ITResponseFactory responseFactory,
        IAccountService accountService
    ) : base(responseFactory)
    {
        this.accountService = accountService;
    }

    /// <summary>
    /// Check if an account exists by email or login ID
    /// </summary>
    /// <param name="email">Email address to check</param>
    /// <param name="loginId">Login ID to check</param>
    /// <returns>True if account exists, false otherwise</returns>
    [AllowAnonymous]
    [HttpGet("identity")]
    public async Task<GeneralResponse<bool>> CheckAccountExist([FromQuery] string? email, [FromQuery] string? loginId)
    {
        try
        {
            var result = await accountService.CheckAccountExist(email, loginId);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode.ToString());
        }
    }

    /// <summary>
    /// Get pending invitations of the current user
    /// </summary>
    /// <returns>Pending invitations of the current user</returns>
    [Authorize(Policy = PolicyConstant.ACCOUNT_ACCESS)]
    [HttpGet("invitation")]
    public async Task<GeneralResponse<PendingInvitationsResponseDto>> GetPendingInvitation()
    {
        try
        {
            var result = await accountService.GetPendingInvitation();
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<PendingInvitationsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new account
    /// </summary>
    /// <param name="dto">Account creation details including email, login ID and password</param>
    /// <returns>Success response with no data if account created successfully</returns>
    [AllowAnonymous]
    [HttpPost()]
    public async Task<GeneralResponse<AccountResponseDto>> CreateAccount([FromBody] CreateAccountRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<AccountResponseDto>();

            var result = await accountService.CreateAccount(dto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AccountResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Accept invitation to join an organization
    /// </summary>
    /// <returns>Success response with no data</returns>
    [Authorize(Policy = PolicyConstant.ACCOUNT_ACCESS)]
    [HttpPost("invitation/accept")]
    public async Task<GeneralResponse<bool>> AcceptInvitation([FromBody] InvitationAcceptRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<bool>();

            var result = await accountService.AcceptInvitation(dto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update an existing account's email or login ID
    /// </summary>
    /// <param name="requestDto">Updated account details containing new email or login ID</param>
    /// <returns>Updated account information</returns>
    [Authorize(Policy = PolicyConstant.ACCOUNT_ACCESS)]
    [HttpPut]
    public async Task<GeneralResponse<AccountResponseDto>> UpdateAccount([FromBody] UpdateAccountRequestDto requestDto)
    {
        try
        {


            var result = await accountService.UpdateAccount(requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AccountResponseDto>(e.ErrorCode);
        }
    }
}
