using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.Auth.Request;

public class ChangePasswordRequestDto
{
    /// <summary>
    /// (*) Current password of the user
    /// </summary>
    [Required]
    public required string OldPassword { get; set; }

    /// <summary>
    /// (*) New password to replace the current password
    /// </summary>
    [Required]
    [Passwordvalidator]
    public required string NewPassword { get; set; }

    /// <summary>
    /// (*) Confirmation of the new password
    /// </summary>
    [Required]
    [Passwordvalidator]
    public required string ConfirmNewPassword { get; set; }
}
