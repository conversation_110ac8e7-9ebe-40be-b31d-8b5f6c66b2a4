using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class PositionQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedEmployees { get; set; } = false;
}

public interface IPositionQueryable
{
    IQueryable<Position> GetPositionQueryable(
        PositionQueryableOptions options
    );
    IQueryable<Position> GetPositionQueryableIncluded(
        PositionQueryableOptions options,
        IQueryable<Position>? query = null
    );

    IQueryable<Position> GetPositionQueryFiltered(
        PositionFilter filter,
        PositionQueryableOptions options,
        IQueryable<Position>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]

public class PositionQueryable(PostgreDbContext context) :
    BaseQueryable<Position>(context), IPositionQueryable
{
    public IQueryable<Position> GetPositionQueryable(
        PositionQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(r => r.IsDeleted == false);
        return query;
    }

    public IQueryable<Position> GetPositionQueryableIncluded(
        PositionQueryableOptions options,
        IQueryable<Position>? query = null
    )
    {
        query ??= GetPositionQueryable(options);
        if (options.IncludedEmployees || options.IncludedAll)
        {
            query = query.Include(r => r.Employees);
        }
        return query;
    }

    public IQueryable<Position> GetPositionQueryFiltered(
        PositionFilter filter,
        PositionQueryableOptions options,
        IQueryable<Position>? query = null
    )
    {
        query ??= GetPositionQueryableIncluded(options);
        if (filter.Keyword is not null)
        {
            query = query.Where(r => r.PositionCode.Contains(filter.Keyword)
                        || r.PositionName.Contains(filter.Keyword));
        }
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(r => r.OrgUid == filter.OrgId);
        }
        return query;
    }
}