using Kantoku.Processor.Jobs.Bases;
using Kantoku.Processor.Models.BatchJobManager;

namespace Kantoku.Processor.Services
{
    /// <summary>
    /// Factory for creating job instances based on job type
    /// </summary>
    public class JobFactory
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<JobFactory> _logger;

        public JobFactory(IServiceProvider serviceProvider, ILogger<JobFactory> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        /// <summary>
        /// Creates an instance of a job based on the job configuration
        /// </summary>
        /// <param name="batchJob">The batch job configuration</param>
        /// <returns>An instance of the job, or null if the job couldn't be created</returns>
        public IJob? CreateJob(BatchJob batchJob)
        {
            try
            {
                // Get the job type from the job configuration
                var jobType = GetJobType(batchJob.JobType);
                if (jobType == null)
                {
                    _logger.LogError("Job type {JobType} not found", batchJob.JobType);
                    return null;
                }

                // Create a scope to resolve scoped services
                using var scope = _serviceProvider.CreateScope();
                
                // Create an instance of the job
                var job = ActivatorUtilities.CreateInstance(scope.ServiceProvider, jobType) as IJob;
                
                if (job == null)
                {
                    _logger.LogError("Failed to create job instance for job type: {JobType}", batchJob.JobType);
                    return null;
                }

                // If the job is a JobBase, set the job configuration and logger
                if (job is JobBase jobBase)
                {
                    jobBase.SetJobContext(batchJob);
                    jobBase.SetLogger(_logger);
                }

                return job;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create job instance for job type: {JobType}", batchJob.JobType);
                throw new InvalidOperationException($"Failed to create job instance for job type: {batchJob.JobType}", ex);
            }
        }

        /// <summary>
        /// Gets the Type for the job type name
        /// </summary>
        private Type? GetJobType(string jobTypeName)
        {
            try
            {
                // Get all loaded assemblies
                var assemblies = AppDomain.CurrentDomain.GetAssemblies();
                
                // Find the type in any of the loaded assemblies
                foreach (var assembly in assemblies)
                {
                    var type = assembly.GetType(jobTypeName);
                    
                    if (type != null && typeof(IJob).IsAssignableFrom(type))
                    {
                        return type;
                    }
                }
                
                _logger.LogWarning("Job type {JobTypeName} not found in any loaded assembly", jobTypeName);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting job type {JobTypeName}", jobTypeName);
                return null;
            }
        }
    }
} 