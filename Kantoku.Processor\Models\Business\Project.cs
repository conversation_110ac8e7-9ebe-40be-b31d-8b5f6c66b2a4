using System.ComponentModel.DataAnnotations.Schema;

namespace Kantoku.Processor.Models.Business;

/// <summary>
/// Simplified Project model for the processor
/// Contains only the essential fields needed for event processing
/// </summary>
public class Project
{
    public Guid ProjectUid { get; set; }
    public Guid OrgUid { get; set; }
    public string? Address { get; set; }

    [NotMapped]
    public double? Latitude { get; set; }

    [NotMapped]
    public double? Longitude { get; set; }

    public string StatusCode { get; set; } = null!;
    public bool IsDeleted { get; set; } = false;
}
