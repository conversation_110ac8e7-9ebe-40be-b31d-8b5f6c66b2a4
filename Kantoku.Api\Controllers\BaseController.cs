using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Helpers;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Controllers;

public interface IBaseController
{
    GeneralResponse<T> Success<T>(T data);
    GeneralResponse<T> SuccessFromResult<T>(ResultDto<T> result);
    GeneralResponse<T> BadRequest<T>();
    GeneralResponse<T> Unauthorized<T>();
    GeneralResponse<T> Forbidden<T>();
    GeneralResponse<T> Fail<T>(string code);

    // GeneralResponse Success();
    // GeneralResponse BadRequest();
    // GeneralResponse Unauthorized();
    // GeneralResponse Forbidden();
    // GeneralResponse Fail(string code);
}

public class BaseController : ControllerBase, IBaseController
{
    private readonly ITResponseFactory responseFactoryT;
    // private readonly IResponseFactory responseFactory;

    public BaseController(ITResponseFactory responseFactoryT)
    {
        this.responseFactoryT = responseFactoryT;
        // this.responseFactory = responseFactory;
    }

    public GeneralResponse<T> Success<T>(T data)
    {
        return responseFactoryT.Success(data);
    }

    public GeneralResponse<T> SuccessFromResult<T>(ResultDto<T>? result)
    {
        if (result is null)
        {
            return responseFactoryT.Fail<T>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        else if (result.Data is not null)
        {
            return responseFactoryT.Success(result.Data);
        }
        else if (result.ErrorCode is not null)
        {
            return responseFactoryT.Fail<T>(result.ErrorCode);
        }
        return responseFactoryT.Fail<T>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
    }

    public GeneralResponse<T> BadRequest<T>()
    {
        return responseFactoryT.BadRequest<T>();
    }

    public GeneralResponse<T> Unauthorized<T>()
    {
        return responseFactoryT.Unauthorized<T>();
    }

    public GeneralResponse<T> Forbidden<T>()
    {
        return responseFactoryT.Forbidden<T>();
    }

    public GeneralResponse<T> Fail<T>(string code)
    {
        return responseFactoryT.Fail<T>(code);
    }

    // protected GeneralResponse Success()
    // {
    //     return responseFactory.Success();
    // }

    // protected GeneralResponse BadRequest()
    // {
    //     return responseFactory.BadRequest();
    // }

    // protected GeneralResponse Unauthorized()
    // {
    //     return responseFactory.Unauthorized();
    // }

    // protected GeneralResponse Forbidden()
    // {
    //     return responseFactory.Forbidden();
    // }

    // protected GeneralResponse Fail(string code)
    // {
    //     return responseFactory.Fail(code);
    // }
}