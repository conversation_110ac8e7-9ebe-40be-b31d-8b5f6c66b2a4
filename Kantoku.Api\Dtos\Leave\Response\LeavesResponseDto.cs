using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.Leave.Response;

public class LeavesResponseDto
{
    public IEnumerable<LeaveResponseDto> Items { get; set; } = [];

    public int PageNum { get; set; }

    public int PageSize { get; set; }

    public int TotalRecords { get; set; }
}

public class LeaveResponseDto
{
    [JsonPropertyName("employeeLeaveId")]
    public string? EmployeeLeaveId { get; set; }

    [JsonPropertyName("employeeId")]
    public string? EmployeeId { get; set; }

    [JsonPropertyName("employeeName")]
    public string? EmployeeName { get; set; }

    [JsonPropertyName("employeeCode")]
    public string? EmployeeCode { get; set; }

    [JsonPropertyName("baseLeave")]
    public float? BaseLeave { get; set; }

    [JsonPropertyName("baseLeaveExpire")]
    public string? BaseLeaveExpire { get; set; }

    [JsonPropertyName("lastRemainLeave")]
    public float? LastRemainLeave { get; set; }

    [JsonPropertyName("lastRemainLeaveExpire")]
    public string? LastRemainLeaveExpire { get; set; }

    [JsonPropertyName("totalUsedLeave")]
    public float? TotalUsedLeave { get; set; }

    [JsonPropertyName("totalSelfUsedLeave")]
    public float? TotalSelfUsedLeave { get; set; }

    [JsonPropertyName("totalOrgUsedLeave")]
    public float? TotalOrgUsedLeave { get; set; }

    [JsonPropertyName("createTime")]
    public string? CreateTime { get; set; }

    [JsonPropertyName("updateTime")]
    public string? UpdateTime { get; set; }
}