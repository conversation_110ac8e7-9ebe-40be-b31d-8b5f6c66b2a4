namespace Kantoku.Api.Dtos.Common;

public class LeaveTypesResponseDto
{
    public IEnumerable<LeaveTypeResponseDto> Items { get; set; } = [];
    public int PageIndex { get; set; }
    public int PageSize { get; set; }
    public int TotalRow { get; set; }
    public int PageCount { get; set; }
}

public class LeaveTypeResponseDto
{
    public string? LeaveTypeId { get; set; }

    public string? LeaveTypeCode { get; set; }

    public string? LeaveTypeName { get; set; }

    public string? Description { get; set; }

    public bool? IsSelf { get; set; }

    public bool? IsPaid { get; set; }
}
