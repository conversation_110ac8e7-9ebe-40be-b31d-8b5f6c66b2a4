using Kantoku.Api.Configurations;
using Kantoku.Api.Utils.Attributes.Class;
using MailKit.Net.Smtp;
using MailKit.Security;
using Microsoft.Extensions.Options;
using MimeKit;
using MimeKit.Text;

namespace Kantoku.Api.Services;

public interface IEmailService
{
    Task<bool> SendAsync(string emailAddress, string content);
    Task<string?> GetTemplate(string templateName);
}

[Service(ServiceLifetime.Scoped)]
public class EmailService : IEmailService
{
    private readonly MailConfig mailConfig;
    private readonly AppConfig appConfig;
    private readonly Serilog.ILogger logger;
    public EmailService(IOptions<MailConfig> mailConfig, IOptions<AppConfig> appConfig, Serilog.ILogger logger)
    {
        this.mailConfig = mailConfig.Value;
        this.appConfig = appConfig.Value;
        this.logger = logger.ForContext<EmailService>();
    }

    public async Task<bool> SendAsync(string emailAddress, string content)
    {
        try
        {
            MimeMessage email = new();
            email.From.Add(new MailboxAddress(mailConfig.FromName, mailConfig.EmailFrom));
            email.To.Add(MailboxAddress.Parse(emailAddress));
            email.Subject = mailConfig.Subject;
            email.Body = new TextPart(TextFormat.Html) { Text = content };

            using (var smtp = new SmtpClient())
            {
                smtp.ServerCertificateValidationCallback = (s, c, h, e) => true;
                smtp.Connect(mailConfig.SmtpHost, mailConfig.SmtpPort, SecureSocketOptions.StartTls);
                smtp.Authenticate(mailConfig.SmtpUser, mailConfig.SmtpPass);
                smtp.Timeout = 10000;
                await smtp.SendAsync(email);
                smtp.Disconnect(true);
            }
            return true;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error sending email to {Email}", emailAddress);
            return false;
        }
    }

    public async Task<string?> GetTemplate(string templateName)
    {
        try
        {
            var fileName = Path.Combine(AppContext.BaseDirectory, "Resources", "Templates", $"{templateName}.html");
            var htmlContent = await File.ReadAllTextAsync(fileName);
            htmlContent = htmlContent.Replace("{{Url}}", appConfig.ApplicationHost);
            return htmlContent;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting email template {TemplateName}", templateName);
            return null;
        }
    }
}
