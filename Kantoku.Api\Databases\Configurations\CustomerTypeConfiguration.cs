using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class CustomerTypeConfiguration(string schema) : IEntityTypeConfiguration<CustomerType>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<CustomerType> builder)
    {   
        builder.ToTable("customer_type", Schema);

        builder.HasKey(e => e.CustomerTypeCode).HasName("customer_type_pkey");

        builder.Property(e => e.CustomerTypeCode)
            .HasColumnName("customer_type_code");
        builder.Property(e => e.TranslatedCustomerType)
            .HasColumnType("jsonb")
            .HasColumnName("translated_customer_type")
            .HasConversion(
                v => Newtonsoft.Json.JsonConvert.SerializeObject(v),
                v => Newtonsoft.Json.JsonConvert.DeserializeObject<ICollection<TranslatedCustomerType>>(v)
            ).Metadata.SetValueComparer(
                new ValueComparer<ICollection<TranslatedCustomerType>>(
                    (c1, c2) => (c1 == null && c2 == null) || (c1 != null && c2 != null && c1.SequenceEqual(c2)),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()
                )
            );
    }
} 