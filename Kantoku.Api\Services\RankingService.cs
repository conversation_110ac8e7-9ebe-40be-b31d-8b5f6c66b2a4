using Kantoku.Api.Dtos.Ranking.Request;
using Kantoku.Api.Dtos.Ranking.Response;
using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Utils.Mappers.EntityMappers;
using Kantoku.Api.Dtos.EmployeeCost.Response;

namespace Kantoku.Api.Services;

public interface IRankingService
{
    Task<ResultDto<RankingResponseDto>> GetRankingById(Guid rankingId);
    Task<ResultDto<RankingsResponseDto>> GetAllRanking(RankingFilter filter);
    Task<ResultDto<SimpleRankingsResponseDto>> GetSimpleRankingInfo(RankingFilter filter);
    Task<ResultDto<RankingResponseDto>> CreateRanking(CreateRankingRequestDto requestDto);
    Task<ResultDto<RankingResponseDto>> UpdateRanking(Guid rankingId, UpdateRankingRequestDto requestDto);
    Task<ResultDto<bool>> DeleteRanking(Guid rankingId);

    Task<ResultDto<EmployeeCostsResponseDto>> GetEmployeeCostByEmployeeId(Guid employeeId, DateOnly dateFrom, DateOnly dateTo);
    Task<ResultDto<EmployeeCostsResponseDto>> GetEmployeeCostByFilter(EmployeeCostFilter filter);
}

[Service(ServiceLifetime.Scoped)]
public class RankingService : BaseService<RankingService>, IRankingService
{
    private readonly IRankingRepository rankingRepository;
    private readonly IEmployeeRepository employeeRepository;
    public RankingService(
        IHttpContextAccessor httpContextAccessor,
        Serilog.ILogger logger,
        IRankingRepository rankingRepository,
        IEmployeeRepository employeeRepository
    )
    : base(logger, httpContextAccessor)
    {
        this.rankingRepository = rankingRepository;
        this.employeeRepository = employeeRepository;
    }
    public async Task<ResultDto<RankingsResponseDto>> GetAllRanking(RankingFilter filter)
    {
        var (rankings, total) = await rankingRepository.GetByFilter(filter, new RankingQueryableOptions());
        if (rankings is null || total == 0 || !rankings.Any())
        {
            logger.Error("No rankings found");
            return new ErrorResultDto<RankingsResponseDto>(ResponseCodeConstant.RANKING_NOT_EXIST);
        }
        var result = rankings.ToRankingResponseDtos(filter.PageNum, filter.PageSize, total);
        return new SuccessResultDto<RankingsResponseDto>(result);
    }

    public async Task<ResultDto<RankingResponseDto>> GetRankingById(Guid rankingId)
    {
        var ranking = await rankingRepository.GetById(rankingId, new RankingQueryableOptions());
        if (ranking is null)
        {
            logger.Error("Ranking with ID: {RankingId} not found", rankingId);
            return new ErrorResultDto<RankingResponseDto>(ResponseCodeConstant.RANKING_NOT_EXIST);
        }
        var res = ranking.ToRankingResponseDto();
        return new SuccessResultDto<RankingResponseDto>(res);
    }

    public async Task<ResultDto<RankingResponseDto>> CreateRanking(CreateRankingRequestDto requestDto)
    {
        if (!GetCurrentOrgGuid(out var orgUid))
        {
            logger.Error("Current organization not found");
            return new ErrorResultDto<RankingResponseDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        var requestedRanking = requestDto.ToEntity(orgUid);
        if (requestedRanking is null)
        {
            logger.Error("Failed to create ranking with request: {Request}", requestDto);
            return new ErrorResultDto<RankingResponseDto>(ResponseCodeConstant.RANKING_CREATE_FAILED);
        }
        var isValidRanking = await ValidateRanking(requestedRanking);
        if (!isValidRanking)
        {
            return new ErrorResultDto<RankingResponseDto>(ResponseCodeConstant.RANKING_CREATE_FAILED);
        }
        var createdRanking = await rankingRepository.Create(requestedRanking);
        if (createdRanking is null)
        {
            logger.Error("Failed to create ranking with request: {Request}", requestDto);
            return new ErrorResultDto<RankingResponseDto>(ResponseCodeConstant.RANKING_CREATE_FAILED);
        }
        var res = createdRanking.ToRankingResponseDto();
        return new SuccessResultDto<RankingResponseDto>(res);
    }

    public async Task<ResultDto<RankingResponseDto>> UpdateRanking(Guid rankingId, UpdateRankingRequestDto requestDto)
    {
        var existRanking = await rankingRepository.GetById(rankingId, new RankingQueryableOptions());
        if (existRanking is null)
        {
            logger.Error("Ranking with ID: {RankingId} not found", rankingId);
            return new ErrorResultDto<RankingResponseDto>(ResponseCodeConstant.RANKING_NOT_EXIST);
        }
        existRanking.UpdateFromDto(requestDto);

        var isValidRanking = await ValidateRanking(existRanking);
        if (!isValidRanking)
        {
            return new ErrorResultDto<RankingResponseDto>(ResponseCodeConstant.RANKING_UPDATE_FAILED);
        }
        var updatedRanking = await rankingRepository.Update(existRanking);
        if (updatedRanking is null)
        {
            logger.Error("Failed to update ranking with ID: {RankingId}", rankingId);
            return new ErrorResultDto<RankingResponseDto>(ResponseCodeConstant.RANKING_UPDATE_FAILED);
        }
        var res = updatedRanking.ToRankingResponseDto();
        return new SuccessResultDto<RankingResponseDto>(res);
    }

    public async Task<ResultDto<bool>> DeleteRanking(Guid rankingId)
    {
        var existRanking = await rankingRepository.GetById(rankingId, new RankingQueryableOptions());
        if (existRanking is null)
        {
            logger.Error("Ranking with ID: {RankingId} not found", rankingId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.RANKING_NOT_EXIST, false);
        }
        existRanking.IsDeleted = true;
        var isDeleted = await rankingRepository.Update(existRanking);
        if (isDeleted is null)
        {
            logger.Error("Failed to delete ranking with ID: {RankingId}", rankingId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.RANKING_DELETE_FAILED, false);
        }
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<SimpleRankingsResponseDto>> GetSimpleRankingInfo(RankingFilter filter)
    {
        var (rankings, total) = await rankingRepository.GetByFilter(filter, new RankingQueryableOptions());
        if (rankings is null || total == 0 || !rankings.Any())
        {
            logger.Error("No rankings found");
            return new ErrorResultDto<SimpleRankingsResponseDto>(ResponseCodeConstant.RANKING_NOT_EXIST);
        }
        var result = rankings.ToSimpleRankingResponseDto(filter.PageNum, filter.PageSize, total);
        return new SuccessResultDto<SimpleRankingsResponseDto>(result);
    }

    public async Task<ResultDto<EmployeeCostsResponseDto>> GetEmployeeCostByEmployeeId(Guid employeeId, DateOnly dateFrom, DateOnly dateTo)
    {
        var employee = await employeeRepository.GetById(employeeId, new EmployeeQueryableOptions
        {
            IncludedEmployeeRanks = true,
            IncludedEmployeeCosts = true,
        });
        if (employee is null)
        {
            logger.Error("Employee with ID: {EmployeeId} not found", employeeId);
            return new ErrorResultDto<EmployeeCostsResponseDto>(ResponseCodeConstant.EMPLOYEE_NOT_EXIST);
        }
        var employeeCosts = employee.ToEmployeeCostsResponseDto(dateFrom, dateTo);
        return new SuccessResultDto<EmployeeCostsResponseDto>(employeeCosts);
    }

    public async Task<ResultDto<EmployeeCostsResponseDto>> GetEmployeeCostByFilter(EmployeeCostFilter filter)
    {
        var (employeeCosts, total) = await rankingRepository.GetEmployeeCostsByFilter(filter, new EmployeeCostQueryableOptions
        {
            IncludedEmployee = true,
        });
        if (employeeCosts is null || total == 0 || !employeeCosts.Any())
        {
            logger.Error("There are no employee costs found");
            return new ErrorResultDto<EmployeeCostsResponseDto>(ResponseCodeConstant.RANKING_NOT_EXIST);
        }
        var (rankings, _) = await rankingRepository.GetByFilter(new RankingFilter
        {
            Keyword = filter.Keyword,
        }, new RankingQueryableOptions());
        var res = employeeCosts.ToEmployeeCostsResponseDto(filter.PageNum, filter.PageSize, total, rankings);
        return new SuccessResultDto<EmployeeCostsResponseDto>(res);
    }

    private async Task<bool> ValidateRanking(Ranking needValidateRanking)
    {
        if (needValidateRanking is null)
        {
            return false;
        }
        if (needValidateRanking.MinValue == null)
        {
            var isExistLowestRanking = await rankingRepository.IsExistLowestRanking();
            if (isExistLowestRanking)
            {
                return false;
            }
        }
        if (needValidateRanking.MaxValue == null)
        {
            var isExistHighestRanking = await rankingRepository.IsExistHighestRanking();
            if (isExistHighestRanking)
            {
                return false;
            }
        }
        if (needValidateRanking.MinValue.HasValue
        && needValidateRanking.MaxValue.HasValue
        && needValidateRanking.MinValue.Value >= needValidateRanking.MaxValue.Value)
        {
            return false;
        }
        return true;
    }
}
