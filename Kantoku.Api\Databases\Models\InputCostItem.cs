using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Models;

public class InputCostItem : AuditableEntity
{
    public Guid InputCostItemUid { get; set; }
    public Guid ConstructionUid { get; set; }
    public Guid? InputCostUid { get; set; }
    public Guid ItemUid { get; set; }
    public Guid? VendorUid { get; set; }
    public Guid OrgUid { get; set; }

    [AuditProperty]
    public DateOnly TransactionDate { get; set; }

    [AuditProperty]
    public string? Unit { get; set; }

    [AuditProperty]
    public float Quantity { get; set; }

    [AuditProperty]
    public int Price { get; set; }

    [AuditProperty]
    public float? TaxRate { get; set; }

    [AuditProperty]
    public long? TotalNonTaxed { get; set; }

    [AuditProperty]
    public long? TotalTaxed { get; set; }

    [AuditProperty]
    public string? Description { get; set; }

    public bool IsDeleted { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual Construction Construction { get; set; } = null!;

    public virtual InputCost? InputCost { get; set; }

    public virtual Item Item { get; set; } = null!;

    public virtual Vendor? Vendor { get; set; }
}
