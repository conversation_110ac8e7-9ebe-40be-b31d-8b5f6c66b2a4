using System.Text.Json;
using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Seeders.Categories;

public interface ICategorySeeder
{
    Task Seed();
}

[Seeder(ServiceLifetime.Scoped)]
public class CategorySeeder : ICategorySeeder
{
    private readonly PostgreDbContext context;
    private readonly Serilog.ILogger logger;

    public CategorySeeder(PostgreDbContext context, Serilog.ILogger logger)
    {
        this.context = context;
        this.logger = logger.ForContext<CategorySeeder>();
    }

    public async Task Seed()
    {
        try
        {
            var jsonPath = Path.Combine("Databases", "Seeders", "Categories", "predefined-categories.json");
            var jsonContent = await System.IO.File.ReadAllTextAsync(jsonPath);
            var jsonData = JsonSerializer.Deserialize<JsonDocument>(jsonContent);
            var categories = jsonData?.RootElement.GetProperty("categories");

            foreach (var category in categories?.EnumerateArray() ?? Enumerable.Empty<JsonElement>())
            {
                if (category.ValueKind == JsonValueKind.Null)
                {
                    continue;
                }

                var systemCategory = new Category
                {
                    CategoryName = string.Empty,
                    IsDefault = true,
                    IsDeleted = false,
                    CategoryUid = category.GetProperty("uid").GetGuid(),
                    CategoryCode = category.GetProperty("code").GetString() ?? string.Empty,
                    TranslatedCategory = category.GetProperty("translations").EnumerateObject()
                        .Select(t => new TranslatedCategory
                        {
                            LanguageCode = t.Name,
                            CategoryName = t.Value.GetProperty("name").GetString() ?? string.Empty,
                            CategoryDescription = t.Value.GetProperty("description").GetString() ?? string.Empty
                        }).ToList(),
                    ParentUid = category.TryGetProperty("parentUid", out var parentUid) ? parentUid.GetGuid() : null,
                };

                if (category.TryGetProperty("parentCode", out var parentCode))
                {
                    systemCategory.RootCategoryCode = parentCode.GetString() ?? string.Empty;
                }
                else
                {
                    systemCategory.RootCategoryCode = systemCategory.CategoryCode;
                }

                var isExist = await context.Categories
                    .Where(c => c.CategoryCode.Equals(systemCategory.CategoryCode))
                    .AnyAsync();

                if (!isExist)
                {
                    await context.Categories.AddAsync(systemCategory);
                    logger.Information($"Added category: {systemCategory.CategoryCode}");
                }
            }

            await context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            logger.Error($"Error in CategorySeeder: {ex.Message}");
        }
    }
}