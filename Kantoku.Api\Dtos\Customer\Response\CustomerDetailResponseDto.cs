using Kantoku.Api.Dtos.Contractor.Response;

namespace Kantoku.Api.Dtos.Customer.Response;

public class CustomerDetailResponseDto : CustomerResponseDto
{
    /// <summary>
    /// List of projects contracted by the customer
    /// </summary>
    public IEnumerable<ContractedProjectResponseDto>? Projects { get; set; }

    /// <summary>
    /// Page number of the project list
    /// </summary>
    public int ProjectListPageNum { get; set; }

    /// <summary>
    /// Page size of the project list
    /// </summary>
    public int ProjectListPageSize { get; set; }

    /// <summary>
    /// Total records of the project list
    /// </summary>
    public int ProjectListTotalRecords { get; set; }
}