namespace Kantoku.Api.Dtos.Construction.Response;

public class ConstructionResponseDto : ConstructionBaseResponseDto
{
    /// <summary>
    /// Contractual Costs
    /// </summary>
    public ConstructionContractCostResponseDto? ContractualCosts { get; set; }

    /// <summary>
    /// Estimated Costs
    /// </summary>
    public ConstructionEstimateCostResponseDto? EstimatedCosts { get; set; }

    /// <summary>
    /// Accumulated Costs
    /// </summary>
    public ConstructionAccumulatedCostResponseDto? AccumulatedCosts { get; set; }
}

public class ConstructionContractCostResponseDto : ContractCostResponseDto
{
    /// <summary>
    /// Construction Id
    /// </summary>
    public string? ConstructionId { get; set; }

    /// <summary>
    /// Is Primary
    /// </summary>
    public bool IsPrimary { get; set; }
}

public class ConstructionEstimateCostResponseDto : EstimateCostResponseDto
{
    /// <summary>
    /// Construction Id
    /// </summary>  
    public string? ConstructionId { get; set; }

    /// <summary>
    /// Is Primary
    /// </summary>
    public bool IsPrimary { get; set; }
}

public class ConstructionAccumulatedCostResponseDto : AccumulatedCostItemResponseDto
{
    /// <summary>
    /// Construction Id
    /// </summary>
    public string? ConstructionId { get; set; }

    /// <summary>
    /// Is Primary
    /// </summary>
    public bool IsPrimary { get; set; }
}