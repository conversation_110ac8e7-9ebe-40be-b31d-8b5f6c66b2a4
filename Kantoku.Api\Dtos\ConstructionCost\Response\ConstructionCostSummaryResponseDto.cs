namespace Kantoku.Api.Dtos.ConstructionCost.Response;

public class ConstructionCostSummaryResponseDto
{
    /// <summary>
    /// Unique identifier for the construction project (*)
    /// </summary>
    public Guid? ConstructionId { get; set; }

    /// <summary>
    /// Indicates if this is the primary cost record
    /// </summary>
    public bool IsPrimary { get; set; }

    /// <summary>
    /// Current cost details for the construction
    /// </summary>
    public ConstructionPartialCostResponseDto? CurrentCost { get; set; }

    /// <summary>
    /// Accumulated cost details up to current period
    /// </summary>
    public ConstructionPartialCostResponseDto? CurrentAccumulateCost { get; set; }

    /// <summary>
    /// Accumulated cost details from previous period
    /// </summary>
    public ConstructionPartialCostResponseDto? LastAccumulateCost { get; set; }
}