using Kantoku.Api.Dtos.Role.Request;
using Kantoku.Api.Dtos.Role.Response;
using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public interface IRoleService
{
    Task<ResultDto<RoleResponseDto>> GetRoleById(Guid roleId);
    Task<ResultDto<RolesResponseDto>> GetRolesByFilter(RoleFilter filter);
    Task<ResultDto<RoleResponseDto>> CreateRole(CreateRoleRequestDto dto);
    Task<ResultDto<RoleResponseDto>> UpdateRole(Guid roleId, UpdateRoleRequestDto dto);
    Task<ResultDto<bool>> DeleteRole(Guid roleId);
}

[Service(ServiceLifetime.Scoped)]
public class RoleService : BaseService<RoleService>, IRoleService
{
    private readonly IRoleRepository roleRepository;
    private readonly IFunctionRepository functionRepository;
    private readonly RoleQueryableOptions options = new()
    {
        IncludedEmployees = true,
        IncludedStructure = true,
    };

    public RoleService(
        IHttpContextAccessor httpContextAccessor,
        Serilog.ILogger logger,
        IRoleRepository roleRepository,
        IFunctionRepository functionRepository
    )
    : base(logger, httpContextAccessor)
    {
        this.roleRepository = roleRepository;
        this.functionRepository = functionRepository;
    }
    public async Task<ResultDto<RolesResponseDto>> GetRolesByFilter(RoleFilter filter)
    {
        var (roles, totalRecord) = await roleRepository.GetByFilter(filter, options);
        if (roles is null || totalRecord == 0 || !roles.Any())
        {
            logger.Error("No roles found for filter: {Filter}", filter);
            return new ErrorResultDto<RolesResponseDto>(ResponseCodeConstant.ROLE_NOT_EXIST);
        }
        var result = roles.ToRolesResponseDto(filter.PageNum, filter.PageSize, totalRecord);
        return new SuccessResultDto<RolesResponseDto>(result);
    }

    public async Task<ResultDto<RoleResponseDto>> GetRoleById(Guid roleId)
    {
        var role = await roleRepository.GetById(roleId, options);
        if (role is null)
        {
            logger.Error("Role ID {RoleId} not found", roleId);
            return new ErrorResultDto<RoleResponseDto>(ResponseCodeConstant.ROLE_NOT_EXIST);
        }
        var result = role.ToRoleResponseDto();
        return new SuccessResultDto<RoleResponseDto>(result);
    }

    public async Task<ResultDto<RoleResponseDto>> CreateRole(CreateRoleRequestDto dto)
    {
        if (!GetCurrentOrgGuid(out var orgUid))
        {
            logger.Error("Failed to get current org uid");
            return new ErrorResultDto<RoleResponseDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        var functions = await functionRepository.GetAll();
        var newRole = dto.ToEntity(orgUid, functions);
        if (newRole is null)
        {
            logger.Error("Failed to create role");
            return new ErrorResultDto<RoleResponseDto>(ResponseCodeConstant.ROLE_CREATE_FAILED);
        }

        var createdRole = await roleRepository.Create(newRole, options);
        if (createdRole is null)
        {
            logger.Error("Failed to create role");
            return new ErrorResultDto<RoleResponseDto>(ResponseCodeConstant.ROLE_CREATE_FAILED);
        }

        var result = createdRole.ToRoleResponseDto();
        return new SuccessResultDto<RoleResponseDto>(result);
    }


    public async Task<ResultDto<RoleResponseDto>> UpdateRole(Guid roleId, UpdateRoleRequestDto dto)
    {
        var existingRole = await roleRepository.GetById(roleId, options);
        if (existingRole is null)
        {
            logger.Error("Role ID {RoleId} not found", roleId);
            return new ErrorResultDto<RoleResponseDto>(ResponseCodeConstant.ROLE_NOT_EXIST);
        }
        existingRole.UpdateFromDto(dto);

        var updatedRole = await roleRepository.Update(existingRole, options);
        if (updatedRole is null)
        {
            logger.Error("Failed to update role ID {RoleId}", roleId);
            return new ErrorResultDto<RoleResponseDto>(ResponseCodeConstant.ROLE_UPDATE_FAILED);
        }

        var result = updatedRole.ToRoleResponseDto();
        return new SuccessResultDto<RoleResponseDto>(result);
    }

    public async Task<ResultDto<bool>> DeleteRole(Guid roleId)
    {

        var role = await roleRepository.GetById(roleId, new RoleQueryableOptions());
        if (role is null)
        {
            logger.Error("Role ID {RoleId} not found", roleId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.ROLE_NOT_EXIST);
        }
        role.IsDeleted = true;
        var isDeleted = await roleRepository.Update(role, new RoleQueryableOptions());
        if (isDeleted is null)
        {
            logger.Error("Failed to delete role ID {RoleId}", roleId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.ROLE_DELETE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }
}

