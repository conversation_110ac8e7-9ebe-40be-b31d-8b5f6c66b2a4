using System;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Text.RegularExpressions;

namespace Kantoku.Api.Extensions.Validators
{
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field, AllowMultiple = false)]
    public class MobileNumberValidator : ValidationAttribute
    {
        protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
        {
            if (value is null)
            {
                return ValidationResult.Success;
            }

            var phoneNumber = value.ToString();
            var currentCulture = CultureInfo.CurrentCulture;

            var patterns = new Dictionary<string, string>
            {
                { "en-US", @"^\+?1?\s*\(?[2-9]\d{2}\)?[-.\s]?\d{3}[-.\s]?\d{4}$" },
                { "ja-JP", @"^0[789]0-?\d{4}-?\d{4}$" },
                { "vi-VN", @"^(03|05|07|08|09)\d{8}$" }
            };

            if (patterns.TryGetValue(currentCulture.Name, out var pattern))
            {
                if (Regex.IsMatch(phoneNumber!, pattern))
                {
                    return ValidationResult.Success;
                }
            }
            else
            {
                var defaultPattern = @"^0[789]0-?\d{4}-?\d{4}$";
                if (Regex.IsMatch(phoneNumber!, defaultPattern))
                {
                    return ValidationResult.Success;
                }
            }

            return new ValidationResult($"The phone number is not valid for the current culture ({currentCulture.Name}).");
        }
    }
}
