using System.Text.Json;

namespace Kantoku.Processor.Models.BatchJobManager;

/// <summary>
/// Represents a batch job configuration with scheduling information
/// </summary>
public class BatchJob
{
    /// <summary>
    /// Unique identifier for the job
    /// </summary>
    public Guid JobId { get; set; }

    /// <summary>
    /// Unique name of the job
    /// </summary>
    public string JobName { get; set; } = string.Empty;

    /// <summary>
    /// Description of what the job does
    /// </summary>
    public string JobDescription { get; set; } = string.Empty;

    /// <summary>
    /// Fully qualified name of the job implementation class
    /// </summary>
    public string JobType { get; set; } = string.Empty;

    /// <summary>
    /// Indicates whether the job is enabled and should be executed according to its schedule
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// CRON expression that defines when the job should be executed
    /// </summary>
    public string Interval { get; set; } = string.Empty;

    /// <summary>
    /// When the job was last executed
    /// </summary>
    public DateTime? LastRunAt { get; set; }

    /// <summary>
    /// Job-specific configuration stored as JSON
    /// </summary>
    public string? Configuration { get; set; }

    /// <summary>
    /// Deserializes the Configuration property to the specified type
    /// </summary>
    public T? GetConfiguration<T>() where T : class
    {
        if (string.IsNullOrEmpty(Configuration))
            return null;

        return JsonSerializer.Deserialize<T>(Configuration);
    }

    /// <summary>
    /// Serializes the provided object and stores it in the Configuration property
    /// </summary>
    public void SetConfiguration<T>(T config) where T : class
    {
        if (config == null)
        {
            Configuration = null;
            return;
        }

        Configuration = JsonSerializer.Serialize(config);
    }
}