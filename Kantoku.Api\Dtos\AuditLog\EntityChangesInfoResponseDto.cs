using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.AuditLog;

public class AuditLogResponseDto
{
    [JsonPropertyName("entityChanges")]
    public IEnumerable<EntityChangeResponseDto> EntityChanges { get; set; } = [];

    [JsonPropertyName("pageNum")]
    public int PageNum { get; set; }

    [JsonPropertyName("pageSize")]
    public int PageSize { get; set; }

    [JsonPropertyName("totalRecords")]
    public int TotalRecords { get; set; }
}

public class EntityChangeResponseDto
{
    [JsonPropertyName("auditLogId")]
    public string? AuditLogId { get; set; }

    [JsonPropertyName("entityId")]
    public string? EntityId { get; set; }

    [JsonPropertyName("action")]
    public string? Action { get; set; }

    [JsonPropertyName("changedList")]
    public IEnumerable<ValueChanges> ChangesList { get; set; } = [];

    [JsonPropertyName("modifiedTime")]
    public DateTime ModifiedTime { get; set; }

    [JsonPropertyName("modifiedUserId")]
    public string? ModifiedAccountId { get; set; }

    [JsonPropertyName("modifiedUserName")]
    public string? ModifiedAccountName { get; set; }
}