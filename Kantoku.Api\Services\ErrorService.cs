using System.Reflection;
using System.Resources;
using Kantoku.Api.Utils.Attributes.Class;

namespace Kantoku.Api.Services;

public interface IErrorService
{
    public string? GetErrorMessage(string code);
}

[Service(ServiceLifetime.Scoped)]
public class ErrorService : BaseService<ErrorService>, IErrorService
{
    public ErrorService(
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor)
        : base(logger, httpContextAccessor)
    {
    }

    public string? GetErrorMessage(string code)
    {
        try
        {
            ResourceManager rm = new("Kantoku.Api.Resources.ErrorMessage", Assembly.GetExecutingAssembly());
            return rm.GetString(code);
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting error message");
            return null;
        }
    }
}