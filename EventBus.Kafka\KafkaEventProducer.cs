using Confluent.Kafka;
using EventBus.Interfaces;
using EventBus.Kafka.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Text;

namespace EventBus.Kafka;

/// <summary>
/// Kafka implementation of the event producer
/// </summary>
public class KafkaEventProducer : IEventProducer, IDisposable
{
    private readonly KafkaConfig _config;
    private readonly ILogger<KafkaEventProducer> _logger;
    private readonly IProducer<string, string> _producer;
    private bool _disposed;

    public KafkaEventProducer(
        IOptions<KafkaConfig> config,
        ILogger<KafkaEventProducer> logger)
    {
        _config = config.Value;
        _logger = logger;

        var producerConfig = new ProducerConfig
        {
            BootstrapServers = _config.BootstrapServers,
            Acks = ParseAcks(_config.Producer.Acks),
            BatchSize = _config.Producer.BatchSize,
            LingerMs = _config.Producer.LingerMs
        };

        _producer = new ProducerBuilder<string, string>(producerConfig).Build();
    }

    public async Task PublishAsync<T>(T @event, CancellationToken cancellationToken = default) where T : class
    {
        try
        {
            var eventType = typeof(T).Name;
            var topicName = GetTopicName(eventType);
            var eventData = JsonConvert.SerializeObject(@event);

            var message = new Message<string, string>
            {
                Key = Guid.NewGuid().ToString(),
                Value = eventData,
                Headers = new Headers
                {
                    { "EventType", Encoding.UTF8.GetBytes(eventType) },
                    { "Timestamp", Encoding.UTF8.GetBytes(DateTime.UtcNow.ToString("O")) }
                }
            };

            var result = await _producer.ProduceAsync(topicName, message, cancellationToken);

            _logger.LogInformation("Published event {EventType} to topic {Topic} at offset {Offset}",
                eventType, topicName, result.Offset);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish event {EventType}", typeof(T).Name);
            throw;
        }
    }

    private string GetTopicName(string eventType)
    {
        return $"{_config.TopicPrefix}.{eventType.ToLowerInvariant()}";
    }

    private static Acks ParseAcks(string acks)
    {
        return acks.ToLowerInvariant() switch
        {
            "none" or "0" => Acks.None,
            "leader" or "1" => Acks.Leader,
            "all" or "-1" => Acks.All,
            _ => Acks.All
        };
    }

    public void Dispose()
    {
        if (_disposed) return;

        try
        {
            _producer?.Flush(TimeSpan.FromSeconds(10));
            _producer?.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disposing Kafka producer");
        }
        finally
        {
            _disposed = true;
        }
    }
}
