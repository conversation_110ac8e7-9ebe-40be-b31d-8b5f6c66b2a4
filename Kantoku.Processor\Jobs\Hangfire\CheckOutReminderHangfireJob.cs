using Kantoku.Processor.Data.Contexts;
using Kantoku.Processor.Dtos;
using Kantoku.Processor.Jobs.Hangfire;
using Kantoku.Processor.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Processor.Jobs.Hangfire;

/// <summary>
/// Hangfire job that sends a reminder to employees who haven't checked out
/// after their scheduled shift end time
/// </summary>
public class CheckOutReminderHangfireJob : HangfireJobBase
{
    public CheckOutReminderHangfireJob(ILogger<CheckOutReminderHangfireJob> logger, IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public override async Task ExecuteAsync(CancellationToken cancellationToken = default)
    {
        LogInformation("Starting check out reminder job execution");

        using var scope = CreateScope();
        var appDbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        var firebaseService = scope.ServiceProvider.GetRequiredService<IFirebaseService>();

        try
        {
            var oneHourAgo = DateTime.Now.AddHours(-1);

            var employeeIds = await appDbContext.EmployeeShifts
                .Where(es =>
                    es.ScheduledEndTime.HasValue &&
                    !es.CheckOutTime.HasValue &&
                    (es.ScheduledEndTime + TimeSpan.FromMinutes(5) == DateTime.Now
                    || es.ScheduledEndTime + TimeSpan.FromMinutes(10) == DateTime.Now))
                .Select(es => es.EmployeeUid)
                .ToListAsync(cancellationToken);

            LogInformation("Found {0} employee shifts that need reminder to check out", employeeIds.Count);

            if (employeeIds.Count == 0)
            {
                LogInformation("No employees need check out reminders at this time");
                return;
            }

            var deviceTokens = await appDbContext.DeviceTokens
                .Where(d => employeeIds.Contains(d.EmployeeUid))
                .Select(d => d.FirebaseToken)
                .ToListAsync(cancellationToken);

            LogInformation("Found {0} device tokens for reminder notifications", deviceTokens.Count);

            // Send notifications in batches of 500
            foreach (var deviceTokenBatch in deviceTokens.Chunk(500))
            {
                ThrowIfCancellationRequested(cancellationToken);

                await firebaseService.SendToDevices([.. deviceTokenBatch!], new FirebaseMessageDto
                {
                    Title = "Check out reminder",
                    Body = "Your shift is ending soon, please check out"
                });

                LogInformation("Sent reminder notifications to {0} devices", deviceTokenBatch.Length);
            }

            LogInformation("Successfully reminded {0} employees", deviceTokens.Count);
        }
        catch (OperationCanceledException)
        {
            LogWarning("Check out reminder job was cancelled");
            throw;
        }
        catch (Exception ex)
        {
            LogError("Error executing check out reminder job: {0}", ex, ex.Message);
            throw;
        }
    }
}
