﻿using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Models;

public class Request : AuditableEntity
{
    public Guid RequestUid { get; set; }
    public Guid AuthorUid { get; set; }
    public Guid? ProjectUid { get; set; }

    [AuditProperty]
    public DateTime RequestFrom { get; set; }

    [AuditProperty]
    public DateTime RequestTo { get; set; }

    [AuditProperty]
    public string RequestTypeCode { get; set; } = null!;

    [AuditProperty]
    public bool? IsUserRequestedLeave { get; set; }
    [AuditProperty]
    public string? Description { get; set; }

    public string? StatusCode { get; set; }
    public bool IsDeleted { get; set; } = false;

    public Guid? Approver1Uid { get; set; }
    public bool? Approver1Status { get; set; }
    public string? Approver1Notes { get; set; }
    public DateTime? Approver1Time { get; set; }

    public Guid? Approver2Uid { get; set; }
    public bool? Approver2Status { get; set; }
    public string? Approver2Notes { get; set; }
    public DateTime? Approver2Time { get; set; }

    public virtual Employee? Approver1 { get; set; }

    public virtual Employee? Approver2 { get; set; }

    public virtual Employee Author { get; set; } = null!;

    public virtual Project Project { get; set; } = null!;

    public virtual RequestType RequestType { get; set; } = null!;

    public virtual Status Status { get; set; } = null!;
}