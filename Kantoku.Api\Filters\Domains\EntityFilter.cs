using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;

namespace Kantoku.Api.Filters.Domains;

public abstract class BaseFilter
{
    [JsonIgnore]
    [BindNever]
    [FromQuery(Name = null)]
    public Guid? OrgId { get; set; }

    /// <summary>
    /// The page number (default: 1)
    /// </summary>
    [FromQuery(Name = "pageNum")]
    public int PageNum { get; set; } = 1;

    /// <summary>
    /// The page size (default: 10)
    /// </summary>
    [FromQuery(Name = "pageSize")]
    public int PageSize { get; set; } = 10;
}