using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.Account.Request;

public class CreateAccountRequestDto
{
    /// <summary>
    /// Email (to receive mail notification) (*)
    /// </summary>
    [Required]
    [EmailAddress]
    public required string Email { get; set; }

    /// <summary>
    /// Login ID (to login to the system) (*)
    /// </summary>
    [Required]
    public required string LoginId { get; set; }

    /// <summary>
    /// Password (to login to the system) (*)
    /// </summary>
    [Required]
    public required string Password { get; set; }

    /// <summary>
    /// Hashed password (to login to the system)
    /// </summary>
    public string? HashedPassword { get; set; }

    /// <summary>
    /// Mail verification OTP (to verify the email) (*)
    /// </summary>
    [Required]
    public string OTP { get; set; } = null!;

    /// <summary>
    /// User information of the account (*)
    /// </summary>
    [Required]
    public UserInfoRequestDto UserInfo { get; set; } = null!;
}

public class UserInfoRequestDto
{
    /// <summary>
    /// Name of the user (*)
    /// </summary>
    [Required]
    public string Name { get; set; } = null!;

    /// <summary>
    /// Address of the user
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Phone number of the user
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// Gender of the user
    /// </summary>
    public bool? Gender { get; set; }

    /// <summary>
    /// Birthday of the user
    /// </summary>
    [DateTimeValidator(typeof(DateOnly))]
    public string? Birthday { get; set; }
}
