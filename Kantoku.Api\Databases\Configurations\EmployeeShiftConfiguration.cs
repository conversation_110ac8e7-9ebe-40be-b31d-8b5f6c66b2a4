using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Kantoku.Api.Databases.Configurations;

public class EmployeeShiftConfiguration(string schema) : IEntityTypeConfiguration<EmployeeShift>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<EmployeeShift> builder)
    {
        builder.HasKey(e => e.EmployeeShiftUid).HasName("employee_shift_pkey");

        builder.ToTable("employee_shift", Schema, tb => tb.HasComment("Thông tin ca làm việc"));

        builder.Property(e => e.EmployeeShiftUid)
            .HasColumnName("employee_shift_uid");
        builder.Property(e => e.EmployeeUid)
            .HasColumnName("employee_uid");
        builder.Property(e => e.ProjectScheduleUid)
            .HasColumnName("project_schedule_uid");
        builder.Property(e => e.MonthlyReportUid)
            .HasColumnName("monthly_report_uid");
        builder.Property(e => e.ProjectUid)
            .HasColumnName("project_uid");
        builder.Property(e => e.WorkingLocation)
            .HasColumnName("working_location");
        builder.Property(e => e.ScheduledStartTime)
            .HasColumnType("timestamp without time zone")
            .HasColumnName("schedule_start_time");
        builder.Property(e => e.ScheduledEndTime)
            .HasColumnType("timestamp without time zone")
            .HasColumnName("schedule_end_time");
        builder.Property(e => e.CheckInTime)
            .HasColumnType("timestamp without time zone")
            .HasColumnName("check_in_time");
        builder.Property(e => e.CheckInTimeLastModifier)
            .HasColumnName("check_in_time_last_modifier");
        builder.Property(e => e.CheckOutTime)
            .HasColumnType("timestamp without time zone")
            .HasColumnName("check_out_time");
        builder.Property(e => e.CheckOutTimeLastModifier)
            .HasColumnName("check_out_time_last_modifier");
        builder.Property(e => e.AutoCheckOutTime)
            .HasColumnType("timestamp without time zone")
            .HasColumnName("auto_check_out_time");
        builder.Property(e => e.AssignedRole)
            .HasColumnName("assigned_role");
        builder.Property(e => e.CheckInLocation)
            .HasColumnName("check_in_location");
        builder.Property(e => e.CheckOutLocation)
            .HasColumnName("check_out_location");
        builder.Property(e => e.TotalScheduledWorkTime)
            .HasColumnName("total_scheduled_worktime");
        builder.Property(e => e.TotalWorkTime)
            .HasColumnName("total_worktime");
        builder.Property(e => e.TotalBreakTime)
            .HasColumnName("total_breaktime");
        builder.Property(e => e.TotalOverTime)
            .HasColumnName("total_overtime");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.EmployeeShiftBreakTimes)
            .HasColumnType("jsonb")
            .HasColumnName("employee_shift_breaks")
            .HasConversion(
                v => JsonConvert.SerializeObject(v, new JsonSerializerSettings
                {
                    DateTimeZoneHandling = DateTimeZoneHandling.Unspecified,
                    DateFormatHandling = DateFormatHandling.IsoDateFormat,
                    DateFormatString = "yyyy-MM-dd HH:mm:ss"
                }),
                v => JsonConvert.DeserializeObject<ICollection<EmployeeShiftBreakTime>>(v, new JsonSerializerSettings
                {
                    DateTimeZoneHandling = DateTimeZoneHandling.Unspecified,
                    DateFormatHandling = DateFormatHandling.IsoDateFormat,
                    DateFormatString = "yyyy-MM-dd HH:mm:ss"
                })
            ).Metadata.SetValueComparer(
                new ValueComparer<ICollection<EmployeeShiftBreakTime>>(
                    (c1, c2) => (c1 == null && c2 == null) || (c1 != null && c2 != null && c1.SequenceEqual(c2)),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()
                )
            );
        builder.Property(e => e.BreakTimeLastModifier)
            .HasColumnName("break_time_last_modifier");
        builder.Property(e => e.IsRequested)
            .HasDefaultValue(false)
            .HasColumnName("is_requested");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.IsApproved)
            .HasDefaultValue(false)
            .HasColumnName("is_approved");
        builder.Property(e => e.ApprovedBy)
            .HasColumnName("approved_by");
        builder.Property(e => e.ApprovedTime)
            .HasColumnType("timestamp without time zone")
            .HasColumnName("approved_time");

        builder.HasOne(d => d.Employee).WithMany(p => p.EmployeeShifts)
            .HasForeignKey(d => d.EmployeeUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("employee_shift_employee_id_fkey");

        builder.HasOne(d => d.Approver).WithMany(p => p.EmployeeShiftApprovers)
            .HasForeignKey(d => d.ApprovedBy)
            .HasConstraintName("employee_shift_approver_id_fkey");

        builder.HasOne(d => d.Project).WithMany(p => p.EmployeeShifts)
            .HasForeignKey(d => d.ProjectUid)
            .HasConstraintName("employee_shift_project_id_fkey");

        builder.HasOne(d => d.ProjectSchedule).WithMany(p => p.EmployeeShifts)
            .HasForeignKey(d => d.ProjectScheduleUid)
            .OnDelete(DeleteBehavior.Cascade)
            .HasConstraintName("employee_shift_project_schedule_id_fkey");

        builder.HasOne(d => d.MonthlyReport).WithMany(p => p.EmployeeShifts)
            .HasForeignKey(d => d.MonthlyReportUid)
            .OnDelete(DeleteBehavior.Cascade)
            .HasConstraintName("employee_shift_monthly_report_id_fkey");
    }
}
