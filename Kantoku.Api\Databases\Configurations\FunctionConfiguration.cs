using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class FunctionConfiguration(string schema) : IEntityTypeConfiguration<Function>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Function> builder)
    {
        builder.HasKey(e => e.FunctionUid).HasName("function_pkey");

        builder.ToTable("function", Schema, tb => tb.HasComment("Thông tin menu trong web"));

        builder.Property(e => e.FunctionUid)
            .HasColumnName("function_uid");
        builder.Property(e => e.Component)
            .HasColumnName("component");
        builder.Property(e => e.FunctionName)
            .HasColumnName("function_name");
        builder.Property(e => e.FunctionUrl)
            .HasColumnName("function_url");
        builder.Property(e => e.HideInMenu)
            .HasColumnName("hide_in_menu");
        builder.Property(e => e.IsHeader)
            .HasColumnName("is_header");
        builder.Property(e => e.OnlySuperUser)
            .HasDefaultValue(false)
            .HasColumnName("only_super_user");
        builder.Property(e => e.Icon)
            .HasColumnName("icon");
        builder.Property(e => e.Locale)
            .HasColumnName("locale");
        builder.Property(e => e.ParentUid)
            .HasColumnName("parent_uid");
        builder.Property(e => e.Redirect)
            .HasColumnName("redirect");
        builder.Property(e => e.Title)
            .HasColumnName("title");
        builder.Property(e => e.HideInBreadcrumb)
            .HasDefaultValue(false)
            .HasColumnName("hide_in_breadcrumb");
        builder.Property(e => e.HideChildrenInMenu)
            .HasDefaultValue(false)
            .HasColumnName("hide_children_in_menu");
        builder.Property(e => e.DisplayOrder)
            .HasColumnName("display_order");

        builder.HasOne(d => d.Parent).WithMany(p => p.Children)
            .HasForeignKey(d => d.ParentUid)
            .HasConstraintName("function_parent_id_fkey");
    }
}
