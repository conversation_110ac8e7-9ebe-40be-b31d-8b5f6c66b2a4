using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;

namespace Kantoku.Api.Databases.Queryables;

public class EntryTypeQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
}

public interface IEntryTypeQueryable
{
    IQueryable<EntryType> GetEntryTypeQuery(
        EntryTypeQueryableOptions options
    );

    IQueryable<EntryType> GetEntryTypeQueryFiltered(
        EntryTypeFilter filter,
        EntryTypeQueryableOptions options
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class EntryTypeQueryable(PostgreDbContext context) :
    BaseQueryable<EntryType>(context), IEntryTypeQueryable
{

    public IQueryable<EntryType> GetEntryTypeQuery(
        EntryTypeQueryableOptions options
    )
    {
        var query = base.GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }


    public IQueryable<EntryType> GetEntryTypeQueryFiltered(
        EntryTypeFilter filter,
        EntryTypeQueryableOptions options
    )
    {
        var query = GetEntryTypeQuery(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(p => p.OrgUid == filter.OrgId);
        }
        if (string.IsNullOrEmpty(filter.Keyword) == false)
        {
            query = query.Where(p => p.EntryTypeName.Contains(filter.Keyword));
        }
        return query;
    }
}


