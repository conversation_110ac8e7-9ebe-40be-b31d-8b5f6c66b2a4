using System.Text.Json.Serialization;
using Kantoku.Api.Dtos.Base.Response;
using Kantoku.Api.Dtos.EmployeeRequest.Response;

namespace Kantoku.Api.Dtos.EmployeeShift.Response;

public class ShiftResponseDto : BaseResponseDto
{
    public string? EmployeeShiftId { get; set; }

    public string? EmployeeId { get; set; }

    public string? EmployeeName { get; set; }

    public string? RankingId { get; set; }

    public string? RankingName { get; set; }

    public string? ProjectId { get; set; }

    public string? ProjectName { get; set; }

    public bool IsScheduled { get; set; }

    public string? WorkingLocation { get; set; }

    public string? WorkingDate { get; set; }

    public string? ScheduledStartTime { get; set; }

    public string? CheckInTime { get; set; }

    public string? ModifiedCheckInTimeLastModifierType { get; set; }

    public string? CheckInLocation { get; set; }

    public string? ScheduledEndTime { get; set; }

    public string? CheckOutTime { get; set; }

    public string? ModifiedCheckOutTimeLastModifierType { get; set; }

    public string? AutoCheckOutTime { get; set; }

    public string? CheckOutLocation { get; set; }

    public IEnumerable<BreakItemResponseDto>? BreakList { get; set; }

    public string? ModifiedBreakListLastModifierType { get; set; }

    public float? TotalScheduledWorkTime { get; set; }

    public float? TotalWorkTime { get; set; }

    public float? TotalBreakTime { get; set; }

    public float? TotalOverTime { get; set; }

    public string? AssignedRole { get; set; }

    public string? Description { get; set; }

    public bool? IsRequested { get; set; }

    public bool? IsApproved { get; set; }

    public string? ApprovedBy { get; set; }

    public string? ApprovedTime { get; set; }
}