using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Models;

public class OutSourcePrice : AuditableEntity
{
    public Guid OutSourcePriceUid { get; set; }
    public Guid OutSourceUid { get; set; }

    public DateOnly? EffectiveDate { get; set; }

    [AuditProperty]
    public int? PricePerDay { get; set; }

    [AuditProperty]
    public string? Description { get; set; }

    public bool IsDeleted { get; set; } = false;

    public virtual OutSource OutSource { get; set; } = null!;
}

