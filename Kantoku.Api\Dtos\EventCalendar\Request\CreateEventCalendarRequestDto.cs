using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.EventCalendar.Request;

public class CreateEventCalendarRequestDto
{
    /// <summary>
    /// Event name (*)
    /// The name of the event
    /// </summary>
    [Required]
    public string EventName { get; set; } = null!;

    /// <summary>
    /// Event start date (YYYY-MM-DD)
    /// The date that event starts
    /// </summary>
    [DateTimeValidator(typeof(DateOnly))]
    public string? EventStartDate { get; set; }

    /// <summary>
    /// Event end date (YYYY-MM-DD)
    /// The date that event ends
    /// </summary>
    [DateTimeValidator(typeof(DateOnly))]
    public string? EventEndDate { get; set; }

    /// <summary>
    /// Event start time (HH:MM:SS)
    /// The time that event starts
    /// </summary>
    [DateTimeValidator(typeof(TimeOnly))]
    public string? EventStartTime { get; set; }

    /// <summary>
    /// Event end time (HH:MM:SS)
    /// The time that event ends
    /// </summary>
    [DateTimeValidator(typeof(TimeOnly))]
    public string? EventEndTime { get; set; }

    /// <summary>
    /// Recurring flag
    /// Indicates whether the event is recurring (true) or not (false)
    /// </summary>
    public bool IsRecurring { get; set; }

    /// <summary>
    /// Recurring start date (YYYY-MM-DD)
    /// The date that recurring starts
    /// </summary>
    [DateTimeValidator(typeof(DateOnly))]
    public string? RecurringFrom { get; set; }

    /// <summary>
    /// Recurring end date (YYYY-MM-DD)
    /// The date that recurring ends
    /// </summary>
    [DateTimeValidator(typeof(DateOnly))]
    public string? RecurringTo { get; set; }

    /// <summary>
    /// Recurring type
    /// Specifies the recurring pattern: DAILY, WEEKLY, MONTHLY, YEARLY
    /// </summary>
    public string? RecurringType { get; set; }

    /// <summary>
    /// Weekly recurring days
    /// Only used when RecurringType is WEEKLY
    /// Array of days [1-7] where 1=Monday through 7=Sunday
    /// </summary>
    public ICollection<int>? RecurringDay { get; set; }

    /// <summary>
    /// Monthly recurring weeks
    /// Only used when RecurringType is MONTHLY
    /// Array of weeks [1-4] where 1=first week through 4=fourth week
    /// </summary>
    public ICollection<int>? RecurringWeek { get; set; }

    /// <summary>
    /// Yearly recurring months
    /// Only used when RecurringType is MONTHLY
    /// Array of months [1-12] where 1=January through 12=December
    /// </summary>
    public ICollection<int>? RecurringMonth { get; set; }

    /// <summary>
    /// Day off flag
    /// Indicates whether this event represents a day off (true) or not (false)
    /// </summary>
    public bool IsDayOff { get; set; } = true;

    /// <summary>
    /// Event description
    /// Additional details or notes about the event
    /// </summary>
    public string? Description { get; set; }
}
