using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace Kantoku.Api.Databases.Configurations;

public class CategoryConfiguration(string schema) : IEntityTypeConfiguration<Category>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Category> builder)
    {
        builder.HasKey(e => e.CategoryUid).HasName("category_pkey");

        builder.ToTable("category", Schema);

        builder.Property(e => e.CategoryUid)
            .HasColumnName("category_uid");
        builder.Property(e => e.CategoryCode)
            .HasColumnName("category_code");
        builder.Property(e => e.CategoryName)
            .HasColumnName("category_name");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.ParentUid)
            .HasColumnName("parent_uid");
        builder.Property(e => e.IsDefault)
            .HasDefaultValue(false)
            .HasColumnName("is_default");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.TranslatedCategory)
            .HasColumnType("jsonb")
            .HasColumnName("translated_category")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<ICollection<TranslatedCategory>>(v)
            ).Metadata.SetValueComparer(
                new ValueComparer<ICollection<TranslatedCategory>>(
                    (c1, c2) => (c1 == null && c2 == null) || (c1 != null && c2 != null && c1.SequenceEqual(c2)),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()
                )
            );
        builder.Property(e => e.RootCategoryCode)
            .HasColumnName("root_category_code");

        builder.Property(e => e.CreatedBy)
            .HasColumnName("created_by");
        builder.Property(e => e.LastModifiedBy)
            .HasColumnName("last_modified_by");
        builder.Property(e => e.CreatedTime)
            .HasColumnName("created_time")
            .HasColumnType("timestamp with time zone");
        builder.Property(e => e.LastModifiedTime)
            .HasColumnName("last_modified_time")
            .HasColumnType("timestamp with time zone");


        builder.HasOne(d => d.Org).WithMany(p => p.Categories)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("category_org_id_fkey");

        builder.HasOne(d => d.ParentCategory).WithMany(p => p.Children)
            .HasForeignKey(d => d.ParentUid)
            .HasConstraintName("category_parent_uid_fkey");
    }
}