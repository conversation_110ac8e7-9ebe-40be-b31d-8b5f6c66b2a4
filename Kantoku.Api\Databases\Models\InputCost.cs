﻿using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Models;

public class InputCost : AuditableEntity
{
    public Guid InputCostUid { get; set; }
    public Guid ConstructionUid { get; set; }

    [AuditProperty]
    public Guid? EntryTypeUid { get; set; }

    [AuditProperty]
    public Guid? VendorUid { get; set; }

    [AuditProperty]
    public Guid? PaymentTypeUid { get; set; }

    [AuditProperty]
    public string? Title { get; set; }

    [AuditProperty]
    public DateOnly IssueDate { get; set; }

    [AuditProperty]
    public DateOnly? PaymentDate { get; set; }

    [AuditProperty]
    public string OriginalNumber { get; set; } = null!;

    [AuditProperty]
    public string? Description { get; set; }

    [AuditProperty]
    public long? TotalAmount { get; set; }

    public ICollection<string>? ImageUrls { get; set; }

    public Guid OrgUid { get; set; }
    public bool IsDeleted { get; set; }

    public virtual EntryType? EntryType { get; set; }

    public virtual Vendor? Vendor { get; set; }

    public virtual PaymentType? PaymentType { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual Construction Construction { get; set; } = null!;

    public virtual ICollection<InputCostItem> InputCostItems { get; set; } = [];
}
