using System.Collections.Concurrent;
using Kantoku.Processor.Data.Repositories;
using Kantoku.Processor.Jobs.Bases;
using Kantoku.Processor.Models.BatchJobManager;
using Kantoku.Processor.Services.Interfaces;

namespace Kantoku.Processor.Services
{
    /// <summary>
    /// Service that handles batch job execution
    /// </summary>
    public class BatchExecutionService : IBatchExecutionService
    {
        private readonly JobFactory _jobFactory;
        private readonly IBatchJobRepository _jobRepository;
        private readonly ILogger<BatchExecutionService> _logger;

        // Track running jobs and their cancellation tokens
        private readonly ConcurrentDictionary<Guid, (CancellationTokenSource TokenSource, BatchJobHistory History)> _runningJobs = new();

        public BatchExecutionService(
            JobFactory jobFactory,
            IBatchJobRepository jobRepository,
            ILogger<BatchExecutionService> logger)
        {
            _jobFactory = jobFactory;
            _jobRepository = jobRepository;
            _logger = logger;
        }

        public async Task ExecuteJobAsync(Batch<PERSON>ob job, CancellationToken cancellationToken)
        {
            // Check if the job is already running
            if (IsJobRunning(job.JobId))
            {
                _logger.LogWarning("Job {JobName} (ID: {JobId}) is already running. Skipping execution.",
                    job.JobName, job.JobId);
                return;
            }

            // Create a linked cancellation token source so we can cancel from both the caller and internally
            var linkedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);

            // Create job history record
            var jobHistory = new BatchJobHistory
            {
                JobId = job.JobId,
                StartedAt = DateTime.UtcNow,
                Status = JobExecutionStatus.InProgress
            };

            try
            {
                // Store in the database
                jobHistory = await _jobRepository.CreateJobHistoryAsync(jobHistory);

                // Add to running jobs dictionary
                _runningJobs[job.JobId] = (linkedCts, jobHistory);

                // Create the job instance using the factory
                var jobInstance = _jobFactory.CreateJob(job);

                if (jobInstance == null)
                {
                    throw new InvalidOperationException($"Failed to create job instance for job type: {job.JobType}");
                }

                if (jobInstance is JobBase baseJob)
                {
                    // Set the job history and cancellation token
                    baseJob.Initialize(jobHistory, linkedCts.Token);

                    _logger.LogInformation("Starting execution of job {JobName} (ID: {JobId})",
                        job.JobName, job.JobId);

                    await jobInstance.ExecuteAsync();

                    // Update job history with success status
                    jobHistory.CompletedAt = DateTime.UtcNow;
                    jobHistory.Status = JobExecutionStatus.Success;
                    jobHistory.ExecutionLog = baseJob.GetExecutionLog();
                }
                else
                {
                    _logger.LogInformation("Starting execution of job {JobName} (ID: {JobId})",
                        job.JobName, job.JobId);

                    await jobInstance.ExecuteAsync();

                    // Update job history with success status
                    jobHistory.CompletedAt = DateTime.UtcNow;
                    jobHistory.Status = JobExecutionStatus.Success;
                }

                await _jobRepository.UpdateJobHistoryAsync(jobHistory);

                // Update the last run time on the job
                await _jobRepository.UpdateJobLastRunTimeAsync(job.JobId, jobHistory.StartedAt);

                _logger.LogInformation("Job {JobName} (ID: {JobId}) completed successfully",
                    job.JobName, job.JobId);
            }
            catch (OperationCanceledException)
            {
                _logger.LogWarning("Job {JobName} (ID: {JobId}) was cancelled",
                    job.JobName, job.JobId);

                // Update job history with cancelled status
                jobHistory.CompletedAt = DateTime.UtcNow;
                jobHistory.Status = JobExecutionStatus.Cancelled;

                await _jobRepository.UpdateJobHistoryAsync(jobHistory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Job {JobName} (ID: {JobId}) failed with error: {ErrorMessage}",
                    job.JobName, job.JobId, ex.Message);

                // Update job history with failed status
                jobHistory.CompletedAt = DateTime.UtcNow;
                jobHistory.Status = JobExecutionStatus.Failed;
                jobHistory.ErrorMessage = ex.Message;

                await _jobRepository.UpdateJobHistoryAsync(jobHistory);
            }
            finally
            {
                // Clean up resources
                linkedCts.Dispose();

                // Remove from running jobs
                _runningJobs.TryRemove(job.JobId, out _);
            }
        }

        public BatchJobHistory? GetRunningJobStatus(Guid jobId)
        {
            if (_runningJobs.TryGetValue(jobId, out var jobInfo))
            {
                return jobInfo.History;
            }

            return null;
        }

        public bool IsJobRunning(Guid jobId)
        {
            return _runningJobs.ContainsKey(jobId);
        }

        public bool TryCancelJob(Guid jobId)
        {
            if (_runningJobs.TryGetValue(jobId, out var jobInfo))
            {
                try
                {
                    _logger.LogInformation("Attempting to cancel job with ID {JobId}", jobId);
                    jobInfo.TokenSource.Cancel();
                    return true;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to cancel job with ID {JobId}", jobId);
                    return false;
                }
            }

            return false;
        }
    }
}