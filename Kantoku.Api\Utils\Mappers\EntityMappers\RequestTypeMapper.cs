using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Common;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class RequestTypeMapper
{
    #region Entity to DTO mappings

    public static RequestTypeResponseDto ToRequestTypeResponseDto(this RequestType requestType, string languageCode)
    {
        if (requestType == null)
            return new RequestTypeResponseDto();
            
        var responseDto = new RequestTypeResponseDto
        {
            RequestTypeCode = requestType.RequestTypeCode,
            RequestTypeName = requestType.TranslatedRequestType?
                .FirstOrDefault(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase))
                ?.RequestTypeName,
            RequiredLevel1Approval = requestType.RequiredLevel1Approval,
            RequiredLevel2Approval = requestType.RequiredLevel2Approval
        };
        
        return responseDto;
    }

    // Add mapping for collection 
    public static IEnumerable<RequestTypeResponseDto> ToRequestTypeResponseDtos(this IEnumerable<RequestType> requestTypes, string languageCode)
    {
        return requestTypes?.Select(rt => rt.ToRequestTypeResponseDto(languageCode)) ?? Enumerable.Empty<RequestTypeResponseDto>();
    }

    // Map to RequestTypesResponseDto with pagination info
    public static RequestTypesResponseDto ToRequestTypesResponseDto(this IEnumerable<RequestType> requestTypes, string languageCode, int pageIndex = 1, int pageSize = 0)
    {
        var items = requestTypes.ToRequestTypeResponseDtos(languageCode);
        var count = items.Count();
        
        return new RequestTypesResponseDto
        {
            Items = items,
            PageIndex = pageIndex,
            PageSize = pageSize > 0 ? pageSize : count,
            TotalRow = count
        };
    }

    #endregion
} 