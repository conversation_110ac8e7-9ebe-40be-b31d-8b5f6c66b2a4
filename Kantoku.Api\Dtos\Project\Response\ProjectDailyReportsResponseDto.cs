namespace Kantoku.Api.Dtos.Project.Response;

public class ProjectDailyReportsResponseDto
{
    public IEnumerable<ProjectDailyReportResponseDto> Items { get; set; } = [];
    public int PageNum { get; set; }
    public int PageSize { get; set; }
    public int TotalRecords { get; set; }
}

public class ProjectDailyReportResponseDto
{
    public string? ReportId { get; set; }
    public string? ProjectId { get; set; }
    public string? ProjectCode { get; set; }
    public string? ProjectName { get; set; }
    public string? Address { get; set; }
    public string? ReportDate { get; set; }
    public string? Description { get; set; }
    public IEnumerable<EmployeeWorkloadResponseDto>? EmployeeWorkloads { get; set; }
    public IEnumerable<OutSourceWorkloadResponseDto>? OutSourceWorkloads { get; set; }
}

public class EmployeeWorkloadResponseDto
{
    public string? EmployeeId { get; set; }
    public string? EmployeeName { get; set; }
    public string? RankingName { get; set; }
    public float? WorkloadOnMainConstruction { get; set; }
    public float? WorkloadOnSubConstruction { get; set; }
}

public class OutSourceWorkloadResponseDto
{
    public string? OutSourceId { get; set; }
    public string? OutSourceName { get; set; }
    public float? WorkloadOnMainConstruction { get; set; }
    public float? WorkloadOnSubConstruction { get; set; }
}
