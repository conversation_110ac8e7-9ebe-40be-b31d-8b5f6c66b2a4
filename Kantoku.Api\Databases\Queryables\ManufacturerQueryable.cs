using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class ManufacturerQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedItems { get; set; } = false;
}

public interface IManufacturerQueryable
{
    IQueryable<Manufacturer> GetManufacturersQuery(
        ManufacturerQueryableOptions options
    );
    IQueryable<Manufacturer> GetManufacturersQueryIncluded(
        ManufacturerQueryableOptions options,
        IQueryable<Manufacturer>? query = null
    );
    IQueryable<Manufacturer> GetManufacturersQueryFiltered(
        ManufacturerFilter filter,
        ManufacturerQueryableOptions options,
        IQueryable<Manufacturer>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class ManufacturerQueryable(PostgreDbContext context) :
    BaseQueryable<Manufacturer>(context), IManufacturerQueryable
{
    public IQueryable<Manufacturer> GetManufacturersQuery(
        ManufacturerQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<Manufacturer> GetManufacturersQueryIncluded(
        ManufacturerQueryableOptions options,
        IQueryable<Manufacturer>? query = null
    )
    {
        query ??= GetManufacturersQuery(options);
        if (options.IncludedItems || options.IncludedAll)
        {
            query = query.Include(p => p.Items)
                .ThenInclude(i => i.Category);
        }

        return query;
    }

    public IQueryable<Manufacturer> GetManufacturersQueryFiltered(
        ManufacturerFilter filter,
        ManufacturerQueryableOptions options,
        IQueryable<Manufacturer>? query = null
    )
    {
        query ??= GetManufacturersQueryIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(p => p.OrgUid == filter.OrgId);
        }
        if (filter.Keyword is not null)
        {
            query = query.Where(p => p.ManufacturerName.Contains(filter.Keyword)
            || p.ManufacturerCode.Contains(filter.Keyword));
        }

        return query;
    }
}

