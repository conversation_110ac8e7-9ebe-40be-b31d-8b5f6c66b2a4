namespace Kantoku.Api.Databases.Models;

public class NotificationTarget
{
    public Guid NotificationTargetUid { get; set; }
    public Guid NotificationUid { get; set; }

    public string TargetType { get; set; } = null!;
    public ICollection<Guid> TargetIds { get; set; } = [];

    public string PublishStatus { get; set; } = null!;
    public DateTime? PublishAt { get; set; }

    public bool IsDeleted { get; set; }

    public virtual Notification Notification { get; set; } = null!;
}

public class TargetTypeConstant
{
    public const string INDIVIDUAL = nameof(INDIVIDUAL);
    public const string ROLE = nameof(ROLE);
    public const string ALL = nameof(ALL);
}

public class NotificationStatusConstant
{
    public const string PENDING = nameof(PENDING);
    public const string PUBLISHED = nameof(PUBLISHED);
    public const string PARTIALLY_PUBLISHED = nameof(PARTIALLY_PUBLISHED);
    public const string FAILED = nameof(FAILED);
    public const string PARTIALLY_FAILED = nameof(PARTIALLY_FAILED);
}