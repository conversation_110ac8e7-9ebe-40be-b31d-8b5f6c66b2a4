namespace Kantoku.Processor.Configurations;

/// <summary>
/// Configuration for Hangfire job processing
/// </summary>
public class HangfireConfig
{
    /// <summary>
    /// Configuration section name
    /// </summary>
    public const string SectionName = "HangfireConfig";

    /// <summary>
    /// Database connection string for Hangfire
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// Schema name for Hangfire tables
    /// </summary>
    public string Schema { get; set; } = "hangfire";

    /// <summary>
    /// Dashboard path (e.g., "/hangfire")
    /// </summary>
    public string DashboardPath { get; set; } = "/hangfire";

    /// <summary>
    /// Whether to enable the dashboard
    /// </summary>
    public bool EnableDashboard { get; set; } = true;

    /// <summary>
    /// Number of worker threads
    /// </summary>
    public int WorkerCount { get; set; } = Environment.ProcessorCount;

    /// <summary>
    /// Job retry attempts
    /// </summary>
    public int RetryAttempts { get; set; } = 3;

    /// <summary>
    /// Job execution timeout in minutes
    /// </summary>
    public int JobTimeoutMinutes { get; set; } = 30;

    /// <summary>
    /// Whether to enable detailed logging
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = true;

    /// <summary>
    /// Builds the connection string from individual components
    /// </summary>
    public string BuildConnectionString()
    {
        return !string.IsNullOrEmpty(ConnectionString) 
            ? ConnectionString 
            : throw new InvalidOperationException("Hangfire connection string is not configured");
    }
}
