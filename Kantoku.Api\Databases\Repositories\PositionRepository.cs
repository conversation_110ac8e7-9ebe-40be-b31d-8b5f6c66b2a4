using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Utils.Helpers;
using Kantoku.Api.Filters.Domains;

namespace Kantoku.Api.Databases.Repositories;

public interface IPositionRepository
{
    Task<(IEnumerable<Position>, int)> GetByFilter(PositionFilter filter, PositionQueryableOptions options);
    Task<Position?> GetById(Guid positionId, PositionQueryableOptions options);
    Task<Position?> Create(Position position);
    Task<Position?> Update(Position position);
}

[Repository(ServiceLifetime.Scoped)]
public class PositionRepository : BaseRepository<Position>, IPositionRepository
{
    private readonly IPositionQueryable positionQueryable;
    public PositionRepository(
        PostgreDbContext context,
        IPositionQueryable positionQueryable,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor
    )
        : base(context, logger, httpContextAccessor)
    {
        this.positionQueryable = positionQueryable;
    }


    public async Task<(IEnumerable<Position>, int)> GetByFilter(PositionFilter filter, PositionQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = positionQueryable.GetPositionQueryFiltered(filter, options);

            var positions = await query
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var total = await query.CountAsync();

            return (positions, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all positions");
            return (new List<Position>(), 0);
        }
    }

    public async Task<Position?> GetById(Guid positionId, PositionQueryableOptions options)
    {
        try
        {
            var query = positionQueryable.GetPositionQueryable(options)
                .Where(p => p.PositionUid == positionId);

            var result = await query.FirstOrDefaultAsync();
            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting position by id {PositionId}", positionId);
            return null;
        }
    }

    public async Task<Position?> Create(Position position)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Positions.AddAsync(position);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(position.PositionUid, new PositionQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating position {Position}", position);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Position?> Update(Position position)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Positions.Update(position);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(position.PositionUid, new PositionQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating position {Position}", position);
            await transaction.RollbackAsync();
            return null;
        }
    }
}

