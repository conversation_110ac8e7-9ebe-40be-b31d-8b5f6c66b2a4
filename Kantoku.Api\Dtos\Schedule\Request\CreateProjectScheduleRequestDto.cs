using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.Schedule.Request;

public class CreateProjectScheduleRequestDto
{
    /// <summary>
    /// Project id (*)
    /// </summary>
    [Required]
    [JsonPropertyName("projectId")]
    public required Guid ProjectId { get; set; }

    /// <summary>
    /// Working date (*)    
    /// Format: yyyy-MM-dd
    /// </summary>
    [Required]
    [JsonPropertyName("workingDate")]
    public required DateOnly WorkingDate { get; set; }

    /// <summary>
    /// Planned workload
    /// </summary>
    [Required]
    [JsonPropertyName("plannedWorkload")]
    public float PlannedWorkload { get; set; }

    /// <summary>
    /// Estimated workload
    /// </summary>
    [Required]
    [JsonPropertyName("estimatedWorkload")]
    public float PresignedWorkload { get; set; }

    /// <summary>
    /// Description of the project schedule
    /// </summary>
    [JsonPropertyName("description")]
    public string? Description { get; set; }

    /// <summary>
    /// Employee shifts
    /// </summary>
    [JsonPropertyName("employeeShifts")]
    public IEnumerable<CreateScheduledEmployeeShiftRequestDto> EmployeeShifts { get; set; } = [];

    /// <summary>
    /// Outsource shifts
    /// </summary>
    [JsonPropertyName("outsourceShifts")]
    public IEnumerable<CreateScheduledOutSourceShiftRequestDto> OutSourceShifts { get; set; } = [];
}