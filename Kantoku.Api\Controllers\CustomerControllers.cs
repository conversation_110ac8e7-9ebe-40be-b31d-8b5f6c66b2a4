using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Factories.Response;
using Microsoft.AspNetCore.Authorization;
using Kantoku.Api.Utils.Exceptions;
using Kantoku.Api.Dtos.Customer.Request;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Dtos.Customer.Response;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Constants;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class CustomerController : BaseController
{
    private readonly ICustomerService customerService;
    private readonly IAuditLogService auditLogService;


    public CustomerController(
        ITResponseFactory responseFactory,
        ICustomerService customerService,
        IAuditLogService auditLogService
    ) : base(responseFactory)
    {
        this.customerService = customerService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get paginated list of customers
    /// </summary>
    /// <param name="filter">Filter parameters for pagination</param>
    /// <returns>Paginated list of customers</returns>
    [HttpGet]
    public async Task<GeneralResponse<CustomersResponseDto>> GetAll([FromQuery] CustomerFilter filter)
    {
        try
        {
            var result = await customerService.GetCustomerByFilter(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<CustomersResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get customer details by ID
    /// </summary>
    /// <param name="id">Customer ID to retrieve</param>
    /// <param name="keyword">Keyword to search for (project code/name)</param>
    /// <param name="pageNum">Page number of project list</param>
    /// <param name="pageSize">Page size of project list</param>
    /// <returns>Customer details</returns>
    [HttpGet("{id}")]
    public async Task<GeneralResponse<CustomerDetailResponseDto>> GetById(
        [FromRoute] Guid id,
        [FromQuery] string? keyword,
        [FromQuery] int pageNum = 1,
        [FromQuery] int pageSize = 10
    )
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<CustomerDetailResponseDto>();

            var result = await customerService.GetCustomerById(id, keyword, pageNum, pageSize);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<CustomerDetailResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get customer logo
    /// </summary>
    /// <param name="id">Customer ID</param>
    /// <param name="orgId">Organization ID</param>
    /// <returns>Customer logo</returns>
    [AllowAnonymous]
    [HttpGet("{id}/logo")]
    public async Task<dynamic> GetLogo([FromRoute] Guid id, [FromQuery] string orgId)
    {
        try
        {
            var result = await customerService.GetLogo(id, orgId);
            if (result.Data is null || result.Data.Length == 0)
            {
                return Fail<FileContentResult>(ResponseCodeConstant.CUSTOMER_LOGO_NOT_EXIST);
            }
            return File(result.Data, "image/jpeg");
        }
        catch (BusinessException e)
        {
            return Fail<FileContentResult>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new customer
    /// </summary>
    /// <param name="requestDto">Customer creation data</param>
    /// <returns>Created customer details</returns>
    [HttpPost]
    [Consumes("multipart/form-data")]
    [Produces("application/json")]
    public async Task<GeneralResponse<CustomerResponseDto>> CreateCustomer([FromForm] CreateCustomerRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<CustomerResponseDto>();

            var result = await customerService.CreateCustomer(requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<CustomerResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update an existing customer
    /// </summary>
    /// <param name="id">Customer ID to update</param>
    /// <param name="requestDto">Updated customer data</param>
    /// <returns>Updated customer details</returns>
    [HttpPut("{id}")]
    [Consumes("multipart/form-data")]
    [Produces("application/json")]
    public async Task<GeneralResponse<CustomerResponseDto>> UpdateCustomer([FromRoute] Guid id, [FromForm] UpdateCustomerRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<CustomerResponseDto>();

            var result = await customerService.UpdateCustomer(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<CustomerResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete a customer
    /// </summary>
    /// <param name="id">Customer ID to delete</param>
    /// <returns>Success response if deletion was successful</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> DeleteCustomer([FromRoute] Guid id)
    {
        try
        {
            var result = await customerService.DeleteCustomer(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for a customer
    /// </summary>
    /// <param name="id">Customer ID</param>
    /// <param name="filter">Filter parameters for audit logs</param>
    /// <returns>Filtered audit log entries</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetCustomerLogs([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<Customer>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
