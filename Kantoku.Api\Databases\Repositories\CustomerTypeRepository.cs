using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Databases.Seeders.CustomerType;
using Kantoku.Api.Filters.Domains;

namespace Kantoku.Api.Databases.Repositories;

public interface ICustomerTypeRepository
{
    Task<(IEnumerable<CustomerType>, int)> GetByFilter(CustomerTypeFilter filter);
}

[Repository(ServiceLifetime.Scoped)]
public class CustomerTypeRepository : BaseRepository<CustomerType>, ICustomerTypeRepository
{
    private readonly ICustomerTypeSeeder customerTypeSeeder;

    public CustomerTypeRepository(
        PostgreDbContext context,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor,
        ICustomerTypeSeeder customerTypeSeeder
    ) : base(context, logger, httpContextAccessor)
    {
        this.customerTypeSeeder = customerTypeSeeder;
    }

    public async Task<(IEnumerable<CustomerType>, int)> GetByFilter(CustomerTypeFilter filter)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = context.CustomerTypes.AsQueryable();
            int v = await query.CountAsync();
            if (v == 0)
            {
                await customerTypeSeeder.Seed();
            }
            if (!string.IsNullOrEmpty(filter.CustomerTypeCode))
            {
                query = query.Where(c => c.CustomerTypeCode.ToUpper().Equals(filter.CustomerTypeCode.ToUpper()));
            }

            var total = await query
                .CountAsync();

            var result = await query
                .OrderBy(c => c.CustomerTypeCode)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            return (result, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all Customers");
            return ([], 0);
        }
    }
}
