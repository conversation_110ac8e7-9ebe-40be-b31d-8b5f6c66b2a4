FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src
COPY ["Kantoku.Api.csproj", "./"]
RUN dotnet restore "Kantoku.Api.csproj"
COPY . .
ARG configuration=Release
RUN dotnet publish "Kantoku.Api.csproj" -c $configuration -o /app/publish /p:UseAppHost=false

FROM mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app
COPY --from=build /app/publish .
EXPOSE 4869
ENV ASPNETCORE_URLS=http://+:4869
USER app
ENTRYPOINT ["dotnet", "Kantoku.Api.dll"]