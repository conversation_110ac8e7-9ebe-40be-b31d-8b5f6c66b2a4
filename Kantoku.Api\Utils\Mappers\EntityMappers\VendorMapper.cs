using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Vendor.Request;
using Kantoku.Api.Dtos.Vendor.Response;
using Kantoku.Api.Utils.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class VendorMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a Vendor entity to a VendorResponseDto
    /// </summary>
    /// <param name="vendor"></param>
    /// <returns> VendorResponseDto </returns>
    public static VendorResponseDto ToVendorResponseDto(this Vendor vendor)
    {
        if (vendor == null)
            return new VendorResponseDto();

        return new VendorResponseDto
        {
            VendorId = vendor.VendorUid.ToString(),
            VendorCode = vendor.VendorCode,
            VendorName = vendor.VendorName,
            VendorSubName = vendor.VendorSubName,
            CorporateNumber = vendor.CorporateNumber,
            Address = vendor.Address,
            PhoneNumber = vendor.PhoneNumber,
            Email = vendor.Email,
            Description = vendor.Description,
            LogoUrl = vendor.LogoUrl
        };
    }

    /// <summary>
    /// Maps a collection of Vendor entities to a VendorsResponseDto
    /// </summary>
    /// <param name="vendors"></param>
    /// <param name="pageNum"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalRecords"></param>
    /// <returns> VendorsResponseDto </returns>
    public static VendorsResponseDto ToVendorsResponseDto(this IEnumerable<Vendor> vendors, int pageNum, int pageSize, int totalRecords)
    {
        if (vendors == null)
            return new VendorsResponseDto();

        return new VendorsResponseDto
        {
            Items = vendors.Select(v => v.ToVendorResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    /// <summary>
    /// Maps a Vendor entity to a VendorDetailResponseDto
    /// </summary>
    /// <param name="vendor"></param>
    /// <param name="inputCosts"></param>
    /// <param name="pageNum"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalRecords"></param>
    /// <returns> VendorDetailResponseDto </returns>
    public static VendorDetailResponseDto ToVendorDetailResponseDto(this Vendor vendor, IEnumerable<InputCost> inputCosts, int pageNum, int pageSize, int totalRecords)
    {
        if (vendor == null)
            return new VendorDetailResponseDto();

        var vendorResponse = vendor.ToVendorResponseDto();
        var inputCostsResponse = inputCosts.Select(i => i.ToVendorInvoice()).ToList();
        return new VendorDetailResponseDto
        {
            VendorId = vendorResponse.VendorId,
            VendorCode = vendorResponse.VendorCode,
            VendorName = vendorResponse.VendorName,
            VendorSubName = vendorResponse.VendorSubName,
            CorporateNumber = vendorResponse.CorporateNumber,
            Address = vendorResponse.Address,
            PhoneNumber = vendorResponse.PhoneNumber,
            Email = vendorResponse.Email,
            Description = vendorResponse.Description,
            LogoUrl = vendorResponse.LogoUrl,
            VendorInvoices = inputCostsResponse,
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Map CreateVendorRequestDto to Vendor entity
    /// </summary>
    /// <param name="dto"></param>
    /// <param name="orgUid"></param>
    /// <returns> Vendor entity </returns>
    public static Vendor? ToEntity(this CreateVendorRequestDto dto, Guid orgUid)
    {
        if (dto == null)
            return null;

        var entity = new Vendor
        {
            VendorUid = GuidHelper.GenerateUUIDv7(),
            OrgUid = orgUid,
            VendorCode = dto.VendorCode,
            VendorName = dto.VendorName,
            VendorSubName = dto.VendorSubName,
            CorporateNumber = dto.CorporateNumber,
            Address = dto.Address,
            PhoneNumber = dto.PhoneNumber,
            Email = dto.Email,
            Description = dto.Description,
            IsDeleted = false
        };

        return entity;
    }

    /// <summary>
    /// Map UpdateVendorRequestDto to Vendor entity
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="dto"></param>
    public static void UpdateFromDto(this Vendor entity, UpdateVendorRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (dto.VendorCode != null)
            entity.VendorCode = dto.VendorCode;

        if (dto.VendorName != null)
            entity.VendorName = dto.VendorName;

        if (dto.VendorSubName != null)
            entity.VendorSubName = dto.VendorSubName;

        if (dto.CorporateNumber != null)
            entity.CorporateNumber = dto.CorporateNumber;

        if (dto.Address != null)
            entity.Address = dto.Address;

        if (dto.PhoneNumber != null)
            entity.PhoneNumber = dto.PhoneNumber;

        if (dto.Email != null)
            entity.Email = dto.Email;

        if (dto.Description != null)
            entity.Description = dto.Description;

        if (dto.Logo != null)
            entity.LogoUrl = dto.Logo.FileName;
    }

    #endregion
}