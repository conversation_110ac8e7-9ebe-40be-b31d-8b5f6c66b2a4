using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class ProjectManagerConfiguration(string schema) : IEntityTypeConfiguration<ProjectManager>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<ProjectManager> builder)
    {
        builder.HasKey(e => new { e.ProjectUid, e.EmployeeUid }).HasName("project_manager_pkey");

        builder.ToTable("project_manager", Schema);

        builder.Property(e => e.ProjectUid)
            .HasColumnName("project_uid");
        builder.Property(e => e.EmployeeUid)
            .HasColumnName("employee_id");
        builder.Property(e => e.EmployeeUid)
            .HasColumnName("employee_uid");
        builder.Property(e => e.Is<PERSON>ry)
            .HasDefaultValue(false)
            .HasColumnName("is_primary");

        builder.HasOne(d => d.Employee).WithMany(p => p.ProjectManagers)
            .HasForeignKey(d => d.EmployeeUid)
            .HasConstraintName("project_manager_employee_id_fkey");

        builder.HasOne(d => d.Project).WithMany(p => p.Managers)
            .HasForeignKey(d => d.ProjectUid)
            .HasConstraintName("project_manager_project_id_fkey");
    }
}

