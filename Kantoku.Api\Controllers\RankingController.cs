using Microsoft.AspNetCore.Mvc;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Services;
using Microsoft.AspNetCore.Authorization;
using Kantoku.Api.Utils.Exceptions;
using Kantoku.Api.Dtos.Ranking.Request;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Dtos.Ranking.Response;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Dtos.EmployeeCost.Response;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class RankingController : BaseController
{
    private readonly IRankingService rankingService;
    private readonly IAuditLogService auditLogService;
    public RankingController(IRankingService rankingService,
        ITResponseFactory responseFactory,
        IAuditLogService auditLogService)
        : base(responseFactory)
    {
        this.rankingService = rankingService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get ranking details by ID
    /// </summary>
    /// <param name="id">Ranking ID</param>
    /// <returns>Ranking details</returns>
    [HttpGet("{id}")]
    public async Task<GeneralResponse<RankingResponseDto>> GetById([FromRoute] Guid id)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<RankingResponseDto>();

            var result = await rankingService.GetRankingById(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<RankingResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get ranking information
    /// </summary>
    /// <param name="filter">Ranking filter</param>
    /// <returns>List of ranking information</returns>
    [HttpGet]
    public async Task<GeneralResponse<RankingsResponseDto>> GetRankingInfo([FromQuery] RankingFilter filter)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<RankingsResponseDto>();

            var result = await rankingService.GetAllRanking(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<RankingsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Getsimple ranking information
    /// </summary>
    /// <param name="filter">Ranking filter</param>
    /// <returns>List of simple ranking information</returns>
    [HttpGet("simple")]
    public async Task<GeneralResponse<SimpleRankingsResponseDto>> GetSimpleRankingInfo([FromQuery] RankingFilter filter)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<SimpleRankingsResponseDto>();

            var result = await rankingService.GetSimpleRankingInfo(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<SimpleRankingsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new ranking
    /// </summary>
    /// <param name="requestDto">Ranking creation details</param>
    /// <returns>Created ranking details</returns>
    [HttpPost]
    public async Task<GeneralResponse<RankingResponseDto>> CreateRanking([FromBody] CreateRankingRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<RankingResponseDto>();

            if (requestDto.MinValue is null && requestDto.MaxValue is null)
                return BadRequest<RankingResponseDto>();

            var result = await rankingService.CreateRanking(requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<RankingResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update an existing ranking
    /// </summary>
    /// <param name="id">Ranking ID to update</param>
    /// <param name="requestDto">Updated ranking details</param>
    /// <returns>Updated ranking details</returns>
    [HttpPut("{id}")]
    public async Task<GeneralResponse<RankingResponseDto>> UpdateRanking([FromRoute] Guid id, [FromBody] UpdateRankingRequestDto requestDto)
    {
        try
        {
            var result = await rankingService.UpdateRanking(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<RankingResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete an existing ranking
    /// </summary>
    /// <param name="id">Ranking ID to delete</param>
    /// <returns>Deleted ranking details</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> DeleteRanking([FromRoute] Guid id)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<bool>();

            var result = await rankingService.DeleteRanking(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get employee rankings by employee ID
    /// </summary>
    /// <param name="employeeId">Employee ID</param>
    /// <param name="dateFrom">Date from</param>
    /// <param name="dateTo">Date to</param>
    /// <returns>List of employee rankings</returns>
    [HttpGet("employee/{employeeId}")]
    public async Task<GeneralResponse<EmployeeCostsResponseDto>> GetEmployeeRankingsByEmployeeId([FromRoute] Guid employeeId,
         [FromQuery] DateOnly dateFrom,
         [FromQuery] DateOnly dateTo)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<EmployeeCostsResponseDto>();

            var result = await rankingService.GetEmployeeCostByEmployeeId(employeeId, dateFrom, dateTo);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<EmployeeCostsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get employee rankings by filter
    /// </summary>
    /// <param name="filter">Employee ranking filter</param>
    /// <returns>List of employee rankings</returns>
    [HttpGet("employee")]
    public async Task<GeneralResponse<EmployeeCostsResponseDto>> GetEmployeeRankingsByFilter([FromQuery] EmployeeCostFilter filter)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<EmployeeCostsResponseDto>();

            var result = await rankingService.GetEmployeeCostByFilter(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<EmployeeCostsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for a specific ranking
    /// </summary>
    /// <param name="id">Ranking ID</param>
    /// <param name="filter">Audit log filter parameters</param>
    /// <returns>List of audit logs</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetRankingLogs([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<Ranking>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
