﻿using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Models;

public class Contractor : AuditableEntity
{
    public Guid ContractorUid { get; set; }

    [AuditProperty]
    public string ContractorCode { get; set; } = null!;

    [AuditProperty]
    public string ContractorName { get; set; } = null!;

    [AuditProperty]
    public string? ContractorSubName { get; set; }

    [AuditProperty]
    public string? CorporateNumber { get; set; }

    [AuditProperty]
    public string? Address { get; set; }

    [AuditProperty]
    public string? PhoneNumber { get; set; }

    [AuditProperty]
    public string? Email { get; set; }

    [AuditProperty]
    public ContactPerson? ContactPerson { get; set; }

    [AuditProperty]
    public string? Description { get; set; }

    public bool IsDeleted { get; set; }

    public Guid OrgUid { get; set; }

    public string? LogoUrl { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual ICollection<ConstructionPaymentRequest> ConstructionPaymentRequests { get; set; } = [];

    public virtual ICollection<Project> Projects { get; set; } = [];
}