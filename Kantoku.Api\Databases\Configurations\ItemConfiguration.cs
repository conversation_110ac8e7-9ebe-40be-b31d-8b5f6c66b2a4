using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Kantoku.Api.Databases.Configurations;

public class ItemConfiguration(string schema) : IEntityTypeConfiguration<Item>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Item> builder)
    {
        builder.HasKey(e => e.ItemUid).HasName("item_pkey");

        builder.ToTable("item", Schema, tb => tb.HasComment("<PERSON><PERSON><PERSON> thông tin hạng mục, không lưu giá"));

        builder.Property(e => e.ItemUid)
            .HasColumnName("item_uid");
        builder.Property(e => e.CategoryUid)
            .HasColumnName("category_uid");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.ItemCode)
            .HasColumnName("item_code");
        builder.Property(e => e.ItemName)
            .HasColumnName("item_name");
        builder.Property(e => e.ItemSubName)
            .HasColumnName("item_sub_name");
        builder.Property(e => e.Size)
            .HasColumnName("size");
        builder.Property(e => e.SerialNumber)
            .HasColumnName("serial_number");
        builder.Property(e => e.EquipmentAttribute)
            .HasColumnName("equipment_attribute")
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<EquipmentAttribute>(v)
            );
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.ManufacturerUid)
            .HasColumnName("manufacturer_uid");
        builder.Property(e => e.ImageUrl)
            .HasColumnName("image_url");

        builder.HasOne(d => d.Org).WithMany(p => p.Items)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("item_org_id_fkey");

        builder.HasOne(d => d.Category).WithMany(p => p.Items)
            .HasForeignKey(d => d.CategoryUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("item_category_id_fkey");

        builder.HasOne(d => d.Manufacturer).WithMany(p => p.Items)
            .HasForeignKey(d => d.ManufacturerUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("item_manufacturer_id_fkey");
    }
}
