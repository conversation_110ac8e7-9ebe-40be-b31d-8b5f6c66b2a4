﻿using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Base.Response;

namespace Kantoku.Api.Dtos.Vendor.Response;

/// <summary>
/// Represents the response data transfer object for a vendor.
/// </summary>
public class VendorResponseDto : BaseResponseDto
{
    /// <summary>
    ///  the unique identifier for the vendor. (*)
    /// </summary>
    public string? VendorId { get; set; }

    /// <summary>
    ///  the code associated with the vendor. (*)
    /// </summary>
    public string? VendorCode { get; set; }

    /// <summary>
    ///  the name of the vendor. (*)
    /// </summary>
    public string? VendorName { get; set; }

    /// <summary>
    ///  the sub-name of the vendor.
    /// </summary>
    public string? VendorSubName { get; set; }

    /// <summary>
    ///  the corporate number of the vendor.
    /// </summary>
    public string? CorporateNumber { get; set; }

    /// <summary>
    ///  the address of the vendor.
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    ///  the phone number of the vendor.
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    ///  the email address of the vendor.
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    ///  the contact person for the vendor.
    /// </summary>
    public ContactPerson? ContactPerson { get; set; }

    /// <summary>
    ///  the description of the vendor.
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    ///  the logo of the vendor.
    /// </summary>
    public string? LogoUrl { get; set; }
}

