using Kantoku.Processor.Data.Contexts;
using Kantoku.Processor.Data.Repositories;
using Kantoku.Processor.Data.Seeders;
using Kantoku.Processor.Jobs.Bases;
using Kantoku.Processor.Models.BatchJobManager;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Processor.Jobs
{
    /// <summary>
    /// Job that automatically checks out employees who haven't checked out
    /// after their scheduled shift end time (after 1 hour)
    /// </summary>
    public class AutoCheckoutJob : JobBase
    {
        private readonly ILogger<AutoCheckoutJob> _logger;

        // We'll use the IServiceScopeFactory instead of IServiceProvider
        // This allows us to create scopes on demand without relying on 
        // a potentially disposed IServiceProvider
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly DatabaseSeeder _databaseSeeder;


        // Job configuration properties
        public const string JobName = nameof(AutoCheckoutJob);

        public AutoCheckoutJob(
            ILogger<AutoCheckoutJob> logger,
            IServiceScopeFactory serviceScopeFactory,
            DatabaseSeeder databaseSeeder)
        {
            _logger = logger;
            _serviceScopeFactory = serviceScopeFactory;
            _databaseSeeder = databaseSeeder;
        }

        /// <summary>
        /// Ensures the job exists in the database with proper configuration
        /// </summary>
        public async Task EnsureJobExistsAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Checking if {JobName} exists in database", JobName);

            // Create a separate scope to handle the initialization
            using var scope = _serviceScopeFactory.CreateScope();
            var batchJobRepository = scope.ServiceProvider.GetRequiredService<IBatchJobRepository>();

            // Check if job exists, if not, seed it to database
            var job = await batchJobRepository.GetJobByNameAsync(JobName);

            if (job == null)
            {
                await _databaseSeeder.SeedCheckOutJobAsync();
            }
        }

        public override async Task ExecuteAsync()
        {
            LogInformation("Starting auto checkout job execution");
            DateTime startTime = DateTime.UtcNow;

            // Create a new scope using the factory which won't be disposed
            using var scope = _serviceScopeFactory.CreateScope();
            var batchJobRepository = scope.ServiceProvider.GetRequiredService<IBatchJobRepository>();
            var appDbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            BatchJobHistory jobHistory = new BatchJobHistory
            {
                FiredId = Guid.NewGuid(),
                JobId = JobConfiguration.JobId,
                StartedAt = startTime,
                Status = JobExecutionStatus.InProgress
            };

            try
            {
                // Create initial job history record
                await batchJobRepository.CreateJobHistoryAsync(jobHistory);

                // Check if job is enabled
                if (!JobConfiguration.IsEnabled)
                {
                    LogInformation("Job is disabled, skipping execution");
                    jobHistory.Status = JobExecutionStatus.Cancelled;
                    jobHistory.ExecutionLog = GetExecutionLog();
                    jobHistory.CompletedAt = DateTime.UtcNow;
                    await batchJobRepository.UpdateJobHistoryAsync(jobHistory);
                    return;
                }

                // Calculate the cutoff time - 1 hour ago in UTC
                var oneHourAgo = DateTime.Now.AddHours(-1);

                var shiftsToUpdate = await appDbContext.EmployeeShifts
                    .Where(es =>
                        es.ScheduledStartTime.HasValue &&
                        es.ScheduledEndTime.HasValue &&
                        es.ScheduledEndTime < oneHourAgo &&
                        !es.CheckOutTime.HasValue &&
                        !es.AutoCheckOutTime.HasValue)
                    .ToListAsync(CancellationToken);

                LogInformation($"Found {shiftsToUpdate.Count} employee shifts that need auto checkout");

                // Update each shift to set the AutoCheckOutTime equal to their ScheduledEndTime
                foreach (var shift in shiftsToUpdate)
                {
                    shift.AutoCheckOutTime = shift.ScheduledEndTime;
                    LogInformation($"Auto checking out employee shift {shift.EmployeeShiftUid} with scheduled end time {shift.ScheduledEndTime}");
                }

                // Save changes to the database
                int updatedCount = await appDbContext.SaveChangesAsync(CancellationToken);

                LogInformation($"Successfully auto checked out {updatedCount} employee shifts");

                // Update job execution status
                jobHistory.Status = JobExecutionStatus.Success;
            }
            catch (OperationCanceledException)
            {
                LogWarning("Auto checkout job was cancelled");
                jobHistory.Status = JobExecutionStatus.Cancelled;
                jobHistory.ErrorMessage = "Job was cancelled";
            }
            catch (Exception ex)
            {
                LogError($"Error executing auto checkout job: {ex.Message}", ex);
                jobHistory.Status = JobExecutionStatus.Failed;
                jobHistory.ErrorMessage = ex.Message;
            }
            finally
            {
                // Record job history
                jobHistory.CompletedAt = DateTime.UtcNow;
                jobHistory.ExecutionLog = GetExecutionLog();
                await batchJobRepository.UpdateJobHistoryAsync(jobHistory);

                // Update the job's LastRunAt time
                await batchJobRepository.UpdateJobLastRunTimeAsync(JobConfiguration.JobId, DateTime.UtcNow);
            }
        }
    }
}
