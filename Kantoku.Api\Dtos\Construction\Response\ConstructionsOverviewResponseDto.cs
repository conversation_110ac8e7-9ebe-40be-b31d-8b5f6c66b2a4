namespace Kantoku.Api.Dtos.Construction.Response;

public class ConstructionOverviewResponseDto
{
    /// <summary>
    /// Project Id
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// Project Name
    /// </summary>
    public string? ProjectName { get; set; }

    /// <summary>
    /// Contractor Name
    /// </summary>
    public string? ContractorName { get; set; }

    /// <summary>
    /// Customer Name
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// Contractual Start Date
    /// </summary>
    public string? ContractualStartDate { get; set; }

    /// <summary>
    /// Contractual End Date
    /// </summary>
    public string? ContractualEndDate { get; set; }

    /// <summary>
    /// Contractual Cost
    /// </summary>
    public ConstructionsContractCostResponseDto ContractCosts { get; set; } = new();

    /// <summary>
    /// Construction List
    /// </summary>
    public ConstructionsEstimateCostResponseDto EstimateCosts { get; set; } = new();

    /// <summary>
    /// Construction List
    /// </summary>
    public ConstructionsAccumulatedCostResponseDto AccumulatedCosts { get; set; } = new();
}

