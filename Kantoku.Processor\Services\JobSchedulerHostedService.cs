using Kantoku.Processor.Services.Interfaces;

namespace Kantoku.Processor.Services
{
    /// <summary>
    /// Hosted service to manage the job scheduler
    /// This class creates a scope for the scheduler to operate in
    /// </summary>
    public class JobSchedulerHostedService : IHostedService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<JobSchedulerHostedService> _logger;
        private IServiceScope? _scope;
        private IJobSchedulerService? _jobScheduler;

        public JobSchedulerHostedService(
            IServiceProvider serviceProvider,
            ILogger<JobSchedulerHostedService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting Job Scheduler Hosted Service");
            
            // Create a scope for the scheduler to operate in
            _scope = _serviceProvider.CreateScope();
            
            // Get the job scheduler from the scope
            _jobScheduler = _scope.ServiceProvider.GetRequiredService<IJobSchedulerService>();
            
            // Start the scheduler
            await _jobScheduler.StartAsync(cancellationToken);
            
            _logger.LogInformation("Job Scheduler Hosted Service started");
        }

        public async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Stopping Job Scheduler Hosted Service");
            
            if (_jobScheduler != null)
            {
                await _jobScheduler.StopAsync(cancellationToken);
            }
            
            // Dispose the scope
            _scope?.Dispose();
            
            _logger.LogInformation("Job Scheduler Hosted Service stopped");
        }
    }
} 