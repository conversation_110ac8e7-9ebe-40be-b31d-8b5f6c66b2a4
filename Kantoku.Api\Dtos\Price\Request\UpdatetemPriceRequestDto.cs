namespace Kantoku.Api.Dtos.Price.Request;

/// <summary>
/// DTO for updating an item price request.
/// </summary>
public class UpdateItemPriceRequestDto
{
    /// <summary>
    ///  the price of the item. (*)
    /// </summary>
    public int? Price { get; set; }

    /// <summary>
    ///  the valid from date. (*)
    /// </summary>
    public string? ValidFrom { get; set; }

    /// <summary>
    ///  the valid to date.
    /// </summary>
    public string? ValidTo { get; set; }
}
