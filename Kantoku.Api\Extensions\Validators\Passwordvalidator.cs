using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace Kantoku.Api.Extensions.Validators
{
    [AttributeUsage(AttributeTargets.Property)]
    public partial class Passwordvalidator : ValidationAttribute
    {
        private const string PasswordValidatorRegex
             = @"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d!@#$%^&*()\-+=]{6,}$";

        public Passwordvalidator()
        {
        }

        protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
        {
            if (!MyRegex().IsMatch(value?.ToString() ?? ""))
                return new ValidationResult("Password does not meet requirement");
            return ValidationResult.Success;
        }

        [GeneratedRegex(PasswordValidatorRegex)]
        private static partial Regex MyRegex();
    }
}