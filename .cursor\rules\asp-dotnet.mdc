---
description: most code generation
globs: *.cs
alwaysApply: false
---

# ASP .NET Development Rules

You are a senior .NET backend developer and an expert in C#, ASP.NET Core, and Entity Framework Core.

## Code Style and Structure
- Write concise, idiomatic C# code with accurate examples.
- Use object-oriented and functional programming patterns as appropriate.
- Prefer LINQ and lambda expressions for collection operations.
- Use descriptive variable and method names (e.g., `IsUserSignedIn`, `CalculateTotal`).
- Adhere to modular development approach to separate concerns between layers (Application, Domain, Infrastructure, etc.).

## Naming Conventions
- Use PascalCase for class names, method names, and public members.
- Use camelCase for local variables and private fields.
- Use UPPERCASE for constants.
- Prefix interface names with "I" (e.g., `IUserService`).

## C# and .NET Usage
- Use C# 10+ features when appropriate (e.g., record types, pattern matching, null-coalescing assignment).
- Leverage built-in ASP.NET Core features and middleware, as well as modules and features (e.g., Permission Management, Setting Management).
- Use Entity Framework Core effectively for database operations, integrating with `DbContext` and repository abstractions.

## Syntax and Formatting
- Follow the C# Coding Conventions (https://docs.microsoft.com/en-us/dotnet/csharp/fundamentals/coding-style/coding-conventions).
- Use C#’s expressive syntax (e.g., null-conditional operators, string interpolation).
- Use `var` for implicit typing when the type is obvious.
- Keep code clean and consistent, utilizing built-in formatting guidelines when applicable.

## Error Handling and Validation
- Use exceptions for exceptional cases, not for control flow.
- Implement proper error logging using SeriLog.
- Use Data Annotations or Fluent Validation for model validation within the application layer.
- Leverage global exception handling middleware for unified error responses.
- Return appropriate HTTP status codes and consistent error responses in your `HttpApi` controllers.

## API Design
- Follow RESTful API design principles.
- Use conventional HTTP API controllers and attribute-based routing.
- Integrate versioning strategies in your APIs if multiple versions are expected.
- Utilize action filters or middleware for cross-cutting concerns (e.g., auditing).

## Performance Optimization
- Use asynchronous programming with `async/await` for I/O-bound operations.
- Always use `IDistributedCache` for caching strategies (instead of `IMemoryCache`), in line with caching abstractions.
- Use efficient LINQ queries and avoid N+1 query problems by including related entities when needed.
- Implement pagination or `PagedResultDto` for large data sets in your application service methods.

## Key Conventions
- Use Dependency Injection (DI) system for loose coupling and testability.
- Implement or leverage repository pattern or use Entity Framework Core directly, depending on complexity.
- Do manual object mapping instead of third-party mapper lib.
- Implement background tasks using background job system or `IHostedService`/`BackgroundService` where appropriate.
- Follow recommended approach for domain events and entities (e.g., using `AuditedAggregateRoot`, `FullAuditedEntity`).
- Keep business rules in the **Domain layer**. Prefer placing them within the entity itself; if not possible, use a `DomainService`.
- Before adding a new package to the application, check if an existing package can fulfill the requirement to avoid unnecessary dependencies.
- Do not alter the dependencies between application layers (Application, Domain, Infrastructure, etc.)- @Do  
- ices  
- @Entities Best Practices

**Applicplicaa Transfer Objects Best Practices

**Entitegration  
- @MongoDB Integrarefer to the @EventHub repository for various examples and best practices beyond testing.

## Testing
- Use xUnit for testing.
- Write unit tests using xUnit (or another supported framework), integrating with built-in test module if available.
- Use NSubstitute (or a similar library) for mocking dependencies.
- Implement integration tests for your modules (e.g., `Application.Tests`, `Domain.Tests`), leveraging test base classes.

## Security
- Use built-in openiddict for authentication and authorization.
- Implement proper permission checks using permission management infrastructure.
- Use HTTPS and enforce SSL.
- Configure CORS policies according to your application's deployment needs.

## API Documentation
- Use Swagger/OpenAPI for API documentation, leveraging built-in support (Swashbuckle.AspNetCore or NSwag).
- Provide XML comments for controllers and DTOs to enhance Swagger documentation.
- Follow guidelines to document your modules and application services.