namespace Kantoku.Api.Utils.Constants
{
    public class StorageConstant
    {
        public static string DefaultBucket() => "kantoku";

        public static string UserInfo(string accountId) => $"user_info/{accountId}/";
        public static string UserAvatar(string accountId) => $"{UserInfo(accountId)}avatar/";

        public static string OrgLogo() => "logo/";
        public static string VendorLogo(string vendorId) => $"vendor/{vendorId}/logo/";
        public static string OutSourceLogo(string outSourceId) => $"outsource/{outSourceId}/logo/";
        public static string ManufacturerLogo(string manufacturerId) => $"manufacturer/{manufacturerId}/logo/";
        public static string ContractorLogo(string contractorId) => $"contractor/{contractorId}/logo/";
        public static string CustomerLogo(string customerId) => $"customer/{customerId}/logo/";

        public static string ItemImage(string itemId) => $"item/{itemId}/image/";
        public static string ItemImage(string itemId, string imageId) => $"{ItemImage(itemId)}{imageId}/";
        public static string InvoiceImage(string invoiceId) => $"invoice/{invoiceId}/image/";
        public static string InvoiceImage(string invoiceId, string invoiceNumber, DateTime uploadTime) => $"{InvoiceImage(invoiceId)}{invoiceNumber}/{uploadTime:yyyyMMddHHmmss}/";
    }
}

