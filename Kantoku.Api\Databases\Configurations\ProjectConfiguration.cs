using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class ProjectConfiguration(string schema) : IEntityTypeConfiguration<Project>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Project> builder)
    {
        builder.HasKey(e => e.ProjectUid).HasName("project_pkey");

        builder.ToTable("project", Schema);

        builder.Property(e => e.ProjectUid)
            .HasColumnName("project_uid");
        builder.Property(e => e.ProjectCode)
            .HasColumnName("project_code");
        builder.Property(e => e.ProjectName)
            .HasColumnName("project_name");
        builder.Property(e => e.CustomerUid)
            .HasColumnName("customer_uid");
        builder.Property(e => e.ProjectTypeUid)
            .HasColumnName("project_type_uid");
        builder.Property(e => e.ContractorUid)
            .HasColumnName("contractor_uid");
        builder.Property(e => e.Address)
            .HasColumnName("address");
        builder.Property(e => e.IsOffice)
            .HasColumnName("is_office");
        builder.Property(e => e.IsDefault)
            .HasColumnName("is_default");
        builder.Property(e => e.MonthlyReportDate)
            .HasColumnName("monthly_report_date");
        builder.Property(e => e.ActualBudget)
            .HasColumnName("actual_budget");
        builder.Property(e => e.ActualEndDate)
            .HasColumnName("actual_end_date");
        builder.Property(e => e.ActualStartDate)
            .HasColumnName("actual_start_date");
        builder.Property(e => e.ExpectedEndDate)
            .HasColumnName("expected_end_date");
        builder.Property(e => e.ExpectedStartDate)
            .HasColumnName("expected_start_date");
        builder.Property(e => e.InitialBudget)
            .HasColumnName("initial_budget");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.StatusCode)
            .HasColumnName("status_code");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.Description)
            .HasColumnName("description");

        builder.HasOne(d => d.Org).WithMany(p => p.Projects)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("project_org_id_fkey");

        builder.HasOne(d => d.ProjectType).WithMany(p => p.Projects)
            .HasForeignKey(d => d.ProjectTypeUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("project_project_type_id_fkey");

        builder.HasOne(d => d.Contractor).WithMany(p => p.Projects)
            .HasForeignKey(d => d.ContractorUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("project_contractor_id_fkey");

        builder.HasOne(d => d.Customer).WithMany(p => p.Projects)
            .HasForeignKey(d => d.CustomerUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("project_customer_id_fkey");

        builder.HasOne(d => d.Status).WithMany(p => p.Projects)
            .HasForeignKey(d => d.StatusCode)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("project_status_code_fkey");
    }
}
