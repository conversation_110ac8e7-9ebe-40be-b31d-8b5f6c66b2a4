using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using File = Kantoku.Api.Databases.Models.File;

namespace Kantoku.Api.Databases.Configurations;

public class FileConfiguration(string schema) : IEntityTypeConfiguration<File>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<File> builder)
    {
        builder.HasKey(e => e.FileUid).HasName("file_pkey");

        builder.ToTable("file", Schema, tb => tb.HasComment("Thông tin file lưu trữ"));

        builder.HasIndex(e => e.FileUrl, "idx_file_file_url").IsUnique();

        builder.Property(e => e.FileUid)
            .HasColumnName("file_uid");
        builder.Property(e => e.FileName)
            .HasColumnName("file_name");
        builder.Property(e => e.FileSize)
            .HasColumnName("file_size");
        builder.Property(e => e.FileType)
            .HasColumnName("file_type");
        builder.Property(e => e.FileUrl)
            .HasColumnName("file_url");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.FileMetadata)
            .HasColumnName("file_metadata")
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<Dictionary<string, string?>>(v)
            ).Metadata.SetValueComparer(
                new ValueComparer<Dictionary<string, string?>>(
                    (c1, c2) => (c1 == null && c2 == null) || (c1 != null && c2 != null && c1.SequenceEqual(c2)),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToDictionary(k => k.Key, v => v.Value)
                )
            );
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
    }
}
