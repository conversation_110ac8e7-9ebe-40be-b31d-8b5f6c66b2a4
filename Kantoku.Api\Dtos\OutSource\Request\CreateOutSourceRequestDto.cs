using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Databases.Models;

namespace Kantoku.Api.Dtos.OutSource.Request;

/// <summary>
/// Data transfer object for creating a new outsource
/// </summary>
public class CreateOutSourceRequestDto
{
    /// <summary>
    /// The unique code identifier for the outsource (*)
    /// </summary>
    [Required]
    public string OutSourceCode { get; set; } = null!;

    /// <summary>
    /// The name of the outsource (*)
    /// </summary>
    [Required]
    public string OutSourceName { get; set; } = null!;

    /// <summary>   
    /// Optional description of the outsource
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Collection of expertise areas associated with the outsource (optional)
    /// </summary>
    public ICollection<string>? Expertise { get; set; }

    /// <summary>
    /// The corporate number of the outsource
    /// </summary>
    public string? CorporateNumber { get; set; }

    /// <summary>
    /// The address of the outsource
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// The phone number of the outsource
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// The email of the outsource
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// The contact person of the outsource
    /// </summary>
    public ContactPerson? ContactPerson { get; set; }

    /// <summary>
    /// The price of the outsource
    /// </summary>
    public OutSourcePriceRequestDto? Price { get; set; }

    /// <summary>
    /// The logo of the outsource
    /// </summary>
    public IFormFile? Logo { get; set; }
}
