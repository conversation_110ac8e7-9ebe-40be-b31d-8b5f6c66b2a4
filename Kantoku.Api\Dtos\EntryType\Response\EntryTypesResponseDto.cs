﻿namespace Kantoku.Api.Dtos.EntryType.Response;

/// <summary>
/// Response DTO containing paginated entry type data
/// </summary>
public class EntryTypesResponseDto
{
    /// <summary>
    /// Collection of entry type details (*)
    /// </summary>
    public IEnumerable<EntryTypeResponseDto> EntryTypes { get; set; } = [];

    /// <summary>
    /// Current page number (*)
    /// </summary>
    public int PageNum { get; set; }

    /// <summary>
    /// Number of records per page (*)
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of records available (*)
    /// </summary>
    public int TotalRecords { get; set; }
}
