using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class EmployeeCostQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedEmployee { get; set; } = false;
}

public interface IEmployeeCostQueryable
{
    IQueryable<EmployeeCost> GetEmployeeCostsQuery(
        EmployeeCostQueryableOptions options
    );
    IQueryable<EmployeeCost> GetEmployeeCostsQueryIncluded(
        EmployeeCostQueryableOptions options,
        IQueryable<EmployeeCost>? query = null
    );
    IQueryable<EmployeeCost> GetEmployeeCostsQueryFiltered(
        EmployeeCostFilter filter,
        EmployeeCostQueryableOptions options,
        IQueryable<EmployeeCost>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class EmployeeCostQueryable(PostgreDbContext context) :
    BaseQueryable<EmployeeCost>(context), IEmployeeCostQueryable
{
    public IQueryable<EmployeeCost> GetEmployeeCostsQuery(
        EmployeeCostQueryableOptions options
    )
    {
        var query = GetQuery(options);
        return query;
    }

    public IQueryable<EmployeeCost> GetEmployeeCostsQueryIncluded(
        EmployeeCostQueryableOptions options,
        IQueryable<EmployeeCost>? query = null
    )
    {
        query ??= GetEmployeeCostsQuery(options);
        if (options.IncludedEmployee)
        {
            query = query.Include(r => r.Employee);
        }
        return query;
    }

    public IQueryable<EmployeeCost> GetEmployeeCostsQueryFiltered(
        EmployeeCostFilter filter,
        EmployeeCostQueryableOptions options,
        IQueryable<EmployeeCost>? query = null
    )
    {
        query ??= GetEmployeeCostsQueryIncluded(options);
        if (filter.Keyword is not null)
        {
            query = query.Where(r => r.Employee.EmployeeName.Contains(filter.Keyword));
        }
        if (filter.DateFrom is not null)
        {
            query = query.Where(r => r.EffectiveDate >= DateOnly.Parse(filter.DateFrom));
        }
        if (filter.DateTo is not null)
        {
            query = query.Where(r => r.EffectiveDate <= DateOnly.Parse(filter.DateTo));
        }
        return query;
    }
}