﻿using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.EntryType.Request;

/// <summary>
/// Data transfer object for creating a new entry type
/// </summary>
public class CreateEntryTypeRequestDto
{
    /// <summary>
    /// Name of the entry type (*)
    /// </summary>
    [Required]
    public string EntryTypeName { get; set; } = null!;

    /// <summary>
    /// Optional description of the entry type
    /// </summary>
    public string? Description { get; set; }
}
