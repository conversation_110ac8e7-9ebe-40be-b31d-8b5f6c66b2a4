# Employee Break-in Flow Documentation

This document describes the business flow for employee break-in functionality.

## API Endpoints

### Break-in
- **URL**: `/attendance/{shiftId}/break-in`
- **Method**: PUT
- **Consumes**: application/json
- **Produces**: application/json
- **Path Parameters**:
  - `shiftId` (GUID): Unique identifier of the shift

## Request Payload

```json
{
    "deviceId": string        // Unique identifier of the device used for break-in
}
```

## Business Flow

```mermaid
graph TD
    A[Employee] -->|Initiates Break-in| B[Validate Shift]
    
    B -->|Shift not found| C[Return 404: Shift not found]
    B -->|Shift found| D{Checked in?}
    
    D -->|No| E[Return 400: Not checked in yet]
    D -->|Yes| F{Already on break?}
    
    F -->|Yes| G[Return 400: Already in a break]
    F -->|No| H{Checked out?}
    
    H -->|Yes| I[Return 400: Already checked out]
    H -->|No| J[Process Break-in]
    
    J -->|Update| K[Set Break-in Time]
    K -->|DB Operation| L[Return Break Details]
```

## Data Processing Steps

1. **Input Validation**
   - Verify shift exists
   - Verify deviceId format

2. **State Validation**
   - Verify shift is checked in
   - Verify shift is not checked out
   - Verify not already in a break
   - Verify break time limit not exceeded

3. **Time Processing**
   - Record break-in time in UTC
   - Convert to project's timezone for display

## Database Operations

### Shifts Table Update

```sql
UPDATE employee_shift
SET 
    break_in_time = @currentUtcTime,
    device_id = @deviceId,
    last_modified_at = @currentUtcTime,
    last_modified_by = @employeeId
WHERE employee_shift_uid = @shiftId
```

## Response

### Success Response (200 OK)
```json
{
    "shiftId": "guid",
    "employeeId": "guid",
    "projectId": "guid",
    "breakInTime": "ISO8601 DateTime",
    "deviceId": "string"
}
```

### Error Responses

#### 400 Bad Request
```json
{
    "errorCode": "SFT10",
    "message": "Already In A Break",
    "status": 400
}
```
```json
{
    "errorCode": "SFT11",
    "message": "Not Checked In Yet",
    "status": 400
}
```
```json
{
    "errorCode": "SFT18",
    "message": "Already Checked Out",
    "status": 400
}
```
```json
{
    "errorCode": "WOS06",
    "message": "Work Shift Break Time Invalid",
    "status": 400
}
```

#### 404 Not Found
```json
{
    "errorCode": "SFT01",
    "message": "Shift Not Exist",
    "status": 404
}
```

#### 500 Internal Server Error
```json
{
    "errorCode": "SFT07",
    "message": "Break In Failed",
    "status": 500
}
```
```json
{
    "errorCode": "KAN500",
    "message": "Unknown Error",
    "status": 500
}
``` 