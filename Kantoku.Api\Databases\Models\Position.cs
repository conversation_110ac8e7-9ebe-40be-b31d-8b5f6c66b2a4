﻿using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Models;

public class Position : AuditableEntity
{
    public Guid PositionUid { get; set; }

    [AuditProperty]
    public string PositionCode { get; set; } = null!;

    [AuditProperty]
    public string PositionName { get; set; } = null!;

    [AuditProperty]
    public string? Description { get; set; }

    public bool IsDeleted { get; set; } = false;
    public Guid OrgUid { get; set; }

    public virtual ICollection<Employee> Employees { get; set; } = [];

    public virtual Org Org { get; set; } = null!;
}
