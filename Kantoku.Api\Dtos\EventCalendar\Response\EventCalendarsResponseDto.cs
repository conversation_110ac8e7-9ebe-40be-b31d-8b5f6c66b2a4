namespace Kantoku.Api.Dtos.EventCalendar.Response;

public class EventCalendarsResponseDto
{
    public IEnumerable<EventCalendarDto> EventCalendars { get; set; } = [];
}

public class EventCalendarDto
{
    /// <summary>
    /// Event id
    /// </summary>
    public string? EventId { get; set; }

    /// <summary>
    /// Event name
    /// </summary>
    public string? EventName { get; set; }

    /// <summary>
    /// Apply date (YYYY-MM-DD)   
    /// </summary>
    public string? ApplyDate { get; set; }

    /// <summary>
    /// Event start time (HH:MM:SS)
    /// </summary>
    public string? EventStartTime { get; set; }

    /// <summary>
    /// Event end time (HH:MM:SS)
    /// </summary>
    public string? EventEndTime { get; set; }

    /// <summary>
    /// Day of week
    /// </summary>
    public string? DayOfWeek { get; set; }

    /// <summary>   
    /// Week of month
    /// </summary>
    public string? WeekOfMonth { get; set; }

    /// <summary>
    /// Event description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// true: day off, false: not day off
    /// </summary>
    public bool IsDayOff { get; set; }
}
