using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Utils.Attributes.Property;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class AuditLogMapper
{
    #region Entity to DTO mappings
    private static readonly JsonSerializerSettings jsonSerializerSettings = new()
    {
        TypeNameHandling = TypeNameHandling.Auto,
        NullValueHandling = NullValueHandling.Ignore,
        ContractResolver = new DefaultContractResolver
        {
            NamingStrategy = new CamelCaseNamingStrategy
            {
                ProcessDictionaryKeys = true,
            }
        }
    };

    /// <summary>
    /// Maps an AuditLog entity to an EntityChangeResponseDto
    /// </summary>
    public static EntityChangeResponseDto ToEntityChangeResponseDto(this AuditLog auditLog)
    {
        if (auditLog == null)
            return new EntityChangeResponseDto();

        return new EntityChangeResponseDto
        {
            AuditLogId = auditLog.AuditLogUid.ToString(),
            EntityId = auditLog.EntityId,
            Action = auditLog.Action.ToUpper(),
            ChangesList = GetValueChanges(auditLog),
            ModifiedTime = auditLog.Timestamp,
            ModifiedAccountId = auditLog.AccountUid?.ToString(),
            ModifiedAccountName = auditLog.Account?.UserInfo?.Name
        };
    }

    /// <summary>
    /// Maps a collection of AuditLog entities to an AuditLogResponseDto
    /// </summary>
    public static AuditLogResponseDto ToAuditLogResponseDto(
        this IEnumerable<AuditLog> auditLogs,
        int pageNum,
        int pageSize,
        int totalRecords)
    {
        if (auditLogs == null || !auditLogs.Any())
            return new AuditLogResponseDto
            {
                PageNum = pageNum,
                PageSize = pageSize,
                TotalRecords = 0,
                EntityChanges = Enumerable.Empty<EntityChangeResponseDto>()
            };

        return new AuditLogResponseDto
        {
            EntityChanges = auditLogs.Select(log => log.ToEntityChangeResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    /// <summary>
    /// Maps a collection of AuditLog entities to an AuditLogResponseDto with typed value changes
    /// </summary>
    public static AuditLogResponseDto ToAuditLogResponseDto<T>(
        this IEnumerable<AuditLog> auditLogs,
        int pageNum,
        int pageSize,
        int totalRecords)
    {
        if (auditLogs == null || !auditLogs.Any())
            return new AuditLogResponseDto
            {
                PageNum = pageNum,
                PageSize = pageSize,
                TotalRecords = 0,
                EntityChanges = Enumerable.Empty<EntityChangeResponseDto>()
            };

        var entityChanges = auditLogs.Select(log => new EntityChangeResponseDto
        {
            AuditLogId = log.AuditLogUid.ToString(),
            EntityId = log.EntityId,
            Action = log.Action.ToUpper(),
            ChangesList = GetValueChanges<T>(log),
            ModifiedTime = log.Timestamp,
            ModifiedAccountId = log.AccountUid?.ToString(),
            ModifiedAccountName = log.Account?.UserInfo?.Name
        });

        return new AuditLogResponseDto
        {
            EntityChanges = entityChanges,
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    /// <summary>
    /// Gets the value changes from an AuditLog entity
    /// </summary>
    private static IEnumerable<ValueChanges> GetValueChanges(AuditLog log)
    {
        try
        {
            var oldValues = JsonConvert.DeserializeObject<Dictionary<string, object>>(log.OldValues, jsonSerializerSettings) ?? new Dictionary<string, object>();
            var newValues = JsonConvert.DeserializeObject<Dictionary<string, object>>(log.NewValues, jsonSerializerSettings) ?? new Dictionary<string, object>();

            var result = new List<ValueChanges>();
            var allKeys = oldValues.Keys.Union(newValues.Keys).Distinct();

            foreach (var key in allKeys)
            {
                object? oldValue = oldValues.ContainsKey(key) ? oldValues[key] : null;
                object? newValue = newValues.ContainsKey(key) ? newValues[key] : null;

                if ((oldValue == null && newValue == null) ||
                    (oldValue != null && newValue != null && oldValue.Equals(newValue)))
                    continue;

                result.Add(new ValueChanges
                {
                    FieldName = key,
                    ValueBefore = oldValue,
                    ValueAfter = newValue
                });
            }

            return result;
        }
        catch
        {
            return Enumerable.Empty<ValueChanges>();
        }
    }

    /// <summary>
    /// Gets the typed value changes from an AuditLog entity
    /// </summary>
    private static IEnumerable<ValueChanges> GetValueChanges<T>(AuditLog log)
    {
        try
        {
            var oldObject = JsonConvert.DeserializeObject<T>(log.OldValues, jsonSerializerSettings);
            var newObject = JsonConvert.DeserializeObject<T>(log.NewValues, jsonSerializerSettings);

            var auditProperties = typeof(T).GetProperties()
                .Where(p => p.GetCustomAttributes(typeof(AuditPropertyAttribute), true).Any())
                .Select(p => p.Name)
                .ToList();

            var oldValuesDict = oldObject?.GetType().GetProperties()
                .Where(p => auditProperties.Contains(p.Name))
                .ToDictionary(p => p.Name, p => p.GetValue(oldObject)) ?? new Dictionary<string, object?>();

            var newValuesDict = newObject?.GetType().GetProperties()
                .Where(p => auditProperties.Contains(p.Name))
                .ToDictionary(p => p.Name, p => p.GetValue(newObject)) ?? new Dictionary<string, object?>();

            var result = new List<ValueChanges>();
            var allKeys = oldValuesDict.Keys.Union(newValuesDict.Keys).Distinct();

            foreach (var key in allKeys)
            {
                object? oldValue = oldValuesDict.ContainsKey(key) ? oldValuesDict[key] : null;
                object? newValue = newValuesDict.ContainsKey(key) ? newValuesDict[key] : null;

                if ((oldValue == null && newValue == null) || 
                    (oldValue != null && newValue != null && oldValue.Equals(newValue)))
                    continue;

                result.Add(new ValueChanges
                {
                    FieldName = key,
                    ValueBefore = oldValue,
                    ValueAfter = newValue
                });
            }

            return result;
        }
        catch
        {
            return Enumerable.Empty<ValueChanges>();
        }
    }
    #endregion

    // No DTO to Entity mapping methods needed for AuditLog since it's system-generated
} 