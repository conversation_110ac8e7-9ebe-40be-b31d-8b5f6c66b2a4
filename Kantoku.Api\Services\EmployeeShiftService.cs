using EventBus.Events;
using EventBus.Interfaces;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.EmployeeShift.Request;
using Kantoku.Api.Dtos.EmployeeShift.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Helpers;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public interface IEmployeeShiftService
{
    Task<ResultDto<ShiftsResponseDto>> GetEmployeeShiftByDate(DateOnly fromDate, DateOnly toDate, Guid? employeeUid = null);
    Task<ResultDto<ShiftResponseDto>> GetEmployeeShiftByid(Guid employeeShiftId);
    Task<ResultDto<WorksiteShiftsResponseDto>> GetEmpShiftByProject(Guid projectId, DateOnly fromDate, DateOnly toDate);
    Task<ResultDto<WorksitesShiftsResponseDto>> GetEmpShiftByProjects(DateOnly fromDate, DateOnly toDate);

    Task<ResultDto<ShiftResponseDto>> Checkin(UnscheduledCheckInRequestDto dto);
    Task<ResultDto<ShiftResponseDto>> Checkin(Guid employeeShiftId, CheckInOutRequestDto dto);
    Task<ResultDto<ShiftResponseDto>> CheckOut(Guid employeeShiftId, CheckInOutRequestDto dto);
    Task<ResultDto<ShiftResponseDto>> BreakIn(Guid employeeShiftId);
    Task<ResultDto<ShiftResponseDto>> BreakOut(Guid employeeShiftId);
    Task<ResultDto<ShiftResponseDto>> AddShift(CreateShiftRequestDto dto);
    Task<ResultDto<ShiftResponseDto>> UpdateShift(Guid employeeShiftId, UpdateShiftRequestDto dto);
    Task<ResultDto<bool>> DeleteShift(Guid employeeShiftId);
    Task<ResultDto<ShiftResponseDto>> RequestDailyApproval(Guid employeeShiftId);
    Task<ResultDto<ShiftResponseDto>> ApproveEmployeeShift(Guid employeeShiftId, CreateDailyApprovalRequestDto dto);
    Task<ResultDto<IEnumerable<Guid>>> CreateMultipleShifts(CreateMultipleShiftRequestDto dto);
    Task<ResultDto<bool>> UpdateMultipleShifts(UpdateMultipleShiftRequestDto dto);
    Task<ResultDto<bool>> SynchronizeOfflineCheckin(SynchronizeShiftRequestDto dto);
}

[Service(ServiceLifetime.Scoped)]
public class EmployeeShiftService : BaseService<EmployeeShiftService>, IEmployeeShiftService
{
    private readonly IEmployeeShiftRepository employeeShiftRepository;
    private readonly IProjectRepository projectRepository;
    private readonly IRequestRepository requestRepository;
    private readonly IPartnerService partnerService;
    private readonly IMonthlyReportRepository monthlyReportRepository;
    private readonly IEmployeeRepository employeeRepository;
    private readonly IEventBus eventBus;
    private readonly EmployeeShiftQueryableOptions options = new()
    {
        IncludedEmployee = true,
        IncludedApprover = true,
        IncludedProject = true,
    };

    public EmployeeShiftService(
        IEmployeeShiftRepository employeeShiftRepository,
        IPartnerService partnerService,
        IRequestRepository requestRepository,
        IProjectRepository projectRepository,
        IMonthlyReportRepository monthlyReportRepository,
        IEmployeeRepository employeeRepository,
        IEventBus eventBus,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor) : base(logger, httpContextAccessor)
    {
        this.employeeShiftRepository = employeeShiftRepository;
        this.partnerService = partnerService;
        this.requestRepository = requestRepository;
        this.projectRepository = projectRepository;
        this.monthlyReportRepository = monthlyReportRepository;
        this.employeeRepository = employeeRepository;
        this.eventBus = eventBus;
    }

    public async Task<ResultDto<ShiftResponseDto>> Checkin(UnscheduledCheckInRequestDto dto)
    {
        if (!GetCurrentEmployeeGuid(out var employeeUid))
        {
            logger.Error("Employee not found");
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        var employee = await employeeRepository.GetById(employeeUid, new EmployeeQueryableOptions());
        if (employee is null)
        {
            logger.Error("Employee not found with id {EmployeeId}", employeeUid);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.EMPLOYEE_NOT_EXIST);
        }

        var hasUnfinishedShift = await employeeShiftRepository.HasUnfinishedShift(employeeUid);
        if (hasUnfinishedShift)
        {
            logger.Error("Employee {EmployeeUid} has unfinished shift", employeeUid);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.ALREADY_CHECKED_IN);
        }
        var currentMonthlyReport = await monthlyReportRepository.GetCurrentByEmployeeId(
            employeeUid, new MonthlyReportQueryableOptions());

        var newShift = dto.ToEntity(employee, currentMonthlyReport);
        if (newShift is null)
        {
            logger.Error("Failed to create shift for employee {EmployeeUid}", employeeUid);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.CHECKIN_FAILED);
        }

        // if (!string.IsNullOrEmpty(dto.Latitude) && !string.IsNullOrEmpty(dto.Longitude))
        // {
        //     var checkInLocation = await GeoDecode(dto.Latitude, dto.Longitude);
        //     newShift.CheckInLocation = checkInLocation;
        // }

        var createdShift = await employeeShiftRepository.Create(newShift, options);
        if (createdShift is null)
        {
            logger.Error("Failed to create shift for employee {EmployeeUid}", employeeUid);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.CHECKIN_FAILED);
        }
        if (dto.ProjectId is null)
        {
            await PublishCheckInEvent(createdShift.EmployeeShiftUid, dto.Latitude, dto.Longitude, true);
        }
        var result = createdShift.ToShiftResponseDto();
        return new SuccessResultDto<ShiftResponseDto>(result);
    }

    public async Task<ResultDto<ShiftResponseDto>> Checkin(Guid employeeShiftId, CheckInOutRequestDto dto)
    {
        var currentShift = await employeeShiftRepository.GetById(employeeShiftId, new EmployeeShiftQueryableOptions());
        if (currentShift is null)
        {
            logger.Error("Shift not found with id {ShiftId}", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.SHIFT_NOT_EXIST);
        }

        if (currentShift.CheckInTime is not null)
        {
            logger.Error("Shift already checked in with id {ShiftId}", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.ALREADY_CHECKED_IN);
        }

        // var checkInLocation = string.Empty;
        // if (!string.IsNullOrEmpty(dto.Latitude) && !string.IsNullOrEmpty(dto.Longitude))
        // {
        //     checkInLocation = await GeoDecode(dto.Latitude, dto.Longitude);
        // }
        // currentShift.CheckIn(checkInLocation);

        var updatedShift = await employeeShiftRepository.Update(currentShift, options);
        if (updatedShift is null)
        {
            logger.Error("Failed to update shift for employee {EmployeeUid}", currentShift.EmployeeUid);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.CHECKIN_FAILED);
        }

        var result = updatedShift.ToShiftResponseDto();
        return new SuccessResultDto<ShiftResponseDto>(result);
    }

    public async Task<ResultDto<ShiftResponseDto>> CheckOut(Guid employeeShiftId, CheckInOutRequestDto dto)
    {
        var currentShift = await employeeShiftRepository.GetById(employeeShiftId, new EmployeeShiftQueryableOptions());
        if (currentShift is null)
        {
            logger.Error("Shift not found with id {ShiftId}", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.SHIFT_NOT_EXIST);
        }

        if (currentShift.CheckInTime is null)
        {
            logger.Error("Shift not checked in with id {ShiftId}", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.NOT_CHECKIN_YET);
        }

        if (currentShift.CheckOutTime is not null)
        {
            logger.Error("Shift already checked out with id {ShiftId}", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.ALREADY_CHECKED_OUT);
        }
        // var checkOutLocation = string.Empty;
        // if (!string.IsNullOrEmpty(dto.Latitude) && !string.IsNullOrEmpty(dto.Longitude))
        // {
        //     checkOutLocation = await GeoDecode(dto.Latitude, dto.Longitude);
        // }
        currentShift.CheckOut();
        var updatedShift = await employeeShiftRepository.Update(currentShift, options);
        if (updatedShift is null)
        {
            logger.Error("Failed to update shift for shift {ShiftId}", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.CHECKOUT_FAILED);
        }
        if (updatedShift.ProjectUid is null)
        {
            await PublishCheckInEvent(updatedShift.EmployeeShiftUid, dto.Latitude, dto.Longitude, true);
        }
        await UpdateMonthlyReport(updatedShift.EmployeeUid, DateOnly.FromDateTime(updatedShift.CheckInTime!.Value));
        var result = updatedShift.ToShiftResponseDto();
        return new SuccessResultDto<ShiftResponseDto>(result);
    }

    public async Task<ResultDto<ShiftResponseDto>> BreakIn(Guid employeeShiftId)
    {
        var currentShift = await employeeShiftRepository.GetById(employeeShiftId, options);
        if (currentShift is null)
        {
            logger.Error("Shift not found with id {ShiftId}", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.SHIFT_NOT_EXIST);
        }

        if (currentShift.CheckInTime is null)
        {
            logger.Error("Shift not checked in with id {ShiftId}", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.NOT_CHECKIN_YET);
        }

        if (currentShift.CheckOutTime is not null)
        {
            logger.Error("Shift already checked out with id {ShiftId}", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.ALREADY_CHECKED_OUT);
        }

        currentShift.BreakIn();

        var updatedShift = await employeeShiftRepository.Update(currentShift, options);
        if (updatedShift is null)
        {
            logger.Error("Failed to update shift for shift {ShiftId}", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.BREAKIN_FAILED);
        }
        var result = updatedShift.ToShiftResponseDto();
        return new SuccessResultDto<ShiftResponseDto>(result);
    }

    public async Task<ResultDto<ShiftResponseDto>> BreakOut(Guid employeeShiftId)
    {
        var currentShift = await employeeShiftRepository.GetById(employeeShiftId, options);
        if (currentShift is null)
        {
            logger.Error("Shift not found with id {ShiftId}", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.SHIFT_NOT_EXIST);
        }

        if (currentShift.CheckInTime is null)
        {
            logger.Error("Shift not checked in or checked out with id {ShiftId}", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.NOT_CHECKIN_YET);
        }

        if (currentShift.CheckOutTime is not null)
        {
            logger.Error("Shift already checked out with id {ShiftId}", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.ALREADY_CHECKED_OUT);
        }

        currentShift.BreakOut();

        var updatedShift = await employeeShiftRepository.Update(currentShift, options);
        if (updatedShift is null)
        {
            logger.Error("Failed to update shift for shift {ShiftId}", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.BREAKOUT_FAILED);
        }
        var result = updatedShift.ToShiftResponseDto();
        return new SuccessResultDto<ShiftResponseDto>(result);
    }

    public async Task<ResultDto<bool>> DeleteShift(Guid employeeShiftId)
    {
        var currentShift = await employeeShiftRepository.GetById(employeeShiftId, new EmployeeShiftQueryableOptions());
        if (currentShift is null)
        {
            logger.Error("Shift not found with id {ShiftId}", employeeShiftId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.SHIFT_NOT_EXIST);
        }
        if (currentShift.IsApproved == true && currentShift.EmployeeUid == GetCurrentEmployeeUid())
        {
            logger.Error("User {employeeUid cannot delete approved shift {EmployeeShiftId}", GetCurrentEmployeeUid(), employeeShiftId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.CANNOT_UPDATE_APPROVED_SHIFT);
        }
        currentShift.IsDeleted = true;
        var isDeleted = await employeeShiftRepository.Update(currentShift);
        if (!isDeleted)
        {
            logger.Error("Failed to delete shift for shift {ShiftId}", employeeShiftId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.DELETE_SHIFT_FAILED);
        }
        return new SuccessResultDto<bool>(isDeleted);
    }

    public async Task<ResultDto<bool>> SynchronizeOfflineCheckin(SynchronizeShiftRequestDto dto)
    {
        var isCreated = await CreateMultipleShifts(dto.Items);
        var isUpdated = await UpdateMultipleShifts(dto.Items);
        return new SuccessResultDto<bool>(isCreated || isUpdated);
    }

    public async Task<ResultDto<ShiftsResponseDto>> GetEmployeeShiftByDate(DateOnly fromDate, DateOnly toDate, Guid? employeeUid = null)
    {
        employeeUid ??= GetCurrentEmployeeUid();
        var filter = new EmployeeShiftFilter
        {
            EmployeeId = employeeUid,
            TimeFrom = fromDate.ToDateTime(TimeOnly.MinValue).ToString(),
            TimeTo = toDate.ToDateTime(TimeOnly.MaxValue).ToString()
        };
        var shifts = await employeeShiftRepository.GetByFilter(filter, options);
        if (shifts is null || !shifts.Any())
        {
            logger.Error("No shifts found for employee {EmployeeUid} from {FromDate} to {ToDate}", employeeUid, fromDate, toDate);
            return new ErrorResultDto<ShiftsResponseDto>(ResponseCodeConstant.SHIFT_NOT_EXIST);
        }

        var (leaveRequest, _) = await requestRepository.GetByAuthor(employeeUid.Value, new RequestFilter
        {
            RequestTypeCodes = [RequestTypeConstants.LEAVE.ToString()],
            StatusCodes = [StatusConstants.APPROVED]
        }, new RequestQueryableOptions());

        var result = shifts.ToShiftsResponseDto(leaveRequest);
        return new SuccessResultDto<ShiftsResponseDto>(result);
    }

    public async Task<ResultDto<ShiftResponseDto>> GetEmployeeShiftByid(Guid employeeShiftId)
    {
        var shift = await employeeShiftRepository.GetById(employeeShiftId, options);
        if (shift is null)
        {
            logger.Error("Shift not found with id {ShiftId}", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.SHIFT_NOT_EXIST);
        }
        var result = shift.ToShiftResponseDto();
        return new SuccessResultDto<ShiftResponseDto>(result);
    }

    public async Task<ResultDto<WorksiteShiftsResponseDto>> GetEmpShiftByProject(Guid projectId, DateOnly fromDate, DateOnly toDate)
    {
        var filter = new EmployeeShiftFilter
        {
            ProjectId = projectId,
            TimeFrom = fromDate.ToDateTime(TimeOnly.MinValue).ToString(),
            TimeTo = toDate.ToDateTime(TimeOnly.MaxValue).ToString()
        };
        var shifts = await employeeShiftRepository.GetByFilter(filter, options);
        if (shifts is null || !shifts.Any())
        {
            logger.Error("No shifts found for project {ProjectId} from {FromDate} to {ToDate}", projectId, fromDate, toDate);
            return new ErrorResultDto<WorksiteShiftsResponseDto>(ResponseCodeConstant.SHIFT_NOT_EXIST);
        }

        var project = await projectRepository.GetById(projectId, new ProjectQueryableOptions { IncludedDailyReports = true });
        if (project is null)
        {
            logger.Error("Project not found for project {ProjectId}", projectId);
            return new ErrorResultDto<WorksiteShiftsResponseDto>(ResponseCodeConstant.PROJECT_NOT_EXIST);
        }
        var result = shifts.ToWorksiteShiftsResponseDto(project, fromDate);
        return new SuccessResultDto<WorksiteShiftsResponseDto>(result);
    }

    public async Task<ResultDto<WorksitesShiftsResponseDto>> GetEmpShiftByProjects(DateOnly fromDate, DateOnly toDate)
    {
        var options = new ProjectQueryableOptions
        {
            IncludedConstructions = true
        };
        var projects = await projectRepository.GetSimpleProjectsByManagerId(GetCurrentEmployeeUid(), options);
        if (projects is null || !projects.Any())
        {
            logger.Error("No managed projects found for employee {EmployeeUid}", GetCurrentEmployeeUid());
            return new ErrorResultDto<WorksitesShiftsResponseDto>(ResponseCodeConstant.SHIFT_NOT_EXIST);
        }
        var attendanceList = new List<WorksiteShiftsResponseDto>();
        foreach (var project in projects)
        {
            var attendanceRes = await GetEmpShiftByProject(project.ProjectUid, fromDate, toDate);
            if (attendanceRes.Data is not null)
            {
                attendanceList.Add(attendanceRes.Data);
            }
        }
        var result = new WorksitesShiftsResponseDto
        {
            Items = attendanceList,
        };
        return new SuccessResultDto<WorksitesShiftsResponseDto>(result);
    }

    public async Task<ResultDto<ShiftResponseDto>> AddShift(CreateShiftRequestDto dto)
    {
        var employeeId = dto.EmployeeId ?? GetCurrentEmployeeUid();
        var employee = await employeeRepository.GetById(employeeId, new EmployeeQueryableOptions());
        if (employee is null)
        {
            logger.Error("Employee not found for employee {EmployeeId}", employeeId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.EMPLOYEE_NOT_EXIST);
        }
        var currentMonthlyReport = await monthlyReportRepository.GetCurrentByEmployeeId(
            employeeId, new MonthlyReportQueryableOptions());
        var newShift = dto.ToEntity(employeeId, employee.StandardWorkingHours, currentMonthlyReport);
        if (newShift is null)
        {
            logger.Error("Failed to create shift for employee {EmployeeId}", employeeId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.SHIFT_CREATE_FAILED);
        }
        var createdShift = await employeeShiftRepository.Create(newShift, options);
        if (createdShift is null)
        {
            logger.Error("Failed to create shift for employee {EmployeeUid}", GetCurrentEmployeeUid());
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.SHIFT_CREATE_FAILED);
        }

        var result = createdShift.ToShiftResponseDto();
        return new SuccessResultDto<ShiftResponseDto>(result);
    }

    public async Task<ResultDto<ShiftResponseDto>> UpdateShift(Guid employeeShiftId, UpdateShiftRequestDto dto)
    {
        var option = options.TrackingOptions();
        var currentShift = await employeeShiftRepository.GetById(employeeShiftId, option);
        if (currentShift is null)
        {
            logger.Error("Shift {EmployeeShiftId} not found", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.SHIFT_NOT_EXIST);
        }

        if (currentShift.IsApproved == true && currentShift.EmployeeUid == GetCurrentEmployeeUid())
        {
            logger.Error("User {employeeUid cannot update approved shift {EmployeeShiftId}", GetCurrentEmployeeUid(), employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.CANNOT_UPDATE_APPROVED_SHIFT);
        }
        currentShift.UpdateFromDto(dto, GetCurrentEmployeeUid());

        var updatedShift = await employeeShiftRepository.Update(currentShift, option);
        if (updatedShift is null)
        {
            logger.Error("Failed to update shift info for shift {EmployeeShiftId}", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.SHIFT_UPDATE_FAILED);
        }
        await UpdateMonthlyReport(GetCurrentEmployeeUid(), DateOnly.FromDateTime(updatedShift.CheckInTime!.Value));
        var result = updatedShift.ToShiftResponseDto();
        return new SuccessResultDto<ShiftResponseDto>(result);
    }

    public async Task<ResultDto<ShiftResponseDto>> RequestDailyApproval(Guid employeeShiftId)
    {
        var option = options.TrackingOptions();
        var shift = await employeeShiftRepository.GetById(employeeShiftId, option);
        if (shift is null)
        {
            logger.Error("Shift not found for shift {ShiftId}", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.SHIFT_NOT_EXIST);
        }

        if (shift.CheckInTime is null || shift.CheckOutTime is null)
        {
            logger.Error("Shift {ShiftId} not checked out yet", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.REQUEST_ATTENDANCE_DAILY_NOT_CHECKOUT);
        }

        if (shift.IsRequested)
        {
            logger.Error("Shift {ShiftId} already requested attendance daily", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.REQUEST_ATTENDANCE_DAILY_IS_REQUESTED);
        }

        shift.IsRequested = true;
        var updatedShift = await employeeShiftRepository.Update(shift, option);
        if (updatedShift is null)
        {
            logger.Error("Failed to update shift info for shift {EmployeeShiftId}", shift.EmployeeShiftUid);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.SHIFT_UPDATE_FAILED);
        }

        var result = updatedShift.ToShiftResponseDto();
        return new SuccessResultDto<ShiftResponseDto>(result);
    }

    public async Task<ResultDto<ShiftResponseDto>> ApproveEmployeeShift(Guid employeeShiftId, CreateDailyApprovalRequestDto dto)
    {
        var option = options.TrackingOptions();
        var shift = await employeeShiftRepository.GetById(employeeShiftId, option);
        if (shift is null)
        {
            logger.Error("Shift not found for shift {ShiftId}", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.SHIFT_NOT_EXIST);
        }

        if (shift.CheckInTime is null || shift.CheckOutTime is null)
        {
            logger.Error("Shift {ShiftId} not checked out yet", employeeShiftId);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.REQUEST_ATTENDANCE_DAILY_NOT_CHECKOUT);
        }

        shift.IsApproved = dto.ApprovalStatus;
        shift.ApprovedBy = GetCurrentEmployeeUid();
        shift.ApprovedTime = DateTime.Now;
        var updatedShift = await employeeShiftRepository.Update(shift, option);
        if (updatedShift is null)
        {
            logger.Error("Failed to update shift info for shift {EmployeeShiftId}", shift.EmployeeShiftUid);
            return new ErrorResultDto<ShiftResponseDto>(ResponseCodeConstant.SHIFT_UPDATE_FAILED);
        }
        await UpdateMonthlyReport(GetCurrentEmployeeUid(), DateOnly.FromDateTime(shift.CheckInTime.Value));
        var result = updatedShift.ToShiftResponseDto();
        return new SuccessResultDto<ShiftResponseDto>(result);
    }

    private async Task<string> GeoDecode(string? latitude, string? longitude)
    {
        try
        {
            if (string.IsNullOrEmpty(latitude) || string.IsNullOrEmpty(longitude))
            {
                return string.Empty;
            }
            logger.Information("Geocoding location for latitude {Latitude} and longitude {Longitude}", latitude, longitude);
            return await partnerService.GetLocation(latitude, longitude);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Exception occur while geocoding location for latitude {Latitude} and longitude {Longitude}", latitude, longitude);
            return string.Empty;
        }
    }

    public async Task UpdateMonthlyReport(Guid employeeId, DateOnly currentDate)
    {
        var reportFrom = new DateOnly(currentDate.Year, currentDate.Month, 1);
        var reportTo = reportFrom.AddMonths(1).AddDays(-1);
        var report = await monthlyReportRepository.GetByEmployeeAndDate(
            employeeId,
            reportFrom,
            reportTo,
            new MonthlyReportQueryableOptions());

        var totalShifts = await employeeShiftRepository.GetByFilter(new EmployeeShiftFilter
        {
            EmployeeId = employeeId,
            TimeFrom = reportFrom.ToDateTime(TimeOnly.MinValue).ToString(),
            TimeTo = reportTo.ToDateTime(TimeOnly.MaxValue).ToString()
        }, options);

        var totalWorkDays = totalShifts
            .Where(s => s.CheckInTime is not null)
            .Select(s => DateOnly.FromDateTime(s.CheckInTime!.Value))
            .Distinct()
            .Count();

        var totalOffDays = totalShifts
            .Where(s => s.ScheduledStartTime is not null && s.CheckInTime is null)
            .Select(s => DateOnly.FromDateTime(s.ScheduledStartTime!.Value))
            .Distinct()
            .Count();

        var totalWorkHours = totalShifts.Sum(s => s.TotalWorkTime);
        var totalOvertime = totalShifts.Sum(s => s.TotalOverTime);

        if (report is null)
        {
            var newReport = new MonthlyReport
            {
                EmployeeUid = employeeId,
                ReportFrom = reportFrom,
                ReportTo = reportTo,
                TotalWorkDays = totalWorkDays,
                TotalOffDays = totalOffDays,
                TotalWorkHours = totalWorkHours,
                TotalOvertime = totalOvertime,
            };
            await monthlyReportRepository.Create(newReport);
        }
        else
        {
            report.TotalWorkDays = totalWorkDays;
            report.TotalOffDays = totalOffDays;
            report.TotalWorkHours = totalWorkHours;
            report.TotalOvertime = totalOvertime;
            await monthlyReportRepository.Update(report);
        }
    }

    public async Task<ResultDto<IEnumerable<Guid>>> CreateMultipleShifts(CreateMultipleShiftRequestDto dto)
    {
        var newShifts = dto.Shifts.ToEntities();
        if (!newShifts.Any())
        {
            logger.Error("Failed to create multiple shifts");
            return new ErrorResultDto<IEnumerable<Guid>>(ResponseCodeConstant.SHIFT_CREATE_FAILED);
        }
        var createdShifts = await employeeShiftRepository.CreateMultiple(newShifts, new EmployeeShiftQueryableOptions());
        return new SuccessResultDto<IEnumerable<Guid>>(createdShifts.Select(s => s.EmployeeShiftUid));
    }

    public async Task<ResultDto<bool>> UpdateMultipleShifts(UpdateMultipleShiftRequestDto dto)
    {
        var existShifts = await employeeShiftRepository.GetByIds(
            dto.Shifts.Select(s => s.ShiftId),
            new EmployeeShiftQueryableOptions());

        if (!existShifts.Any())
        {
            logger.Error("Failed to update multiple shifts");
            return new ErrorResultDto<bool>(ResponseCodeConstant.SHIFT_NOT_EXIST, false);
        }

        existShifts.UpdateFromDto(dto.Shifts, GetCurrentEmployeeUid());
        var isUpdated = await employeeShiftRepository.UpdateMultiple(existShifts);
        return new SuccessResultDto<bool>(isUpdated);
    }

    private async Task<bool> CreateMultipleShifts(IEnumerable<SynchronizeShiftRequestItem> items)
    {
        try
        {
            var createShiftsTask = items
                .Where(i => i.EmployeeShiftId is null || i.EmployeeShiftId == Guid.Empty)
                .Select(async item =>
                {
                    return new EmployeeShift
                    {
                        EmployeeShiftUid = GuidHelper.GenerateUUIDv7(),
                        EmployeeUid = GetCurrentEmployeeUid(),
                        ProjectUid = item.ProjectId,
                        WorkingLocation = item.WorkingLocation,
                        CheckInTime = DateTime.TryParse(item.CheckInTime, out var checkInTime) ? checkInTime : null,
                        CheckOutTime = DateTime.TryParse(item.CheckOutTime, out var checkOutTime) ? checkOutTime : null,
                        Description = item.Description,
                        CheckInLocation = item.CheckInCoordinate is not null
                            ? await GeoDecode(item.CheckInCoordinate.Latitude, item.CheckInCoordinate.Longitude)
                            : null,
                        CheckOutLocation = item.CheckOutCoordinate is not null
                            ? await GeoDecode(item.CheckOutCoordinate.Latitude, item.CheckOutCoordinate.Longitude)
                            : null,
                        EmployeeShiftBreakTimes = item.Breaks?
                            .Where(b => b.BreakInTime is not null && b.BreakOutTime is not null)
                            .Select(b =>
                            {
                                return new EmployeeShiftBreakTime
                                {
                                    BreakInTime = DateTime.TryParse(b.BreakInTime, out var breakInTime) ? breakInTime : null,
                                    BreakOutTime = DateTime.TryParse(b.BreakOutTime, out var breakOutTime) ? breakOutTime : null,
                                };
                            }).ToList() ?? [],
                    };
                }).ToList();
            var createShifts = await Task.WhenAll(createShiftsTask);
            var createdShifts = await employeeShiftRepository.CreateMultiple(createShifts, new EmployeeShiftQueryableOptions());
            return createdShifts.Count() == createShifts.Length;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Exception occur while create multiple shifts");
            return false;
        }
    }

    private async Task<bool> UpdateMultipleShifts(IEnumerable<SynchronizeShiftRequestItem> items)
    {
        var employeeShiftIds = items.Where(i => i.EmployeeShiftId is not null && i.EmployeeShiftId != Guid.Empty)
            .Select(i => i.EmployeeShiftId!.Value);
        var existEmployeeShifts = await employeeShiftRepository.GetByIds(
            employeeShiftIds,
            new EmployeeShiftQueryableOptions());

        if (!existEmployeeShifts.Any())
        {
            logger.Error("No exist employee shifts found for employee {EmployeeUid}", GetCurrentEmployeeUid());
            return false;
        }

        var updateShifts = existEmployeeShifts.UpdateFromDto(items, GetCurrentEmployeeUid());
        var isUpdated = await employeeShiftRepository.UpdateMultiple(updateShifts);
        return isUpdated;
    }

    /// <summary>
    /// Publishes a check-in event for project detection when no project ID is provided
    /// </summary>
    /// <param name="shiftId">The shift ID</param>
    /// <param name="latitude">The latitude coordinate</param>
    /// <param name="longtitude">The longitude coordinate</param>
    /// <param name="isCheckIn">Indicates if the check-in or check-out event is being published</param>
    private async Task PublishCheckInEvent(Guid shiftId, string? latitude, string? longtitude, bool isCheckIn)
    {
        try
        {
            var checkInEvent = new AttendanceEvent
            {
                ShiftId = shiftId,
                OrgId = GetCurrentOrgUid(),
                Coordinates = new GpsCoordinates
                {
                    Latitude = Double.TryParse(latitude, out var lat) ? lat : null,
                    Longitude = Double.TryParse(longtitude, out var lng) ? lng : null
                },
                IsCheckIn = isCheckIn
            };

            await eventBus.PublishAsync(checkInEvent);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Exception occur while publish check-in event for shift {ShiftId}", shiftId);
        }
    }
}

