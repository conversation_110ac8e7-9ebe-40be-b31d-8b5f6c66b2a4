using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class CategorizedCostConfiguration(string schema) : IEntityTypeConfiguration<CategorizedCost>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<CategorizedCost> builder)
    {
        builder.HasKey(e => e.CategorizedCostUid).HasName("categorized_cost_pkey");

        builder.ToTable("categorized_cost", Schema);

        builder.Property(e => e.CategorizedCostUid)
            .HasColumnName("categorized_cost_uid");
        builder.Property(e => e.ConstructionCostUid)
            .HasColumnName("construction_cost_uid");
        builder.Property(e => e.CategoryUid)
            .HasColumnName("category_uid");
        builder.Property(e => e.Quantity)
            .HasColumnName("quantity");
        builder.Property(e => e.TotalAmount)
            .HasColumnName("total_amount");
        builder.Property(e => e.AvgAmount)
            .HasColumnName("avg_amount");

        builder.HasOne(d => d.ConstructionCost)
            .WithMany(p => p.CategorizedCosts)
            .HasForeignKey(d => d.ConstructionCostUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("categorized_cost_construction_cost_id_fkey");

        builder.HasOne(d => d.Category)
            .WithMany(p => p.CategorizedCosts)
            .HasForeignKey(d => d.CategoryUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("categorized_cost_category_id_fkey");
    }
} 