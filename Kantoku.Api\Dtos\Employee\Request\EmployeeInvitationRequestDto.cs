using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.Employee.Request;

public class EmployeeInvitationRequestDto
{
    /// <summary>
    /// (*) Employee's email address
    /// </summary>
    [Required]
    public string Email { get; set; } = null!;

    /// <summary>
    /// (*) Unique employee code/ID
    /// </summary>
    [Required]
    public string EmployeeCode { get; set; } = null!;

    /// <summary>
    /// (*) Role IDs
    /// </summary>
    [Required]
    public IEnumerable<string> RoleIds { get; set; } = [];

    /// <summary>
    /// (*) Invitation description
    /// </summary>
    public string? InvitationDescription { get; set; }
}