using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class ConstructionConfiguration(string schema) : IEntityTypeConfiguration<Construction>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Construction> builder)
    {
        builder.HasKey(e => e.ConstructionUid).HasName("construction_pkey");

        builder.ToTable("construction", Schema);

        builder.Property(e => e.ConstructionUid)
            .HasColumnName("construction_uid");
        builder.Property(e => e.ProjectUid)
            .HasColumnName("project_uid");
        builder.Property(e => e.ConstructionName)
            .HasColumnName("construction_name");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.IsPrimary)
            .HasColumnName("is_primary");
        builder.Property(e => e.Is<PERSON>eleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.InitialContractualCosts)
            .HasColumnType("jsonb")
            .HasColumnName("initial_contractual_costs")
            .HasConversion(
                v => Newtonsoft.Json.JsonConvert.SerializeObject(v),
                v => Newtonsoft.Json.JsonConvert.DeserializeObject<ICollection<InitalCost>>(v)
            ).Metadata.SetValueComparer(
                new ValueComparer<ICollection<InitalCost>>(
                    (c1, c2) => (c1 == null && c2 == null) || (c1 != null && c2 != null && c1.SequenceEqual(c2)),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()
                )
            );
        builder.Property(e => e.ModifiedContractualCosts)
            .HasColumnType("jsonb")
            .HasColumnName("modified_contractual_costs")
            .HasConversion(
                v => Newtonsoft.Json.JsonConvert.SerializeObject(v),
                v => Newtonsoft.Json.JsonConvert.DeserializeObject<ICollection<InitalCost>>(v)
            ).Metadata.SetValueComparer(
                new ValueComparer<ICollection<InitalCost>>(
                    (c1, c2) => (c1 == null && c2 == null) || (c1 != null && c2 != null && c1.SequenceEqual(c2)),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()
                )
            );
        builder.Property(e => e.InitialEstimatedCosts)
            .HasColumnType("jsonb")
            .HasColumnName("initial_estimated_costs")
            .HasConversion(
                v => Newtonsoft.Json.JsonConvert.SerializeObject(v),
                v => Newtonsoft.Json.JsonConvert.DeserializeObject<ICollection<InitalCost>>(v)
            ).Metadata.SetValueComparer(
                new ValueComparer<ICollection<InitalCost>>(
                    (c1, c2) => (c1 == null && c2 == null) || (c1 != null && c2 != null && c1.SequenceEqual(c2)),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()
                )
            );

        builder.HasOne(d => d.Project)
            .WithMany(p => p.Constructions)
            .HasForeignKey(d => d.ProjectUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("construction_project_id_fkey");

        builder.HasOne(d => d.Org)
            .WithMany(p => p.Constructions)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("construction_org_id_fkey");
    }
}