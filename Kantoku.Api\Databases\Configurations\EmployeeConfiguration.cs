using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace Kantoku.Api.Databases.Configurations;

public class EmployeeConfiguration(string schema) : IEntityTypeConfiguration<Employee>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Employee> builder)
    {
        builder.HasKey(e => e.EmployeeUid).HasName("employee_pkey");

        builder.ToTable("employee", Schema, tb => tb.HasComment("Bảng lưu thông tin user trong org"));

        builder.HasIndex(e => new { e.OrgUid, e.AccountUid }, "employee_org_uid_account_uid_ukey").IsUnique();

        builder.Property(e => e.EmployeeUid)
            .HasColumnName("employee_uid");
        builder.Property(e => e.EmployeeName)
            .HasColumnName("employee_name");
        builder.Property(e => e.EmployeeCode)
            .HasColumnName("employee_code");
        builder.Property(e => e.EmployeeType)
            .HasColumnName("employee_type");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.AccountUid)
            .HasColumnName("account_uid");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.StructureUid)
            .HasColumnName("structure_uid");
        builder.Property(e => e.PositionUid)
            .HasColumnName("position_uid");
        builder.Property(e => e.RankingUid)
            .HasColumnName("ranking_uid");
        builder.Property(e => e.WorkingStatus)
            .HasColumnName("working_status");
        builder.Property(e => e.SalaryInMonth)
            .HasColumnName("salary_in_month");
        builder.Property(e => e.StandardWorkingHours)
            .HasColumnName("standard_working_hours");
        builder.Property(e => e.WorkingFromDate)
            .HasColumnType("timestamp")
            .HasColumnName("active_from");
        builder.Property(e => e.WorkingToDate)
            .HasColumnType("timestamp")
            .HasColumnName("active_to");
        builder.Property(e => e.IsHidden)
            .HasDefaultValue(false)
            .HasColumnName("is_hidden");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");
        builder.Property(e => e.IsOrgOwner)
            .HasDefaultValue(false)
            .HasColumnName("is_org_owner");
        builder.Property(e => e.IsOrgAdmin)
            .HasDefaultValue(false)
            .HasColumnName("is_org_admin");
        builder.Property(e => e.HasApprovalAuthority)
            .HasDefaultValue(false)
            .HasColumnName("has_approval_authority");
        builder.Property(e => e.EmployeeMails)
            .HasColumnType("jsonb")
            .HasColumnName("employee_mails")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<IEnumerable<EmployeeMail>>(v)
            ).Metadata.SetValueComparer(
                new ValueComparer<IEnumerable<EmployeeMail>>(
                    (c1, c2) => (c1 == null && c2 == null) || (c1 != null && c2 != null && c1.SequenceEqual(c2)),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()
                )
            );
        builder.Property(e => e.EmployeePhones)
            .HasColumnType("jsonb")
            .HasColumnName("employee_phones")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<IEnumerable<EmployeePhone>>(v)
            ).Metadata.SetValueComparer(
                new ValueComparer<IEnumerable<EmployeePhone>>(
                    (c1, c2) => (c1 == null && c2 == null) || (c1 != null && c2 != null && c1.SequenceEqual(c2)),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()
                )
            );
        builder.Property(e => e.EmployeeAddress)
            .HasColumnName("employee_address");

        builder.HasOne(d => d.Org).WithMany(p => p.Employees)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("employee_org_id_fkey");

        builder.HasOne(d => d.Account).WithMany(p => p.Employees)
            .HasForeignKey(d => d.AccountUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("employee_account_uid_fkey");

        builder.HasOne(d => d.Position).WithMany(p => p.Employees)
            .HasForeignKey(d => d.PositionUid)
            .HasConstraintName("employee_position_id_fkey");

        builder.HasOne(d => d.Structure).WithMany(p => p.Employees)
            .HasForeignKey(d => d.StructureUid)
            .HasConstraintName("employee_structure_id_fkey");

        builder.HasOne(d => d.Ranking).WithMany(p => p.Employees)
            .HasForeignKey(d => d.RankingUid)
            .HasConstraintName("employee_ranking_uid_fkey");

        builder.HasOne(d => d.Status).WithMany(p => p.Employees)
            .HasForeignKey(d => d.WorkingStatus)
            .HasConstraintName("employee_status_code_fkey");
    }
}
