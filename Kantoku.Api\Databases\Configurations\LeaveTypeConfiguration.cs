using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;

namespace Kantoku.Api.Databases.Configurations;

public class LeaveTypeConfiguration(string schema) : IEntityTypeConfiguration<LeaveType>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<LeaveType> builder)
    {
        builder.HasKey(e => e.LeaveTypeCode).HasName("leave_type_pkey");

        builder.ToTable("leave_type", Schema);

        builder.Property(e => e.LeaveTypeCode)
            .HasColumnName("leave_type_code");
        builder.Property(e => e.IsSelf)
            .HasDefaultValue(false)
            .HasColumnName("is_self");
        builder.Property(e => e.IsPaid)
            .HasDefaultValue(false)
            .HasColumnName("is_paid");
        builder.Property(e => e.TranslatedLeaveType)
            .HasColumnName("translated_leave_type")
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<IEnumerable<TranslatedLeaveType>>(v) ?? Enumerable.Empty<TranslatedLeaveType>()
            ).Metadata.SetValueComparer(
                new ValueComparer<IEnumerable<TranslatedLeaveType>>(
                    (c1, c2) => (c1 ?? Enumerable.Empty<TranslatedLeaveType>()).SequenceEqual(c2 ?? Enumerable.Empty<TranslatedLeaveType>()),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()
                )
            );
    }
}
