using FirebaseAdmin;
using FirebaseAdmin.Messaging;
using Google.Apis.Auth.OAuth2;
using Kantoku.Api.Dtos.Notification;
using Kantoku.Api.Utils.Attributes.Class;

namespace Kantoku.Api.Services;

public interface IFirebaseService
{
    Task<string> SendToDevice(string deviceToken, NotificationDto notification);
    Task<FirebaseBatchResponseDto> SendToDevice(string deviceTokens, IEnumerable<NotificationDto> notifications);

    Task<FirebaseBatchResponseDto> SendToDevices(IEnumerable<string> deviceTokens, NotificationDto notification);
    Task<FirebaseBatchResponseDto> SendToDevices(IEnumerable<string> deviceTokens, IEnumerable<NotificationDto> notifications);

    Task<string> SendToTopic(string topic, NotificationDto notification);
    Task<FirebaseBatchResponseDto> SendToTopic(string topic, IEnumerable<NotificationDto> notifications);

    Task<TopicManagementResponse> SubscribeToTopic(string topic, IReadOnlyList<string> deviceTokens);
    Task<TopicManagementResponse> UnsubscribeFromTopic(string topic, IReadOnlyList<string> deviceTokens);
}

[Service(ServiceLifetime.Singleton)]
public class FirebaseService : IFirebaseService
{
    private readonly FirebaseMessaging _firebaseMessaging;
    private readonly ILogger<FirebaseService> _logger;

    public FirebaseService(ILogger<FirebaseService> logger)
    {
        _logger = logger;
        if (FirebaseApp.DefaultInstance == null)
        {
            var firebaseConfigPath = Path.Combine(AppContext.BaseDirectory, "firebase-service-account.json");
            if (!File.Exists(firebaseConfigPath))
            {
                throw new FileNotFoundException("Firebase service account file not found");
            }
            try
            {
                FirebaseApp.Create(new AppOptions
                {
                    Credential = GoogleCredential.FromFile(firebaseConfigPath)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating Firebase app");
                throw;
            }
        }

        _firebaseMessaging = FirebaseMessaging.DefaultInstance;
    }

    public async Task<string> SendToDevice(string deviceToken, NotificationDto notification)
    {
        var message = new Message
        {
            Token = deviceToken,
            Notification = new Notification
            {
                Title = notification.Title,
                Body = notification.Body
            },
            Data = notification.Data
        };

        try
        {
            string response = await _firebaseMessaging.SendAsync(message);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification to device {DeviceToken}", deviceToken);
            throw;
        }
    }

    public async Task<FirebaseBatchResponseDto> SendToDevice(string deviceTokens, IEnumerable<NotificationDto> notifications)
    {
        try
        {
            var messages = notifications.Select(notification => new Message
            {
                Token = deviceTokens,
                Notification = new Notification
                {
                    Title = notification.Title,
                    Body = notification.Body
                },
                Data = notification.Data
            });

            var responses = new List<BatchResponse>();
            foreach (var message in messages.Chunk(500))
            {
                var response = await _firebaseMessaging.SendEachAsync(message);
                responses.Add(response);
            }
            return new FirebaseBatchResponseDto
            {
                Responses = responses.SelectMany(r => r.Responses).ToList(),
                SuccessCount = responses.Sum(r => r.SuccessCount),
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification to multiple devices");
            throw;
        }
    }

    public async Task<FirebaseBatchResponseDto> SendToDevices(IEnumerable<string> deviceTokens, NotificationDto notification)
    {
        var message = new MulticastMessage
        {
            Notification = new Notification
            {
                Title = notification.Title,
                Body = notification.Body
            },
            Data = notification.Data
        };

        try
        {
            var responses = new List<BatchResponse>();
            foreach (var token in deviceTokens.Chunk(500))
            {
                message.Tokens = [.. token];
                BatchResponse response = await _firebaseMessaging.SendEachForMulticastAsync(message);
                responses.Add(response);
            }
            return new FirebaseBatchResponseDto
            {
                Responses = responses.SelectMany(r => r.Responses).ToList(),
                SuccessCount = responses.Sum(r => r.SuccessCount),
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification to multiple devices");
            throw;
        }
    }

    public async Task<FirebaseBatchResponseDto> SendToDevices(IEnumerable<string> deviceTokens, IEnumerable<NotificationDto> notifications)
    {
        try
        {
            var responses = new List<FirebaseBatchResponseDto>();
            foreach (var notification in notifications.Chunk(500))
            {
                var response = await SendToDevices(deviceTokens, notification);
                responses.Add(response);
            }
                return new FirebaseBatchResponseDto
                {
                    Responses = responses.SelectMany(r => r.Responses).ToList(),
                    SuccessCount = responses.Sum(r => r.SuccessCount),
                };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification to multiple devices");
            throw;
        }
    }

    public async Task<string> SendToTopic(string topic, NotificationDto notification)
    {
        var message = new Message
        {
            Topic = topic,
            Notification = new Notification
            {
                Title = notification.Title,
                Body = notification.Body
            },
            Data = notification.Data
        };

        try
        {
            var response = await _firebaseMessaging.SendAsync(message);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification to topic {Topic}", topic);
            throw;
        }
    }

    public async Task<FirebaseBatchResponseDto> SendToTopic(string topic, IEnumerable<NotificationDto> notifications)
    {
        var messages = notifications.Select(notification => new Message
        {
            Topic = topic,
            Notification = new Notification
            {
                Title = notification.Title,
                Body = notification.Body
            },
            Data = notification.Data
        }).ToList();

        try
        {
            var responses = new List<BatchResponse>();
            foreach (var message in messages.Chunk(500))
            {
                var response = await _firebaseMessaging.SendEachAsync(message);
                responses.Add(response);
            }
            return new FirebaseBatchResponseDto
            {
                Responses = responses.SelectMany(r => r.Responses).ToList(),
                SuccessCount = responses.Sum(r => r.SuccessCount),
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending notification to topic {Topic}", topic);
            throw;
        }
    }

    public async Task<TopicManagementResponse> SubscribeToTopic(string topic, IReadOnlyList<string> deviceTokens)
    {
        var response = await _firebaseMessaging.SubscribeToTopicAsync(deviceTokens, topic);
        return response;
    }

    public async Task<TopicManagementResponse> UnsubscribeFromTopic(string topic, IReadOnlyList<string> deviceTokens)
    {
        var response = await _firebaseMessaging.UnsubscribeFromTopicAsync(deviceTokens, topic);
        return response;
    }
}