using Kantoku.Api.Dtos.Base.Response;

namespace Kantoku.Api.Dtos.Price.Response;

/// <summary>
/// Represents the response data for item prices, including pagination information.
/// </summary>
public class ItemPricesResponseDto
{
    /// <summary>
    ///  the list of item price response DTOs.
    /// </summary>
    public IEnumerable<ItemPriceResponseDto>? Items { get; set; }

    /// <summary>
    ///  the current page number (*).
    /// </summary>
    public int PageNum { get; set; }

    /// <summary>
    ///  the number of items per page (*).
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    ///  the total number of records (*).
    /// </summary>
    public int TotalRecords { get; set; }
}
