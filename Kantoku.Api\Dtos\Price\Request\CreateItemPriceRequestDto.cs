using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Dtos.Vendor.Request;

namespace Kantoku.Api.Dtos.Price.Request;

/// <summary>
/// DTO for creating an item price request.
/// </summary>
public class CreateItemPriceRequestDto
{
    /// <summary>
    ///  the vendor creation DTO. (*)
    /// </summary>
    [Required]
    public CreateVendorBaseRequestDto Vendor { get; set; } = null!;

    /// <summary>
    ///  the unit of the item.
    /// </summary>
    [Required]
    public string Unit { get; set; } = null!;

    /// <summary>
    ///  the price of the item. (*)
    /// </summary>
    [Required]
    public int Price { get; set; }

    /// <summary>
    ///  the valid from date (null => today)
    /// </summary>
    public string? ValidFrom { get; set; }

    /// <summary>
    ///  the valid to date.
    /// </summary>
    public string? ValidTo { get; set; }
}
