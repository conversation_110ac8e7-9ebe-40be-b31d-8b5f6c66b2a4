using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.Ranking.Request;

/// <summary>
/// Create ranking request DTO
/// MinValue MaxValue can not be both null
/// </summary>
public class CreateRankingRequestDto
{
    /// <summary>
    /// The name of the ranking (*)
    /// </summary>
    [Required]
    public string RankingName { get; set; } = null!;

    /// <summary>
    /// The description of the ranking
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// The minimum value of the ranking
    /// </summary>
    public int? MinValue { get; set; }

    /// <summary>
    /// The maximum value of the ranking
    /// </summary>
    public int? MaxValue { get; set; }
}
