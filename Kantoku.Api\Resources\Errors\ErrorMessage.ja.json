[{"errorStatus": 200, "errorCode": "KAN00", "errorMessage": "成功"}, {"errorStatus": 404, "errorCode": "KAN01", "errorMessage": "要求されたリソースが存在しません"}, {"errorStatus": 304, "errorCode": "KAN02", "errorMessage": "リソースが変更されていません"}, {"errorStatus": 400, "errorCode": "KAN03", "errorMessage": "リソースが変更されていません"}, {"errorStatus": 401, "errorCode": "KAN04", "errorMessage": "認証されていません"}, {"errorStatus": 403, "errorCode": "KAN05", "errorMessage": "アクセスが禁止されています"}, {"errorStatus": 500, "errorCode": "KAN500", "errorMessage": "不明なエラー"}, {"errorStatus": 400, "errorCode": "AUTH01", "errorMessage": "パスワードが正しくありません"}, {"errorStatus": 500, "errorCode": "AUTH02", "errorMessage": "パスワードの変更に失敗しました"}, {"errorStatus": 400, "errorCode": "AUTH03", "errorMessage": "確認用パスワードが一致しません"}, {"errorStatus": 400, "errorCode": "AUTH04", "errorMessage": "パスワードが要件を満たしていません"}, {"errorStatus": 500, "errorCode": "AUTH05", "errorMessage": "パスワードのリセットに失敗しました"}, {"errorStatus": 403, "errorCode": "AUTH06", "errorMessage": "外部労働者はログインできません"}, {"errorStatus": 500, "errorCode": "AUTH07", "errorMessage": "パスワードの生成に失敗しました"}, {"errorStatus": 401, "errorCode": "AUTH08", "errorMessage": "ログインに失敗しました"}, {"errorStatus": 500, "errorCode": "AUTH09", "errorMessage": "サインアップに失敗しました"}, {"errorStatus": 500, "errorCode": "AUTH10", "errorMessage": "サインオンに失敗しました"}, {"errorStatus": 500, "errorCode": "AUTH11", "errorMessage": "OTPコードの生成に失敗しました"}, {"errorStatus": 404, "errorCode": "AUTH12", "errorMessage": "OTPコードが存在しません"}, {"errorStatus": 400, "errorCode": "AUTH13", "errorMessage": "OTPコードが正しくありません"}, {"errorStatus": 401, "errorCode": "TKN01", "errorMessage": "トークンの有効期限が切れています"}, {"errorStatus": 401, "errorCode": "TKN02", "errorMessage": "トークンが無効です"}, {"errorStatus": 500, "errorCode": "TKN03", "errorMessage": "トークンの検証に失敗しました"}, {"errorStatus": 500, "errorCode": "TKN04", "errorMessage": "トークンの生成に失敗しました"}, {"errorStatus": 404, "errorCode": "USR01", "errorMessage": "ユーザーが存在しません"}, {"errorStatus": 500, "errorCode": "USR03", "errorMessage": "ユーザーの作成に失敗しました"}, {"errorStatus": 500, "errorCode": "USR04", "errorMessage": "ユーザーの更新に失敗しました"}, {"errorStatus": 500, "errorCode": "USR05", "errorMessage": "ユーザーの削除に失敗しました"}, {"errorStatus": 500, "errorCode": "USR06", "errorMessage": "ロールの割り当てに失敗しました"}, {"errorStatus": 409, "errorCode": "USR07", "errorMessage": "ログインIDが既に存在します"}, {"errorStatus": 500, "errorCode": "USR08", "errorMessage": "ユーザーのアバターの更新に失敗しました"}, {"errorStatus": 404, "errorCode": "USR09", "errorMessage": "ユーザーのアバターが設定されていません"}, {"errorStatus": 500, "errorCode": "USR10", "errorMessage": "ユーザーのアバターの削除に失敗しました"}, {"errorStatus": 404, "errorCode": "EML01", "errorMessage": "有休日数が存在しません"}, {"errorStatus": 500, "errorCode": "EML02", "errorMessage": "有休日数が新規作成に失敗しました"}, {"errorStatus": 500, "errorCode": "EML03", "errorMessage": "有休日数の更新に失敗しました"}, {"errorStatus": 500, "errorCode": "EML04", "errorMessage": "有休日数の削除に失敗しました"}, {"errorStatus": 409, "errorCode": "EML05", "errorMessage": "有休日数設定が既に存在します"}, {"errorStatus": 400, "errorCode": "EML06", "errorMessage": "有休日数の有効期限が無効です"}, {"errorStatus": 500, "errorCode": "CRT01", "errorMessage": "契約の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "CRT02", "errorMessage": "契約の更新に失敗しました"}, {"errorStatus": 404, "errorCode": "ORG01", "errorMessage": "組織が存在しません"}, {"errorStatus": 500, "errorCode": "ORG02", "errorMessage": "組織の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "ORG03", "errorMessage": "組織の更新に失敗しました"}, {"errorStatus": 500, "errorCode": "ORG04", "errorMessage": "組織の削除に失敗しました"}, {"errorStatus": 403, "errorCode": "ORG05", "errorMessage": "組織の所有者ではありません"}, {"errorStatus": 403, "errorCode": "ORG06", "errorMessage": "組織の管理者ではありません"}, {"errorStatus": 404, "errorCode": "STR01", "errorMessage": "構造が存在しません"}, {"errorStatus": 500, "errorCode": "STR02", "errorMessage": "構造の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "STR03", "errorMessage": "構造の更新に失敗しました"}, {"errorStatus": 500, "errorCode": "STR04", "errorMessage": "構造の削除に失敗しました"}, {"errorStatus": 400, "errorCode": "STR05", "errorMessage": "構造の入力データが無効です"}, {"errorStatus": 404, "errorCode": "POS01", "errorMessage": "役割が存在しません"}, {"errorStatus": 500, "errorCode": "POS02", "errorMessage": "役割の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "POS03", "errorMessage": "役割の更新に失敗しました"}, {"errorStatus": 500, "errorCode": "POS04", "errorMessage": "役割の削除に失敗しました"}, {"errorStatus": 404, "errorCode": "RNK01", "errorMessage": "ランキングが存在しません"}, {"errorStatus": 500, "errorCode": "RNK02", "errorMessage": "ランキングの作成に失敗しました"}, {"errorStatus": 500, "errorCode": "RNK03", "errorMessage": "ランキングの更新に失敗しました"}, {"errorStatus": 500, "errorCode": "RNK04", "errorMessage": "ランキングの削除に失敗しました"}, {"errorStatus": 404, "errorCode": "WPL01", "errorMessage": "職場が存在しません"}, {"errorStatus": 500, "errorCode": "WPL02", "errorMessage": "職場の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "WPL03", "errorMessage": "職場の更新に失敗しました"}, {"errorStatus": 404, "errorCode": "GLC01", "errorMessage": "グローバル設定が存在しません"}, {"errorStatus": 404, "errorCode": "MAIL01", "errorMessage": "メールテンプレートが存在しません"}, {"errorStatus": 500, "errorCode": "MAIL02", "errorMessage": "メールの送信に失敗しました"}, {"errorStatus": 404, "errorCode": "ROLE01", "errorMessage": "役割が存在しません"}, {"errorStatus": 500, "errorCode": "ROLE02", "errorMessage": "役割の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "ROLE03", "errorMessage": "役割の更新に失敗しました"}, {"errorStatus": 500, "errorCode": "ROLE04", "errorMessage": "役割の削除に失敗しました"}, {"errorStatus": 404, "errorCode": "COMN01", "errorMessage": "単位が存在しません"}, {"errorStatus": 404, "errorCode": "COMN02", "errorMessage": "申請種別が存在しません"}, {"errorStatus": 404, "errorCode": "COMN03", "errorMessage": "休暇種別が存在しません"}, {"errorStatus": 404, "errorCode": "COMN04", "errorMessage": "状態が存在しません"}, {"errorStatus": 404, "errorCode": "REQ01", "errorMessage": "申請が存在しません"}, {"errorStatus": 500, "errorCode": "REQ02", "errorMessage": "申請の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "REQ03", "errorMessage": "申請の更新に失敗しました"}, {"errorStatus": 500, "errorCode": "REQ04", "errorMessage": "出勤の承認に失敗しました"}, {"errorStatus": 500, "errorCode": "REQ05", "errorMessage": "申請の承認に失敗しました"}, {"errorStatus": 500, "errorCode": "REQ06", "errorMessage": "申請の拒否に失敗しました"}, {"errorStatus": 500, "errorCode": "REQ07", "errorMessage": "申請のキャンセルに失敗しました"}, {"errorStatus": 400, "errorCode": "REQ08", "errorMessage": "申請の詳細が無効です"}, {"errorStatus": 403, "errorCode": "REQ09", "errorMessage": "承認権限がありません"}, {"errorStatus": 400, "errorCode": "REQ10", "errorMessage": "処理済みまたは取消済み申請が更新できません"}, {"errorStatus": 400, "errorCode": "REQ11", "errorMessage": "申請には現場情報が必要です"}, {"errorStatus": 400, "errorCode": "REQ12", "errorMessage": "休日に休暇申請ができません"}, {"errorStatus": 400, "errorCode": "REQ13", "errorMessage": "日次出勤申請に退勤が必要です"}, {"errorStatus": 500, "errorCode": "REQ14", "errorMessage": "申請承認に失敗しました"}, {"errorStatus": 500, "errorCode": "REQ15", "errorMessage": "日次出勤申請の作成に失敗しました"}, {"errorStatus": 409, "errorCode": "REQ16", "errorMessage": "日次勤怠が申請済みです"}, {"errorStatus": 400, "errorCode": "REQ17", "errorMessage": "出勤申請が休日に申請できません"}, {"errorStatus": 400, "errorCode": "REQ18", "errorMessage": "勤怠の日付が一日以上申請できません"}, {"errorStatus": 409, "errorCode": "REQ19", "errorMessage": "申請はすでに承認されています"}, {"errorStatus": 409, "errorCode": "REQ20", "errorMessage": "申請はすでにキャンセルされています"}, {"errorStatus": 500, "errorCode": "REQ21", "errorMessage": "申請の削除に失敗しました"}, {"errorStatus": 404, "errorCode": "TRL01", "errorMessage": "翻訳が存在しません"}, {"errorStatus": 404, "errorCode": "SFT01", "errorMessage": "シフトが存在しません"}, {"errorStatus": 500, "errorCode": "SFT02", "errorMessage": "シフトの作成に失敗しました"}, {"errorStatus": 500, "errorCode": "SFT03", "errorMessage": "シフトの更新に失敗しました"}, {"errorStatus": 500, "errorCode": "SFT04", "errorMessage": "シフトの削除に失敗しました"}, {"errorStatus": 500, "errorCode": "SFT05", "errorMessage": "チェックインに失敗しました"}, {"errorStatus": 500, "errorCode": "SFT06", "errorMessage": "チェックアウトに失敗しました"}, {"errorStatus": 500, "errorCode": "SFT07", "errorMessage": "休憩開始に失敗しました"}, {"errorStatus": 500, "errorCode": "SFT08", "errorMessage": "休憩終了に失敗しました"}, {"errorStatus": 409, "errorCode": "SFT09", "errorMessage": "すでにチェックインしています"}, {"errorStatus": 409, "errorCode": "SFT10", "errorMessage": "すでに休憩中です"}, {"errorStatus": 409, "errorCode": "SFT11", "errorMessage": "まだチェックインしていません"}, {"errorStatus": 400, "errorCode": "SFT12", "errorMessage": "シフトはすでに終了しています"}, {"errorStatus": 400, "errorCode": "SFT13", "errorMessage": "休憩中ではありません"}, {"errorStatus": 400, "errorCode": "SFT14", "errorMessage": "まだ休憩を終了していません"}, {"errorStatus": 409, "errorCode": "SFT15", "errorMessage": "休憩時間を超過しています"}, {"errorStatus": 500, "errorCode": "SFT16", "errorMessage": "追加シフトの作成に失敗しました"}, {"errorStatus": 400, "errorCode": "SFT17", "errorMessage": "承認済みシフトを更新できません"}, {"errorStatus": 409, "errorCode": "SFT18", "errorMessage": "すでにチェックアウトしています"}, {"errorStatus": 500, "errorCode": "SFT19", "errorMessage": "シフトの削除に失敗しました"}, {"errorStatus": 404, "errorCode": "SCH01", "errorMessage": "スケジュールが存在しません"}, {"errorStatus": 500, "errorCode": "SCH02", "errorMessage": "スケジュールの作成に失敗しました"}, {"errorStatus": 500, "errorCode": "SCH03", "errorMessage": "スケジュールの更新に失敗しました"}, {"errorStatus": 500, "errorCode": "SCH04", "errorMessage": "スケジュールの削除に失敗しました"}, {"errorStatus": 500, "errorCode": "SCH05", "errorMessage": "スケジュールのエクスポートに失敗しました"}, {"errorStatus": 409, "errorCode": "SCH06", "errorMessage": "すでにスケジュールが設定されています"}, {"errorStatus": 400, "errorCode": "SCH07", "errorMessage": "スケジュールの解析に失敗しました"}, {"errorStatus": 404, "errorCode": "PRJ01", "errorMessage": "プロジェクトが存在しません"}, {"errorStatus": 500, "errorCode": "PRJ02", "errorMessage": "プロジェクトの作成に失敗しました"}, {"errorStatus": 500, "errorCode": "PRJ03", "errorMessage": "プロジェクトの更新に失敗しました"}, {"errorStatus": 500, "errorCode": "PRJ04", "errorMessage": "プロジェクトの削除に失敗しました"}, {"errorStatus": 404, "errorCode": "PRJ05", "errorMessage": "プロジェクトのサマリーが存在しません"}, {"errorStatus": 404, "errorCode": "GPS01", "errorMessage": "位置情報が存在しません"}, {"errorStatus": 400, "errorCode": "GPS02", "errorMessage": "位置情報が無効です"}, {"errorStatus": 404, "errorCode": "HOL01", "errorMessage": "休日が存在しません"}, {"errorStatus": 500, "errorCode": "HOL02", "errorMessage": "休日の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "HOL03", "errorMessage": "休日の更新に失敗しました"}, {"errorStatus": 404, "errorCode": "URF01", "errorMessage": "機能的役割が存在しません"}, {"errorStatus": 500, "errorCode": "URF02", "errorMessage": "機能的役割の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "URF03", "errorMessage": "機能的役割の更新に失敗しました"}, {"errorStatus": 404, "errorCode": "FNC01", "errorMessage": "機能が存在しません"}, {"errorStatus": 500, "errorCode": "FNC02", "errorMessage": "機能の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "FNC03", "errorMessage": "機能の更新に失敗しました"}, {"errorStatus": 500, "errorCode": "HST01", "errorMessage": "追跡履歴の取得に失敗しました"}, {"errorStatus": 500, "errorCode": "HST02", "errorMessage": "追跡履歴の作成に失敗しました"}, {"errorStatus": 404, "errorCode": "MIO01", "errorMessage": "バケットが存在しません"}, {"errorStatus": 404, "errorCode": "MIO02", "errorMessage": "オブジェクトが存在しません"}, {"errorStatus": 404, "errorCode": "MIO03", "errorMessage": "オブジェクトのメタデータが存在しません"}, {"errorStatus": 500, "errorCode": "MIO04", "errorMessage": "オブジェクトのアップロードに失敗しました"}, {"errorStatus": 500, "errorCode": "MIO05", "errorMessage": "オブジェクトのダウンロードに失敗しました"}, {"errorStatus": 500, "errorCode": "MIO06", "errorMessage": "オブジェクトの作成に失敗しました"}, {"errorStatus": 500, "errorCode": "MIO07", "errorMessage": "オブジェクトの削除に失敗しました"}, {"errorStatus": 500, "errorCode": "RPT01", "errorMessage": "出勤レポートの作成に失敗しました"}, {"errorStatus": 500, "errorCode": "RPT02", "errorMessage": "出勤レポートの更新に失敗しました"}, {"errorStatus": 404, "errorCode": "RPT03", "errorMessage": "出勤レポートが存在しません"}, {"errorStatus": 409, "errorCode": "RPT04", "errorMessage": "出勤レポートは既に承認されています、更新できません"}, {"errorStatus": 409, "errorCode": "RPT05", "errorMessage": "プロジェクトの日報がすでに存在します"}, {"errorStatus": 404, "errorCode": "RPT06", "errorMessage": "プロジェクトの日報が存在しません"}, {"errorStatus": 500, "errorCode": "RPT07", "errorMessage": "プロジェクトの日報の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "RPT08", "errorMessage": "プロジェクトの日報の更新に失敗しました"}, {"errorStatus": 500, "errorCode": "RPT09", "errorMessage": "プロジェクトの日報の削除に失敗しました"}, {"errorStatus": 404, "errorCode": "CTG01", "errorMessage": "カテゴリが存在しません"}, {"errorStatus": 500, "errorCode": "CTG02", "errorMessage": "カテゴリの作成に失敗しました"}, {"errorStatus": 500, "errorCode": "CTG03", "errorMessage": "カテゴリの更新に失敗しました"}, {"errorStatus": 500, "errorCode": "CTG04", "errorMessage": "カテゴリの削除に失敗しました"}, {"errorStatus": 404, "errorCode": "VDR01", "errorMessage": "ベンダーが存在しません"}, {"errorStatus": 500, "errorCode": "VDR02", "errorMessage": "ベンダーの作成に失敗しました"}, {"errorStatus": 500, "errorCode": "VDR03", "errorMessage": "ベンダーの更新に失敗しました"}, {"errorStatus": 500, "errorCode": "VDR04", "errorMessage": "ベンダーの削除に失敗しました"}, {"errorStatus": 404, "errorCode": "VDR05", "errorMessage": "ベンダーのロゴが存在しません"}, {"errorStatus": 400, "errorCode": "VDR06", "errorMessage": "ベンダーの入力データが無効です"}, {"errorStatus": 404, "errorCode": "PRS01", "errorMessage": "プロセスが存在しません"}, {"errorStatus": 500, "errorCode": "PRS02", "errorMessage": "プロセスの作成に失敗しました"}, {"errorStatus": 500, "errorCode": "PRS03", "errorMessage": "プロセスの更新に失敗しました"}, {"errorStatus": 404, "errorCode": "IT01", "errorMessage": "アイテムが存在しません"}, {"errorStatus": 500, "errorCode": "IT02", "errorMessage": "アイテムの作成に失敗しました"}, {"errorStatus": 500, "errorCode": "IT03", "errorMessage": "アイテムの更新に失敗しました"}, {"errorStatus": 500, "errorCode": "IT04", "errorMessage": "アイテムの削除に失敗しました"}, {"errorStatus": 404, "errorCode": "IT05", "errorMessage": "アイテムのイメージが存在しません"}, {"errorStatus": 404, "errorCode": "ITP01", "errorMessage": "アイテム価格が存在しません"}, {"errorStatus": 500, "errorCode": "ITP02", "errorMessage": "アイテム価格の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "ITP03", "errorMessage": "アイテム価格の更新に失敗しました"}, {"errorStatus": 404, "errorCode": "IPC01", "errorMessage": "入力コストが存在しません"}, {"errorStatus": 500, "errorCode": "IPC02", "errorMessage": "入力コストの作成に失敗しました"}, {"errorStatus": 500, "errorCode": "IPC03", "errorMessage": "入力コストの更新に失敗しました"}, {"errorStatus": 500, "errorCode": "IPC04", "errorMessage": "入力コストの削除に失敗しました"}, {"errorStatus": 404, "errorCode": "ET01", "errorMessage": "エントリ種別が存在しません"}, {"errorStatus": 500, "errorCode": "ET02", "errorMessage": "エントリ種別の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "ET03", "errorMessage": "エントリ種別の更新に失敗しました"}, {"errorStatus": 500, "errorCode": "ET04", "errorMessage": "エントリ種別の削除に失敗しました"}, {"errorStatus": 404, "errorCode": "MFTR01", "errorMessage": "メーカーが存在しません"}, {"errorStatus": 500, "errorCode": "MFTR02", "errorMessage": "メーカーの作成が失敗しました"}, {"errorStatus": 500, "errorCode": "MFTR03", "errorMessage": "メーカーの更新に失敗しました"}, {"errorStatus": 500, "errorCode": "MFTR04", "errorMessage": "メーカーの削除に失敗しました"}, {"errorStatus": 404, "errorCode": "MFTR05", "errorMessage": "メーカーのロゴが存在しません"}, {"errorStatus": 404, "errorCode": "IPCI01", "errorMessage": "入力コストアイテムが存在しません"}, {"errorStatus": 500, "errorCode": "IPCI02", "errorMessage": "入力コストアイテムの作成が失敗しました"}, {"errorStatus": 500, "errorCode": "IPCI03", "errorMessage": "入力コストアイテムの更新に失敗しました"}, {"errorStatus": 500, "errorCode": "IPCI04", "errorMessage": "入力コストアイテムの削除に失敗しました"}, {"errorStatus": 404, "errorCode": "PMT01", "errorMessage": "支払いタイプが存在しません"}, {"errorStatus": 500, "errorCode": "PMT02", "errorMessage": "支払いタイプの作成に失敗しました"}, {"errorStatus": 500, "errorCode": "PMT03", "errorMessage": "支払いタイプの更新に失敗しました"}, {"errorStatus": 500, "errorCode": "PMT04", "errorMessage": "支払いタイプの削除に失敗しました"}, {"errorStatus": 409, "errorCode": "AC01", "errorMessage": "アカウントは既に存在します"}, {"errorStatus": 404, "errorCode": "AC02", "errorMessage": "アカウントが存在しません"}, {"errorStatus": 500, "errorCode": "AC03", "errorMessage": "アカウントの作成に失敗しました"}, {"errorStatus": 500, "errorCode": "AC04", "errorMessage": "アカウントの更新に失敗しました"}, {"errorStatus": 500, "errorCode": "AC05", "errorMessage": "アカウントの削除に失敗しました"}, {"errorStatus": 500, "errorCode": "AC06", "errorMessage": "アカウントのロックに失敗しました"}, {"errorStatus": 500, "errorCode": "AC07", "errorMessage": "アカウントのロック解除に失敗しました"}, {"errorStatus": 400, "errorCode": "AC08", "errorMessage": "メールアドレスまたはログインIDは既に存在します"}, {"errorStatus": 404, "errorCode": "USI01", "errorMessage": "ユーザー情報が存在しません"}, {"errorStatus": 409, "errorCode": "USI02", "errorMessage": "ユーザー情報は既に存在します"}, {"errorStatus": 500, "errorCode": "USI03", "errorMessage": "ユーザー情報の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "USI04", "errorMessage": "ユーザー情報の更新に失敗しました"}, {"errorStatus": 500, "errorCode": "USI05", "errorMessage": "ユーザー情報の削除に失敗しました"}, {"errorStatus": 404, "errorCode": "USI06", "errorMessage": "ユーザー情報のアバターが設定されていません"}, {"errorStatus": 500, "errorCode": "USI07", "errorMessage": "ユーザー情報のアバターの更新に失敗しました"}, {"errorStatus": 500, "errorCode": "USI08", "errorMessage": "ユーザー情報のアバターの削除に失敗しました"}, {"errorStatus": 404, "errorCode": "EMP01", "errorMessage": "従業員が存在しません"}, {"errorStatus": 409, "errorCode": "EMP02", "errorMessage": "従業員は既に存在します"}, {"errorStatus": 500, "errorCode": "EMP03", "errorMessage": "従業員の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "EMP04", "errorMessage": "従業員の更新に失敗しました"}, {"errorStatus": 500, "errorCode": "EMP05", "errorMessage": "従業員の削除に失敗しました"}, {"errorStatus": 500, "errorCode": "EMP06", "errorMessage": "従業員の招待に失敗しました"}, {"errorStatus": 404, "errorCode": "EMP07", "errorMessage": "従業員の招待が存在しません"}, {"errorStatus": 500, "errorCode": "EMP08", "errorMessage": "従業員の招待の削除に失敗しました"}, {"errorStatus": 500, "errorCode": "EMP09", "errorMessage": "従業員の招待の承認に失敗しました"}, {"errorStatus": 500, "errorCode": "EMP10", "errorMessage": "従業員の招待の拒否に失敗しました"}, {"errorStatus": 409, "errorCode": "EMP11", "errorMessage": "従業員の招待は既に存在します"}, {"errorStatus": 500, "errorCode": "EMP12", "errorMessage": "組織へのアカウントのリンクに失敗しました"}, {"errorStatus": 500, "errorCode": "FIL01", "errorMessage": "ファイルのアップロードに失敗しました"}, {"errorStatus": 500, "errorCode": "FIL02", "errorMessage": "ファイルのダウンロードに失敗しました"}, {"errorStatus": 500, "errorCode": "FIL03", "errorMessage": "ファイルの削除に失敗しました"}, {"errorStatus": 404, "errorCode": "FIL04", "errorMessage": "ファイルのメタデータが存在しません"}, {"errorStatus": 500, "errorCode": "FIL05", "errorMessage": "ファイルのメタデータの作成に失敗しました"}, {"errorStatus": 500, "errorCode": "FIL06", "errorMessage": "ファイルのメタデータの更新に失敗しました"}, {"errorStatus": 404, "errorCode": "WOS01", "errorMessage": "勤務シフトが存在しません"}, {"errorStatus": 500, "errorCode": "WOS02", "errorMessage": "勤務シフトの作成に失敗しました"}, {"errorStatus": 500, "errorCode": "WOS03", "errorMessage": "勤務シフトの更新に失敗しました"}, {"errorStatus": 500, "errorCode": "WOS04", "errorMessage": "勤務シフトの削除に失敗しました"}, {"errorStatus": 500, "errorCode": "WOS05", "errorMessage": "勤務シフトの割り当てに失敗しました"}, {"errorStatus": 400, "errorCode": "WOS06", "errorMessage": "休憩時間は勤務時間内でなければなりません"}, {"errorStatus": 400, "errorCode": "WOS07", "errorMessage": "勤務シフトが割り当てられていません"}, {"errorStatus": 404, "errorCode": "EVC01", "errorMessage": "イベントカレンダーが存在しません"}, {"errorStatus": 500, "errorCode": "EVC02", "errorMessage": "イベントカレンダーの作成に失敗しました"}, {"errorStatus": 500, "errorCode": "EVC03", "errorMessage": "イベントカレンダーの更新に失敗しました"}, {"errorStatus": 500, "errorCode": "EVC04", "errorMessage": "イベントカレンダーの削除に失敗しました"}, {"errorStatus": 400, "errorCode": "EVC05", "errorMessage": "イベントカレンダーの日付が無効です"}, {"errorStatus": 409, "errorCode": "EVC06", "errorMessage": "イベントカレンダーは既に存在します"}, {"errorStatus": 404, "errorCode": "UNT01", "errorMessage": "単位が存在しません"}, {"errorStatus": 500, "errorCode": "UNT02", "errorMessage": "単位の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "UNT03", "errorMessage": "単位の更新に失敗しました"}, {"errorStatus": 500, "errorCode": "UNT04", "errorMessage": "単位の削除に失敗しました"}, {"errorStatus": 404, "errorCode": "OS01", "errorMessage": "外部委託が存在しません"}, {"errorStatus": 500, "errorCode": "OS02", "errorMessage": "外部委託の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "OS03", "errorMessage": "外部委託の更新に失敗しました"}, {"errorStatus": 500, "errorCode": "OS04", "errorMessage": "外部委託の削除に失敗しました"}, {"errorStatus": 404, "errorCode": "OS05", "errorMessage": "外部委託の価格が存在しません"}, {"errorStatus": 500, "errorCode": "OS06", "errorMessage": "外部委託の価格の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "OS07", "errorMessage": "外部委託の価格の更新に失敗しました"}, {"errorStatus": 404, "errorCode": "OS08", "errorMessage": "外部委託のロゴが見つかりません"}, {"errorStatus": 404, "errorCode": "PRT01", "errorMessage": "プロジェクトタイプが存在しません"}, {"errorStatus": 500, "errorCode": "PRT02", "errorMessage": "プロジェクトタイプの作成に失敗しました"}, {"errorStatus": 500, "errorCode": "PRT03", "errorMessage": "プロジェクトタイプの更新に失敗しました"}, {"errorStatus": 404, "errorCode": "CON01", "errorMessage": "工事が存在しません"}, {"errorStatus": 500, "errorCode": "CON02", "errorMessage": "工事の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "CON03", "errorMessage": "工事の更新に失敗しました"}, {"errorStatus": 500, "errorCode": "CON04", "errorMessage": "工事の削除に失敗しました"}, {"errorStatus": 409, "errorCode": "CON05", "errorMessage": "別工事が存在します"}, {"errorStatus": 404, "errorCode": "CUS01", "errorMessage": "顧客が存在しません"}, {"errorStatus": 500, "errorCode": "CUS02", "errorMessage": "顧客の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "CUS03", "errorMessage": "顧客の更新に失敗しました"}, {"errorStatus": 500, "errorCode": "CUS04", "errorMessage": "顧客の削除に失敗しました"}, {"errorStatus": 404, "errorCode": "CUS05", "errorMessage": "顧客のロゴが見つかりません"}, {"errorStatus": 404, "errorCode": "CTR01", "errorMessage": "業者が存在しません"}, {"errorStatus": 500, "errorCode": "CTR02", "errorMessage": "業者の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "CTR03", "errorMessage": "業者の更新に失敗しました"}, {"errorStatus": 500, "errorCode": "CTR04", "errorMessage": "業者の削除に失敗しました"}, {"errorStatus": 404, "errorCode": "CTR05", "errorMessage": "業者のロゴが見つかりません"}, {"errorStatus": 404, "errorCode": "CT01", "errorMessage": "顧客種別が存在しません"}, {"errorStatus": 404, "errorCode": "CONC01", "errorMessage": "工事費用が存在しません"}, {"errorStatus": 500, "errorCode": "CONC02", "errorMessage": "工事費用の作成に失敗しました"}, {"errorStatus": 500, "errorCode": "CONC03", "errorMessage": "工事費用の更新に失敗しました"}, {"errorStatus": 500, "errorCode": "CONC04", "errorMessage": "工事費用の削除に失敗しました"}, {"errorStatus": 404, "errorCode": "AUD01", "errorMessage": "変更履歴が存在しません"}]