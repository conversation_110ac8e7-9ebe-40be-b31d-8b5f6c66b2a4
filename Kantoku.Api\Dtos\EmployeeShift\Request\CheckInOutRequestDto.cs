using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.EmployeeShift.Request;

/// <summary>
/// Used for checkin and checkout, 
/// when user checkin or checkout without scheduled time, need to provide projectId and working location
/// otherwise, provide only gps info
/// </summary>
public class CheckInOutRequestDto
{
    /// <summary>
    /// The latitude coordinate of the user's location
    /// </summary>
    public string? Latitude { get; set; } = "35.76312255859375";

    /// <summary>
    /// The longitude coordinate of the user's location
    /// </summary>
    public string? Longitude { get; set; } = "139.67684936523438";
}