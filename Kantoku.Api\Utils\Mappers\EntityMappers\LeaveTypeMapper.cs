using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Common;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class LeaveTypeMapper
{
    #region Entity to DTO mappings

    public static LeaveTypeResponseDto ToLeaveTypeResponseDto(this LeaveType leaveType, string languageCode)
    {
        if (leaveType == null)
            return new LeaveTypeResponseDto();
            
        var responseDto = new LeaveTypeResponseDto
        {
            LeaveTypeId = leaveType.LeaveTypeCode, // Map LeaveTypeCode to both Id and Code
            LeaveTypeCode = leaveType.LeaveTypeCode,
            LeaveTypeName = leaveType.TranslatedLeaveType?
                .FirstOrDefault(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase))
                ?.LeaveTypeName,
            IsSelf = leaveType.IsSelf,
            IsPaid = leaveType.IsPaid
            // Description is not directly in the entity model
        };
        
        return responseDto;
    }

    // Add mapping for collection 
    public static IEnumerable<LeaveTypeResponseDto> ToLeaveTypeResponseDtos(this IEnumerable<LeaveType> leaveTypes, string languageCode)
    {
        return leaveTypes?.Select(lt => lt.ToLeaveTypeResponseDto(languageCode)) ?? Enumerable.Empty<LeaveTypeResponseDto>();
    }

    // Map to LeaveTypesResponseDto with pagination info
    public static LeaveTypesResponseDto ToLeaveTypesResponseDto(this IEnumerable<LeaveType> leaveTypes, string languageCode, int pageIndex = 1, int pageSize = 0)
    {
        var items = leaveTypes.ToLeaveTypeResponseDtos(languageCode);
        var count = items.Count();
        
        return new LeaveTypesResponseDto
        {
            Items = items,
            PageIndex = pageIndex,
            PageSize = pageSize > 0 ? pageSize : count,
            TotalRow = count,
            PageCount = pageSize > 0 ? (int)Math.Ceiling(count / (double)pageSize) : 1
        };
    }

    #endregion
} 