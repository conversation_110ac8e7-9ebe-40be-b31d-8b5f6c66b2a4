using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.EmployeeRequest.Request;

public class UpdateEmployeeRequestRequestDto
{   /// <summary>
    /// The employee who submitted the request
    /// </summary>
    public string? RequestFrom { get; set; }

    /// <summary>
    /// The employee who will receive/approve the request
    /// </summary>
    public string? RequestTo { get; set; }

    /// <summary>
    /// The type code of the request (e.g. leave, overtime)
    /// </summary>
    public string? RequestTypeCode { get; set; }

    /// <summary>
    /// The type code of the leave (e.g. annual leave, sick leave)
    /// </summary>
    public string? LeaveTypeCode { get; set; }

    /// <summary>
    /// The project identifier associated with the request
    /// </summary>
    public string? ProjectId { get; set; }

    /// <summary>
    /// Additional details or reason for the request
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Is user request
    /// Whether the request is a user request
    /// </summary>
    public bool? IsUserRequestedLeave { get; set; }
}

public class DurationUpdateRequestDto
{
    /// <summary>
    /// The number of days requested
    /// </summary>
    public float? Days { get; set; }

    /// <summary>
    /// The number of hours requested
    /// </summary>
    public float? Hours { get; set; }

    /// <summary>
    /// The number of minutes requested
    /// </summary>
    public float? Minutes { get; set; }
}