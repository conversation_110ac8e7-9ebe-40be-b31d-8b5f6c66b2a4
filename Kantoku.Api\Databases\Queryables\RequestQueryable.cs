using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class RequestQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedAuthor { get; set; } = false;
    public bool IncludedApprover1 { get; set; } = false;
    public bool IncludedApprover2 { get; set; } = false;
    public bool IncludedProject { get; set; } = false;

    public RequestQueryableOptions TrackingOptions()
    {
        IsTracking = true;
        return this;
    }

    public RequestQueryableOptions SplitQueryOptions()
    {
        IsSplitQuery = true;
        return this;
    }
}

public interface IRequestQueryable
{
    IQueryable<Request> GetRequestQuery(
        RequestQueryableOptions options
    );
    IQueryable<Request> GetRequestQueryIncluded(
        RequestQueryableOptions options,
        IQueryable<Request>? query = null
    );
    IQueryable<Request> GetRequestQueryFiltered(
        RequestFilter filter,
        RequestQueryableOptions options,
        IQueryable<Request>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class RequestQueryable(PostgreDbContext context) :
    BaseQueryable<Request>(context), IRequestQueryable
{
    public IQueryable<Request> GetRequestQuery(
        RequestQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(r => r.IsDeleted == false);
        return query;
    }

    public IQueryable<Request> GetRequestQueryIncluded(
        RequestQueryableOptions options,
        IQueryable<Request>? query = null
    )
    {
        query ??= GetRequestQuery(options);
        query = query.Include(r => r.RequestType);
        query = query.Include(r => r.Status);
        if (options.IncludedAuthor || options.IncludedAll)
        {
            query = query.Include(r => r.Author);
        }
        if (options.IncludedApprover1 || options.IncludedAll)
        {
            query = query.Include(r => r.Approver1);
        }
        if (options.IncludedApprover2 || options.IncludedAll)
        {
            query = query.Include(r => r.Approver2);
        }
        if (options.IncludedProject || options.IncludedAll)
        {
            query = query.Include(r => r.Project)
                .ThenInclude(p => p.Managers)
                .ThenInclude(pm => pm.Employee);
        }
        return query;
    }

    public IQueryable<Request> GetRequestQueryFiltered(
        RequestFilter filter,
        RequestQueryableOptions options,
        IQueryable<Request>? query = null
    )
    {
        query ??= GetRequestQueryIncluded(options);
        if (DateTime.TryParse(filter.FromDate, out DateTime fromDate))
        {
            query = query.Where(r => r.RequestFrom >= fromDate);
        }
        if (DateTime.TryParse(filter.ToDate, out DateTime toDate))
        {
            query = query.Where(r => r.RequestTo <= toDate);
        }
        if (!string.IsNullOrEmpty(filter.StatusCode))
        {
            query = query.Where(r => string.Equals(r.StatusCode, filter.StatusCode));
        }
        if (!string.IsNullOrEmpty(filter.RequestTypeCode))
        {
            query = query.Where(r => string.Equals(r.RequestTypeCode, filter.RequestTypeCode));
        }
        if (filter.IsUserRequestedLeave.HasValue)
        {
            query = query.Where(r => r.IsUserRequestedLeave == filter.IsUserRequestedLeave);
        }
        if (filter.StatusCodes != null && filter.StatusCodes.Any())
        {
            query = query.Where(r => filter.StatusCodes.Any(sc => string.Equals(r.StatusCode, sc)));
        }
        if (filter.RequestTypeCodes != null && filter.RequestTypeCodes.Any())
        {
            query = query.Where(r => filter.RequestTypeCodes.Any(rt => string.Equals(r.RequestTypeCode, rt)));
        }
        return query;
    }
}

