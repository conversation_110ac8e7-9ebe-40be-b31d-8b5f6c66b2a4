namespace Kantoku.Api.Dtos.EventCalendar.Response;

public class EventCalendarRulesResponseDto
{
    public IEnumerable<EventCalendarRuleDto> EventCalendarRules { get; set; } = [];
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalRecords { get; set; }
}

public class EventCalendarRuleDto
{
    public string? EventId { get; set; }

    public string? EventName { get; set; }

    /// <summary>
    /// Event start date (YYYY-MM-DD)   
    /// </summary>
    public string? EventStartDate { get; set; }

    /// <summary>
    /// Event end date (YYYY-MM-DD)
    /// </summary>
    public string? EventEndDate { get; set; }

    /// <summary>
    /// Event start time (HH:MM:SS)
    /// </summary>
    public string? EventStartTime { get; set; }

    /// <summary>
    /// Event end time (HH:MM:SS)
    /// </summary>
    public string? EventEndTime { get; set; }

    /// <summary>
    /// true: recurring, false: not recurring
    /// </summary>
    public bool IsRecurring { get; set; }

    /// <summary>
    /// Recurring type: DAILY, WEEKLY, MONTHLY, YEARLY 
    /// </summary>
    public string? RecurringType { get; set; }

    /// <summary>
    /// Recurring start date (YYYY-MM-DD)
    /// </summary>
    public string? RecurringFrom { get; set; }

    /// <summary>
    /// Recurring end date (YYYY-MM-DD)
    /// </summary>
    public string? RecurringTo { get; set; }

    /// <summary>
    /// only if RecurringType is WEEKLY
    /// for example: [1, 2, 3, 4, 5, 6, 7] as day of week 
    /// 1: Monday, 2: Tuesday, 3: Wednesday, 4: Thursday, 5: Friday, 6: Saturday, 7: Sunday
    /// </summary>
    public ICollection<int> RecurringDay { get; set; } = [];

    /// <summary>
    /// only if RecurringType is WEEKLY
    /// for example: [1, 2, 3, 4] as week of month
    /// 1: first week, 2: second week, 3: third week, 4: fourth week
    /// </summary>
    public ICollection<int> RecurringWeek { get; set; } = [];

    /// <summary>
    /// only if RecurringType is MONTHLY
    /// for example: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12] as month of year
    /// 1: January, 2: February, 3: March, 4: April, 5: May, 6: June, 7: July, 8: August, 9: September, 10: October, 11: November, 12: December
    /// </summary>
    public ICollection<int> RecurringMonth { get; set; } = [];

    /// <summary>
    /// Event description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// true: day off, false: not day off
    /// </summary>
    public bool IsDayOff { get; set; }
}
