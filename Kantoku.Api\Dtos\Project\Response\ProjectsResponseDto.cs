using Kantoku.Api.Dtos.Base.Response;
using Kantoku.Api.Dtos.WorkShift.Response;

namespace Kantoku.Api.Dtos.Project.Response;

public class ProjectsResponseDto
{
    public IEnumerable<ProjectResponseDto> Items { get; set; } = [];
    public int PageNum { get; set; }
    public int PageSize { get; set; }
    public int TotalRecords { get; set; }
}

public class ProjectResponseDto : BaseResponseDto
{
    public string? ProjectId { get; set; }

    public string? ProjectCode { get; set; }

    public string? ProjectName { get; set; }

    public string? ProjectTypeId { get; set; }

    public string? ProjectTypeName { get; set; }

    public string? CustomerId { get; set; }

    public string? CustomerName { get; set; }

    public string? ContractorId { get; set; }

    public string? ContractorName { get; set; }

    public string? Address { get; set; }

    public IEnumerable<ProjectManagerResponseDto>? ManagersInfo { get; set; }

    public IEnumerable<WorkShiftResponseDto>? WorkShifts { get; set; }

    public string? ExpectedStartDate { get; set; }

    public string? ExpectedEndDate { get; set; }

    public string? ActualStartDate { get; set; }

    public string? ActualEndDate { get; set; }

    public float? InitialBudget { get; set; }

    public float? ActualBudget { get; set; }

    public string? Description { get; set; }

    public string? StatusCode { get; set; }

    public string? StatusName { get; set; }
}