using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Notification;
using Kantoku.Api.Utils.Helpers;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Dtos.Notification.Request;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.Notification.Response;
using Kantoku.Api.Dtos.Device.Request;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public interface INotificationService
{
    Task<ResultDto<Guid>> CreateNotification(CreateNotificationRequestDto notificationDto);
    Task<ResultDto<bool>> UpdateNotification(Guid notificationId, UpdateNotificationRequestDto notificationDto);
    Task<ResultDto<bool>> DeleteNotification(Guid notificationId);
    Task<ResultDto<bool>> PublishNotification(Guid notificationId, Guid targetId);
    Task<ResultDto<bool>> PublishNotification(string token, NotificationDto notification);

    Task<ResultDto<NotificationsResponseDto>> GetByFilter(OrgNotificationFilter filter);
    Task<ResultDto<EmployeeNotificationsResponseDto>> GetEmployeeNotifications(EmployeeNotificationFilter filter);
    Task<ResultDto<bool>> MarkNotificationAsRead(Guid notificationId);
    Task<ResultDto<bool>> MarkAllNotificationsAsRead();
    Task<ResultDto<bool>> MarkNotificationsAsUnread(Guid notificationId);

    Task<ResultDto<bool>> RegisterDeviceToken(DeviceRequestDto dto);
    Task<ResultDto<bool>> UnregisterDeviceToken(string firebaseToken);
}

[Service(ServiceLifetime.Scoped)]
public class NotificationService : BaseService<NotificationService>, INotificationService
{
    private readonly INotificationRepository notificationRepository;
    private readonly IEmployeeNotificationRepository employeeNotificationRepository;
    private readonly IDeviceTokenRepository deviceTokenRepository;
    private readonly IEmployeeRepository employeeRepository;
    private readonly FirebaseService firebaseService;

    public NotificationService(
        INotificationRepository notificationRepository,
        IEmployeeNotificationRepository employeeNotificationRepository,
        IDeviceTokenRepository deviceTokenRepository,
        FirebaseService firebaseService,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor,
        IEmployeeRepository employeeRepository)
        : base(logger, httpContextAccessor)
    {
        this.notificationRepository = notificationRepository;
        this.employeeNotificationRepository = employeeNotificationRepository;
        this.deviceTokenRepository = deviceTokenRepository;
        this.firebaseService = firebaseService;
        this.employeeRepository = employeeRepository;
    }

    public async Task<ResultDto<Guid>> CreateNotification(CreateNotificationRequestDto requestDto)
    {
        if (!GetCurrentOrgGuid(out var orgId))
        {
            logger.Error("Failed to get current org id");
            return new ErrorResultDto<Guid>("Failed to get current org id");
        }
        var newNotification = requestDto.ToEntity(orgId);
        if (newNotification is null)
        {
            logger.Error("Failed to create notification");
            return new ErrorResultDto<Guid>("Failed to create notification");
        }
        var createdId = await notificationRepository.Create(newNotification);
        if (createdId is null)
        {
            logger.Error("Failed to create notification");
            return new ErrorResultDto<Guid>("Failed to create notification");
        }
        return new SuccessResultDto<Guid>(createdId.Value);
    }

    public async Task<ResultDto<bool>> UpdateNotification(Guid notificationId, UpdateNotificationRequestDto requestDto)
    {
        var existNotification = await notificationRepository.GetById(notificationId, new NotificationQueryableOptions
        {
            IncludedNotificationTargets = true,
            IsTracking = true,
        });
        if (existNotification == null)
        {
            logger.Error("Notification not found: {NotificationId}", notificationId);
            return new ErrorResultDto<bool>("Notification not found");
        }
        existNotification.UpdateFromDto(requestDto);

        var isNotificationUpdated = await notificationRepository.Update(existNotification);
        if (!isNotificationUpdated)
        {
            logger.Error("Failed to update notification: {NotificationId}", notificationId);
            return new ErrorResultDto<bool>("Failed to update notification");
        }
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> DeleteNotification(Guid notificationId)
    {
        var existingNotification = await notificationRepository.GetById(notificationId, new NotificationQueryableOptions());
        if (existingNotification == null)
        {
            logger.Error("Notification not found: {NotificationId}", notificationId);
            return new ErrorResultDto<bool>("Notification not found");
        }
        var isDeleted = await notificationRepository.Delete(existingNotification);
        if (!isDeleted)
        {
            logger.Error("Failed to delete notification: {NotificationId}", notificationId);
            return new ErrorResultDto<bool>("Failed to delete notification");
        }
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> PublishNotification(Guid notificationId, Guid targetId)
    {
        var existingNotification = await notificationRepository.GetById(notificationId, new NotificationQueryableOptions
        {
            IncludedNotificationTargets = true,
        });
        if (existingNotification == null)
        {
            logger.Error("Notification not found: {NotificationId}", notificationId);
            return new ErrorResultDto<bool>("Notification not found");
        }

        var target = existingNotification.NotificationTargets.FirstOrDefault(t => t.NotificationTargetUid == targetId);
        if (target == null)
        {
            logger.Error("Target not found: {NotificationId}", notificationId);
            return new ErrorResultDto<bool>("Target not found");
        }

        switch (target.TargetType)
        {
            case TargetTypeConstant.INDIVIDUAL:
                return await SendNotificationToEmployee(target.TargetIds, target);
            case TargetTypeConstant.ROLE:
                return await SendNotificationByRole(target.TargetIds, target);
            case TargetTypeConstant.ALL:
                return await SendNotificationToAllEmployees(target);
            default:
                logger.Error("Unsupported target type: {NotificationId}", notificationId);
                return new ErrorResultDto<bool>("Unsupported target type");
        }
    }

    public async Task<ResultDto<bool>> PublishNotification(string token, NotificationDto notification)
    {
        try
        {
            var response = await firebaseService.SendToDevice(token, notification);

            return new SuccessResultDto<bool>(true);
        }
        catch (Exception)
        {
            return new ErrorResultDto<bool>("Failed to send notification to employee");
        }
    }

    // Send notification to specific employee
    private async Task<ResultDto<bool>> SendNotificationToEmployee(ICollection<Guid> employeeIds, NotificationTarget notificationTarget)
    {
        var newEmployeeNotifications = employeeIds.Select(id => new EmployeeNotification
        {
            EmployeeNotificationUid = GuidHelper.GenerateUUIDv7(),
            NotificationUid = notificationTarget.NotificationUid,
            EmployeeUid = id,
            IsRead = false,
            IsDeleted = false,
        }).ToList();
        await employeeNotificationRepository.Create(newEmployeeNotifications);

        var devices = await deviceTokenRepository.GetByEmployeeIds(employeeIds);

        if (!devices.Any())
        {
            notificationTarget.PublishStatus = NotificationStatusConstant.FAILED;
            await notificationRepository.UpdateTarget(notificationTarget);
            return new ErrorResultDto<bool>("No devices found");
        }
        try
        {
            var notificationPayload = new NotificationDto
            {
                Title = notificationTarget.Notification.Title,
                Body = notificationTarget.Notification.Body,
            };

            var deviceTokens = devices
                .Where(device => !string.IsNullOrEmpty(device.FirebaseToken))
                .Select(device => device.FirebaseToken!)
                .ToList();
            var response = await firebaseService.SendToDevices(deviceTokens, notificationPayload);

            notificationTarget.PublishAt = DateTime.Now;
            notificationTarget.PublishStatus = response.SuccessCount == deviceTokens.Count
                ? NotificationStatusConstant.PUBLISHED
                : response.SuccessCount > 0
                    ? NotificationStatusConstant.PARTIALLY_FAILED
                    : NotificationStatusConstant.FAILED;


            await notificationRepository.UpdateTarget(notificationTarget);
            return new SuccessResultDto<bool>(true);
        }
        catch (Exception)
        {
            notificationTarget.PublishStatus = NotificationStatusConstant.FAILED;
            await notificationRepository.UpdateTarget(notificationTarget);
            return new ErrorResultDto<bool>("Failed to send notification to employee");
        }
    }

    // Send notification to employees by role
    private async Task<ResultDto<bool>> SendNotificationByRole(ICollection<Guid> roleIds, NotificationTarget notificationTarget)
    {
        var employees = await employeeRepository.GetByRoleId(roleIds, new EmployeeQueryableOptions());
        if (!employees.Any())
        {
            notificationTarget.PublishStatus = NotificationStatusConstant.FAILED;
            await notificationRepository.UpdateTarget(notificationTarget);
            return new ErrorResultDto<bool>("No employees found");
        }

        var employeeIds = employees.Select(e => e.EmployeeUid).ToList();
        return await SendNotificationToEmployee(employeeIds, notificationTarget);
    }

    // Send notification to all employees
    private async Task<ResultDto<bool>> SendNotificationToAllEmployees(NotificationTarget notificationTarget)
    {
        // Get employee's device tokens
        var devices = await deviceTokenRepository.GetByOrgId(notificationTarget.Notification.OrgUid);

        if (!devices.Any())
        {
            notificationTarget.PublishStatus = NotificationStatusConstant.FAILED;
            await notificationRepository.UpdateTarget(notificationTarget);
            return new ErrorResultDto<bool>("No devices found");
        }

        try
        {
            var notificationPayload = new NotificationDto
            {
                Title = notificationTarget.Notification.Title,
                Body = notificationTarget.Notification.Body,
            };

            var deviceTokens = devices
                .Where(device => !string.IsNullOrEmpty(device.FirebaseToken))
                .Select(device => device.FirebaseToken!)
                .ToList();

            var response = await firebaseService.SendToDevices(deviceTokens, notificationPayload);

            notificationTarget.PublishAt = DateTime.Now;
            notificationTarget.PublishStatus = response.SuccessCount == deviceTokens.Count
                ? NotificationStatusConstant.PUBLISHED
                : response.SuccessCount > 0
                    ? NotificationStatusConstant.PARTIALLY_FAILED
                    : NotificationStatusConstant.FAILED;

            var newEmployeeNotifications = devices.Select(device => new EmployeeNotification
            {
                EmployeeNotificationUid = GuidHelper.GenerateUUIDv7(),
                NotificationUid = notificationTarget.NotificationUid,
                EmployeeUid = device.EmployeeUid,
                IsRead = false,
                IsDeleted = false,
            }).ToList();
            await employeeNotificationRepository.Create(newEmployeeNotifications);

            await notificationRepository.UpdateTarget(notificationTarget);
            return new SuccessResultDto<bool>(true);
        }
        catch (Exception)
        {
            notificationTarget.PublishStatus = NotificationStatusConstant.FAILED;
            await notificationRepository.UpdateTarget(notificationTarget);
            return new ErrorResultDto<bool>("Failed to send notification to all employees");
        }
    }

    public async Task<ResultDto<EmployeeNotificationsResponseDto>> GetEmployeeNotifications(EmployeeNotificationFilter filter)
    {
        if (!GetCurrentEmployeeGuid(out var employeeId))
        {
            logger.Error("Failed to get current employee id");
            return new ErrorResultDto<EmployeeNotificationsResponseDto>("Failed to get current employee id");
        }
        var (employeeNotifications, total) = await employeeNotificationRepository
            .GetByFilter(employeeId, filter, new EmployeeNotificationQueryableOptions
            {
                IncludedNotification = true,
            });
        if (!employeeNotifications.Any())
        {
            logger.Error("No employee notifications found");
            return new ErrorResultDto<EmployeeNotificationsResponseDto>("No employee notifications found");
        }
        var result = new EmployeeNotificationsResponseDto
        {
            Items = employeeNotifications.Select(n => new EmployeeNotificationResponseDto
            {
                NotificationId = n.NotificationUid.ToString(),
                Title = n.Notification.Title,
                Body = n.Notification.Body,
                IsRead = n.IsRead,
                CreatedTime = n.Notification.CreatedTime.ToString(),
            }).ToList(),
            TotalRecords = total,
            PageNum = filter.PageNum,
            PageSize = filter.PageSize,
        };
        return new SuccessResultDto<EmployeeNotificationsResponseDto>(result);
    }

    public async Task<ResultDto<NotificationsResponseDto>> GetByFilter(OrgNotificationFilter filter)
    {
        var (notifications, total) = await notificationRepository.GetByFilter(filter, new NotificationQueryableOptions
        {
            IncludedNotificationTargets = true,
        });
        if (!notifications.Any())
        {
            logger.Error("No notifications found");
            return new ErrorResultDto<NotificationsResponseDto>("No notifications found");
        }
        var result = new NotificationsResponseDto
        {
            Items = notifications.Select(n => new NotificationResponseDto
            {
                NotificationId = n.NotificationUid.ToString(),
                Title = n.Title,
                Body = n.Body,
                NotificationType = n.NotificationType,
                Targets = n.NotificationTargets.Select(t => new NotificationTargetResponseDto
                {
                    NotificationTargetUid = t.NotificationTargetUid.ToString(),
                    TargetType = t.TargetType,
                    TargetIds = t.TargetIds,
                    PublishStatus = t.PublishStatus,
                    PublishAt = t.PublishAt,
                }).ToList(),
                CreatedTime = n.CreatedTime.ToString(),
            }).ToList(),
            TotalRecords = total,
            PageNum = filter.PageNum,
            PageSize = filter.PageSize,
        };
        return new SuccessResultDto<NotificationsResponseDto>(result);
    }

    public async Task<ResultDto<bool>> MarkNotificationAsRead(Guid notificationId)
    {
        var existingNotification = await employeeNotificationRepository.GetById(notificationId, new EmployeeNotificationQueryableOptions());
        if (existingNotification == null)
        {
            logger.Error("Notification not found: {NotificationId}", notificationId);
            return new ErrorResultDto<bool>("Notification not found");
        }

        existingNotification.IsRead = true;
        await employeeNotificationRepository.Update(existingNotification);
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> MarkAllNotificationsAsRead()
    {
        if (!GetCurrentEmployeeGuid(out var employeeId))
        {
            logger.Error("Failed to get current employee id");
            return new ErrorResultDto<bool>("Failed to get current employee id");
        }
        var existingNotifications = await employeeNotificationRepository.GetByEmployeeId(employeeId, new EmployeeNotificationQueryableOptions());
        if (!existingNotifications.Where(n => n.IsRead == false).Any())
        {
            logger.Error("No notifications found");
            return new ErrorResultDto<bool>("No notifications found");
        }

        foreach (var notification in existingNotifications)
        {
            notification.IsRead = true;
        }
        await employeeNotificationRepository.UpdateRange(existingNotifications);

        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> MarkNotificationsAsUnread(Guid notificationId)
    {
        var existingNotification = await employeeNotificationRepository.GetById(notificationId, new EmployeeNotificationQueryableOptions());
        if (existingNotification == null)
        {
            logger.Error("Notification not found: {NotificationId}", notificationId);
            return new ErrorResultDto<bool>("Notification not found");
        }

        existingNotification.IsRead = false;
        await employeeNotificationRepository.Update(existingNotification);
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> RegisterDeviceToken(DeviceRequestDto dto)
    {
        var existingDevice = await deviceTokenRepository.GetByFirebaseToken(dto.FirebaseToken);
        if (existingDevice != null)
        {
            logger.Error("Device token already exists: {FirebaseToken}", dto.FirebaseToken);
            return new SuccessResultDto<bool>(true);
        }

        var employeeUid = GetCurrentEmployeeUid();
        var newDevice = new DeviceToken
        {
            DeviceTokenUid = GuidHelper.GenerateUUIDv7(),
            Platform = dto.Platform,
            DeviceId = dto.DeviceId,
            OsVersion = dto.OsVersion,
            LastActive = DateTime.Now,
            AppVersion = dto.AppVersion,
            FirebaseToken = dto.FirebaseToken,
            EmployeeUid = employeeUid,
        };
        var createdDevice = await deviceTokenRepository.Create(newDevice);
        if (createdDevice == null)
        {
            logger.Error("Failed to register device token: {FirebaseToken}", dto.FirebaseToken);
            return new ErrorResultDto<bool>("Failed to register device token", false);
        }
        return new SuccessResultDto<bool>(true);
    }

    public async Task<ResultDto<bool>> UnregisterDeviceToken(string firebaseToken)
    {
        var existingDevice = await deviceTokenRepository.GetByFirebaseToken(firebaseToken);
        if (existingDevice == null)
        {
            logger.Error("Device token not found: {FirebaseToken}", firebaseToken);
            return new ErrorResultDto<bool>("Device token not found");
        }

        var isDeleted = await deviceTokenRepository.Delete(existingDevice);
        if (!isDeleted)
        {
            logger.Error("Failed to unregister device token: {FirebaseToken}", firebaseToken);
            return new ErrorResultDto<bool>("Failed to unregister device token");
        }
        return new SuccessResultDto<bool>(true);
    }
}
