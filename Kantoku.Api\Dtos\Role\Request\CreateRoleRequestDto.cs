using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.Role.Request;

public class CreateRoleRequestDto
{
    /// <summary>
    /// The name of the role (*) 
    /// </summary>
    [Required]
    public string RoleName { get; set; } = null!;

    /// <summary>
    /// The ID of the structure this role belongs to
    /// </summary>
    public string? StructureId { get; set; }

    /// <summary>
    /// A description of the role's purpose and responsibilities
    /// </summary>
    public string? Description { get; set; }
}
