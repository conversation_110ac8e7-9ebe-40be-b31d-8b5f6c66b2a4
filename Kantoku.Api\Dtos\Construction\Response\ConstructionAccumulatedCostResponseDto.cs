using Kantoku.Api.Dtos.CategorizedCost.Response;

namespace Kantoku.Api.Dtos.Construction.Response;

public class ConstructionsAccumulatedCostResponseDto
{
    /// <summary>
    /// Items of the accumulated cost
    /// </summary>
    public AccumulatedCostItemResponseDto? MainConstructionAccumulatedCost { get; set; }

    /// <summary>
    /// Items of the accumulated cost
    /// </summary>
    public AccumulatedCostItemResponseDto? SubConstructionAccumulatedCost { get; set; }

    /// <summary>
    /// Items of the accumulated cost
    /// </summary>
    public AccumulatedCostItemResponseDto? OverallConstructionAccumulatedCost { get; set; }
}

public class AccumulatedCostItemResponseDto
{
    /// <summary>
    /// Costs broken down by categories
    /// </summary>
    public IEnumerable<CategorizedCostResponseDto> CategorizedCosts { get; set; } = [];

    /// <summary>
    /// Amount allocated for risk management
    /// </summary>
    public long RiskAmount { get; set; } = 0;

    /// <summary>
    /// Total accumulated cost
    /// </summary>
    public long TotalAccumulatedCost { get; set; } = 0;
}
