using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class ProjectRankingCostConfiguration(string schema) : IEntityTypeConfiguration<ProjectRankingCost>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<ProjectRankingCost> builder)
    {
        builder.<PERSON>Key(e => new { e.ProjectUid, e.RankingUid }).HasName("project_ranking_cost_pkey");

        builder.ToTable("project_ranking_cost", Schema);

        builder.Property(e => e.ProjectUid)
            .HasColumnName("project_uid");
        builder.Property(e => e.RankingUid)
            .HasColumnName("ranking_uid");
        builder.Property(e => e.MinValue)
            .HasColumnName("min_value");
        builder.Property(e => e.<PERSON>)
            .HasColumnName("max_value");

        builder.HasOne(d => d.Project).WithMany(p => p.ProjectRankingCosts)
            .HasForeignKey(d => d.ProjectUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("project_ranking_cost_project_id_fkey");

        builder.HasOne(d => d.Ranking).WithMany(p => p.ProjectRankingCosts)
            .HasForeignKey(d => d.RankingUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("project_ranking_cost_ranking_id_fkey");
    }
}

