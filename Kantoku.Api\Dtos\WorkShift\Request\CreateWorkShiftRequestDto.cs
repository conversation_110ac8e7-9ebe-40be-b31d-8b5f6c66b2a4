using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.WorkShift.Request;

public class CreateWorkShiftRequestDto
{
    /// <summary>
    /// Code of the work shift (*)
    /// </summary>
    [Required]
    public string WorkShiftCode { get; set; } = null!;

    /// <summary>
    /// Name of the work shift (*)
    /// </summary>
    [Required]
    public string WorkShiftName { get; set; } = null!;

    /// <summary>
    /// Time when employees should check in (*)
    /// </summary>
    [DateTimeValidator(typeof(TimeOnly))]
    public string CheckInTime { get; set; } = null!;

    /// <summary>
    /// Time when employees should check out (*)
    /// </summary>
    [DateTimeValidator(typeof(TimeOnly))]
    public string CheckOutTime { get; set; } = null!;

    /// <summary>
    /// Additional description for the work shift
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// List of break times during the work shift
    /// </summary>
    [Required]
    public IEnumerable<WorkShiftBreakRequestDto> WorkShiftBreaks { get; set; } = [];
}

public class WorkShiftBreakRequestDto
{
    /// <summary>
    /// Time when break starts (*)
    /// </summary>
    [DateTimeValidator(typeof(TimeOnly))]
    public string BreakInTime { get; set; } = null!;

    /// <summary>
    /// Time when break ends (*)
    /// </summary>
    [DateTimeValidator(typeof(TimeOnly))]
    public string BreakOutTime { get; set; } = null!;
}