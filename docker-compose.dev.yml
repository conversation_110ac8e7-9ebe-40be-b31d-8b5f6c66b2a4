version: '3.4'

services:
  kantoku-api:
    image: kantoku-api-dev
    container_name: kantoku-api-dev
    build:
      context: ./Kantoku.Api
      dockerfile: Dockerfile
      args:
        - configuration=Debug
    ports:
      - 4869:4869
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - TZ=Asia/Tokyo
    volumes:
      - ./Logs:/app/Logs:rw

  kantoku-processor:
    image: kantoku-processor-dev
    container_name: kantoku-processor-dev
    build:
      context: ./Kantoku.Processor
      dockerfile: Dockerfile
      args:
        - configuration=Debug
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - TZ=Asia/Tokyo
    volumes:
      - ./Logs:/app/Logs:rw

  # Zookeeper service for Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: zookeeper-dev
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"
    volumes:
      - zookeeper-data:/var/lib/zookeeper/data
      - zookeeper-logs:/var/lib/zookeeper/log

  # Kafka service
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: kafka-dev
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
      - "9101:9101"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_DELETE_TOPIC_ENABLE: 'true'
    volumes:
      - kafka-data:/var/lib/kafka/data

volumes:
  zookeeper-data:
    driver: local
  zookeeper-logs:
    driver: local
  kafka-data:
    driver: local

