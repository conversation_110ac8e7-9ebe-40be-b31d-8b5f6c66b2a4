﻿using Kantoku.Api.Dtos.Base.Response;

namespace Kantoku.Api.Dtos.PaymentType.Response;

/// <summary>
/// Data transfer object for paginated response of payment types
/// </summary>
public class PaymentTypesResponseDto : BaseResponseDto
{
    /// <summary>
    /// List of payment type response DTOs
    /// </summary>
    public IEnumerable<PaymentTypeResponseDto> Items { get; set; } = [];

    /// <summary>
    /// Current page number (*)
    /// </summary>
    public int PageNum { get; set; }

    /// <summary>
    /// Number of items per page (*)
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of records (*)
    /// </summary>
    public int TotalRecords { get; set; }
}

