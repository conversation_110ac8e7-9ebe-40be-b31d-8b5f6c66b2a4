using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.Schedule.Request;

public class DuplicateScheduledOutSourceShiftsRequestDto
{
    /// <summary>
    /// Target project id (*)   
    /// </summary>
    [Required]
    [JsonPropertyName("projectId")]
    public required Guid ProjectId { get; set; }

    /// <summary>
    /// Working date from (copy to this range of date, start from this date) (*)
    /// Format: yyyy-MM-dd
    /// </summary>
    [Required]
    [JsonPropertyName("workingDateFrom")]
    public DateOnly WorkingDateFrom { get; set; }

    /// <summary>
    /// Working date to (copy to this range of date, end at this date) (*)
    /// Format: yyyy-MM-dd
    /// </summary>
    [Required]
    [JsonPropertyName("workingDateTo")]
    public DateOnly WorkingDateTo { get; set; }
}