﻿using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Dtos.Vendor.Request;
using Kantoku.Api.Dtos.Vendor.Response;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/cost/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class VendorController : BaseController
{
    private readonly IVendorService vendorService;
    private readonly IAuditLogService auditLogService;
    public VendorController(IVendorService vendorService,
        ITResponseFactory responseFactory,
        IAuditLogService auditLogService) : base(responseFactory)
    {
        this.vendorService = vendorService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get vendors by filter
    /// </summary>
    /// <param name="filter">Filter parameters</param>
    /// <returns>List of vendors</returns>
    [HttpGet]
    public async Task<GeneralResponse<VendorsResponseDto>> Get([FromQuery] VendorFilter filter)
    {
        try
        {
            var res = await vendorService.GetByFilter(filter);
            return SuccessFromResult(res);
        }
        catch (BusinessException e)
        {
            return Fail<VendorsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get vendor by ID
    /// </summary>
    /// <param name="id">Vendor ID</param>
    /// <param name="keyword">Keyword to search for (invoice number/title)</param>
    /// <param name="pageNum">Page number of input costs</param>
    /// <param name="pageSize">Page size of input costs</param>
    /// <returns>Vendor details</returns>
    [HttpGet("{id}")]
    public async Task<GeneralResponse<VendorDetailResponseDto>> GetById(
        [FromRoute] Guid id,
        [FromQuery] string? keyword,
        [FromQuery] int pageNum = 1,
        [FromQuery] int pageSize = 10
    )
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<VendorDetailResponseDto>();

            var result = await vendorService.GetById(id, keyword, pageNum, pageSize);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<VendorDetailResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get vendor logo
    /// </summary>
    /// <param name="id">Vendor ID</param>
    /// <param name="orgId">Organization ID</param>
    /// <returns>Vendor logo</returns>
    [AllowAnonymous]
    [HttpGet("{id}/logo")]
    public async Task<dynamic> GetLogo([FromRoute] Guid id, [FromQuery] string orgId)
    {
        try
        {
            var result = await vendorService.GetLogo(id, orgId);
            if (result.Data is null || result.Data.Length == 0)
            {
                return Fail<FileContentResult>(ResponseCodeConstant.VENDOR_LOGO_NOT_EXIST);
            }
            return File(result.Data, "image/jpeg");
        }
        catch (BusinessException e)
        {
            return Fail<FileContentResult>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create new vendor
    /// </summary>
    /// <param name="requestDto">Vendor creation data</param>
    /// <returns>Created vendor details</returns>
    [HttpPost]
    [Consumes("multipart/form-data")]
    [Produces("application/json")]
    public async Task<GeneralResponse<VendorResponseDto>> Create([FromForm] CreateVendorRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<VendorResponseDto>();

            var result = await vendorService.Create(requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<VendorResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update existing vendor
    /// </summary>
    /// <param name="id">Vendor ID</param>
    /// <param name="requestDto">Vendor update data</param>
    /// <returns>Updated vendor details</returns>
    [HttpPut("{id}")]
    [Consumes("multipart/form-data")]
    [Produces("application/json")]
    public async Task<GeneralResponse<VendorResponseDto>> Update([FromRoute] Guid id, [FromForm] UpdateVendorRequestDto requestDto)
    {
        try
        {
            var result = await vendorService.Update(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<VendorResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete vendor
    /// </summary>
    /// <param name="id">Vendor ID</param>
    /// <returns>Success response</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> Delete([FromRoute] Guid id)
    {
        try
        {
            await vendorService.Delete(id);
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for a vendor
    /// </summary>
    /// <param name="id">Vendor ID</param>
    /// <param name="filter">Audit log filter parameters</param>
    /// <returns>List of audit logs</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetAuditLogByEntity([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<Vendor>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}