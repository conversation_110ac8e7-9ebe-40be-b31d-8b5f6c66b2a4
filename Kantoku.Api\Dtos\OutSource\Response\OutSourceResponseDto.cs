using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Base.Response;

namespace Kantoku.Api.Dtos.OutSource.Response;

public class OutSourceResponseDto : BaseResponseDto
{
    /// <summary>
    /// The unique identifier for the outsource
    /// </summary>
    public string? OutSourceId { get; set; }

    /// <summary>
    /// The code assigned to the outsource
    /// </summary>
    public string? OutSourceCode { get; set; }

    /// <summary>
    /// The name of the outsource   
    /// </summary>
    public string? OutSourceName { get; set; }

    /// <summary>
    /// The description of the outsource
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// The expertise associated with the outsource
    /// </summary>
    public ICollection<string>? Expertise { get; set; }

    /// <summary>
    /// Corporate number
    /// </summary>
    public string? CorporateNumber { get; set; }

    /// <summary>
    /// Address
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Phone number
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Email
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Contact person
    /// </summary>
    public ContactPerson? ContactPerson { get; set; }

        /// <summary>
    /// Logo
    /// </summary>
    public string? LogoUrl { get; set; }
}