<?xml version="1.0" encoding="utf-8"?>
<root>
	<!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader><resheader name="version">2.0</resheader><resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader><resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader><data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data><data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data><data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64"><value>[base64 mime encoded serialized .NET Framework object]</value></data><data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64"><value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value><comment>This is a comment</comment></data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
	<xsd:schema id="root"
		xmlns=""
		xmlns:xsd="http://www.w3.org/2001/XMLSchema"
		xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
		<xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
		<xsd:element name="root" msdata:IsDataSet="true">
			<xsd:complexType>
				<xsd:choice maxOccurs="unbounded">
					<xsd:element name="metadata">
						<xsd:complexType>
							<xsd:sequence>
								<xsd:element name="value" type="xsd:string" minOccurs="0" />
							</xsd:sequence>
							<xsd:attribute name="name" use="required" type="xsd:string" />
							<xsd:attribute name="type" type="xsd:string" />
							<xsd:attribute name="mimetype" type="xsd:string" />
							<xsd:attribute ref="xml:space" />
						</xsd:complexType>
					</xsd:element>
					<xsd:element name="assembly">
						<xsd:complexType>
							<xsd:attribute name="alias" type="xsd:string" />
							<xsd:attribute name="name" type="xsd:string" />
						</xsd:complexType>
					</xsd:element>
					<xsd:element name="data">
						<xsd:complexType>
							<xsd:sequence>
								<xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
								<xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
							</xsd:sequence>
							<xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
							<xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
							<xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
							<xsd:attribute ref="xml:space" />
						</xsd:complexType>
					</xsd:element>
					<xsd:element name="resheader">
						<xsd:complexType>
							<xsd:sequence>
								<xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
							</xsd:sequence>
							<xsd:attribute name="name" type="xsd:string" use="required" />
						</xsd:complexType>
					</xsd:element>
				</xsd:choice>
			</xsd:complexType>
		</xsd:element>
	</xsd:schema>
	<resheader name="resmimetype">
		<value>text/microsoft-resx</value>
	</resheader>
	<resheader name="version">
		<value>2.0</value>
	</resheader>
	<resheader name="reader">
		<value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
	</resheader>
	<resheader name="writer">
		<value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
	</resheader>
	<data name="KAN00" xml:space="preserve">
		<value>Thành công</value>
	</data>
	<data name="KAN01" xml:space="preserve">
		<value>Tài nguyên yêu cầu không tồn tại</value>
	</data>
	<data name="KAN02" xml:space="preserve">
		<value>Tài nguyên không thay đổi</value>
	</data>
	<data name="KAN03" xml:space="preserve">
		<value>Tài nguyên không thay đổi</value>
	</data>
	<data name="KAN04" xml:space="preserve">
		<value>Không được phép</value>
	</data>
	<data name="KAN05" xml:space="preserve">
		<value>Cấm</value>
	</data>
	<data name="KAN500" xml:space="preserve">
		<value>Lỗi không xác định</value>
	</data>
	<data name="AUTH01" xml:space="preserve">
		<value>Mật khẩu không chính xác</value>
	</data>
	<data name="AUTH02" xml:space="preserve">
		<value>Thay đổi mật khẩu thất bại</value>
	</data>
	<data name="AUTH03" xml:space="preserve">
		<value>Xác nhận mật khẩu không khớp</value>
	</data>
	<data name="AUTH04" xml:space="preserve">
		<value>Mật khẩu không đáp ứng yêu cầu</value>
	</data>
	<data name="AUTH05" xml:space="preserve">
		<value>Đặt lại mật khẩu thất bại</value>
	</data>
	<data name="AUTH06" xml:space="preserve">
		<value>Người dùng là lao động thuê ngoài, không thể đăng nhập</value>
	</data>
	<data name="AUTH07" xml:space="preserve">
		<value>Tạo mật khẩu thất bại</value>
	</data>
	<data name="AUTH08" xml:space="preserve">
		<value>Đăng nhập thất bại</value>
	</data>
	<data name="AUTH09" xml:space="preserve">
		<value>Đăng ký thất bại</value>
	</data>
	<data name="AUTH10" xml:space="preserve">
		<value>Đăng nhập thất bại</value>
	</data>
	<data name="AUTH11" xml:space="preserve">
		<value>Tạo mã OTP thất bại</value>
	</data>
	<data name="AUTH12" xml:space="preserve">
		<value>Mã OTP không tồn tại</value>
	</data>
	<data name="AUTH13" xml:space="preserve">
		<value>Mã OTP không chính xác</value>
	</data>
	<data name="TKN01" xml:space="preserve">
		<value>Token đã hết hạn</value>
	</data>
	<data name="TKN02" xml:space="preserve">
		<value>Token không hợp lệ</value>
	</data>
	<data name="TKN03" xml:space="preserve">
		<value>Xác thực token thất bại</value>
	</data>
	<data name="TKN04" xml:space="preserve">
		<value>Tạo token thất bại</value>
	</data>
	<data name="USR01" xml:space="preserve">
		<value>Người dùng không tồn tại</value>
	</data>
	<data name="USR03" xml:space="preserve">
		<value>Tạo người dùng thất bại</value>
	</data>
	<data name="USR04" xml:space="preserve">
		<value>Cập nhật người dùng thất bại</value>
	</data>
	<data name="USR05" xml:space="preserve">
		<value>Xóa người dùng thất bại</value>
	</data>
	<data name="USR06" xml:space="preserve">
		<value>Gán vai trò thất bại</value>
	</data>
	<data name="USR07" xml:space="preserve">
		<value>ID đăng nhập đã tồn tại</value>
	</data>
	<data name="USR09" xml:space="preserve">
		<value>Avatar người dùng chưa được đặt</value>
	</data>
	<data name="USR08" xml:space="preserve">
		<value>Cập nhật avatar người dùng thất bại</value>
	</data>
	<data name="USR10" xml:space="preserve">
		<value>Xóa avatar người dùng thất bại</value>
	</data>
	<data name="EML01" xml:space="preserve">
		<value>Không tồn tại nghỉ phép của nhân viên</value>
	</data>
	<data name="EML02" xml:space="preserve">
		<value>Khởi tạo nghỉ phép của nhân viên thất bại</value>
	</data>
	<data name="EML03" xml:space="preserve">
		<value>Cập nhật nghỉ phép của nhân viên thất bại</value>
	</data>
	<data name="EML04" xml:space="preserve">
		<value>Xóa nghỉ phép của nhân viên thất bại</value>
	</data>
	<data name="EML05" xml:space="preserve">
		<value>Nghỉ phép của nhân viên đã tồn tại</value>
	</data>
	<data name="EML06" xml:space="preserve">
		<value>Ngày hết hạn nghỉ phép của nhân viên không hợp lệ</value>
	</data>
	<data name="CRT01" xml:space="preserve">
		<value>Tạo hợp đồng thất bại</value>
	</data>
	<data name="CRT02" xml:space="preserve">
		<value>Cập nhật hợp đồng thất bại</value>
	</data>
	<data name="ORG01" xml:space="preserve">
		<value>Tổ chức không tồn tại</value>
	</data>
	<data name="ORG02" xml:space="preserve">
		<value>Tạo tổ chức thất bại</value>
	</data>
	<data name="ORG03" xml:space="preserve">
		<value>Cập nhật tổ chức thất bại</value>
	</data>
	<data name="ORG04" xml:space="preserve">
		<value>Xóa tổ chức thất bại</value>
	</data>
	<data name="ORG05" xml:space="preserve">
		<value>Không phải chủ sở hữu tổ chức</value>
	</data>
	<data name="ORG06" xml:space="preserve">
		<value>Không phải quản trị viên tổ chức</value>
	</data>
	<data name="STR01" xml:space="preserve">
		<value>Phòng ban không tồn tại</value>
	</data>
	<data name="STR02" xml:space="preserve">
		<value>Tạo phòng ban thất bại</value>
	</data>
	<data name="STR03" xml:space="preserve">
		<value>Cập nhật phòng ban thất bại</value>
	</data>
	<data name="STR04" xml:space="preserve">
		<value>Xóa phòng ban thất bại</value>
	</data>
	<data name="POS01" xml:space="preserve">
		<value>Vị trí không tồn tại</value>
	</data>
	<data name="POS02" xml:space="preserve">
		<value>Tạo chức vụ thất bại</value>
	</data>
	<data name="POS03" xml:space="preserve">
		<value>Cập nhật chức vụ thất bại</value>
	</data>
	<data name="POS04" xml:space="preserve">
		<value>Xóa chức vụ thất bại</value>
	</data>
	<data name="RNK01" xml:space="preserve">
		<value>Bậc thợ không tồn tại</value>
	</data>
	<data name="RNK02" xml:space="preserve">
		<value>Tạo bậc thợ thất bại</value>
	</data>
	<data name="RNK03" xml:space="preserve">
		<value>Cập nhật bậc thợ thất bại</value>
	</data>
	<data name="RNK04" xml:space="preserve">
		<value>Xóa bậc thợ thất bại</value>
	</data>
	<data name="WPL01" xml:space="preserve">
		<value>Nơi làm việc không tồn tại</value>
	</data>

	<data name="WPL02" xml:space="preserve">
		<value>Tạo nơi làm việc thất bại</value>
	</data>
	<data name="WPL03" xml:space="preserve">
		<value>Cập nhật nơi làm việc thất bại</value>
	</data>
	<data name="GLC01" xml:space="preserve">
		<value>Cấu hình toàn cầu không tồn tại</value>
	</data>
	<data name="MAIL01" xml:space="preserve">
		<value>Mẫu email không tồn tại</value>
	</data>
	<data name="MAIL02" xml:space="preserve">
		<value>Gửi email thất bại</value>
	</data>
	<data name="ROLE01" xml:space="preserve">
		<value>Vai trò không tồn tại</value>
	</data>
	<data name="ROLE02" xml:space="preserve">
		<value>Tạo vai trò thất bại</value>
	</data>
	<data name="ROLE03" xml:space="preserve">
		<value>Cập nhật vai trò thất bại</value>
	</data>
	<data name="ROLE04" xml:space="preserve">
		<value>Xóa vai trò thất bại</value>
	</data>
	<data name="COMN01" xml:space="preserve">
		<value>Đơn vị không tồn tại</value>
	</data>
	<data name="COMN02" xml:space="preserve">
		<value>Loại yêu cầu không tồn tại</value>
	</data>
	<data name="COMN03" xml:space="preserve">
		<value>Loại nghỉ phép không tồn tại</value>
	</data>
	<data name="COMN04" xml:space="preserve">
		<value>Trạng thái không tồn tại</value>
	</data>
	<data name="REQ01" xml:space="preserve">
		<value>Yêu cầu không tồn tại</value>
	</data>
	<data name="REQ02" xml:space="preserve">
		<value>Tạo yêu cầu thất bại</value>
	</data>
	<data name="REQ03" xml:space="preserve">
		<value>Cập nhật yêu cầu thất bại</value>
	</data>
	<data name="REQ04" xml:space="preserve">
		<value>Phê duyệt điểm danh thất bại</value>
	</data>
	<data name="REQ05" xml:space="preserve">
		<value>Phê duyệt yêu cầu thất bại</value>
	</data>
	<data name="REQ06" xml:space="preserve">
		<value>Từ chối yêu cầu thất bại</value>
	</data>
	<data name="REQ07" xml:space="preserve">
		<value>Hủy yêu cầu thất bại</value>
	</data>
	<data name="REQ08" xml:space="preserve">
		<value>Chi tiết yêu cầu không hợp lệ</value>
	</data>
	<data name="REQ09" xml:space="preserve">
		<value>Không có quyền phê duyệt</value>
	</data>
	<data name="REQ10" xml:space="preserve">
		<value>Đã được xử lý hoặc hủy</value>
	</data>
	<data name="REQ11" xml:space="preserve">
		<value>Không có dự án nào được gán với yêu cầu này</value>
	</data>
	<data name="REQ12" xml:space="preserve">
		<value>Yêu cầu nghỉ phép vào ngày nghỉ</value>
	</data>
	<data name="REQ13" xml:space="preserve">
		<value>Yêu cầu điểm danh hàng ngày chưa checkout</value>
	</data>
	<data name="REQ14" xml:space="preserve">
		<value>Cập nhật trạng thái yêu cầu thất bại</value>
	</data>
	<data name="REQ15" xml:space="preserve">
		<value>Tạo điểm danh hàng ngày thất bại</value>
	</data>
	<data name="REQ16" xml:space="preserve">
		<value>Tất cả ca làm việc cho ngày mong muốn này đã được yêu cầu</value>
	</data>
	<data name="REQ17" xml:space="preserve">
		<value>Yêu cầu vào/ra vào ngày nghỉ</value>
	</data>
	<data name="REQ18" xml:space="preserve">
		<value>Ngày điểm danh hàng ngày không hợp lệ</value>
	</data>
	<data name="REQ19" xml:space="preserve">
		<value>Yêu cầu đã được phê duyệt</value>
	</data>
	<data name="REQ20" xml:space="preserve">
		<value>Yêu cầu đã bị hủy</value>
	</data>
	<data name="REQ21" xml:space="preserve">
		<value>Xóa yêu cầu thất bại</value>
	</data>
	<data name="TRL01" xml:space="preserve">
		<value>Người dịch không tồn tại</value>
	</data>
	<data name="SFT01" xml:space="preserve">
		<value>Ca làm việc không tồn tại</value>
	</data>
	<data name="SFT02" xml:space="preserve">
		<value>Tạo ca làm việc thất bại</value>
	</data>
	<data name="SFT03" xml:space="preserve">
		<value>Cập nhật ca làm việc thất bại</value>
	</data>
	<data name="SFT04" xml:space="preserve">
		<value>Xóa ca làm việc thất bại</value>
	</data>
	<data name="SFT05" xml:space="preserve">
		<value>Check In thất bại</value>
	</data>
	<data name="SFT06" xml:space="preserve">
		<value>Check Out thất bại</value>
	</data>
	<data name="SFT07" xml:space="preserve">
		<value>Break In thất bại</value>
	</data>
	<data name="SFT08" xml:space="preserve">
		<value>Break Out thất bại</value>
	</data>
	<data name="SFT09" xml:space="preserve">
		<value>Đã Check In</value>
	</data>
	<data name="SFT10" xml:space="preserve">
		<value>Đã trong giờ nghỉ</value>
	</data>
	<data name="SFT11" xml:space="preserve">
		<value>Chưa Check In</value>
	</data>
	<data name="SFT12" xml:space="preserve">
		<value>Ca làm việc đã kết thúc</value>
	</data>
	<data name="SFT13" xml:space="preserve">
		<value>Không trong giờ nghỉ</value>
	</data>
	<data name="SFT14" xml:space="preserve">
		<value>Chưa Break Out</value>
	</data>
	<data name="SFT15" xml:space="preserve">
		<value>Thời gian nghỉ vượt quá</value>
	</data>
	<data name="SFT16" xml:space="preserve">
		<value>Tạo ca làm việc bổ sung thất bại</value>
	</data>
	<data name="SFT17" xml:space="preserve">
		<value>Không thể cập nhật ca làm việc đã được phê duyệt</value>
	</data>
	<data name="SFT18" xml:space="preserve">
		<value>Đã Check Out</value>
	</data>
	<data name="SCH01" xml:space="preserve">
		<value>Lịch trình không tồn tại</value>
	</data>
	<data name="SCH02" xml:space="preserve">
		<value>Tạo lịch trình thất bại</value>
	</data>
	<data name="SCH03" xml:space="preserve">
		<value>Cập nhật lịch trình thất bại</value>
	</data>
	<data name="SCH04" xml:space="preserve">
		<value>Xóa lịch trình thất bại</value>
	</data>
	<data name="SCH05" xml:space="preserve">
		<value>Xuất lịch trình thất bại</value>
	</data>
	<data name="SCH06" xml:space="preserve">
		<value>Đã có lịch trình</value>
	</data>
	<data name="SCH07" xml:space="preserve">
		<value>Phân tích lịch trình thất bại</value>
	</data>
	<data name="PRJ01" xml:space="preserve">
		<value>Dự án không tồn tại</value>
	</data>
	<data name="PRJ02" xml:space="preserve">
		<value>Tạo dự án thất bại</value>
	</data>
	<data name="PRJ03" xml:space="preserve">
		<value>Cập nhật dự án thất bại</value>
	</data>
	<data name="PRJ04" xml:space="preserve">
		<value>Xóa dự án thất bại</value>
	</data>
	<data name="PRJ05" xml:space="preserve">
		<value>Tổng quan dự án không tồn tại</value>
	</data>
	<data name="GPS01" xml:space="preserve">
		<value>Vị trí không tồn tại</value>
	</data>
	<data name="GPS02" xml:space="preserve">
		<value>Thông tin chức vụ không hợp lệ</value>
	</data>
	<data name="HOL01" xml:space="preserve">
		<value>Ngày lễ không tồn tại</value>
	</data>
	<data name="HOL02" xml:space="preserve">
		<value>Tạo ngày lễ thất bại</value>
	</data>
	<data name="HOL03" xml:space="preserve">
		<value>Cập nhật ngày lễ thất bại</value>
	</data>
	<data name="URF01" xml:space="preserve">
		<value>Vai trò chức năng không tồn tại</value>
	</data>
	<data name="URF02" xml:space="preserve">
		<value>Tạo vai trò chức năng thất bại</value>
	</data>
	<data name="URF03" xml:space="preserve">
		<value>Cập nhật vai trò chức năng thất bại</value>
	</data>
	<data name="FNC01" xml:space="preserve">
		<value>Chức năng không tồn tại</value>
	</data>
	<data name="FNC02" xml:space="preserve">
		<value>Tạo chức năng thất bại</value>
	</data>
	<data name="FNC03" xml:space="preserve">
		<value>Cập nhật chức năng thất bại</value>
	</data>
	<data name="HST01" xml:space="preserve">
		<value>Truy xuất lịch sử theo dõi thất bại</value>
	</data>
	<data name="HST02" xml:space="preserve">
		<value>Tạo lịch sử theo dõi thất bại</value>
	</data>
	<data name="MIO01" xml:space="preserve">
		<value>Bucket không tồn tại</value>
	</data>
	<data name="MIO02" xml:space="preserve">
		<value>Đối tượng không tồn tại</value>
	</data>
	<data name="MIO03" xml:space="preserve">
		<value>Metadata đối tượng không tồn tại</value>
	</data>
	<data name="MIO04" xml:space="preserve">
		<value>Tải lên đối tượng thất bại</value>
	</data>
	<data name="MIO05" xml:space="preserve">
		<value>Tải xuống đối tượng thất bại</value>
	</data>
	<data name="MIO06" xml:space="preserve">
		<value>Tạo đối tượng thất bại</value>
	</data>
	<data name="MIO07" xml:space="preserve">
		<value>Xóa đối tượng thất bại</value>
	</data>
	<data name="RPT01" xml:space="preserve">
		<value>Tạo báo cáo điểm danh thất bại</value>
	</data>
	<data name="RPT02" xml:space="preserve">
		<value>Cập nhật báo cáo điểm danh thất bại</value>
	</data>
	<data name="RPT03" xml:space="preserve">
		<value>Báo cáo điểm danh không tồn tại</value>
	</data>
	<data name="RPT04" xml:space="preserve">
		<value>Báo cáo điểm danh đã được phê duyệt, không thể cập nhật</value>
	</data>
	<data name="RPT05" xml:space="preserve">
		<value>Báo cáo ngày đã tồn tại</value>
	</data>
	<data name="RPT06" xml:space="preserve">
		<value>Báo cáo ngày không tồn tại</value>
	</data>
	<data name="RPT07" xml:space="preserve">
		<value>Tạo báo cáo ngày thất bại</value>
	</data>
	<data name="RPT08" xml:space="preserve">
		<value>Cập nhật báo cáo ngày thất bại</value>
	</data>
	<data name="RPT09" xml:space="preserve">
		<value>Xóa báo cáo ngày thất bại</value>
	</data>
	<data name="CTG01" xml:space="preserve">
		<value>Danh mục không tồn tại</value>
	</data>
	<data name="CTG02" xml:space="preserve">
		<value>Tạo danh mục thất bại</value>
	</data>
	<data name="CTG03" xml:space="preserve">
		<value>Cập nhật danh mục thất bại</value>
	</data>
	<data name="CTG04" xml:space="preserve">
		<value>Xóa danh mục thất bại</value>
	</data>
	<data name="VDR01" xml:space="preserve">
		<value>Nhà cung cấp không tồn tại</value>
	</data>
	<data name="VDR02" xml:space="preserve">
		<value>Tạo nhà cung cấp thất bại</value>
	</data>
	<data name="VDR03" xml:space="preserve">
		<value>Cập nhật nhà cung cấp thất bại</value>
	</data>
	<data name="VDR04" xml:space="preserve">
		<value>Xóa nhà cung cấp thất bại</value>
	</data>
	<data name="VDR05" xml:space="preserve">
		<value>Logo nhà cung cấp không tồn tại</value>
	</data>
	<data name="PRS01" xml:space="preserve">
		<value>Quy trình không tồn tại</value>
	</data>
	<data name="PRS02" xml:space="preserve">
		<value>Tạo quy trình thất bại</value>
	</data>
	<data name="PRS03" xml:space="preserve">
		<value>Cập nhật quy trình thất bại</value>
	</data>
	<data name="IT01" xml:space="preserve">
		<value>Mục không tồn tại</value>
	</data>
	<data name="IT02" xml:space="preserve">
		<value>Tạo mục thất bại</value>
	</data>
	<data name="IT03" xml:space="preserve">
		<value>Cập nhật mục thất bại</value>
	</data>
	<data name="IT04" xml:space="preserve">
		<value>Xóa mục thất bại</value>
	</data>
	<data name="IT05" xml:space="preserve">
		<value>Hình ảnh minh họa mục không tồn tại</value>
	</data>
	<data name="ITP01" xml:space="preserve">
		<value>Giá mục không tồn tại</value>
	</data>
	<data name="ITP02" xml:space="preserve">
		<value>Tạo giá mục thất bại</value>
	</data>
	<data name="ITP03" xml:space="preserve">
		<value>Cập nhật giá mục thất bại</value>
	</data>
	<data name="IPC01" xml:space="preserve">
		<value>Chi phí đầu vào không tồn tại</value>
	</data>
	<data name="IPC02" xml:space="preserve">
		<value>Tạo chi phí đầu vào thất bại</value>
	</data>
	<data name="IPC03" xml:space="preserve">
		<value>Cập nhật chi phí đầu vào thất bại</value>
	</data>	
	<data name="IPC04" xml:space="preserve">
		<value>Xóa chi phí đầu vào thất bại</value>
	</data>
	<data name="ET01" xml:space="preserve">
		<value>Loại mục không tồn tại</value>
	</data>
	<data name="ET02" xml:space="preserve">
		<value>Tạo loại mục thất bại</value>
	</data>
	<data name="ET03" xml:space="preserve">
		<value>Cập nhật loại mục thất bại</value>
	</data>
	<data name="ET04" xml:space="preserve">
		<value>Xóa loại mục thất bại</value>
	</data>
	<data name="MFTR01" xml:space="preserve">
		<value>Nhà sản xuất không tồn tại</value>
	</data>
	<data name="MFTR02" xml:space="preserve">
		<value>Tạo nhà sản xuất thất bại</value>
	</data>
	<data name="MFTR03" xml:space="preserve">
		<value>Cập nhật nhà sản xuất thất bại</value>
	</data>
	<data name="MFTR04" xml:space="preserve">
		<value>Xóa nhà sản xuất thất bại</value>
	</data>
	<data name="MFTR05" xml:space="preserve">
		<value>Logo nhà sản xuất không tồn tại</value>
	</data>
	<data name="IPCI01" xml:space="preserve">
		<value>Mục chi phí đầu vào không tồn tại</value>
	</data>
	<data name="IPCI02" xml:space="preserve">
		<value>Tạo mục chi phí đầu vào thất bại</value>
	</data>
	<data name="IPCI03" xml:space="preserve">
		<value>Cập nhật mục chi phí đầu vào thất bại</value>
	</data>
	<data name="IPCI04" xml:space="preserve">
		<value>Xóa mục chi phí đầu vào thất bại</value>
	</data>
	<data name="PMT01" xml:space="preserve">
		<value>Loại thanh toán không tồn tại</value>
	</data>
	<data name="PMT02" xml:space="preserve">
		<value>Tạo loại thanh toán thất bại</value>
	</data>
	<data name="PMT03" xml:space="preserve">
		<value>Cập nhật loại thanh toán thất bại</value>
	</data>
	<data name="PMT04" xml:space="preserve">
		<value>Xóa loại thanh toán thất bại</value>
	</data>
	<data name="AC01" xml:space="preserve">
		<value>Tài khoản đã tồn tại</value>
	</data>
	<data name="AC02" xml:space="preserve">
		<value>Tài khoản không tồn tại</value>
	</data>
	<data name="AC03" xml:space="preserve">
		<value>Tạo tài khoản thất bại</value>
	</data>
	<data name="AC04" xml:space="preserve">
		<value>Cập nhật tài khoản thất bại</value>
	</data>
	<data name="AC05" xml:space="preserve">
		<value>Xóa tài khoản thất bại</value>
	</data>
	<data name="AC06" xml:space="preserve">
		<value>Khóa tài khoản thất bại</value>
	</data>
	<data name="AC07" xml:space="preserve">
		<value>Mở khóa tài khoản thất bại</value>
	</data>
	<data name="AC08" xml:space="preserve">
		<value>Email hoặc ID đăng nhập đã tồn tại</value>
	</data>
	<data name="USI01" xml:space="preserve">
		<value>Thông tin người dùng không tồn tại</value>
	</data>
	<data name="USI02" xml:space="preserve">
		<value>Thông tin người dùng đã tồn tại</value>
	</data>
	<data name="USI03" xml:space="preserve">
		<value>Tạo thông tin người dùng thất bại</value>
	</data>
	<data name="USI04" xml:space="preserve">
		<value>Cập nhật thông tin người dùng thất bại</value>
	</data>
	<data name="USI05" xml:space="preserve">
		<value>Xóa thông tin người dùng thất bại</value>
	</data>
	<data name="USI06" xml:space="preserve">
		<value>Avatar thông tin người dùng chưa được đặt</value>
	</data>
	<data name="USI07" xml:space="preserve">
		<value>Cập nhật avatar thông tin người dùng thất bại</value>
	</data>
	<data name="USI08" xml:space="preserve">
		<value>Xóa avatar thông tin người dùng thất bại</value>
	</data>
	<data name="EMP01" xml:space="preserve">
		<value>Nhân viên không tồn tại</value>
	</data>
	<data name="EMP02" xml:space="preserve">
		<value>Nhân viên đã tồn tại</value>
	</data>
	<data name="EMP03" xml:space="preserve">
		<value>Tạo nhân viên thất bại</value>
	</data>
	<data name="EMP04" xml:space="preserve">
		<value>Cập nhật nhân viên thất bại</value>
	</data>
	<data name="EMP05" xml:space="preserve">
		<value>Xóa nhân viên thất bại</value>
	</data>
	<data name="EMP06" xml:space="preserve">
		<value>Mời nhân viên thất bại</value>
	</data>
	<data name="EMP07" xml:space="preserve">
		<value>Lời mời nhân viên không tồn tại</value>
	</data>
	<data name="EMP08" xml:space="preserve">
		<value>Xóa lời mời nhân viên thất bại</value>
	</data>
	<data name="EMP09" xml:space="preserve">
		<value>Chấp nhận lời mời nhân viên thất bại</value>
	</data>
	<data name="EMP10" xml:space="preserve">
		<value>Từ chối lời mời nhân viên thất bại</value>
	</data>
	<data name="EMP11" xml:space="preserve">
		<value>Lời mời nhân viên đã tồn tại</value>
	</data>
	<data name="EMP12" xml:space="preserve">
		<value>Liên kết tài khoản với tổ chức thất bại</value>
	</data>
	<data name="FIL01" xml:space="preserve">
		<value>Tải lên tệp thất bại</value>
	</data>
	<data name="FIL02" xml:space="preserve">
		<value>Tải xuống tệp thất bại</value>
	</data>
	<data name="FIL03" xml:space="preserve">
		<value>Xóa tệp thất bại</value>
	</data>
	<data name="FIL04" xml:space="preserve">
		<value>Metadata tệp không tồn tại</value>
	</data>
	<data name="FIL05" xml:space="preserve">
		<value>Tạo metadata tệp thất bại</value>
	</data>
	<data name="FIL06" xml:space="preserve">
		<value>Cập nhật metadata tệp thất bại</value>
	</data>
	<data name="WOS01" xml:space="preserve">
		<value>Ca làm việc không tồn tại</value>
	</data>
	<data name="WOS02" xml:space="preserve">
		<value>Tạo ca làm việc thất bại</value>
	</data>
	<data name="WOS03" xml:space="preserve">
		<value>Cập nhật ca làm việc thất bại</value>
	</data>
	<data name="WOS04" xml:space="preserve">
		<value>Xóa ca làm việc thất bại</value>
	</data>
	<data name="WOS05" xml:space="preserve">
		<value>Gán ca làm việc thất bại</value>
	</data>
	<data name="WOS06" xml:space="preserve">
		<value>Thời gian nghỉ ca làm việc không hợp lệ</value>
	</data>
	<data name="WOS07" xml:space="preserve">
		<value>Không có ca làm việc được gán cho dự án</value>
	</data>
	<data name="EVC01" xml:space="preserve">
		<value>Sự kiện không tồn tại</value>
	</data>
	<data name="EVC02" xml:space="preserve">
		<value>Tạo sự kiện thất bại</value>
	</data>
	<data name="EVC03" xml:space="preserve">
		<value>Cập nhật sự kiện thất bại</value>
	</data>
	<data name="EVC04" xml:space="preserve">
		<value>Xóa sự kiện thất bại</value>
	</data>
	<data name="EVC05" xml:space="preserve">
		<value>Ngày của sự kiện không hợp lệ</value>
	</data>
	<data name="EVC06" xml:space="preserve">
		<value>Sự kiện đã tồn tại</value>
	</data>
	<data name="UNT01" xml:space="preserve">
		<value>Đơn vị không tồn tại</value>
	</data>	
	<data name="UNT02" xml:space="preserve">
		<value>Tạo đơn vị thất bại</value>
	</data>
	<data name="UNT03" xml:space="preserve">
		<value>Cập nhật đơn vị thất bại</value>
	</data>
	<data name="UNT04" xml:space="preserve">
		<value>Xóa đơn vị thất bại</value>
	</data>
	<data name="OS01" xml:space="preserve">
		<value>Thuê ngoài không tồn tại</value>
	</data>
	<data name="OS02" xml:space="preserve">
		<value>Tạo thuê ngoài thất bại</value>
	</data>
	<data name="OS03" xml:space="preserve">
		<value>Cập nhật thuê ngoài thất bại</value>
	</data>
	<data name="OS04" xml:space="preserve">
		<value>Xóa thuê ngoài thất bại</value>
	</data>
	<data name="OS05" xml:space="preserve">
		<value>Giá thuê ngoài không tồn tại</value>
	</data>
	<data name="OS06" xml:space="preserve">
		<value>Tạo giá thuê ngoài thất bại</value>
	</data>
	<data name="OS07" xml:space="preserve">
		<value>Cập nhật giá thuê ngoài thất bại</value>
	</data>
	<data name="OS08" xml:space="preserve">
		<value>Logo thuê ngoài không tồn tại</value>
	</data>
	<data name="PRT01" xml:space="preserve">
		<value>Loại dự án không tồn tại</value>
	</data>
	<data name="PRT02" xml:space="preserve">
		<value>Tạo loại dự án thất bại</value>
	</data>
	<data name="PRT03" xml:space="preserve">
		<value>Cập nhật loại dự án thất bại</value>
	</data>
	<data name="CON01" xml:space="preserve">
		<value>Công trình không tồn tại</value>
	</data>
	<data name="CON02" xml:space="preserve">
		<value>Tạo công trình thất bại</value>
	</data>
	<data name="CON03" xml:space="preserve">
		<value>Cập nhật công trình thất bại</value>
	</data>
	<data name="CON04" xml:space="preserve">
		<value>Xóa công trình thất bại</value>
	</data>
	<data name="CON05" xml:space="preserve">
		<value>Công trình phụ đã tồn tại</value>
	</data>
	<data name="CUS01" xml:space="preserve">
		<value>Khách hàng không tồn tại</value>
	</data>
	<data name="CUS02" xml:space="preserve">
		<value>Tạo khách hàng thất bại</value>
	</data>
	<data name="CUS03" xml:space="preserve">
		<value>Cập nhật khách hàng thất bại</value>
	</data>
	<data name="CUS04" xml:space="preserve">
		<value>Xóa khách hàng thất bại</value>
	</data>
	<data name="CT01" xml:space="preserve">
		<value>Loại khách hàng không tồn tại</value>
	</data>
	<data name="CONC01" xml:space="preserve">
		<value>Chi phí công trình không tồn tại</value>
	</data>
	<data name="CONC02" xml:space="preserve">
		<value>Tạo chi phí công trình thất bại</value>
	</data>
	<data name="CONC03" xml:space="preserve">
		<value>Cập nhật chi phí công trình thất bại</value>
	</data>
	<data name="CONC04" xml:space="preserve">
		<value>Xóa chi phí công trình thất bại</value>
	</data>
	<data name="AUD01" xml:space="preserve">
		<value>Không có lịch sử thay đổi</value>
	</data>
</root>