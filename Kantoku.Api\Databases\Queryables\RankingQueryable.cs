using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class RankingQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
}

public interface IRankingQueryable
{
    IQueryable<Ranking> GetRankingsQuery(
        RankingQueryableOptions options
    );
    IQueryable<Ranking> GetRankingsQueryIncluded(
        RankingQueryableOptions options,
        IQueryable<Ranking>? query = null
    );
    IQueryable<Ranking> GetRankingsQueryFiltered(
        RankingFilter filter,
        RankingQueryableOptions options,
        IQueryable<Ranking>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class RankingQueryable(PostgreDbContext context) :
    BaseQueryable<Ranking>(context), IRankingQueryable
{
    public IQueryable<Ranking> GetRankingsQuery(
        RankingQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(r => r.IsDeleted == false);
        return query;
    }

    public IQueryable<Ranking> GetRankingsQueryIncluded(
        RankingQueryableOptions options,
        IQueryable<Ranking>? query = null
    )
    {
        query ??= GetRankingsQuery(options);
        return query;
    }

    public IQueryable<Ranking> GetRankingsQueryFiltered(
        RankingFilter filter,
        RankingQueryableOptions options,
        IQueryable<Ranking>? query = null
    )
    {
        query ??= GetRankingsQueryIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(r => r.OrgUid == filter.OrgId);
        }
        if (filter.Keyword is not null)
        {
            query = query.Where(r => r.RankingName.Contains(filter.Keyword));
        }
        if (filter.MinValue.HasValue)
        {
            query = query.Where(r => r.MinValue >= filter.MinValue.Value);
        }
        if (filter.MaxValue.HasValue)
        {
            query = query.Where(r => r.MaxValue <= filter.MaxValue.Value);
        }
        return query;
    }
}