﻿using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.EntryType.Response;

/// <summary>
/// Response DTO for Entry Type data
/// </summary>
public class EntryTypeResponseDto
{
    /// <summary>
    /// The unique identifier for the entry type (*)
    /// </summary>
    public string? EntryTypeId { get; set; }

    /// <summary>
    /// The name of the entry type (*)
    /// </summary>
    public string? EntryTypeName { get; set; }

    /// <summary>
    /// Optional description for the entry type
    /// </summary>
    public string? Description { get; set; }
}
