using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class OutSourceShiftQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedOutSource { get; set; } = false;
    public bool IncludedProjectSchedule { get; set; } = false;
}

public interface IOutSourceShiftQueryable
{
    IQueryable<OutSourceShift> GetOutSourceShiftQuery(
        OutSourceShiftQueryableOptions options
    );

    IQueryable<OutSourceShift> GetOutSourceShiftQueryIncluded(
        OutSourceShiftQueryableOptions options,
        IQueryable<OutSourceShift>? query = null
    );
    IQueryable<OutSourceShift> GetOutSourceShiftQueryFiltered(
        OutSourceShiftFilter filter,
        OutSourceShiftQueryableOptions options,
        IQueryable<OutSourceShift>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class OutSourceShiftQueryable(PostgreDbContext context) :
    BaseQueryable<OutSourceShift>(context), IOutSourceShiftQueryable
{
    public IQueryable<OutSourceShift> GetOutSourceShiftQuery(
        OutSourceShiftQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<OutSourceShift> GetOutSourceShiftQueryIncluded(
        OutSourceShiftQueryableOptions options,
        IQueryable<OutSourceShift>? query = null
    )
    {
        query ??= GetOutSourceShiftQuery(options);

        if (options.IncludedOutSource || options.IncludedAll)
        {
            query = query.Include(s => s.OutSource);
        }

        if (options.IncludedProjectSchedule || options.IncludedAll)
        {
            query = query.Include(s => s.ProjectSchedule);
        }

        return query;
    }

    public IQueryable<OutSourceShift> GetOutSourceShiftQueryFiltered(
        OutSourceShiftFilter filter,
        OutSourceShiftQueryableOptions options,
        IQueryable<OutSourceShift>? query = null
    )
    {
        query ??= GetOutSourceShiftQueryIncluded(options);

        if (filter.OutSourceId is not null && filter.OutSourceId != Guid.Empty)
        {
            query = query.Where(s => s.OutSourceUid == filter.OutSourceId);
        }
        if (filter.ProjectId is not null && filter.ProjectId != Guid.Empty)
        {
            query = query.Where(s => s.ProjectSchedule.ProjectUid == filter.ProjectId);
        }
        if (filter.ProjectScheduleId is not null && filter.ProjectScheduleId != Guid.Empty)
        {
            query = query.Where(s => s.ProjectScheduleUid == filter.ProjectScheduleId);
        }
        if (DateOnly.TryParse(filter.WorkingDate, out var workingDate))
        {
            query = query.Where(s => DateOnly.FromDateTime(s.ScheduledStartTime) == workingDate);
        }
        if (DateOnly.TryParse(filter.WorkingDateFrom, out var workingDateFrom))
        {
            query = query.Where(s => DateOnly.FromDateTime(s.ScheduledStartTime) >= workingDateFrom);
        }
        if (DateOnly.TryParse(filter.WorkingDateTo, out var workingDateTo))
        {
            query = query.Where(s => DateOnly.FromDateTime(s.ScheduledStartTime) <= workingDateTo);
        }
        if (filter.Role is not null)
        {
            query = query.Where(s => s.Role != null && s.Role.Contains(filter.Role));
        }

        return query;
    }
}

