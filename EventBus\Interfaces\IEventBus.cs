namespace EventBus.Interfaces;

/// <summary>
/// Event producer interface for publishing events
/// </summary>
public interface IEventProducer
{
    /// <summary>
    /// Publishes an event to the event bus
    /// </summary>
    /// <typeparam name="T">Type of the event</typeparam>
    /// <param name="event">The event to publish</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task PublishAsync<T>(T @event, CancellationToken cancellationToken = default) where T : class;
}

/// <summary>
/// Event consumer interface for subscribing to events
/// </summary>
public interface IEventConsumer
{
    /// <summary>
    /// Subscribes to events of a specific type
    /// </summary>
    /// <typeparam name="T">Type of the event</typeparam>
    /// <typeparam name="TH">Type of the event handler</typeparam>
    void Subscribe<T, TH>()
        where T : class
        where TH : class, IEventHandler<T>;

    /// <summary>
    /// Unsubscribes from events of a specific type
    /// </summary>
    /// <typeparam name="T">Type of the event</typeparam>
    /// <typeparam name="TH">Type of the event handler</typeparam>
    void Unsubscribe<T, TH>()
        where T : class
        where TH : class, IEventHandler<T>;

    /// <summary>
    /// Starts the event consumer
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task StartAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Stops the event consumer
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task StopAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Event bus interface for publishing and subscribing to events
/// Combines both producer and consumer functionality for backward compatibility
/// </summary>
public interface IEventBus : IEventProducer, IEventConsumer
{
}
