using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Filters.Domains;

public class EmployeeShiftFilter : BaseFilter
{
    // [FromQuery(Name = "employeeId")]
    public Guid? EmployeeId { get; set; }

    // [FromQuery(Name = "projectId")]
    public Guid? ProjectId { get; set; }

    // [FromQuery(Name = "timeFrom")]
    public string? TimeFrom { get; set; }

    // [FromQuery(Name = "timeTo")]
    public string? TimeTo { get; set; }

    // [FromQuery(Name = "hasCheckedIn")]
    public bool? HasCheckedIn { get; set; }

    // [FromQuery(Name = "hasCheckedOutEmployee")]
    public bool? HasCheckedOut { get; set; }

    // [FromQuery(Name = "hasScheduledCheckIn")]
    public bool? HasScheduledCheckIn { get; set; }

    // [FromQuery(Name = "hasScheduledCheckOut")]
    public bool? HasScheduledCheckOut { get; set; }

    // [FromQuery(Name = "isApproved")]
    public bool? IsApproved { get; set; }
}
