using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.Schedule.Request;

public class DuplicateScheduledOutSourceShiftRequestDto
{
    /// <summary>
    /// Project id (*)
    /// </summary>
    [Required]
    [JsonPropertyName("projectId")]
    public required Guid ProjectId { get; set; }

    /// <summary>
    /// Working date (copy to this date) (*)
    /// Format: yyyy-MM-dd
    /// </summary>
    [Required]
    [JsonPropertyName("workingDate")]
    public DateOnly WorkingDate { get; set; }
}