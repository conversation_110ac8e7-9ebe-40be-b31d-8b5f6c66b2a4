﻿using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Databases.Models;

namespace Kantoku.Api.Dtos.Vendor.Request;

/// <summary>
/// Data transfer object for updating vendor information.
/// </summary>
public class UpdateVendorRequestDto
{
    /// <summary>
    /// The unique code for the vendor
    /// </summary>
    public string? VendorCode { get; set; }

    /// <summary>
    /// The name of the vendor
    /// </summary>
    public string? VendorName { get; set; }

    /// <summary>
    /// The sub-name of the vendor, if applicable.
    /// </summary>
    public string? VendorSubName { get; set; }

    /// <summary>
    /// The corporate number of the vendor, if applicable.
    /// </summary>
    public string? CorporateNumber { get; set; }

    /// <summary>
    /// The address of the vendor, if applicable.
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// The phone number of the vendor, if applicable.
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// The email address of the vendor, if applicable.
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// The contact person for the vendor, if applicable.
    /// </summary>
    public ContactPerson? ContactPerson { get; set; }

    /// <summary>
    /// A description of the vendor, if applicable.
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// The logo of the vendor, if applicable.
    /// </summary>
    public IFormFile? Logo { get; set; }
}

