﻿namespace Kantoku.Api.Databases.Models;

public class Status
{
    public string StatusCode { get; set; } = null!;

    public string Group { get; set; } = null!;

    public IEnumerable<TranslatedStatus> TranslatedStatus { get; set; } = [];

    public virtual ICollection<Request> Requests { get; set; } = [];

    public virtual ICollection<Employee> Employees { get; set; } = [];

    public virtual ICollection<Project> Projects { get; set; } = [];
}

public class TranslatedStatus
{
    public string LanguageCode { get; set; } = null!;
    public string StatusName { get; set; } = null!;
}
