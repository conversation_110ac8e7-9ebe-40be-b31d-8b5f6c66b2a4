using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.Schedule.Request;

public class CreateScheduledOutSourceShiftRequestDto
{
    /// <summary>
    /// (*) The unique identifier of the outsource to assign the shift to
    /// </summary>
    [Required]
    [JsonPropertyName("outSourceId")]
    public required Guid OutSourceId { get; set; }

    /// <summary>
    /// (*) The start time of the outsource's shift
    /// Format: HH:mm:ss
    /// </summary>
    [Required]
    [JsonPropertyName("startTime")]
    public required TimeOnly StartTime { get; set; }

    /// <summary>
    /// (*) The end time of the employee's shift
    /// Format: HH:mm:ss
    /// </summary>
    [Required]
    [JsonPropertyName("endTime")]
    public required TimeOnly EndTime { get; set; }

    /// <summary>
    /// (*) The role assigned to the outsource for this shift
    /// The role has been assigned based on outsource's ability, use as string code
    /// </summary>
    [Required]
    [JsonPropertyName("assignedWorkload")]
    public float AssignedWorkload { get; set; }

    /// <summary>
    /// (*) The role assigned to the outsource for this shift
    /// The role has been assigned based on outsource's ability, use as string code
    /// </summary>
    [Required]
    [JsonPropertyName("assignedRole")]
    public string? AssignedRole { get; set; }
}

public class CreateScheduledOutSourceShiftRequestDto2 : CreateScheduledOutSourceShiftRequestDto
{
    /// <summary>
    /// (*) The unique identifier of the project schedule to assign the shift to
    /// </summary>
    [Required]
    [JsonPropertyName("projectId")]
    public required Guid ProjectId { get; set; }

    /// <summary>
    /// (*) The working date of the shift
    /// Format: YYYY-MM-DD
    /// </summary>
    [Required]
    [JsonPropertyName("workingDate")]
    public required DateOnly WorkingDate { get; set; }
}
