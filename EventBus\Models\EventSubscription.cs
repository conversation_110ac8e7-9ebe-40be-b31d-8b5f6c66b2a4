namespace EventBus.Models;

/// <summary>
/// Represents an event subscription
/// </summary>
public class EventSubscription
{
    /// <summary>
    /// The event type name
    /// </summary>
    public string EventType { get; set; } = string.Empty;

    /// <summary>
    /// The handler type name
    /// </summary>
    public string HandlerType { get; set; } = string.Empty;

    /// <summary>
    /// Whether the subscription is active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// When the subscription was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
