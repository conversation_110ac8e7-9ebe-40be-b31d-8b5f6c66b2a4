﻿using Kantoku.Api.Dtos.Item.Request;
using Kantoku.Api.Utils.Attributes.Binder;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Dtos.InputCost.Request;

/// <summary>
/// Data transfer object for updating an input cost
/// </summary>
public class UpdateInputCostRequestDto
{
    /// <summary>
    /// Title of the input cost
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// Issue date of the input cost
    /// </summary>
    public string? IssueDate { get; set; }

    /// <summary>
    /// Payment date of the input cost
    /// </summary>
    public string? PaymentDate { get; set; }

    /// <summary>
    /// Original reference number
    /// </summary>
    public string? OriginalNumber { get; set; }

    /// <summary>
    /// ID of the associated construction
    /// </summary>
    public string? ConstructionId { get; set; }

    /// <summary>
    /// ID of the entry type
    /// </summary>
    public string? EntryTypeId { get; set; }

    /// <summary>
    /// ID of the vendor
    /// </summary>
    public string? VendorId { get; set; }

    /// <summary>
    /// ID of the payment type
    /// </summary>
    public string? PaymentTypeId { get; set; }

    /// <summary>
    /// Description of the input cost
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Total amount of the input cost
    /// </summary>
    public long? TotalAmount { get; set; }

    /// <summary>
    /// Collection of input cost items
    /// </summary>
    [FromForm]
    [ModelBinder(BinderType = typeof(JsonModelBinder))]
    public IEnumerable<UpdateInputCostItemDto>? InputCostItems { get; set; }

    /// <summary>
    /// The image of the input cost
    /// </summary>
    [FromForm]
    public IEnumerable<IFormFile>? Images { get; set; }
}

/// <summary>
/// Data transfer object for updating an input cost item
/// </summary>
public class UpdateInputCostItemDto
{
    /// <summary>
    /// ID of the input cost item
    /// </summary>
    public string? InputCostItemId { get; set; }

    /// <summary>
    /// Transaction date of the input cost item
    /// </summary>
    public string? TransactionDate { get; set; }

    /// <summary>
    /// ID of the associated item
    /// </summary>
    public CreateItemBaseRequestDto? Item { get; set; }

    /// <summary>
    /// Unit of measurement
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// Quantity of items
    /// </summary>
    public float? Quantity { get; set; }

    /// <summary>
    /// Price per unit
    /// </summary>
    public int? Price { get; set; }

    /// <summary>
    /// Tax rate applied
    /// </summary>
    public float? TaxRate { get; set; }

    /// <summary>
    /// Total amount before tax
    /// </summary>
    public long? TotalNonTaxed { get; set; }

    /// <summary>
    /// Total amount after tax
    /// </summary>
    public long? TotalTaxed { get; set; }

    /// <summary>
    /// Description of the input cost item
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Flag indicating if the item is deleted
    /// </summary>
    public bool? IsDeleted { get; set; }
}
