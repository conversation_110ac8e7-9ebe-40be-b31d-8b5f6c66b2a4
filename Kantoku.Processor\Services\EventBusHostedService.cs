using EventBus.Events;
using EventBus.Interfaces;
using Kantoku.Processor.Services.EventHandlers;

namespace Kantoku.Processor.Services;

/// <summary>
/// Hosted service that manages the EventConsumer lifecycle for continuous message processing
/// </summary>
public class EventBusHostedService : BackgroundService
{
    private readonly IEventConsumer _eventConsumer;
    private readonly ILogger<EventBusHostedService> _logger;

    public EventBusHostedService(
        IEventConsumer eventConsumer,
        ILogger<EventBusHostedService> logger)
    {
        _eventConsumer = eventConsumer;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Starting EventConsumer hosted service...");

        // Subscribe to events
        _eventConsumer.Subscribe<AttendanceEvent, AttendanceEventHandler>();

        // Start the consumer to begin listening for messages
        await _eventConsumer.StartAsync(stoppingToken);

        _logger.LogInformation("EventConsumer hosted service started successfully");

        // Keep the service running until cancellation is requested
        try
        {
            await Task.Delay(Timeout.Infinite, stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("EventConsumer hosted service is stopping...");
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping EventConsumer hosted service...");

        await _eventConsumer.StopAsync(cancellationToken);

        await base.StopAsync(cancellationToken);

        _logger.LogInformation("EventConsumer hosted service stopped");
    }
}
