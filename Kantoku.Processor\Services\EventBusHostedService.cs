using EventBus.Events;
using EventBus.Interfaces;
using Kantoku.Processor.Services.EventHandlers;

namespace Kantoku.Processor.Services;

/// <summary>
/// Hosted service that manages the EventConsumer lifecycle for continuous message processing
/// </summary>
public class EventBusHostedService : BackgroundService
{
    private readonly IEventConsumer _eventConsumer;
    private readonly ILogger<EventBusHostedService> _logger;

    public EventBusHostedService(
        IEventConsumer eventConsumer,
        ILogger<EventBusHostedService> logger)
    {
        _eventConsumer = eventConsumer;
        _logger = logger;
    }

    protected override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Starting EventConsumer hosted service...");

        // Subscribe to events
        _eventConsumer.Subscribe<AttendanceEvent, AttendanceEventHandler>();

        _logger.LogInformation("EventConsumer hosted service started successfully");

        return Task.CompletedTask;
    }
}
