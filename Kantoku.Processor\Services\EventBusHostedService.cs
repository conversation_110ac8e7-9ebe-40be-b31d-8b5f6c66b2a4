using EventBus.Events;
using EventBus.Interfaces;
using Kantoku.Processor.Services.EventHandlers;

namespace Kantoku.Processor.Services;

/// <summary>
/// Hosted service that manages the EventBus lifecycle for continuous message processing
/// </summary>
public class EventBusHostedService : BackgroundService
{
    private readonly IEventBus _eventBus;
    private readonly ILogger<EventBusHostedService> _logger;

    public EventBusHostedService(
        IEventBus eventBus,
        ILogger<EventBusHostedService> logger)
    {
        _eventBus = eventBus;
        _logger = logger;
    }

    protected override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Starting EventBus hosted service...");

        // Subscribe to events
        _eventBus.Subscribe<AttendanceEvent, AttendanceEventHandler>();
        
        _logger.LogInformation("EventBus hosted service started successfully");

        return Task.CompletedTask;
    }
}
