using System.Text;

namespace Kantoku.Api.Middlewares.Logging;

public class RequestLogger : IMiddleware
{
    private readonly Serilog.ILogger logger;

    public RequestLogger(Serilog.ILogger logger)
    {
        this.logger = logger.ForContext<RequestLogger>();
    }

    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        StringBuilder logBuilder = new StringBuilder();

        try
        {
            if (context.Request.Method == "OPTIONS")
            {
                await next(context);
            }
            else
            {
                logBuilder.Append("Request = ").Append(" \n")
                                .Append("Request to : ").Append(context.Request.Path).Append(" \n")
                                .Append("Method     : ").Append(context.Request.Method).Append(" \n")
                                .Append("Header     : ").Append(string.Join(", ", context.Request.Headers.Select(h => $"{h.Key}: {h.Value}").ToArray())).Append(" \n")
                                .Append("Params     : ").Append(string.Join(", ", context.Request.Query.Select(h => $"{h.Key}: {h.Value}").ToArray())).Append(" \n");

                if (context.Request.ContentType == "application/json")
                {
                    context.Request.EnableBuffering();
                    using var reader = new StreamReader(context.Request.Body, Encoding.UTF8, leaveOpen: true);
                var body = await reader.ReadToEndAsync();

                    logBuilder.Append("Body       : ").Append(body).Append(" \n")
                            .Append(" \n");

                    context.Request.Body.Position = 0;
                }
                logger.Information(logBuilder.ToString());

                await next(context);
            }
        }
        catch (Exception e)
        {
            logger.Error(e, "An error occurred while logging the request: {Error} at {StackTrace}", e.Message, e.StackTrace);
        }
    }
}

