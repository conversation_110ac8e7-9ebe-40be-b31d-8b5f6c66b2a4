using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Newtonsoft.Json;
using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace Kantoku.Api.Databases.Configurations;

public class InputCostConfiguration(string schema) : IEntityTypeConfiguration<InputCost>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<InputCost> builder)
    {
        builder.HasKey(e => e.InputCostUid).HasName("input_cost_pkey");

        builder.ToTable("input_cost", Schema, tb => tb.HasComment("Thông tin phiếu nhập chi phí"));

        builder.Property(e => e.InputCostUid)
            .HasColumnName("input_cost_uid");
        builder.Property(e => e.Title)
            .HasColumnName("title");
        builder.Property(e => e.IssueDate)
            .HasColumnType("date")
            .HasColumnName("issue_date");
        builder.Property(e => e.PaymentDate)
            .HasColumnType("date")
            .HasColumnName("payment_date");
        builder.Property(e => e.OriginalNumber)
            .HasColumnName("original_number");
        builder.Property(e => e.OrgUid)
            .HasColumnName("org_uid");
        builder.Property(e => e.ConstructionUid)
            .HasColumnName("construction_uid");
        builder.Property(e => e.EntryTypeUid)
            .HasColumnName("entry_type_uid");
        builder.Property(e => e.VendorUid)
            .HasColumnName("vendor_uid");
        builder.Property(e => e.PaymentTypeUid)
            .HasColumnName("payment_type_uid");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.TotalAmount)
            .HasColumnName("total_amount");
        builder.Property(e => e.ImageUrls)
            .HasColumnName("image_urls")
            .HasColumnType("jsonb")
            .HasConversion(
                v => JsonConvert.SerializeObject(v),
                v => JsonConvert.DeserializeObject<ICollection<string>>(v)
            ).Metadata.SetValueComparer(
                new ValueComparer<ICollection<string>>(
                    (c1, c2) => (c1 == null && c2 == null) || (c1 != null && c2 != null && c1.SequenceEqual(c2)),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()
                )
            );
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");

        builder.HasOne(d => d.EntryType).WithMany(p => p.InputCosts)
            .HasForeignKey(d => d.EntryTypeUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("input_cost_entry_type_id_fkey");

        builder.HasOne(d => d.PaymentType).WithMany(p => p.InputCosts)
            .HasForeignKey(d => d.PaymentTypeUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("input_cost_payment_type_id_fkey");

        builder.HasOne(d => d.Construction).WithMany(p => p.InputCosts)
            .HasForeignKey(d => d.ConstructionUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("input_cost_construction_id_fkey");

        builder.HasOne(d => d.Vendor).WithMany(p => p.InputCosts)
            .HasForeignKey(d => d.VendorUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("input_cost_vendor_id_fkey");

        builder.HasOne(d => d.Org).WithMany(p => p.InputCosts)
            .HasForeignKey(d => d.OrgUid)
            .OnDelete(DeleteBehavior.Restrict)
            .HasConstraintName("input_cost_org_id_fkey");
    }
}
