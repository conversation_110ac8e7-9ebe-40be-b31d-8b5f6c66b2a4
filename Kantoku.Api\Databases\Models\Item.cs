using System.Text.Json;
using System.Text.Json.Nodes;
using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Models;

public class Item : AuditableEntity
{
    public Guid ItemUid { get; set; }
    public Guid CategoryUid { get; set; }
    public Guid? ManufacturerUid { get; set; }

    [AuditProperty]
    public string ItemCode { get; set; } = null!;

    [AuditProperty]
    public string ItemName { get; set; } = null!;

    [AuditProperty]
    public string? ItemSubName { get; set; }

    [AuditProperty]
    public string? Size { get; set; }

    [AuditProperty]
    public string? SerialNumber { get; set; }

    [AuditProperty]
    public EquipmentAttribute? EquipmentAttribute { get; set; }

    [AuditProperty]
    public string? Description { get; set; }

    public Guid OrgUid { get; set; }
    public bool IsDeleted { get; set; }
    public string? ImageUrl { get; set; }

    public virtual Org Org { get; set; } = null!;

    public virtual Manufacturer? Manufacturer { get; set; }

    public virtual Category Category { get; set; } = null!;

    public virtual ICollection<InputCostItem> InputCostItems { get; set; } = [];

    public virtual ICollection<ItemPrice> ItemPrices { get; set; } = [];
}

public class EquipmentAttribute
{
    public string? Model { get; set; }

    public string? FuelConsumption { get; set; }

    public string? EquipmentStatus { get; set; }

    public DateTime? LastMaintenanceDate { get; set; }
}