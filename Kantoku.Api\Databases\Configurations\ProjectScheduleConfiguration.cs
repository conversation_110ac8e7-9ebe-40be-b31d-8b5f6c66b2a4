using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class ProjectScheduleConfiguration(string schema) : IEntityTypeConfiguration<ProjectSchedule>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<ProjectSchedule> builder)
    {
        builder.HasKey(e => e.ProjectScheduleUid).HasName("project_schedule_pkey");

        builder.ToTable("project_schedule", Schema);

        builder.HasIndex(e => new { e.WorkingDate, e.ProjectUid }, "uk_project_schedule").IsUnique();

        builder.Property(e => e.ProjectScheduleUid)
            .HasColumnName("project_schedule_uid");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.PlannedWorkload)
            .HasColumnName("planned_workload");
        builder.Property(e => e.PresignedWorkload)
            .HasColumnName("presigned_workload");
        builder.Property(e => e.ProjectUid)
            .HasColumnName("project_uid");
        builder.Property(e => e.WorkingDate)
            .HasColumnType("date")
            .HasColumnName("working_date");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");

        builder.HasOne(d => d.Project).WithMany(p => p.ProjectSchedules)
            .HasForeignKey(d => d.ProjectUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("project_schedule_project_id_fkey");
    }
}
