using Kantoku.Api.Dtos.Auth.Request;
using Kantoku.Api.Dtos.UserInfo.Request;

namespace Kantoku.Api.Dtos.Employee.Request;

public class UpdateEmployeeRequestDto
{
    /// <summary>
    /// List of employee email addresses
    /// </summary>
    public IEnumerable<UpdateEmployeeMailRequestDto>? EmployeeMails { get; set; }

    /// <summary>
    /// List of employee phone numbers
    /// </summary>
    public IEnumerable<UpdateEmployeePhoneRequestDto>? EmployeePhones { get; set; }

    /// <summary>
    /// Employee's residential address
    /// </summary>
    public string? EmployeeAddress { get; set; }

    /// <summary>
    /// Unique employee identification code
    /// </summary>
    public string? EmployeeCode { get; set; }

    /// <summary>
    /// ID of the organizational structure
    /// </summary>
    public string? StructureId { get; set; }

    /// <summary>
    /// ID of the employee's position
    /// </summary>
    public string? PositionId { get; set; }

    /// <summary>
    /// Type of employment (e.g. full-time, part-time)
    /// </summary>
    public bool? EmployeeType { get; set; }

    /// <summary>
    /// Current working status of the employee
    /// </summary>
    public string? WorkingStatus { get; set; }

    /// <summary>
    /// Employment start date
    /// </summary>
    public string? WorkingFromDate { get; set; }

    /// <summary>
    /// Employment end date
    /// </summary>
    public string? WorkingToDate { get; set; }

    /// <summary>
    /// Employee's salary in month
    /// </summary>
    public int? SalaryInMonth { get; set; }

    /// <summary>
    /// Standard working hours of the employee
    /// </summary>
    public float? StandardWorkingHours { get; set; }

    /// <summary>
    /// List of role IDs assigned to the employee
    /// </summary>
    public IEnumerable<string>? RoleIds { get; set; }
}

public class UpdateEmployeeMailRequestDto
{
    /// <summary>
    /// Email address
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Indicates if this is the primary email address
    /// </summary>
    public bool IsPrimary { get; set; } = false;
}

public class UpdateEmployeePhoneRequestDto
{
    /// <summary>
    /// Phone number
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// Indicates if this is the primary phone number
    /// </summary>
    public bool IsPrimary { get; set; } = false;
}
