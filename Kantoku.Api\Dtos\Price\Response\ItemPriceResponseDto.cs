using Kantoku.Api.Dtos.Base.Response;

namespace Kantoku.Api.Dtos.Price.Response;

/// <summary>
/// Represents the response data for item pricing information.
/// </summary>
public class ItemPriceResponseDto : BaseResponseDto
{
    /// <summary>
    ///  the unique identifier for the item price. (*)
    /// </summary>
    public string? ItemPriceId { get; set; }

    /// <summary>
    ///  the unique identifier for the item. (*)
    /// </summary>
    public string? ItemId { get; set; }

    /// <summary>
    ///  the code of the item.
    /// </summary>
    public string? ItemCode { get; set; }

    /// <summary>
    ///  the name of the item.
    /// </summary>
    public string? ItemName { get; set; }

    /// <summary>
    ///  the unique identifier for the vendor. (*)
    /// </summary>
    public string? VendorId { get; set; }

    /// <summary>
    ///  the code of the vendor.
    /// </summary>
    public string? VendorCode { get; set; }

    /// <summary>
    ///  the name of the vendor.
    /// </summary>
    public string? VendorName { get; set; }

    /// <summary>
    ///  the unit of measurement for the item.
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    ///  the price of the item. (*)
    /// </summary>
    public string? Price { get; set; }

    /// <summary>
    ///  the date from which the price is valid.
    /// </summary>
    public string? ValidFrom { get; set; }

    /// <summary>
    ///  the date until which the price is valid.
    /// </summary>
    public string? ValidTo { get; set; }
}