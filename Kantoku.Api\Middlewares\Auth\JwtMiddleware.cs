using Kantoku.Api.Dtos.Auth;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Constants;
using Microsoft.AspNetCore.Authorization;

namespace Kantoku.Api.Middlewares.Auth
{
    public class JwtMiddleware : IMiddleware
    {
        private readonly ITokenService tokenService;
        private readonly ITResponseFactory responseFactory;
        private readonly Serilog.ILogger logger;
        public JwtMiddleware(ITokenService tokenService, ITResponseFactory responseFactory, Serilog.ILogger logger)
        {
            this.logger = logger.ForContext<JwtMiddleware>();
            this.tokenService = tokenService;
            this.responseFactory = responseFactory;
        }

        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            // Skip validate for preflight request
            if (context.Request.Method == HttpMethods.Options)
            {
                await next(context);
                return;
            }

            var endpoint = context.GetEndpoint();
            if (endpoint?.Metadata?.GetMetadata<IAllowAnonymous>() is object)
            {
                await next(context);
                return;
            }
            var token = context.Request.Headers.Authorization.FirstOrDefault()?.Replace("Bearer ", "");

            if (!string.IsNullOrEmpty(token))
            {
                try
                {
                    var isValidate = tokenService.ValidateAccessToken(token, out AuthenticatedUser? authenticatedUser);
                    if (!isValidate || authenticatedUser is null || string.IsNullOrEmpty(authenticatedUser.Scope))
                    {
                        logger.Warning("Token validation failed - Invalid user claim");
                        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                        await context.Response.WriteAsJsonAsync(responseFactory.Fail<object>(ResponseCodeConstant.TOKEN_INVALID));
                        return;
                    }
                    if (authenticatedUser.Scope.Equals(ScopeConstant.ACCOUNT))
                    {
                        context.Items[ClaimConstant.ACCOUNT_UID] = authenticatedUser?.AccountUid;
                        await next(context);
                        return;
                    }
                    else if (authenticatedUser.Scope.Equals(ScopeConstant.ORG))
                    {
                        context.Items[ClaimConstant.ACCOUNT_UID] = authenticatedUser?.AccountUid;
                        context.Items[ClaimConstant.ORG_UID] = authenticatedUser?.OrgUid;
                        context.Items[ClaimConstant.EMPLOYEE_UID] = authenticatedUser?.EmployeeUid;
                        await next(context);
                        return;
                    }
                    else
                    {
                        logger.Warning("Token validation failed - Invalid user claim");
                        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                        await context.Response.WriteAsJsonAsync(responseFactory.Fail<object>(ResponseCodeConstant.TOKEN_INVALID));
                        return;
                    }
                }
                catch (Exception e)
                {
                    logger.Error(e, "Token validation failed");
                    context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                    await context.Response.WriteAsJsonAsync(responseFactory.Fail<object>(e.Message));
                    return;
                }
            }
            else
            {
                logger.Information("Token is null or empty");
                context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                await context.Response.WriteAsJsonAsync(responseFactory.Fail<object>(ResponseCodeConstant.TOKEN_INVALID));
                return;
            }
        }
    }
}
