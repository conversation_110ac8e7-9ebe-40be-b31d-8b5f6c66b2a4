using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.OutSource.Request;
using Kantoku.Api.Dtos.OutSource.Response;
using Kantoku.Api.Utils.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class OutSourceMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps an OutSource entity to an OutSourceResponseDto
    /// </summary>
    public static OutSourceResponseDto ToOutSourceResponseDto(this OutSource outSource)
    {
        if (outSource == null)
            return new OutSourceResponseDto();

        return new OutSourceResponseDto
        {
            OutSourceId = outSource.OutSourceUid.ToString(),
            OutSourceCode = outSource.OutSourceCode,
            OutSourceName = outSource.OutSourceName,
            Description = outSource.Description,
            Expertise = outSource.Expertise,
            CorporateNumber = outSource.CorporateNumber,
            Address = outSource.Address,
            PhoneNumber = outSource.PhoneNumber,
            Email = outSource.Email,
            ContactPerson = outSource.ContactPerson,
            LogoUrl = outSource.LogoUrl
        };
    }

    /// <summary>
    /// Maps a collection of OutSource entities to an OutSourcesResponseDto
    /// </summary>
    public static OutSourcesResponseDto ToOutSourcesResponseDto(
        this IEnumerable<OutSource> outSources,
        int pageNum,
        int pageSize,
        int totalRecords)
    {
        if (outSources == null)
            return new OutSourcesResponseDto();

        return new OutSourcesResponseDto
        {
            Items = outSources.Select(os => os.ToOutSourceResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    /// <summary>
    /// Maps an OutSourcePrice entity to an OutSourcePriceResponseDto
    /// </summary>
    public static OutSourcePriceResponseDto ToOutSourcePriceResponseDto(
        this OutSourcePrice outSourcePrice,
        OutSource? outSource = null)
    {
        if (outSourcePrice == null)
            return new OutSourcePriceResponseDto();

        return new OutSourcePriceResponseDto
        {
            OutSourcePriceId = outSourcePrice.OutSourcePriceUid.ToString(),
            OutSourceId = outSourcePrice.OutSourceUid.ToString(),
            OutSourceCode = outSource?.OutSourceCode,
            OutSourceName = outSource?.OutSourceName,
            PricePerDay = outSourcePrice.PricePerDay,
            EffectiveDate = outSourcePrice.EffectiveDate?.ToString("yyyy-MM-dd"),
            Description = outSourcePrice.Description
        };
    }

    /// <summary>
    /// Maps a collection of OutSourcePrice entities to an OutSourcePricesResponseDto
    /// </summary>
    public static OutSourcePricesResponseDto ToOutSourcePricesResponseDto(
        this IEnumerable<OutSourcePrice> outSourcePrices,
        Dictionary<Guid, OutSource>? outSourceMap = null,
        int pageNum = 1,
        int pageSize = 10,
        int totalRecords = 0)
    {
        if (outSourcePrices == null)
            return new OutSourcePricesResponseDto();

        return new OutSourcePricesResponseDto
        {
            Items = outSourcePrices.Select(op =>
            {
                OutSource? outSource = null;
                if (outSourceMap != null && outSourceMap.TryGetValue(op.OutSourceUid, out var os))
                {
                    outSource = os;
                }
                return op.ToOutSourcePriceResponseDto(outSource);
            }),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords > 0 ? totalRecords : outSourcePrices.Count()
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateOutSourceRequestDto to an OutSource entity
    /// </summary>
    public static OutSource? ToEntity(this CreateOutSourceRequestDto dto, Guid orgUid)
    {
        if (dto == null)
            return null;

        var entity = new OutSource
        {
            OutSourceUid = GuidHelper.GenerateUUIDv7(),
            OrgUid = orgUid,
            OutSourceCode = dto.OutSourceCode,
            OutSourceName = dto.OutSourceName,
            Description = dto.Description,
            Expertise = dto.Expertise,
            CorporateNumber = dto.CorporateNumber,
            Address = dto.Address,
            PhoneNumber = dto.PhoneNumber,
            Email = dto.Email,
            ContactPerson = dto.ContactPerson,
            IsDeleted = false
        };

        if (dto.Price is not null)
        {
            var price = dto.Price.ToEntity(entity.OutSourceUid, DateOnly.FromDateTime(DateTime.Today));
            if (price is not null)
            {
                entity.OutSourcePrices.Add(price);
            }
        }

        return entity;
    }

    /// <summary>
    /// Maps a OutSourcePriceRequestDto to an OutSourcePrice entity
    /// </summary>
    public static OutSourcePrice? ToEntity(
        this OutSourcePriceRequestDto dto,
        Guid outSourceUid,
        DateOnly effectiveDate)
    {
        if (dto == null)
            return null;

        var entity = new OutSourcePrice
        {
            OutSourcePriceUid = GuidHelper.GenerateUUIDv7(),
            OutSourceUid = outSourceUid,
            PricePerDay = dto.PricePerDay,
            EffectiveDate = effectiveDate,
            Description = dto.Description,
            IsDeleted = false
        };

        return entity;
    }

    /// <summary>
    /// Updates an OutSource entity from an UpdateOutSourceRequestDto
    /// </summary>
    public static void UpdateFromDto(this OutSource entity, UpdateOutSourceRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (dto.OutSourceCode != null)
            entity.OutSourceCode = dto.OutSourceCode;

        if (dto.OutSourceName != null)
            entity.OutSourceName = dto.OutSourceName;

        if (dto.Description != null)
            entity.Description = dto.Description;

        if (dto.Expertise != null)
            entity.Expertise = dto.Expertise;

        if (dto.CorporateNumber != null)
            entity.CorporateNumber = dto.CorporateNumber;

        if (dto.Address != null)
            entity.Address = dto.Address;

        if (dto.PhoneNumber != null)
            entity.PhoneNumber = dto.PhoneNumber;

        if (dto.Email != null)
            entity.Email = dto.Email;

        if (dto.ContactPerson != null)
            entity.ContactPerson = dto.ContactPerson;

        if (dto.Price is not null)
        {
            var newPrice = dto.Price.ToEntity(entity.OutSourceUid, DateOnly.FromDateTime(DateTime.Today));
            if (newPrice is not null)
            {
                entity.OutSourcePrices.Add(newPrice);
            }
        }
    }

    #endregion
}