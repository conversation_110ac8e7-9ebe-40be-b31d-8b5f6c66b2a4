using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.FunctionAccessibility.Response;

public class RolePrivilegesResponseDto
{
    [JsonPropertyName("Items")]
    public IEnumerable<RolePrivilegeResponseDto>? Items { get; set; }
}

public class RolePrivilegeResponseDto
{
    [JsonPropertyName("StructureId")]
    public string? StructureId { get; set; }

    [JsonPropertyName("RoleId")]
    public string? RoleId { get; set; }

    [JsonPropertyName("RoleName")]
    public string? RoleName { get; set; }

    [JsonPropertyName("FunctionItems")]
    public IEnumerable<FunctionPrivilegeResponseDto>? FunctionPrivileges { get; set; }
}
