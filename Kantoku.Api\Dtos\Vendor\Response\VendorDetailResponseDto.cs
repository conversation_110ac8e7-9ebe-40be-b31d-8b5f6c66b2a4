﻿using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Base.Response;

namespace Kantoku.Api.Dtos.Vendor.Response;

/// <summary>
/// Represents the response data transfer object for a vendor.
/// </summary>
public class VendorDetailResponseDto : VendorResponseDto
{
    /// <summary>
    ///  the list of vendor accounts.
    /// </summary>
    public IEnumerable<VendorInvoice>? VendorInvoices { get; set; }

    /// <summary>
    ///  the page number of input costs.
    /// </summary>
    public int PageNum { get; set; }

    /// <summary>
    ///  the page size of input costs.
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    ///  the total number of input costs.
    /// </summary>
    public int TotalRecords { get; set; }
}

public class VendorInvoice
{
    /// <summary>
    ///  the unique identifier for the vendor invoice.
    /// </summary>
    public string? InputCostId { get; set; }

    /// <summary>
    ///  the invoice number.
    /// </summary>
    public string? OriginalNumber { get; set; }

    /// <summary>
    ///  the unique identifier for the vendor.
    /// </summary>
    public string? InvoiceTitle { get; set; }

    /// <summary>
    ///  the invoice date.
    /// </summary>
    public string? IssueDate { get; set; }

    /// <summary>
    ///  the payment date.
    /// </summary>
    public string? PaymentDate { get; set; }

    /// <summary>
    ///  the invoice amount.
    /// </summary>
    public decimal? TotalAmount { get; set; }

    /// <summary>
    /// Description
    /// </summary>
    public string? Description { get; set; }
}

