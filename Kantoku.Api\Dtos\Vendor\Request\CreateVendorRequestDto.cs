﻿using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Databases.Models;

namespace Kantoku.Api.Dtos.Vendor.Request;

/// <summary>
/// DTO for creating a vendor.
/// </summary>
public class CreateVendorRequestDto
{
    /// <summary>
    /// The unique code for the vendor. (*)
    /// </summary>
    [Required]
    public string VendorCode { get; set; } = null!;

    /// <summary>
    /// The name of the vendor. (*)
    /// </summary>
    [Required]
    public string VendorName { get; set; } = null!;

    /// <summary>
    /// The sub-name of the vendor, if applicable.
    /// </summary>
    public string? VendorSubName { get; set; }

    /// <summary>
    /// The corporate number of the vendor, if applicable.
    /// </summary>
    public string? CorporateNumber { get; set; }

    /// <summary>
    /// The address of the vendor, if applicable.
    /// </summary>
    public string? Address { get; set; }
    /// <summary>
    /// The phone number of the vendor, if applicable.
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// The email address of the vendor, if applicable.
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// The contact person for the vendor, if applicable.
    /// </summary>
    public ContactPerson? ContactPerson { get; set; }

    /// <summary>
    /// A description of the vendor, if applicable.
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// The logo of the vendor, if applicable.
    /// </summary>
    public IFormFile? Logo { get; set; }
}
