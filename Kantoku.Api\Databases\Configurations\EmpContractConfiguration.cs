using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class EmpContractConfiguration(string schema) : IEntityTypeConfiguration<EmpContract>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<EmpContract> builder)
    {
        builder.<PERSON>Key(e => e.EmpContractUid).HasName("emp_contract_pkey");

        builder.ToTable("employee_contract", Schema, tb => tb.HasComment("Thông tin hợp đồng (tạm)"));

        builder.Property(e => e.EmpContractUid)
            .HasColumnName("emp_contract_uid");
        builder.Property(e => e.Description)
            .HasColumnName("description");
        builder.Property(e => e.EmpContractCode)
            .HasColumnName("emp_contract_code");
        builder.Property(e => e.EmpContractName)
            .HasColumnName("emp_contract_name");
        builder.Property(e => e.EmployeeUid)
            .HasColumnName("employee_uid");
        builder.Property(e => e.ExpireTime)
            .HasColumnType("timestamp")
            .HasColumnName("expire_time");
        builder.Property(e => e.Status)
            .HasColumnName("status");
        builder.Property(e => e.IsDeleted)
            .HasDefaultValue(false)
            .HasColumnName("is_deleted");

        builder.HasOne(d => d.Employee).WithMany(p => p.EmpContracts)
            .HasForeignKey(d => d.EmployeeUid)
            .OnDelete(DeleteBehavior.ClientSetNull)
            .HasConstraintName("emp_contract_employee_id_fkey");
    }
}
