using System.Globalization;
using Kantoku.Api.Configurations;
using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Middlewares.Locale;
using Microsoft.AspNetCore.Localization;
using Microsoft.OpenApi.Models;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Kantoku.Api.Middlewares.Auth;
using System.Reflection;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.IO;
using Kantoku.Api.Extensions.Services;
using Serilog;
using Serilog.Exceptions;

using Kantoku.Api.Middlewares.Logging;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Services;
using EventBus.Kafka.Extensions;
using EventBus.Interfaces;
using EventBus.Kafka;

namespace Kantoku.Api;

public class Program
{
    public static async Task Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        #region "configuration"
        var env = builder.Environment;
        builder.Configuration.AddEnvironmentVariables();
        builder.Configuration.AddJsonFile($"appsettings.{env.EnvironmentName}.json", optional: false, reloadOnChange: false);
        builder.Services.Configure<AppConfig>(builder.Configuration.GetSection("AppConfig"));
        builder.Services.Configure<AuthConfig>(builder.Configuration.GetSection("AuthConfig"));
        builder.Services.Configure<MailConfig>(builder.Configuration.GetSection("MailConfig"));
        builder.Services.Configure<MinioConfig>(builder.Configuration.GetSection("MinioConfig"));
        builder.Services.Configure<RedisConfig>(builder.Configuration.GetSection("RedisConfig"));
        builder.Services.Configure<PostgreDbConfig>(builder.Configuration.GetSection("PostgreDbConfig"));
        #endregion

        #region "application"
        builder.Services.AddEndpointsApiExplorer();
        builder.Services.AddHttpClient();
        builder.Services.AddSwaggerGen(option =>
        {
            option.SwaggerDoc("v1", new OpenApiInfo { Title = "Kantoku API", Version = "v1" });
            option.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                In = ParameterLocation.Header,
                Description = "Please enter a valid token",
                Name = "Authorization",
                Type = SecuritySchemeType.Http,
                BearerFormat = "JWT",
                Scheme = "Bearer"
            });
            option.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type=ReferenceType.SecurityScheme,
                                Id="Bearer"
                            },
                            Scheme = "oauth2",
                            Name = "Bearer",
                            In = ParameterLocation.Header,
                        },
                        Array.Empty<string>()
                    }
            });
            var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
            option.IncludeXmlComments(xmlPath);
        });
        builder.Services.AddLocalization(options => options.ResourcesPath = "Resources");
        builder.Services.AddControllers(options =>
        {
            options.Filters.Add(new AllowAnonymousFilter());
            options.AllowEmptyInputInBodyModelBinding = true;
        });
        builder.Services.AddResponseCaching(option => option.MaximumBodySize = 2048);
        builder.Services.AddRouting(options => options.LowercaseUrls = true);
        builder.Services.AddAuthentication(option =>
            {
                option.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                option.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
                option.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
            })
                        .AddJwtBearer(options =>
                            {
                                options.TokenValidationParameters = new TokenValidationParameters
                                {
                                    ValidateIssuer = true,
                                    ValidateAudience = true,
                                    ValidateLifetime = true,
                                    ValidateIssuerSigningKey = true,
                                    ValidIssuer = builder.Configuration["AuthConfig:Issuer"],
                                    ValidAudience = builder.Configuration["AuthConfig:Audience"],
                                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["AuthConfig:SecretKey"]!)),
                                    ClockSkew = TimeSpan.Zero
                                };
                            });
        builder.Services.AddAuthorizationBuilder()
            .AddPolicy(PolicyConstant.ACCOUNT_ACCESS, policy =>
            {
                policy.RequireClaim(ClaimConstant.SCOPE)
                    .RequireClaim(ClaimConstant.ACCOUNT_UID);
            })
            .AddPolicy(PolicyConstant.ORG_ACCESS, policy =>
            {
                policy.RequireClaim(ClaimConstant.SCOPE)
                    .RequireClaim(ClaimConstant.ACCOUNT_UID)
                    .RequireClaim(ClaimConstant.ORG_UID)
                    .RequireClaim(ClaimConstant.EMPLOYEE_UID);
            });

        string[] AllowOrigins = builder.Configuration["AppConfig:AllowOrigins"]!.Split(",", StringSplitOptions.RemoveEmptyEntries);
        string corsPolicies = builder.Configuration["ApplicationName"]!;
        builder.Services.AddCors(options =>
        {
            options.AddPolicy(
                name: corsPolicies,
                builder =>
                    {
                        builder.WithOrigins(AllowOrigins)
                                .AllowAnyHeader()
                                .AllowAnyMethod();
                    });
        });
        builder.Services.AddHttpContextAccessor();
        builder.Host.UseSerilog((context, configuration) =>
        {
            configuration
                .Enrich.FromLogContext()
                .Enrich.WithExceptionDetails()
                .Enrich.WithClientIp()
                .Enrich.WithCorrelationId()
                .WriteTo.Console()
                .WriteTo.Debug()
                // .WriteTo.Elasticsearch(new ElasticsearchSinkOptions(new Uri(builder.Configuration["ElasticSearch:Url"]!))
                // {
                //     AutoRegisterTemplate = true,
                //     IndexFormat = $"{builder.Configuration["ApplicationName"]?.ToLower()}" +
                //                   "-logs" +
                //                   $"-{env.EnvironmentName?.ToLower()}" +
                //                   $"-{DateTime.UtcNow:yyyy-MM}",
                // })
                .WriteTo.File(Path.Combine(env.ContentRootPath, "Logs", "Kantoku.Api", "log-.txt"), rollingInterval: RollingInterval.Day)
                .Enrich.WithProperty("Application", builder.Configuration["ApplicationName"])
                .Enrich.WithEnvironmentName()
                .Enrich.FromLogContext()
                .ReadFrom.Configuration(context.Configuration);

        });
        #endregion

        builder.Services.ServiceCollector();

        #region "EventBus"
        // Only configure EventBus producer for publishing events (no consumer/handler)
        builder.Services.AddKafkaEventProducer(builder.Configuration);
        // Also register IEventBus for backward compatibility
        builder.Services.AddSingleton<IEventBus>(provider =>
        {
            var producer = provider.GetRequiredService<IEventProducer>();
            var logger = provider.GetRequiredService<ILogger<KafkaEventBus>>();
            // Create a dummy consumer that throws on usage since API should only produce
            var dummyConsumer = new DummyEventConsumer();
            return new KafkaEventBus(producer, dummyConsumer, logger);
        });
        #endregion

        #region "Library"
        builder.Services.AddSingleton<RecyclableMemoryStreamManager>();
        #endregion

        #region "middleware"
        builder.Services.AddScoped<CorrelationIdMiddleware>();
        builder.Services.AddScoped<RequestLogger>();
        builder.Services.AddScoped<LocaleMiddleware>();
        builder.Services.AddScoped<JwtMiddleware>();
        builder.Services.AddScoped<ResponseLogger>();
        #endregion

        #region "collector"

        builder.Services.AddDbContext<PostgreDbContext>(options =>
        {
            if (env.IsDevelopment())
            {
                options.EnableDetailedErrors();
                options.EnableSensitiveDataLogging();
            }
        });

        #endregion

        builder.WebHost.ConfigureKestrel((context, options) =>
        {
            options.Limits.MaxConcurrentConnections = 100;
            options.Limits.MaxConcurrentUpgradedConnections = 100;
            options.Limits.MaxRequestBodySize = 12428800;  //10MB
            options.AddServerHeader = false;
        });

        // Register notification services
        builder.Services.AddScoped<FirebaseService>();
        builder.Services.AddScoped<NotificationService>();

        var app = builder.Build();

        app.UseResponseCaching();
        app.UseHttpsRedirection();

        if (app.Environment.IsDevelopment())
        {
            app.UseSwagger();
            app.UseSwaggerUI();
        }

        string langConfig = builder.Configuration.GetValue<string>("AppConfig:DefaultLanguage")!;
        app.UseRequestLocalization(new RequestLocalizationOptions
        {
            DefaultRequestCulture = new RequestCulture(new CultureInfo(langConfig))
        });

        app.UseRouting();

        app.UseMiddleware<CorrelationIdMiddleware>();
        app.UseMiddleware<RequestLogger>();
        app.UseMiddleware<LocaleMiddleware>();
        app.UseMiddleware<JwtMiddleware>();
        app.UseMiddleware<ResponseLogger>();

        app.UseCors(x => x
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials()
              .WithOrigins(corsPolicies)
              .SetIsOriginAllowed(origin => true));

        app.UseAuthentication();
        app.UseAuthorization();

        app.MapControllers();

        var errorService = app.Services.GetRequiredService<MultiLanguageErrorService>();
        try
        {
            // Define path relative to application root (adjust as needed)
            // Ensure your JSON files are copied to the output directory
            string errorFilesPath = Path.Combine(AppContext.BaseDirectory, "Resources", "Errors");
            await errorService.LoadAllMessagesAsync(errorFilesPath); // Load files like ErrorMessages/ErrorMessage.en.json etc.
        }
        catch (Exception ex)
        {
            // Log critical failure during startup
            var logger = app.Services.GetRequiredService<ILogger<Program>>();
            logger.LogCritical(ex, "Failed to preload error messages during application startup.");
            // Optionally prevent the application from starting if loading fails
            // throw;
        }


        app.Run();
    }
}