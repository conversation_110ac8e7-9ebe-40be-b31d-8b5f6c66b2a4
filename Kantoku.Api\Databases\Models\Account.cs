using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Models;

public class Account : AuditableEntity
{
    public Guid AccountUid { get; set; }

    [AuditProperty]
    public string LoginId { get; set; } = null!;

    public string Password { get; set; } = null!;

    public string? HashedPassword { get; set; }

    [AuditProperty]
    public string Email { get; set; } = null!;

    public string AccountType { get; set; } = null!;

    public bool IsLocked { get; set; }

    public bool IsDeleted { get; set; } = false;

    public virtual UserInfo UserInfo { get; set; } = null!;

    public virtual ICollection<Employee> Employees { get; set; } = [];

    public virtual ICollection<AuditLog> AuditLogs { get; set; } = [];
}

