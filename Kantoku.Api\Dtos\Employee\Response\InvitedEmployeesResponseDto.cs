namespace Kantoku.Api.Dtos.Employee.Response;

public class InvitedEmployeesResponseDto
{
    public IEnumerable<InvitedEmployeeResponseDto> Items { get; set; } = [];
    public int PageNum { get; set; }
    public int PageSize { get; set; }
    public int TotalRecords { get; set; }
}

public class InvitedEmployeeResponseDto
{

    public string? InvitationId { get; set; }

    public string? InvitedEmail { get; set; }

    public string? InvitationDescription { get; set; }

    public bool IsAccepted { get; set; }

    public string? AcceptedTime { get; set; }

    public string? ExpiredTime { get; set; }
}