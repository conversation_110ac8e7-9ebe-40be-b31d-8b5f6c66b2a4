using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.Auth.Request;

/// <summary>
/// Data transfer object for user login request
/// </summary>
public class UserLoginRequestDto
{
    /// <summary>
    /// User's login identifier (*)
    /// </summary>
    [Required]
    public required string LoginId { get; set; }

    /// <summary>
    /// User's password (*)
    /// </summary>
    [Required]
    public required string Password { get; set; }

    /// <summary>
    /// Organization identifier (*)
    /// </summary>
    [Required]
    public required string OrgId { get; set; }
}
