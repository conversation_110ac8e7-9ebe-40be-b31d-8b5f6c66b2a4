using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.PaymentType.Request;
using Kantoku.Api.Dtos.PaymentType.Response;
using Kantoku.Api.Utils.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class PaymentTypeMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a PaymentType entity to a PaymentTypeResponseDto
    /// </summary>
    public static PaymentTypeResponseDto ToPaymentTypeResponseDto(this PaymentType paymentType)
    {
        if (paymentType == null)
            return new PaymentTypeResponseDto();

        return new PaymentTypeResponseDto
        {
            PaymentTypeId = paymentType.PaymentTypeUid.ToString(),
            PaymentTypeName = paymentType.PaymentTypeName,
            Description = paymentType.Description
        };
    }

    /// <summary>
    /// Maps a collection of PaymentType entities to a PaymentTypesResponseDto
    /// </summary>
    public static PaymentTypesResponseDto ToPaymentTypesResponseDto(
        this IEnumerable<PaymentType> paymentTypes, 
        int pageNum, 
        int pageSize, 
        int totalRecords)
    {
        if (paymentTypes == null)
            return new PaymentTypesResponseDto();

        return new PaymentTypesResponseDto
        {
            Items = paymentTypes.Select(pt => pt.ToPaymentTypeResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreatePaymentTypeRequestDto to a PaymentType entity
    /// </summary>
    public static PaymentType? ToEntity(this CreatePaymentTypeRequestDto dto, Guid orgUid)
    {
        if (dto == null)
            return null;

        var entity = new PaymentType
        {
            PaymentTypeUid = GuidHelper.GenerateUUIDv7(),
            OrgUid = orgUid,
            PaymentTypeName = dto.PaymentTypeName,
            Description = dto.Description,
            IsDeleted = false
        };

        return entity;
    }

    /// <summary>
    /// Updates a PaymentType entity from an UpdatePaymentTypeRequestDto
    /// </summary>
    public static void UpdateFromDto(this PaymentType entity, UpdatePaymentTypeRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (dto.PaymentTypeName != null)
            entity.PaymentTypeName = dto.PaymentTypeName;

        if (dto.Description != null)
            entity.Description = dto.Description;
    }

    #endregion
} 