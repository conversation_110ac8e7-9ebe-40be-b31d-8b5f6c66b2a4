using Kantoku.Processor.Data.Contexts;
using Kantoku.Processor.Data.Repositories;
using Kantoku.Processor.Data.Seeders;
using Kantoku.Processor.Jobs.Bases;
using Kantoku.Processor.Models.BatchJobManager;
using Kantoku.Processor.Models.Business;
using Kantoku.Processor.Utils;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Processor.Jobs
{
    /// <summary>
    /// Job that calculates the employee rank
    /// </summary>
    public class EmployeeCostCalculateJob : JobBase
    {
        private readonly ILogger<EmployeeCostCalculateJob> _logger;

        // We'll use the IServiceScopeFactory instead of IServiceProvider
        // This allows us to create scopes on demand without relying on 
        // a potentially disposed IServiceProvider
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly DatabaseSeeder _databaseSeeder;

        // Job configuration properties
        public const string JobName = nameof(EmployeeCostCalculateJob);

        public EmployeeCostCalculateJob(
            ILogger<EmployeeCostCalculateJob> logger,
            IServiceScopeFactory serviceScopeFactory,
            DatabaseSeeder databaseSeeder)
        {
            _logger = logger;
            _serviceScopeFactory = serviceScopeFactory;
            _databaseSeeder = databaseSeeder;
        }

        /// <summary>
        /// Ensures the job exists in the database with proper configuration
        /// </summary>
        public async Task EnsureJobExistsAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Checking if {JobName} exists in database", JobName);

            // Create a separate scope to handle the initialization
            using var scope = _serviceScopeFactory.CreateScope();
            var batchJobRepository = scope.ServiceProvider.GetRequiredService<IBatchJobRepository>();

            // Check if job exists, if not, seed it to database
            var job = await batchJobRepository.GetJobByNameAsync(JobName);

            if (job == null)
            {
                await _databaseSeeder.SeedEmployeeCostCalculateJobAsync();
            }
        }

        public override async Task ExecuteAsync()
        {
            LogInformation("Starting employee rank calculate job execution");
            DateTime startTime = DateTime.UtcNow;

            // Create a new scope using the factory which won't be disposed
            using var scope = _serviceScopeFactory.CreateScope();
            var batchJobRepository = scope.ServiceProvider.GetRequiredService<IBatchJobRepository>();
            var appDbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            BatchJobHistory jobHistory = new BatchJobHistory
            {
                FiredId = Guid.NewGuid(),
                JobId = JobConfiguration.JobId,
                StartedAt = startTime,
                Status = JobExecutionStatus.InProgress
            };

            try
            {
                // Create initial job history record
                await batchJobRepository.CreateJobHistoryAsync(jobHistory);

                // Check if job is enabled
                if (!JobConfiguration.IsEnabled)
                {
                    LogInformation("Job is disabled, skipping execution");
                    jobHistory.Status = JobExecutionStatus.Cancelled;
                    jobHistory.ExecutionLog = GetExecutionLog();
                    jobHistory.CompletedAt = DateTime.UtcNow;
                    await batchJobRepository.UpdateJobHistoryAsync(jobHistory);
                    return;
                }

                // Get all employees
                var allEmployees = await appDbContext.Employees
                    .Where(e => !e.IsDeleted)
                    .Include(e => e.Ranking)
                    .Select(e => new
                    {
                        e.EmployeeUid,
                        e.OrgUid,
                        e.SalaryInMonth,
                        e.Ranking
                    })
                    .ToListAsync(CancellationToken);

                var allRankings = await appDbContext.Rankings
                    .Where(r => !r.IsDeleted)
                    .ToListAsync(CancellationToken);

                var allEventCalendars = await appDbContext.EventCalendars
                    .Where(e => !e.IsDeleted)
                    .ToListAsync(CancellationToken);

                //group employee and ranking by org uid
                var employeeGroups = allEmployees.GroupBy(e => e.OrgUid)
                    .ToDictionary(g => g.Key, g => g.ToList());

                foreach (var month in Enumerable.Range(1, 12))
                {
                    var effectiveDateFrom = new DateOnly(DateTime.Now.Year, month, 1);
                    var effectiveDateTo = effectiveDateFrom.AddMonths(1).AddDays(-1);

                    foreach (var orgUid in employeeGroups.Keys)
                    {
                        var rankingsInOrg = allRankings
                            .Where(r => r.OrgUid == orgUid)
                            .ToList();
                        if (rankingsInOrg.Count == 0)
                        {
                            LogError($"No rankings found for org: {orgUid}");
                            continue;
                        }
                        var eventCalendarsInOrg = allEventCalendars
                            .Where(e => e.OrgUid == orgUid)
                            .ToList();
                        var standardWorkingDateCount =
                            CalculateStandardWorkingDateCount(effectiveDateFrom, effectiveDateTo, eventCalendarsInOrg);
                        var employees = employeeGroups[orgUid];
                        foreach (var employee in employees)
                        {
                            var employeeCost = await appDbContext.EmployeeCosts
                                .Where(er => er.EmployeeUid == employee.EmployeeUid)
                                .Where(er => er.EffectiveDate >= effectiveDateFrom && er.EffectiveDate <= effectiveDateTo)
                                .FirstOrDefaultAsync(CancellationToken);

                            if (employeeCost != null)
                            {
                                employeeCost.DailyCostAmount = employee.SalaryInMonth / standardWorkingDateCount;
                                employeeCost.EffectiveDate = effectiveDateFrom;

                                appDbContext.EmployeeCosts.Update(employeeCost);
                            }

                            if (employeeCost == null)
                            {
                                var newEmployeeCost = new EmployeeCost
                                {
                                    EmployeeCostUid = GuidHelper.GenerateUUIDv7(),
                                    EmployeeUid = employee.EmployeeUid,
                                    DailyCostAmount = employee.SalaryInMonth / standardWorkingDateCount,
                                    EffectiveDate = effectiveDateFrom,
                                };

                                await appDbContext.EmployeeCosts.AddAsync(newEmployeeCost, CancellationToken);
                            }
                        }
                    }
                }

                await appDbContext.SaveChangesAsync(CancellationToken);

                // Update job execution status
                jobHistory.Status = JobExecutionStatus.Success;
            }
            catch (OperationCanceledException)
            {
                LogWarning("Cost calculate job was cancelled");
                jobHistory.Status = JobExecutionStatus.Cancelled;
                jobHistory.ErrorMessage = "Job was cancelled";
            }
            catch (Exception ex)
            {
                LogError($"Error executing cost calculate job: {ex.Message}", ex);
                jobHistory.Status = JobExecutionStatus.Failed;
                jobHistory.ErrorMessage = ex.Message;
            }
            finally
            {
                // Record job history
                jobHistory.CompletedAt = DateTime.UtcNow;
                jobHistory.ExecutionLog = GetExecutionLog();
                await batchJobRepository.UpdateJobHistoryAsync(jobHistory);

                // Update the job's LastRunAt time
                await batchJobRepository.UpdateJobLastRunTimeAsync(JobConfiguration.JobId, DateTime.UtcNow);
            }
        }

        private int CalculateStandardWorkingDateCount(
            DateOnly dateFrom,
            DateOnly dateTo,
            IEnumerable<EventCalendar> eventCalendars)
        {
            var dayCount = Math.Abs(dateTo.DayNumber - dateFrom.DayNumber);
            var nonRecurringEvents = eventCalendars.Where(e => !e.IsRecurring);
            var recurringEvents = eventCalendars.Where(e => e.IsRecurring);

            for (var date = dateFrom; date <= dateTo; date = date.AddDays(1))
            {
                try
                {
                    if (nonRecurringEvents.Any(e => e.EventStartDate <= date && e.EventEndDate >= date && e.IsDayOff))
                    {
                        dayCount--;
                        continue;
                    }

                    if (recurringEvents.Any(e =>
                        e.IsDayOff &&
                        e.RecurringFrom <= date && e.RecurringTo >= date &&
                        (
                            (e.RecurringDay != null && e.RecurringDay.Contains((int)date.DayOfWeek == 0 ? 7 : (int)date.DayOfWeek) &&
                                (e.RecurringWeek == null || e.RecurringWeek.Contains((date.Day - 1) / 7 + 1))
                            )
                            ||
                            (e.RecurringMonth != null && e.RecurringMonth.Contains(date.Month))
                        )
                    ))
                    {
                        dayCount--;
                        continue;
                    }
                }
                catch (Exception ex)
                {
                    LogError($"Error calculating standard working date count on date: {date.ToString("yyyy-MM-dd")}, {ex.Message}", ex);
                    continue;
                }
            }
            return dayCount;
        }
    }
}
