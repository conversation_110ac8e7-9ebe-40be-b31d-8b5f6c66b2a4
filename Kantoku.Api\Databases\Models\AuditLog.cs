namespace Kantoku.Api.Databases.Models;

public class AuditLog
{
    public Guid AuditLogUid { get; set; }
    public string EntityName { get; set; } = null!;
    public string EntityId { get; set; } = null!;
    public string Action { get; set; } = null!;
    public DateTime Timestamp { get; set; }
    public string OldValues { get; set; } = null!;
    public string NewValues { get; set; } = null!;
    public Guid? AccountUid { get; set; }

    public virtual Account? Account { get; set; }
}
