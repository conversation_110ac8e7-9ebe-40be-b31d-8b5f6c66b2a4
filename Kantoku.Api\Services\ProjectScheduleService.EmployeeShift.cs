using Kantoku.Api.Databases.Models;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Dtos.Schedule.Request;
using Kantoku.Api.Dtos.Schedule.Response;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Utils.Helpers;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public partial interface IProjectScheduleService
{
    Task<ResultDto<ScheduledEmployeeShiftResponseDto>> CreateScheduledEmployeeShift(CreateScheduledEmployeeShiftRequestDto2 dto);
    Task<ResultDto<ScheduledEmployeeShiftResponseDto>> CreateScheduledEmployeeShift(Guid projectScheduleId, CreateScheduledEmployeeShiftRequestDto dto);
    Task<ResultDto<ScheduledEmployeeShiftResponseDto>> UpdateScheduledEmployeeShift(Guid employeeShiftId, UpdateScheduledEmployeeShiftRequestDto dto);
    Task<ResultDto<ScheduledEmployeeShiftResponseDto>> DuplicateScheduledEmployeeShift(Guid employeeShiftId, DuplicateScheduledEmployeeShiftRequestDto dto);
    Task<ResultDto<ScheduledEmployeeShiftResponsesDto>> DuplicateScheduledEmployeeShifts(Guid employeeShiftId, DuplicateScheduledEmployeeShiftsRequestDto dto);
    Task<ResultDto<bool>> DeleteScheduledEmployeeShift(Guid employeeShiftId);
}

public partial class ProjectScheduleService : BaseService<ProjectScheduleService>, IProjectScheduleService
{
    public async Task<ResultDto<ScheduledEmployeeShiftResponseDto>> CreateScheduledEmployeeShift(
        Guid projectScheduleId,
        CreateScheduledEmployeeShiftRequestDto dto
    )
    {
        var existSchedule = await projectScheduleRepository.GetById(projectScheduleId, new ScheduleQueryableOptions
        {
            IncludedProject = false,
            IncludedEmployeeShifts = false,
        });
        if (existSchedule is null)
        {
            logger.Error("Project schedule not found for schedule id {ProjectScheduleId}", projectScheduleId);
            return new ErrorResultDto<ScheduledEmployeeShiftResponseDto>(ResponseCodeConstant.SCHEDULE_NOT_EXIST);
        }

        var workingDate = existSchedule.WorkingDate;

        var currentMonthlyReport = await monthlyReportRepository.GetByEmployeeAndDate(
            dto.EmployeeId,
            new DateOnly(workingDate.Year, workingDate.Month, 1),
            new DateOnly(workingDate.Year, workingDate.Month, 1).AddMonths(1).AddDays(-1),
            new MonthlyReportQueryableOptions());

        var newEmployeeShift = dto.ToEntity(existSchedule, currentMonthlyReport);
        if (newEmployeeShift is null)
        {
            logger.Error("Failed to create employee shift on schedule id {ProjectScheduleId}", projectScheduleId);
            return new ErrorResultDto<ScheduledEmployeeShiftResponseDto>(ResponseCodeConstant.SHIFT_CREATE_FAILED);
        }

        var createdEmployeeShift = await employeeShiftRepository.Create(newEmployeeShift, new EmployeeShiftQueryableOptions
        {
            IncludedEmployee = true,
            IncludedProject = true,
            IncludedSchedule = true,
        });
        if (createdEmployeeShift is null)
        {
            logger.Error("Failed to create employee shift on schedule id {ProjectScheduleId}", projectScheduleId);
            return new ErrorResultDto<ScheduledEmployeeShiftResponseDto>(ResponseCodeConstant.SHIFT_CREATE_FAILED);
        }
        var result = ScheduledEmployeeShiftResponseDtoParser(createdEmployeeShift);
        return new SuccessResultDto<ScheduledEmployeeShiftResponseDto>(result);
    }

    public async Task<ResultDto<ScheduledEmployeeShiftResponseDto>> CreateScheduledEmployeeShift(
        CreateScheduledEmployeeShiftRequestDto2 dto
    )
    {
        var existSchedule = await projectScheduleRepository.GetByProjectIdAndDate(
            dto.ProjectId, dto.WorkingDate, new ScheduleQueryableOptions
            {
                IncludedProject = false,
                IncludedEmployeeShifts = false,
                IncludedOutSourceShifts = false
            });
        existSchedule ??= new ProjectSchedule
        {
            ProjectScheduleUid = GuidHelper.GenerateUUIDv7(),
            ProjectUid = dto.ProjectId,
            WorkingDate = dto.WorkingDate,
            PlannedWorkload = 0,
            PresignedWorkload = 0,
            IsDeleted = false,
        };

        var workingDate = existSchedule.WorkingDate;
        var currentMonthlyReport = await monthlyReportRepository.GetByEmployeeAndDate(
            dto.EmployeeId,
            new DateOnly(workingDate.Year, workingDate.Month, 1),
            new DateOnly(workingDate.Year, workingDate.Month, 1).AddMonths(1).AddDays(-1),
            new MonthlyReportQueryableOptions());

        var newEmployeeShift = dto.ToEntity(existSchedule, currentMonthlyReport);
        if (newEmployeeShift is null)
        {
            logger.Error("Failed to create employee shift on project id {ProjectId} and date {WorkingDate}", dto.ProjectId, dto.WorkingDate);
            return new ErrorResultDto<ScheduledEmployeeShiftResponseDto>(ResponseCodeConstant.SHIFT_CREATE_FAILED);
        }

        var createdEmployeeShift = await employeeShiftRepository.Create(newEmployeeShift, new EmployeeShiftQueryableOptions
        {
            IncludedEmployee = true,
            IncludedProject = true,
            IncludedSchedule = true,
        });
        if (createdEmployeeShift is null)
        {
            logger.Error("Failed to create employee shift on project id {ProjectId} and date {WorkingDate}", dto.ProjectId, dto.WorkingDate);
            return new ErrorResultDto<ScheduledEmployeeShiftResponseDto>(ResponseCodeConstant.SHIFT_CREATE_FAILED);
        }
        var result = ScheduledEmployeeShiftResponseDtoParser(createdEmployeeShift);
        return new SuccessResultDto<ScheduledEmployeeShiftResponseDto>(result);
    }

    public async Task<ResultDto<ScheduledEmployeeShiftResponseDto>> UpdateScheduledEmployeeShift(
        Guid employeeShiftId, UpdateScheduledEmployeeShiftRequestDto dto
    )
    {
        var employeeShift = await employeeShiftRepository.GetById(employeeShiftId, new EmployeeShiftQueryableOptions
        {
            IncludedEmployee = true,
            IncludedProject = true,
            IncludedSchedule = true,
        });
        if (employeeShift is null)
        {
            logger.Error("Employee shift not found for id {EmployeeShiftId}", employeeShiftId);
            return new ErrorResultDto<ScheduledEmployeeShiftResponseDto>(ResponseCodeConstant.SHIFT_NOT_EXIST);
        }

        if (employeeShift.CheckInTime.HasValue)
        {
            logger.Error("Employee shift cannot be updated for shift id {EmployeeShiftId}", employeeShiftId);
            return new ErrorResultDto<ScheduledEmployeeShiftResponseDto>(ResponseCodeConstant.UPDATE_ON_ATTENDANCE_SHIFT);
        }

        if (employeeShift.ProjectSchedule is null)
        {
            logger.Error("Project schedule not found for employee shift id {EmployeeShiftId}", employeeShiftId);
            return new ErrorResultDto<ScheduledEmployeeShiftResponseDto>(ResponseCodeConstant.SCHEDULE_NOT_EXIST);
        }

        if (TimeOnly.TryParse(dto.StartTime, out var start))
        {
            var startWorkingTime = new DateTime(
                employeeShift.ProjectSchedule.WorkingDate.Year,
                employeeShift.ProjectSchedule.WorkingDate.Month,
                employeeShift.ProjectSchedule.WorkingDate.Day,
                start.Hour, start.Minute, 0);
            employeeShift.ScheduledStartTime = startWorkingTime;
        }
        if (TimeOnly.TryParse(dto.EndTime, out var end))
        {
            var endWorkingTime = new DateTime(
                employeeShift.ProjectSchedule.WorkingDate.Year,
                employeeShift.ProjectSchedule.WorkingDate.Month,
                employeeShift.ProjectSchedule.WorkingDate.Day,
                end.Hour, end.Minute, 0);
            employeeShift.ScheduledEndTime = endWorkingTime;
        }

        employeeShift.AssignedRole = dto.AssignedRole ?? employeeShift.AssignedRole;
        employeeShift.TotalScheduledWorkTime = dto.TotalScheduledWorkTime ?? employeeShift.TotalScheduledWorkTime;

        var updatedEmployeeShift = await employeeShiftRepository.Update(employeeShift, new EmployeeShiftQueryableOptions
        {
            IncludedEmployee = true,
            IncludedProject = true,
            IncludedSchedule = true,
        });
        if (updatedEmployeeShift is null)
        {
            logger.Error("Failed to update employee shift for shift id {EmployeeShiftId}", employeeShiftId);
            return new ErrorResultDto<ScheduledEmployeeShiftResponseDto>(ResponseCodeConstant.SHIFT_UPDATE_FAILED);
        }

        var result = ScheduledEmployeeShiftResponseDtoParser(updatedEmployeeShift);
        return new SuccessResultDto<ScheduledEmployeeShiftResponseDto>(result);
    }

    public async Task<ResultDto<ScheduledEmployeeShiftResponseDto>> DuplicateScheduledEmployeeShift(
        Guid employeeShiftId, DuplicateScheduledEmployeeShiftRequestDto dto
    )
    {
        var employeeShift = await employeeShiftRepository.GetById(employeeShiftId, new EmployeeShiftQueryableOptions
        {
            IncludedEmployee = true,
            IncludedProject = true,
            IncludedSchedule = true,
        });
        if (employeeShift is null)
        {
            logger.Error("Employee shift not found for id {EmployeeShiftId}", employeeShiftId);
            return new ErrorResultDto<ScheduledEmployeeShiftResponseDto>(ResponseCodeConstant.SHIFT_NOT_EXIST);
        }

        if (employeeShift.ScheduledStartTime is null || employeeShift.ScheduledEndTime is null)
        {
            logger.Error("Employee shift not found for id {EmployeeShiftId}", employeeShiftId);
            return new ErrorResultDto<ScheduledEmployeeShiftResponseDto>(ResponseCodeConstant.SHIFT_CREATE_FAILED);
        }

        var destinationSchedule = await projectScheduleRepository.GetByProjectIdAndDate(
            dto.ProjectId, dto.WorkingDate, new ScheduleQueryableOptions
            {
                IncludedEmployeeShifts = true,
                IncludedProject = true,
            });
        if (destinationSchedule is null)
        {
            logger.Error("Project schedule not found for project id {ProjectId} at date {WorkingDate}",
                dto.ProjectId, dto.WorkingDate);
            var newProjectSchedule = new ProjectSchedule
            {
                ProjectUid = dto.ProjectId,
                WorkingDate = dto.WorkingDate,
                PlannedWorkload = 0,
                PresignedWorkload = 0,
            };
            var createdProjectSchedule = await projectScheduleRepository.Create(newProjectSchedule);
            if (createdProjectSchedule is null)
            {
                logger.Error("Failed to create project schedule for project id {ProjectId}", dto.ProjectId);
                return new ErrorResultDto<ScheduledEmployeeShiftResponseDto>(ResponseCodeConstant.SHIFT_CREATE_FAILED);
            }
            destinationSchedule = createdProjectSchedule;
        }

        var newScheduledStart = new DateTime(
            dto.WorkingDate.Year,
            dto.WorkingDate.Month,
            dto.WorkingDate.Day,
            employeeShift.ScheduledStartTime.Value.Hour,
            employeeShift.ScheduledStartTime.Value.Minute,
            0);
        var newScheduledEnd = new DateTime(
            dto.WorkingDate.Year,
            dto.WorkingDate.Month,
            dto.WorkingDate.Day,
            employeeShift.ScheduledEndTime.Value.Hour,
            employeeShift.ScheduledEndTime.Value.Minute,
            0);

        var currentMonthlyReport = await monthlyReportRepository.GetCurrentByEmployeeId(employeeShift.EmployeeUid, new MonthlyReportQueryableOptions());
        if (currentMonthlyReport is null)
        {
            var newReport = new MonthlyReport
            {
                EmployeeUid = employeeShift.EmployeeUid,
                ReportFrom = new DateOnly(DateTime.Now.Year, DateTime.Now.Month, 1),
                ReportTo = new DateOnly(DateTime.Now.Year, DateTime.Now.Month, 1)
                        .AddMonths(1)
                        .AddDays(-1),
            };
            currentMonthlyReport = await monthlyReportRepository.Create(newReport);
            if (currentMonthlyReport is null)
            {
                logger.Error("Failed to create monthly report for employee {EmployeeId}", employeeShift.EmployeeUid);
                return new ErrorResultDto<ScheduledEmployeeShiftResponseDto>(ResponseCodeConstant.CHECKIN_FAILED);
            }
        }

        var newEmployeeShift = new EmployeeShift
        {
            EmployeeUid = employeeShift.EmployeeUid,
            ProjectUid = destinationSchedule.ProjectUid,
            ProjectScheduleUid = destinationSchedule.ProjectScheduleUid,
            ScheduledStartTime = newScheduledStart,
            ScheduledEndTime = newScheduledEnd,
            AssignedRole = employeeShift.AssignedRole,
            TotalScheduledWorkTime = employeeShift.TotalScheduledWorkTime,
            MonthlyReportUid = currentMonthlyReport.MonthlyReportUid,
        };

        var createdEmployeeShift = await employeeShiftRepository.Create(newEmployeeShift, new EmployeeShiftQueryableOptions
        {
            IncludedEmployee = true,
            IncludedProject = true,
            IncludedSchedule = true,
        });
        if (createdEmployeeShift is null)
        {
            logger.Error("Failed to duplicate employee shift for shift id {EmployeeShiftId}", employeeShiftId);
            return new ErrorResultDto<ScheduledEmployeeShiftResponseDto>(ResponseCodeConstant.SHIFT_CREATE_FAILED);
        }
        var result = ScheduledEmployeeShiftResponseDtoParser(createdEmployeeShift);
        return new SuccessResultDto<ScheduledEmployeeShiftResponseDto>(result);
    }


    public async Task<ResultDto<ScheduledEmployeeShiftResponsesDto>> DuplicateScheduledEmployeeShifts(
        Guid employeeShiftId, DuplicateScheduledEmployeeShiftsRequestDto dto
    )
    {
        var response = new List<ScheduledEmployeeShiftResponseDto>();
        for (var workingDate = dto.WorkingDateFrom; workingDate <= dto.WorkingDateTo; workingDate = workingDate.AddDays(1))
        {
            var duplicatedShift = await DuplicateScheduledEmployeeShift(employeeShiftId, new DuplicateScheduledEmployeeShiftRequestDto
            {
                ProjectId = dto.ProjectId,
                WorkingDate = workingDate,
            });
            if (duplicatedShift is null || duplicatedShift.Data is null)
            {
                continue;
            }
            response.Add(duplicatedShift.Data);
        }
        return new SuccessResultDto<ScheduledEmployeeShiftResponsesDto>(new ScheduledEmployeeShiftResponsesDto
        {
            Items = response
        });
    }

    public async Task<ResultDto<bool>> DeleteScheduledEmployeeShift(Guid employeeShiftId)
    {
        var employeeShift = await employeeShiftRepository.GetById(employeeShiftId, new EmployeeShiftQueryableOptions());
        if (employeeShift is null)
        {
            logger.Error("Employee shift not found for id {EmployeeShiftId}", employeeShiftId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.SHIFT_NOT_EXIST);
        }

        if (employeeShift.CheckInTime.HasValue)
        {
            logger.Error("Employee shift cannot be deleted for shift id {EmployeeShiftId}", employeeShiftId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.SHIFT_REMOVE_FAILED);
        }

        employeeShift.IsDeleted = true;
        var isDeleted = await employeeShiftRepository.Update(employeeShift, new EmployeeShiftQueryableOptions());
        if (isDeleted is null)
        {
            logger.Error("Failed to delete employee shift for shift id {EmployeeShiftId}", employeeShiftId);
            return new ErrorResultDto<bool>(ResponseCodeConstant.SHIFT_REMOVE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }

    private ScheduledEmployeeShiftResponseDto ScheduledEmployeeShiftResponseDtoParser(EmployeeShift employeeShift)
    {
        try
        {
            var assignedWorkTime = employeeShift.ScheduledStartTime.HasValue && employeeShift.ScheduledEndTime.HasValue
                                            ? (employeeShift.ScheduledEndTime.Value - employeeShift.ScheduledStartTime.Value).TotalHours
                                            : 0;
            var assignedWorkload = employeeShift.TotalScheduledWorkTime == 0
                                        ? 0
                                        : assignedWorkTime < employeeShift.TotalScheduledWorkTime
                                            ? assignedWorkTime / employeeShift.TotalScheduledWorkTime
                                            : 1;
            var workingDate = DateTimeHelper.ParseToLocalTime(employeeShift.ScheduledStartTime)?.ToString("yyyy-MM-dd");
            var scheduledStartTime = DateTimeHelper.ParseToLocalTime(employeeShift.ScheduledStartTime)?.ToString("HH:mm:ss");
            var scheduledEndTime = DateTimeHelper.AdjustBeyondTime(employeeShift.ScheduledStartTime, employeeShift.ScheduledEndTime);

            return new ScheduledEmployeeShiftResponseDto
            {
                EmployeeShiftId = employeeShift.EmployeeShiftUid.ToString(),
                ProjectScheduleId = employeeShift.ProjectScheduleUid.ToString(),
                EmployeeName = employeeShift.Employee?.EmployeeName,
                EmployeeCode = employeeShift.Employee?.EmployeeCode,
                WorkingDate = workingDate,
                ScheduledStartTime = scheduledStartTime,
                ScheduledEndTime = scheduledEndTime,
                TotalScheduledWorkTime = employeeShift.TotalScheduledWorkTime,
                AssignedWorkload = (float)Math.Round(assignedWorkload, 2),
                WorkingRole = employeeShift.AssignedRole,
            };
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Exception occurred while parsing employee shift info");
            return new ScheduledEmployeeShiftResponseDto();
        }
    }
}