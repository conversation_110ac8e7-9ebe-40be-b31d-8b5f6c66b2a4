﻿namespace Kantoku.Api.Dtos.Vendor.Response;
/// <summary>
/// Represents the response containing a list of vendors and pagination information.
/// </summary>
public class VendorsResponseDto
{
    /// <summary>
    ///  the collection of vendor response data.
    /// </summary>
    public IEnumerable<VendorResponseDto> Items { get; set; } = []; // (*)

    /// <summary>
    ///  the current page number.
    /// </summary>
    public int PageNum { get; set; } // (*)

    /// <summary>
    ///  the size of the page.
    /// </summary>
    public int PageSize { get; set; } // (*)

    /// <summary>
    ///  the total number of records available.
    /// </summary>
    public int TotalRecords { get; set; } // (*)
}

