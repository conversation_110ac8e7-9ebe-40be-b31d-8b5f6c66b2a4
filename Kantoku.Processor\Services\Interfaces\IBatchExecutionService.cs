using Kantoku.Processor.Models.BatchJobManager;

namespace Kantoku.Processor.Services.Interfaces
{
    /// <summary>
    /// Interface for the service that executes batch jobs
    /// </summary>
    public interface IBatchExecutionService
    {
        /// <summary>
        /// Executes a batch job
        /// </summary>
        Task ExecuteJobAsync(BatchJob job, CancellationToken cancellationToken);
        
        /// <summary>
        /// Gets the current status of a running job
        /// </summary>
        BatchJobHistory? GetRunningJobStatus(Guid jobId);
        
        /// <summary>
        /// Checks if a job is currently running
        /// </summary>
        bool IsJobRunning(Guid jobId);
        
        /// <summary>
        /// Attempts to cancel a running job
        /// </summary>
        bool TryCancelJob(Guid jobId);
    }
} 