﻿using Kantoku.Api.Databases.Models;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.CategorizedCost.Response;
using Kantoku.Api.Dtos.Construction.Request;
using Kantoku.Api.Dtos.Construction.Response;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public interface IConstructionService
{
    Task<ResultDto<ConstructionResponseDto>> GetConstructionById(Guid constructionId);
    Task<ResultDto<ConstructionsResponseDto>> GetConstructionsByProjectId(Guid projectId);
    Task<ResultDto<ConstructionSimpleResponseDto>> GetConstructionSimpleByProjectId(Guid projectId);
    Task<ResultDto<ConstructionOverviewResponseDto>> GetConstructionOverviewByProjectId(Guid projectId);
    Task<ResultDto<ConstructionResponseDto>> CreateConstruction(Guid projectId, CreateConstructionRequestDto constructionRequestDto);
    Task<ResultDto<ConstructionResponseDto>> UpdateConstruction(Guid constructionId, UpdateConstructionRequestDto constructionRequestDto);
    Task<ResultDto<bool>> DeleteConstruction(Guid constructionId);
}

[Service(ServiceLifetime.Scoped)]
public class ConstructionService : BaseService<ConstructionService>, IConstructionService
{
    private readonly IConstructionRepository constructionRepository;
    private readonly IProjectRepository projectRepository;
    private readonly ICategoryRepository categoryRepository;
    public ConstructionService(
        IConstructionRepository constructionRepository,
        IProjectRepository projectRepository,
        ICategoryRepository categoryRepository,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor) : base(logger, httpContextAccessor)
    {
        this.constructionRepository = constructionRepository;
        this.projectRepository = projectRepository;
        this.categoryRepository = categoryRepository;
    }

    public async Task<ResultDto<ConstructionResponseDto>> GetConstructionById(Guid constructionId)
    {
        var construction = await constructionRepository.GetById(constructionId, new ConstructionQueryableOptions());
        if (construction is null)
        {
            logger.Error("Construction not found");
            return new ErrorResultDto<ConstructionResponseDto>(ResponseCodeConstant.CONSTRUCTION_NOT_EXIST);
        }
        var rootCategories = await categoryRepository.GetRoot(new CategoryQueryableOptions());
        var result = construction.ToConstructionResponseDto(rootCategories, GetCurrentLanguageCode());
        return new SuccessResultDto<ConstructionResponseDto>(result);
    }

    public async Task<ResultDto<ConstructionsResponseDto>> GetConstructionsByProjectId(Guid projectId)
    {
        var constructions = await constructionRepository.GetByProjectId(projectId, new ConstructionQueryableOptions());
        if (constructions is null || !constructions.Any())
        {
            logger.Error("Constructions not found");
            return new ErrorResultDto<ConstructionsResponseDto>(ResponseCodeConstant.CONSTRUCTION_NOT_EXIST);
        }
        var rootCategories = await categoryRepository.GetRoot(new CategoryQueryableOptions());
        var result = constructions.ToConstructionsResponseDto(rootCategories, GetCurrentLanguageCode());
        return new SuccessResultDto<ConstructionsResponseDto>(result);
    }

    public async Task<ResultDto<ConstructionSimpleResponseDto>> GetConstructionSimpleByProjectId(Guid projectId)
    {
        var constructions = await constructionRepository.GetByProjectId(projectId, new ConstructionQueryableOptions());
        if (constructions is null || !constructions.Any())
        {
            logger.Error("Constructions not found");
            return new ErrorResultDto<ConstructionSimpleResponseDto>(ResponseCodeConstant.CONSTRUCTION_NOT_EXIST);
        }
        var result = constructions.ToConstructionSimpleResponseDto();
        return new SuccessResultDto<ConstructionSimpleResponseDto>(result);
    }

    public async Task<ResultDto<ConstructionOverviewResponseDto>> GetConstructionOverviewByProjectId(Guid projectId)
    {
        var options = new ConstructionQueryableOptions
        {
            IncludedProject = true,
            IncludedConstructionCosts = true,
        };
        var constructions = await constructionRepository.GetByProjectId(projectId, options);
        if (constructions is null || !constructions.Any())
        {
            logger.Error("Constructions not found");
            return new ErrorResultDto<ConstructionOverviewResponseDto>(ResponseCodeConstant.CONSTRUCTION_NOT_EXIST);
        }
        var result = constructions.ToConstructionOverviewResponseDto(GetCurrentLanguageCode());
        return new SuccessResultDto<ConstructionOverviewResponseDto>(result);
    }

    public async Task<ResultDto<ConstructionResponseDto>> CreateConstruction(Guid projectId, CreateConstructionRequestDto constructionRequestDto)
    {
        var options = new ProjectQueryableOptions
        {
            IncludedConstructions = true,
        };
        var project = await projectRepository.GetById(projectId, options);
        if (project is null)
        {
            logger.Error("Project not found");
            return new ErrorResultDto<ConstructionResponseDto>(ResponseCodeConstant.PROJECT_NOT_EXIST);
        }
        if (project.Constructions.Any(c => !c.IsPrimary))
        {
            logger.Error("Project already has a main construction");
            return new ErrorResultDto<ConstructionResponseDto>(ResponseCodeConstant.SUB_CONSTRUCTION_EXIST);
        }

        var newConstruction = constructionRequestDto.ToEntity(project.ProjectUid, project.OrgUid, true);
        if (newConstruction is null)
        {
            logger.Error("Error creating construction");
            return new ErrorResultDto<ConstructionResponseDto>(ResponseCodeConstant.CONSTRUCTION_CREATE_FAILED);
        }
        var createdConstruction = await constructionRepository.Create(newConstruction);
        if (createdConstruction is null)
        {
            logger.Error("Error creating construction");
            return new ErrorResultDto<ConstructionResponseDto>(ResponseCodeConstant.CONSTRUCTION_CREATE_FAILED);
        }
        var rootCategories = await categoryRepository.GetRoot(new CategoryQueryableOptions());
        var result = createdConstruction.ToConstructionResponseDto(rootCategories, GetCurrentLanguageCode());
        return new SuccessResultDto<ConstructionResponseDto>(result);
    }

    public async Task<ResultDto<ConstructionResponseDto>> UpdateConstruction(Guid constructionId, UpdateConstructionRequestDto constructionRequestDto)
    {
        var existConstruction = await constructionRepository.GetById(constructionId, new ConstructionQueryableOptions());
        if (existConstruction is null)
        {
            logger.Error("Construction not found");
            return new ErrorResultDto<ConstructionResponseDto>(ResponseCodeConstant.CONSTRUCTION_NOT_EXIST);
        }

        existConstruction.UpdateFromDto(constructionRequestDto);

        var updatedConstruction = await constructionRepository.Update(existConstruction);
        if (updatedConstruction is null)
        {
            logger.Error("Error updating construction");
            return new ErrorResultDto<ConstructionResponseDto>(ResponseCodeConstant.CONSTRUCTION_UPDATE_FAILED);
        }
        var rootCategories = await categoryRepository.GetRoot(new CategoryQueryableOptions());
        var result = updatedConstruction.ToConstructionResponseDto(rootCategories, GetCurrentLanguageCode());
        return new SuccessResultDto<ConstructionResponseDto>(result);
    }

    public async Task<ResultDto<bool>> DeleteConstruction(Guid constructionId)
    {
        var options = new ConstructionQueryableOptions
        {
            IncludedConstructionCosts = true,
        };
        var construction = await constructionRepository.GetById(constructionId, options);
        if (construction is null)
        {
            logger.Error("Construction not found");
            return new ErrorResultDto<bool>(ResponseCodeConstant.CONSTRUCTION_NOT_EXIST);
        }
        if (construction.IsPrimary)
        {
            logger.Error("Cannot delete primary construction");
            return new ErrorResultDto<bool>(ResponseCodeConstant.CONSTRUCTION_DELETE_FAILED);
        }
        construction.IsDeleted = true;
        var isDeleted = await constructionRepository.Update(construction);
        if (isDeleted is null)
        {
            logger.Error("Error deleting construction");
            return new ErrorResultDto<bool>(ResponseCodeConstant.CONSTRUCTION_DELETE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }
}
