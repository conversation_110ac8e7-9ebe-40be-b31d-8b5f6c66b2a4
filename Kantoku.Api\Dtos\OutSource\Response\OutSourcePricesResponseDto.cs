namespace Kantoku.Api.Dtos.OutSource.Response;

public class OutSourcePricesResponseDto
{
    public IEnumerable<OutSourcePriceResponseDto> Items { get; set; } = [];

    /// <summary>
    /// The page number
    /// </summary>
    public int PageNum { get; set; }

    /// <summary>
    /// The page size
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// The total number of items
    /// </summary>
    public int TotalRecords { get; set; }
}

public class OutSourcePriceResponseDto
{
    /// <summary>
    /// The uid of the outsource
    /// </summary>
    public string? OutSourceId { get; set; }

    /// <summary>
    /// The code of the outsource
    /// </summary>
    public string? OutSourceCode { get; set; }

    /// <summary>
    /// The name of the outsource
    /// </summary>
    public string? OutSourceName { get; set; }

    /// <summary>
    /// The uid of the outsource price
    /// </summary>
    public string? OutSourcePriceId { get; set; }

    /// <summary>
    /// The price per day of the outsource
    /// </summary>
    public int? PricePerDay { get; set; }

    /// <summary>
    /// The start date of the outsource price
    /// </summary>
    public string? EffectiveDate { get; set; }

    /// <summary>
    /// The description of the outsource price changes
    /// </summary>
    public string? Description { get; set; }
}

