﻿using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Dtos.InputCostItem.Request;
using Kantoku.Api.Dtos.InputCostItem.Response;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Controllers;

[Route("api/v1/cost/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class InputCostItemController : BaseController
{
    private readonly IInputCostItemService inputCostItemService;
    private readonly IAuditLogService auditLogService;
    public InputCostItemController(IInputCostItemService inputCostItemService,
        ITResponseFactory responseFactory,
        IAuditLogService auditLogService) : base(responseFactory)
    {
        this.inputCostItemService = inputCostItemService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get input cost items by filter criteria
    /// </summary>
    /// <param name="filter">Filter parameters to search input cost items</param>
    /// <returns>List of filtered input cost items</returns>
    [HttpGet]
    public async Task<GeneralResponse<InputCostItemsResponseDto>> GetByFilter([FromQuery] InputCostItemCategoryFilter filter)
    {
        try
        {
            var result = await inputCostItemService.GetByFilter(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<InputCostItemsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get input cost items by filter criteria
    /// </summary>
    /// <param name="categoryId">Category ID of input cost items</param>
    /// <param name="filter">Filter parameters to search input cost items</param>
    /// <returns>List of filtered input cost items</returns>
    [HttpGet("category/{categoryId}")]
    public async Task<GeneralResponse<InputCostItemsResponseDto>> GetByCategory([FromRoute] Guid categoryId, [FromQuery] InputCostItemFilter filter)
    {
        try
        {
            var result = await inputCostItemService.GetByCategory(categoryId, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<InputCostItemsResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get input cost item by ID
    /// </summary>
    /// <param name="id">ID of input cost item to retrieve</param>
    /// <returns>Input cost item details</returns>
    [HttpGet("{id}")]
    public async Task<GeneralResponse<InputCostItemResponseDto>> GetById([FromRoute] Guid id)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<InputCostItemResponseDto>();

            var result = await inputCostItemService.GetById(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<InputCostItemResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create new input cost item
    /// </summary>
    /// <param name="requestDto">Input cost item details for creation</param>
    /// <returns>Created input cost item</returns>
    [HttpPost]
    public async Task<GeneralResponse<InputCostItemResponseDto>> Create([FromBody] CreateInputCostItemRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<InputCostItemResponseDto>();

            var result = await inputCostItemService.Create(requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<InputCostItemResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update existing input cost item
    /// </summary>
    /// <param name="requestDto">Updated input cost item details</param>
    /// <returns>Updated input cost item</returns>
    [HttpPut]
    public async Task<GeneralResponse<bool>> Update([FromBody] UpdateMultipleInputCostItemsRequestDto requestDto)
    {
        try
        {
            await inputCostItemService.UpdateMultiple(requestDto);
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update existing input cost item
    /// </summary>
    /// <param name="id">ID of input cost item to update</param>
    /// <param name="requestDto">Updated input cost item details</param>
    /// <returns>Updated input cost item</returns>
    [HttpPut("{id}")]
    public async Task<GeneralResponse<InputCostItemResponseDto>> Update([FromRoute] Guid id, [FromBody] UpdateInputCostItemRequestDto requestDto)
    {
        try
        {
            var result = await inputCostItemService.Update(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<InputCostItemResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete input cost item
    /// </summary>
    /// <param name="id">ID of input cost item to delete</param>
    /// <returns>Success response with no content</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> Delete([FromRoute] Guid id)
    {
        try
        {
            await inputCostItemService.Delete(id);
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for specific input cost item
    /// </summary>
    /// <param name="id">ID of input cost item to get logs for</param>
    /// <param name="filter">Filter parameters for audit logs</param>
    /// <returns>List of audit logs for the input cost item</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetAuditLogByEntity([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<InputCostItem>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
