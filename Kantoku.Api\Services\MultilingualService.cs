using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Utils.Attributes.Class;

namespace Kantoku.Api.Services;

public interface IMultilingualService
{
    public Task<string> GetTranslatedRequestTypeName(string requestTypeCode);
    public Task<string> GetTranslatedLeaveTypeName(string? leaveTypeCode);
    public Task<string> GetTranslatedStatusName(string? statusCode);
}

[Service(ServiceLifetime.Scoped)]
public class MultilingualService : BaseService<MultilingualService>, IMultilingualService
{
    private readonly IRequestTypeRepository requestTypeRepository;
    private readonly ILeaveTypeRepository leaveTypeRepository;
    private readonly IStatusRepository statusRepository;

    public MultilingualService(
        IRequestTypeRepository requestTypeRepository,
        ILeaveTypeRepository leaveTypeRepository,
        IStatusRepository statusRepository,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor)
        : base(logger, httpContextAccessor)
    {
        this.requestTypeRepository = requestTypeRepository;
        this.leaveTypeRepository = leaveTypeRepository;
        this.statusRepository = statusRepository;
    }

    public async Task<string> GetTranslatedRequestTypeName(string requestTypeCode)
    {
        try
        {
            var languageCode = GetCurrentLanguageCode();
            var requestType = await requestTypeRepository.GetByCode(requestTypeCode);
            if (requestType is null)
                return string.Empty;
            var requestTypeTranslated = requestType.TranslatedRequestType.FirstOrDefault(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase))?.RequestTypeName;
            return requestTypeTranslated ?? string.Empty;
        }
        catch (System.Exception)
        {
            logger.Error("Error getting translated request type name");
            return string.Empty;
        }
    }

    public async Task<string> GetTranslatedLeaveTypeName(string? leaveTypeCode)
    {
        try
        {
            var languageCode = GetCurrentLanguageCode();
            if (leaveTypeCode is null)
                return string.Empty;
            var leaveType = await leaveTypeRepository.GetByCode(leaveTypeCode);
            if (leaveType is null)
                return string.Empty;
            var leaveTypeTranslated = leaveType.TranslatedLeaveType.FirstOrDefault(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase))?.LeaveTypeName;
            return leaveTypeTranslated ?? string.Empty;
        }
        catch (System.Exception)
        {
            logger.Error("Error getting translated leave type name");
            return string.Empty;
        }
    }

    public async Task<string> GetTranslatedStatusName(string? statusCode)
    {
        try
        {
            var languageCode = GetCurrentLanguageCode();
            if (statusCode is null)
                return string.Empty;
            var status = await statusRepository.GetByCode(statusCode);
            if (status is null)
                return string.Empty;
            var translatedStatus = status.TranslatedStatus.FirstOrDefault(t => string.Equals(t.LanguageCode, languageCode, StringComparison.OrdinalIgnoreCase))?.StatusName;
            return translatedStatus ?? string.Empty;
        }
        catch (System.Exception)
        {
            logger.Error("Error getting translated status name");
            return string.Empty;
        }
    }
}