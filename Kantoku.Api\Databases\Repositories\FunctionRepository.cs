using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;

namespace Kantoku.Api.Databases.Repositories;

public interface IFunctionRepository
{
    Task<IEnumerable<Function>> GetAll();
    Task Create(Function function);
}

[Repository(ServiceLifetime.Scoped)]
public class FunctionRepository : BaseRepository<Function>, IFunctionRepository
{
    public FunctionRepository(PostgreDbContext context, Serilog.ILogger logger, IHttpContextAccessor httpContextAccessor)
        : base(context, logger, httpContextAccessor)
    {
    }

    public async Task<IEnumerable<Function>> GetAll()
    {
        try
        {
            return await context.Functions
                .Where(f => f.IsHeader == false)
                .AsNoTracking()
                .OrderBy(f => f.DisplayOrder)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all functions");
            return [];
        }
    }

    public async Task Create(Function function)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Functions.AddAsync(function);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
            }
            else
            {
                await transaction.RollbackAsync();
            }
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating function");
            await transaction.RollbackAsync();
        }
    }
}
