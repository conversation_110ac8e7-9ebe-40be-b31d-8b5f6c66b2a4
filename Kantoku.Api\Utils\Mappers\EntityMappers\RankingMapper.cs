using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Ranking.Request;
using Kantoku.Api.Dtos.Ranking.Response;
using Kantoku.Api.Utils.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class RankingMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a Ranking entity to a RankingResponseDto
    /// </summary>
    /// <param name="ranking">The Ranking entity to map</param>
    /// <returns>A RankingResponseDto representing the Ranking entity</returns>
    public static RankingResponseDto ToRankingResponseDto(this Ranking ranking)
    {
        if (ranking == null)
            return new RankingResponseDto();

        return new RankingResponseDto
        {
            RankingId = ranking.RankingUid.ToString(),
            RankingName = ranking.RankingName,
            MinValue = ranking.MinValue,
            MaxValue = ranking.MaxValue,
            Description = ranking.Description,
            CreatedTime = ranking.CreatedTime?.ToString("yyyy-MM-dd HH:mm:ss"),
            UpdatedTime = ranking.LastModifiedTime?.ToString("yyyy-MM-dd HH:mm:ss")
        };
    }

    /// <summary>
    /// Maps a collection of Ranking entities to a collection of RankingResponseDto
    /// </summary>
    /// <param name="rankings">The collection of Ranking entities to map</param>
    /// <param name="pageNum">The page number to use for the mapping</param>
    /// <param name="pageSize">The page size to use for the mapping</param>
    /// <param name="total">The total number of records to use for the mapping</param>
    /// <returns>A RankingsResponseDto representing the collection of Ranking entities</returns>
    public static RankingsResponseDto ToRankingResponseDtos(this IEnumerable<Ranking> rankings, int pageNum, int pageSize, int total)
    {
        if (rankings == null)
            return new RankingsResponseDto();

        return new RankingsResponseDto
        {
            Items = rankings.Select(r => r.ToRankingResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = total
        };
    }

    public static SimpleRankingsResponseDto ToSimpleRankingResponseDto(this IEnumerable<Ranking> rankings, int pageNum, int pageSize, int total)
    {
        if (rankings == null)
            return new SimpleRankingsResponseDto();

        return new SimpleRankingsResponseDto
        {
            Items = rankings.Select(r => new SimpleRankingResponseDto
            {
                RankingId = r.RankingUid.ToString(),
                RankingName = r.RankingName
            }),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = total
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateRankingRequestDto to a Ranking entity
    /// </summary>
    /// <param name="dto">The CreateRankingRequestDto to map</param>
    /// <param name="orgUid">The organization UID to use for the mapping</param>
    /// <returns>A Ranking entity representing the CreateRankingRequestDto</returns>
    public static Ranking? ToEntity(this CreateRankingRequestDto dto, Guid orgUid)
    {
        if (dto == null)
            return null;

        return new Ranking
        {
            RankingUid = GuidHelper.GenerateUUIDv7(),
            OrgUid = orgUid,
            RankingName = dto.RankingName,
            MinValue = dto.MinValue,
            MaxValue = dto.MaxValue,
            Description = dto.Description,
            IsDeleted = false
        };
    }

    /// <summary>
    /// Updates a Ranking entity from an UpdateRankingRequestDto
    /// </summary>
    /// <param name="entity">The Ranking entity to update</param>
    /// <param name="dto">The UpdateRankingRequestDto to use for the update</param>
    public static void UpdateFromDto(this Ranking entity, UpdateRankingRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (dto.RankingName != null)
            entity.RankingName = dto.RankingName;

        if (dto.MinValue.HasValue)
            entity.MinValue = dto.MinValue;

        if (dto.MaxValue.HasValue)
            entity.MaxValue = dto.MaxValue;

        if (dto.Description != null)
            entity.Description = dto.Description;
    }

    #endregion
}