namespace Kantoku.Api.Dtos.ConstructionCost.Response;
/// <summary>
/// Response DTO for claim details
/// </summary>
public class ConstructionPaymentResponseDto
{
    /// <summary>
    /// Construction ID
    /// </summary>
    public Guid? ConstructionId { get; set; }

    /// <summary>
    /// Amount being claimed
    /// </summary>
    public long? RequestAmount { get; set; }

    /// <summary>
    /// Amount retained from the claim
    /// </summary>
    public int? RetentionAmount { get; set; }

    /// <summary>
    /// Amount last released from retention
    /// </summary>
    public int? ReleasedAmount { get; set; }

    /// <summary>
    /// Total amount that can be claimed
    /// = RequestAmount - RetentionAmount + ReleasedAmount
    /// </summary>
    public long? TotalClaimedAmount { get; set; }
}