using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace Kantoku.Api.Extensions.Validators
{
    [AttributeUsage(AttributeTargets.Property)]
    public partial class EmailValidator : ValidationAttribute
    {
        private const string EmailValidatorRegex
             = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";

        public EmailValidator()
        {
        }

        protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
        {
            try
            {
                if (value is null)
                    return ValidationResult.Success;
                if (!MyRegex().IsMatch(value.ToString()!))
                    return new ValidationResult("Invalid email format");
                return ValidationResult.Success;
            }
            catch (System.Exception)
            {
                return new ValidationResult("Invalid email format");
            }
        }

        [GeneratedRegex(EmailValidatorRegex)]
        private static partial Regex MyRegex();
    }
}
