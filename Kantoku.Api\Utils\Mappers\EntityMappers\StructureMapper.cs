using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Structure.Request;
using Kantoku.Api.Dtos.Structure.Response;
using Kantoku.Api.Utils.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class StructureMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a Structure entity to a StructureResponseDto
    /// </summary>
    /// <param name="structure"></param>
    /// <returns> StructureResponseDto </returns>
    public static StructureResponseDto ToStructureResponseDto(this Structure structure)
    {
        if (structure == null)
            return new StructureResponseDto();

        return new StructureResponseDto
        {
            StructureId = structure.StructureUid.ToString(),
            StructureCode = structure.StructureCode,
            StructureName = structure.StructureName,
            StructureParentId = structure.StructureParentUid?.ToString(),
            Description = structure.Description,
            Children = structure.Children?.Select(c => c.ToStructureResponseDto()),
            Roles = structure.Roles?.Select(r => r.ToRoleResponseDto())
        };
    }

    /// <summary>
    /// Maps a collection of Structure entities to a StructuresResponseDto
    /// </summary>
    /// <param name="structures"></param>
    /// <param name="pageNum"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalRecords"></param>
    /// <returns> StructuresResponseDto </returns>
    public static StructuresResponseDto ToStructuresResponseDto(
        this IEnumerable<Structure> structures,
        int pageNum,
        int pageSize,
        int totalRecords)
    {
        if (structures == null)
            return new StructuresResponseDto
            {
                Items = [],
                PageNum = pageNum,
                PageSize = pageSize,
                TotalRecords = 0
            };

        return new StructuresResponseDto
        {
            Items = structures.Select(s => s.ToStructureResponseDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    /// <summary>
    /// Maps a Structure entity to a SimpleStructureDto
    /// </summary>
    /// <param name="structure"></param>
    /// <returns> SimpleStructureDto </returns>
    public static SimpleStructureDto ToSimpleStructureDto(this Structure structure)
    {
        if (structure == null)
            return new SimpleStructureDto();

        return new SimpleStructureDto
        {
            Id = structure.StructureUid.ToString(),
            Name = structure.StructureName
        };
    }

    /// <summary>
    /// Maps a collection of Structure entities to a SimpleStructureResponseDto
    /// </summary>
    /// <param name="structures"></param>
    /// <param name="pageNum"></param>
    /// <param name="pageSize"></param>
    /// <param name="totalRecords"></param>
    /// <returns> SimpleStructureResponseDto </returns>
    public static SimpleStructureResponseDto ToSimpleStructureResponseDto(
        this IEnumerable<Structure> structures,
        int pageNum,
        int pageSize,
        int totalRecords)
    {
        if (structures == null)
            return new SimpleStructureResponseDto
            {
                Items = [],
                PageNum = pageNum,
                PageSize = pageSize,
                TotalRecords = 0
            };

        return new SimpleStructureResponseDto
        {
            Items = structures.Select(s => s.ToSimpleStructureDto()),
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = totalRecords
        };
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateStructureRequestDto to a Structure entity
    /// </summary>
    /// <param name="dto"></param>
    /// <param name="orgUid"></param>
    /// <returns> Structure entity </returns>
    public static Structure? ToEntity(this CreateStructureRequestDto dto, Guid orgUid)
    {
        if (dto == null)
            return null;

        var entity = new Structure
        {
            StructureUid = GuidHelper.GenerateUUIDv7(),
            OrgUid = orgUid,
            StructureCode = dto.StructureCode,
            StructureName = dto.StructureName,
            StructureParentUid = Guid.TryParse(dto.StructureParentId, out var parentUid) && parentUid != Guid.Empty
                ? parentUid
                : null,
            Description = dto.Description,
            IsDeleted = false,
            IsDefault = false,
            IsHidden = false
        };
        entity.Roles =
        [
            new() {
                RoleUid = GuidHelper.GenerateUUIDv7(),
                RoleName = entity.StructureName + " " + "Default",
                Description = "Default role for " + entity.StructureName,
                OrgUid = orgUid,
                IsDeleted = false,
                IsDefault = true,
                IsHidden = false,
                StructureUid = entity.StructureUid
            }
        ];

        return entity;
    }

    /// <summary>
    /// Updates a Structure entity from an UpdateStructureRequestDto
    /// </summary>
    /// <param name="entity"></param>
    /// <param name="dto"></param>
    public static void UpdateFromDto(this Structure entity, UpdateStructureRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (dto.StructureCode != null)
            entity.StructureCode = dto.StructureCode;

        if (dto.StructureName != null)
            entity.StructureName = dto.StructureName;

        if (Guid.TryParse(dto.StructureParentId, out var parentUid) && parentUid != Guid.Empty)
        {
            entity.StructureParentUid = parentUid;
        }

        if (dto.Description != null)
            entity.Description = dto.Description;
    }

    #endregion
}