using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Databases.Queryables;
using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;

namespace Kantoku.Api.Databases.Repositories;

public interface IAccountRepository
{
    Task<Account?> GetById(Guid accountId, AccountQueryableOptions options);
    Task<Account?> GetByEmployeeId(Guid employeeId, AccountQueryableOptions options);
    Task<Account?> GetByIdentity(string identityInfo, AccountQueryableOptions options);
    Task<bool> IsExist(IEnumerable<string?> identityInfo);
    Task<Guid?> Create(Account account);
    Task<bool> Update(Account account);
    Task<bool> Lock(Account account);
    Task<bool> Unlock(Account account);
}

[Repository(ServiceLifetime.Scoped)]
public class AccountRepository : BaseRepository<Account>, IAccountRepository
{
    private readonly IAccountQueryable accountQueryable;
    public AccountRepository(
        PostgreDbContext context,
        IAccountQueryable accountQueryable,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor
    )
        : base(context, logger, httpContextAccessor)
    {
        this.accountQueryable = accountQueryable;
    }

    public async Task<Account?> GetById(Guid accountId, AccountQueryableOptions options)
    {
        try
        {
            var query = accountQueryable.GetAccountQueryIncluded(new AccountQueryableOptions())
                        .Where(a => a.AccountUid == accountId);

            return await query.FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting account by id");
            return null;
        }
    }

    public async Task<Account?> GetByEmployeeId(Guid employeeId, AccountQueryableOptions options)
    {
        try
        {
            var query = accountQueryable.GetAccountQueryIncluded(options)
                .Where(a => a.Employees.Any(e => e.EmployeeUid == employeeId));

            return await query.FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting account by employee id");
            return null;
        }
    }

    public async Task<Account?> GetByIdentity(string identityInfo, AccountQueryableOptions options)
    {
        try
        {
            var query = accountQueryable.GetAccountQueryIncluded(options)
                .Where(a => a.LoginId.Equals(identityInfo) || a.Email.Equals(identityInfo))
                .Where(a => !a.IsLocked);

            return await query.FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting account by identity info");
            return null;
        }
    }

    public async Task<bool> IsExist(IEnumerable<string?> identityInfo)
    {
        try
        {
            var validIdentityInfo = identityInfo.Where(ei => !string.IsNullOrEmpty(ei));
            return await accountQueryable.GetAccountQueryIncluded(new AccountQueryableOptions())
                .Where(a => validIdentityInfo.Contains(a.LoginId) || validIdentityInfo.Contains(a.Email))
                .AnyAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error checking if account exists");
            return false;
        }
    }

    public async Task<bool> Lock(Account account)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            account.IsLocked = true;
            context.Entry(account).Property(e => e.IsLocked).IsModified = true;
            var affectedRows = await context.SaveChangesAsync();
            if (affectedRows > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error locking account");
            await transaction.RollbackAsync();
            return false;
        }
    }

    public async Task<bool> Unlock(Account account)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            account.IsLocked = false;
            context.Entry(account).Property(e => e.IsLocked).IsModified = true;
            var affectedRows = await context.SaveChangesAsync();
            if (affectedRows > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error unlocking account");
            await transaction.RollbackAsync();
            return false;
        }
    }

    public async Task<Guid?> Create(Account account)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Accounts.AddAsync(account);
            var affectedRows = await context.SaveChangesAsync();
            if (affectedRows > 0)
            {
                await transaction.CommitAsync();
                return account.AccountUid;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating account");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<bool> Update(Account account)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Accounts.Update(account);
            var affectedRows = await context.SaveChangesAsync();
            if (affectedRows > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating account");
            await transaction.RollbackAsync();
            return false;
        }
    }
}