namespace Kantoku.Api.Dtos.Employee.Response;

public class EmployeesResponseDto
{
    public IEnumerable<EmployeeResponseDto> Items { get; set; } = [];
    public int PageIndex { get; set; }
    public int PageSize { get; set; }
    public int TotalRecords { get; set; }
}

public class EmployeeResponseDto
{
    public string? EmployeeId { get; set; }

    public string? EmployeeCode { get; set; }

    public string? EmployeeName { get; set; }

    public IEnumerable<EmployeeMailInfoDto>? EmployeeMails { get; set; }

    public IEnumerable<EmployeePhoneInfoDto>? EmployeePhones { get; set; }

    public string? EmployeeAddress { get; set; }

    public string? WorkingStatus { get; set; }

    public string? WorkingStatusName { get; set; }

    public string? WorkingFromDate { get; set; }

    public string? WorkingToDate { get; set; }

    public string? StructureId { get; set; }

    public string? StructureName { get; set; }

    public string? PositionId { get; set; }

    public string? PositionName { get; set; }

    public string? RankingId { get; set; }

    public string? RankingName { get; set; }

    public float? StandardWorkingHours { get; set; }

    public int? SalaryInMonth { get; set; }

    public IEnumerable<EmployeeRoleInfoDto>? Roles { get; set; }

    public bool? HasApprovalAuthority { get; set; }
}

public class EmployeeRoleInfoDto
{
    public string? RoleId { get; set; }
    public string? RoleName { get; set; }
}

public class EmployeeMailInfoDto
{
    public string? Email { get; set; }
    public bool? IsPrimary { get; set; }
}

public class EmployeePhoneInfoDto
{
    public string? Phone { get; set; }
    public bool? IsPrimary { get; set; }
}
