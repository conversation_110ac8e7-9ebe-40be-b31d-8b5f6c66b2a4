using Kantoku.Api.Dtos.Base.Response;
using Kantoku.Api.Dtos.Role.Response;

namespace Kantoku.Api.Dtos.Structure.Response;

public class StructureResponseDto : BaseResponseDto
{
    public string? StructureId { get; set; }

    public string? StructureCode { get; set; }

    public string? StructureName { get; set; }

    public string? StructureParentId { get; set; }

    public IEnumerable<StructureResponseDto>? Children { get; set; }

    public IEnumerable<RoleResponseDto>? Roles { get; set; }

    public string? Description { get; set; }
}