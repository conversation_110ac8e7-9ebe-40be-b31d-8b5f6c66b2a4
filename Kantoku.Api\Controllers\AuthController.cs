using Kantoku.Api.Dtos.Auth;
using Kantoku.Api.Dtos.Auth.Request;
using Kantoku.Api.Dtos.Auth.Response;
using Kantoku.Api.Dtos.Device.Request;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Kantoku.Api.Controllers;

[Route("/api/v1/[controller]")]
[ApiController]
public class AuthController : BaseController
{
    private readonly IAuthenticationService authenticationService;
    public AuthController(
        ITResponseFactory responseFactory,
        IAuthenticationService authenticationService
    ) : base(responseFactory)
    {
        this.authenticationService = authenticationService;
    }

    /// <summary>
    /// Get OTP for email verification
    /// </summary>
    /// <param name="email">Email address to verify</param>
    /// <returns>Success response with OTP</returns>
    [AllowAnonymous]
    [HttpGet("verification/email")]
    public async Task<GeneralResponse<bool>> GetOTP([FromQuery] string email)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<bool>();

            await authenticationService.GetOTP(email);
            return Success(true);

        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Sign in to the system with username and password
    /// </summary>
    /// <param name="dto">Sign in request containing username and password</param>
    /// <returns>Success response with authentication token</returns>
    [AllowAnonymous]
    [HttpPost("sign-in")]
    public async Task<GeneralResponse<TokenDto>> SignIn([FromBody] SignInRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<TokenDto>();

            var headers = Request.Headers;
            var deviceInfoStr = headers["device_info"].ToString();

            var deviceInfo = JsonConvert.DeserializeObject<DeviceRequestDto>(deviceInfoStr);

            var result = await authenticationService.SignIn(dto, deviceInfo);
            return SuccessFromResult(result);
        }

        catch (BusinessException e)
        {
            return Fail<TokenDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Sign on to an organization after successful authentication
    /// </summary>
    /// <param name="dto">Sign on request containing organization details</param>
    /// <returns>Success response with organization access token</returns>
    [Authorize(Policy = PolicyConstant.ACCOUNT_ACCESS)]
    [HttpPost("sign-on")]
    public async Task<GeneralResponse<TokenDto>> SignOn([FromBody] SignOnRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<TokenDto>();

            var headers = Request.Headers;
            var deviceInfoStr = headers["device_info"].ToString();

            var deviceInfo = JsonConvert.DeserializeObject<DeviceRequestDto>(deviceInfoStr);
            var result = await authenticationService.SignOn(dto, deviceInfo);
            return SuccessFromResult(result);
        }

        catch (BusinessException e)
        {
            return Fail<TokenDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Allows authenticated users to change their password
    /// </summary>
    /// <param name="dto">Change password request containing old and new passwords</param>
    /// <returns>Success response indicating password was changed</returns>
    [Authorize(Policy = PolicyConstant.ACCOUNT_ACCESS)]
    [HttpPost("change-password")]
    public async Task<GeneralResponse<bool>> ChangePassword([FromBody] ChangePasswordRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid || !dto.NewPassword.Equals(dto.ConfirmNewPassword))
                return BadRequest<bool>();

            await authenticationService.ChangePassword(dto);
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Allows administrators to reset a user's password
    /// </summary>
    /// <param name="dto">Reset password request containing user ID and new password</param>
    /// <returns>Success response indicating password was reset</returns>
    [Authorize(Policy = PolicyConstant.ORG_ACCESS)]
    [HttpPost("reset-password")]
    public async Task<GeneralResponse<bool>> ResetPassword([FromBody] ResetPasswordRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<bool>();

            await authenticationService.ResetPassword(dto);
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Allows users to recover their password through email verification
    /// </summary>
    /// <param name="dto">Recover password request containing email address</param>
    /// <returns>Success response indicating recovery email was sent</returns>
    [AllowAnonymous]
    [HttpPost("recover-password")]
    public async Task<GeneralResponse<bool>> RecoverPassword([FromBody] RecoverPasswordRequestDto dto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<bool>();

            await authenticationService.RecoverPassword(dto);
            return Success(true);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }
}