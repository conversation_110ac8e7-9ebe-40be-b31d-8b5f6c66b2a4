using Kantoku.Processor.Models.BatchJobManager;

namespace Kantoku.Processor.Data.Repositories;

/// <summary>
/// Interface for batch job repository operations
/// </summary>
public interface IBatchJobRepository
{
    /// <summary>
    /// Gets all batch jobs
    /// </summary>
    Task<IEnumerable<BatchJob>> GetAllJobsAsync();
    
    /// <summary>
    /// Gets all enabled batch jobs
    /// </summary>
    Task<IEnumerable<BatchJob>> GetEnabledJobsAsync();
    
    /// <summary>
    /// Gets a batch job by ID
    /// </summary>
    Task<BatchJob?> GetJobByIdAsync(Guid id);
    
    /// <summary>
    /// Gets a batch job by name
    /// </summary>
    Task<BatchJob?> GetJobByNameAsync(string name);
    
    /// <summary>
    /// Creates a new batch job
    /// </summary>
    Task<BatchJob> CreateJobAsync(BatchJob job);
    
    /// <summary>
    /// Updates an existing batch job
    /// </summary>
    Task<BatchJob> UpdateJobAsync(BatchJob job);

    /// <summary>
    /// Checks if a batch job exists by name
    /// </summary>
    Task<bool> ExistsAsync(string name);
    
    /// <summary>
    /// Enables or disables a batch job
    /// </summary>
    Task<bool> SetJobStatusAsync(Guid id, bool isEnabled);
    
    /// <summary>
    /// Deletes a batch job
    /// </summary>
    Task<bool> DeleteJobAsync(Guid id);
    
    /// <summary>
    /// Updates the LastRunAt timestamp for a job
    /// </summary>
    Task UpdateJobLastRunTimeAsync(Guid id, DateTime lastRunTime);
    
    /// <summary>
    /// Records a new job execution history entry
    /// </summary>
    Task<BatchJobHistory> CreateJobHistoryAsync(BatchJobHistory history);
    
    /// <summary>
    /// Updates an existing job history entry
    /// </summary>
    Task<BatchJobHistory> UpdateJobHistoryAsync(BatchJobHistory history);
    
    /// <summary>
    /// Gets job execution history for a specific job
    /// </summary>
    Task<IEnumerable<BatchJobHistory>> GetJobHistoryAsync(Guid jobId, int limit = 10);
    
    /// <summary>
    /// Gets a specific job history entry
    /// </summary>
    Task<BatchJobHistory?> GetJobHistoryByIdAsync(Guid historyId);
}
