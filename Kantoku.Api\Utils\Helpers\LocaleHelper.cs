using System.Globalization;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Mappers;
using Microsoft.AspNetCore.Http;

namespace Kantoku.Api.Utils.Helpers;

public static class LocaleHelper
{
    public static bool NeedTranslate(CultureInfo? culture)
    {
        if (culture is null) return false;
        return !culture.Name.Equals("ja-JP") && !culture.Name.Equals("ja");
    }

    public static string GetLanguageCode(CultureInfo? culture)
    {
        if (culture is null) return LanguageConstant.JA;
        return CultureInfoMapper.ToLanguage(culture);
    }

    public static TimeZoneInfo GetTimeZone(string? orgTimeZoneId)
    {
        if (string.IsNullOrEmpty(orgTimeZoneId))
        {
            return TimeZoneInfo.FindSystemTimeZoneById("Asia/Tokyo");
        }
        return TimeZoneInfo.FindSystemTimeZoneById(orgTimeZoneId);
    }
}
