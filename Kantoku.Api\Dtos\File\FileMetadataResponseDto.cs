namespace Kantoku.Api.Dtos.File;

public partial class FileMetadataResponseDto
{
    public string? FileId { get; set; }

    public string? FileName { get; set; }

    public string? FileType { get; set; }

    public string? FileUrl { get; set; }

    public long FileSize { get; set; }

    public IDictionary<string, string?>? Metadata { get; set; }

    public DateTime? CreateTime { get; set; }

    public DateTime? UpdateTime { get; set; }
}
