using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Utils.Attributes.Class;
namespace Kantoku.Api.Databases.Queryables;

public class DeviceTokenQueryableOptions : EntityQueryableOptions
{
}

public interface IDeviceTokenQueryable
{
    IQueryable<DeviceToken> GetDeviceTokenQuery(
        DeviceTokenQueryableOptions options
    );

    IQueryable<DeviceToken> GetDeviceTokenQueryIncluded(
        DeviceTokenQueryableOptions options,
        IQueryable<DeviceToken>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class DeviceTokenQueryable(PostgreDbContext context) :
    BaseQueryable<DeviceToken>(context), IDeviceTokenQueryable
{
    public IQueryable<DeviceToken> GetDeviceTokenQuery(
        DeviceTokenQueryableOptions options
    )
    {
        var query = GetQuery(options);
        return query;
    }

    public IQueryable<DeviceToken> GetDeviceTokenQueryIncluded(
        DeviceTokenQueryableOptions options,
        IQueryable<DeviceToken>? query = null
    )
    {
        query ??= GetDeviceTokenQuery(options);

        return query;
    }
}
