using Kantoku.Api.Utils.Constants;
using System.Resources;
using System.Linq;
using System.Reflection;
using System.Globalization;

namespace Kantoku.Api.Utils.Mappers
{
    public static class StatusCodeMapper
    {
        private static readonly Dictionary<string, int> CodeToStatusCodeMap = new()
        {
            { ResponseCodeConstant.SUCCESS, StatusCodes.Status200OK },
            { ResponseCodeConstant.NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.NOT_CHANGED, StatusCodes.Status304NotModified },
            { ResponseCodeConstant.BAD_REQUEST, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.UNAUTHORIZED, StatusCodes.Status401Unauthorized },
            { ResponseCodeConstant.FORBIDDEN, StatusCodes.Status403Forbidden },
            { ResponseCodeConstant.PASSWORD_INCORRECT, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.PASSWORD_CHANGES_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CONFIRM_PASSWORD_MISMATCH, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.PASSWORD_DOES_NOT_MEET_REQUIREMENT, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.PASSWORD_RESET_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.OUTSOURCE_BLOCKED, StatusCodes.Status403Forbidden },
            { ResponseCodeConstant.PASSWORD_GENERATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.LOGIN_FAILED, StatusCodes.Status401Unauthorized },
            { ResponseCodeConstant.TOKEN_EXPIRED, StatusCodes.Status401Unauthorized },
            { ResponseCodeConstant.TOKEN_INVALID, StatusCodes.Status401Unauthorized },
            { ResponseCodeConstant.TOKEN_VALIDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.OTP_GENERATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.OTP_CODE_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.OTP_CODE_INCORRECT, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.EMPLOYEE_LEAVE_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.INITIAL_EMPLOYEE_LEAVE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.UPDATE_EMPLOYEE_LEAVE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.DELETE_EMPLOYEE_LEAVE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CONTRACT_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CONTRACT_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CONTRACTOR_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.CONTRACTOR_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CONTRACTOR_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CONTRACTOR_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CONTRACTOR_LOGO_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.ORG_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.ORG_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ORG_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ORG_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.NOT_ORG_OWNER, StatusCodes.Status403Forbidden },
            { ResponseCodeConstant.NOT_ORG_ADMIN, StatusCodes.Status403Forbidden },
            { ResponseCodeConstant.STRUCTURE_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.STRUCTURE_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.STRUCTURE_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.POSITION_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.POSITION_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.POSITION_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.POSITION_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.RANKING_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.RANKING_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.RANKING_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.RANKING_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.WORKPLACE_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.WORKPLACE_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.WORKPLACE_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.GLOBALCONFIG_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.EMAIL_TEMPLATE_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.EMAIL_SENT_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ROLE_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.ROLE_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ROLE_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ROLE_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.UNIT_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.REQUEST_TYPE_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.TOKEN_GENERATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.STRUCTURE_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.LEAVE_TYPE_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.STATUS_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.REQUEST_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.REQUEST_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.REQUEST_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.APPROVE_ATTENDANCE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.APPROVE_REQUEST_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.REJECT_REQUEST_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CANCEL_REQUEST_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.DAILY_ATT_REQUEST_HAS_NO_SHIFT, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.NO_APPROVAL_PERMISSION, StatusCodes.Status403Forbidden },
            { ResponseCodeConstant.ALREADY_PROCESSED_OR_CANCELLED, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.REQUEST_HAS_NO_PROJECT, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.REQUEST_LEAVE_ON_DAYOFF, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.REQUEST_ATTENDANCE_DAILY_NOT_CHECKOUT, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.REQUEST_UPDATE_STATUS_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.REQUEST_ATTENDANCE_DAILY_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.REQUEST_ATTENDANCE_DAILY_IS_REQUESTED, StatusCodes.Status409Conflict },
            { ResponseCodeConstant.REQUEST_INOUT_ON_DAYOFF, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.REQUEST_ATTENDANCE_DAILY_INVALID_DATE, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.TRANSLATOR_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.SHIFT_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.SHIFT_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.SHIFT_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.SHIFT_REMOVE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CHECKIN_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CHECKOUT_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.BREAKIN_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.BREAKOUT_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ALREADY_CHECKED_IN, StatusCodes.Status409Conflict },
            { ResponseCodeConstant.ALREADY_IN_A_BREAK, StatusCodes.Status409Conflict },
            { ResponseCodeConstant.NOT_CHECKIN_YET, StatusCodes.Status409Conflict },
            { ResponseCodeConstant.MORE_THAN_ONE_BREAK, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.NOT_IN_A_BREAK, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.NOT_IN_A_SHIFT, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.BREAK_TIME_EXCEEDED, StatusCodes.Status409Conflict },
            { ResponseCodeConstant.CREATE_ADDITIONAL_SHIFT_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CANNOT_UPDATE_APPROVED_SHIFT, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.SCHEDULE_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.SCHEDULE_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.SCHEDULE_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.SCHEDULE_REMOVE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.SCHEDULE_EXPORT_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ALREADY_HAS_SCHEDULED, StatusCodes.Status409Conflict },
            { ResponseCodeConstant.UPDATE_ON_ATTENDANCE_SHIFT, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.PROJECT_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.PROJECT_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.PROJECT_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.PROJECT_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.PROJECT_SUMMARY_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.PROJECT_TYPE_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.PROJECT_TYPE_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.PROJECT_TYPE_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.PROJECT_DAILY_REPORT_EXIST, StatusCodes.Status409Conflict },
            { ResponseCodeConstant.PROJECT_DAILY_REPORT_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.PROJECT_DAILY_REPORT_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.PROJECT_DAILY_REPORT_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.PROJECT_DAILY_REPORT_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.LOCATION_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.LOCATION_INFO_INVALID, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.HOLIDAY_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.HOLIDAY_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.HOLIDAY_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.FUNCTIONAL_ROLE_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.FUNCTIONAL_ROLE_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.FUNCTIONAL_ROLE_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.FUNCTION_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.FUNCTION_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.FUNCTION_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.TRACKING_HISTORY_RETRIEVE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.TRACKING_HISTORY_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.BUCKET_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.OBJECT_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.OBJECT_METADATA_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.OBJECT_UPLOAD_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.OBJECT_DOWNLOAD_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.BUCKET_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.OBJECT_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ATTENDANCE_REPORT_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ATTENDANCE_REPORT_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.WORK_SHIFT_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.WORK_SHIFT_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.WORK_SHIFT_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.WORK_SHIFT_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.WORK_SHIFT_ASSIGN_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.WORK_SHIFT_BREAK_TIME_INVALID, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.WORK_SHIFT_NOT_ASSIGNED, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.CATEGORY_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.CATEGORY_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CATEGORY_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.VENDOR_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.VENDOR_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.VENDOR_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.VENDOR_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.VENDOR_LOGO_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.PROCESS_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.PROCESS_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.PROCESS_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ITEM_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.ITEM_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ITEM_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ITEM_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ITEM_ILLUSTRATION_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.INPUTCOST_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.INPUTCOST_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.INPUTCOST_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ENTRYTYPE_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.ENTRYTYPE_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ENTRYTYPE_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.MANUFACTURER_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.MANUFACTURER_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.MANUFACTURER_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.MANUFACTURER_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.MANUFACTURER_LOGO_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.INPUTCOSTITEM_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.INPUTCOSTITEM_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.INPUTCOSTITEM_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.SIGN_UP_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.SIGN_ON_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ACCOUNT_ALREADY_EXISTS, StatusCodes.Status409Conflict },
            { ResponseCodeConstant.ACCOUNT_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.ACCOUNT_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ACCOUNT_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ACCOUNT_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ACCOUNT_LOCK_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ACCOUNT_UNLOCK_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.USER_INFO_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.USER_INFO_ALREADY_EXIST, StatusCodes.Status409Conflict },
            { ResponseCodeConstant.USER_INFO_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.USER_INFO_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.USER_INFO_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.USER_INFO_AVATAR_NOT_SET, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.USER_INFO_AVATAR_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.USER_INFO_AVATAR_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.EMPLOYEE_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.EMPLOYEE_ALREADY_EXIST, StatusCodes.Status409Conflict },
            { ResponseCodeConstant.EMPLOYEE_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.EMPLOYEE_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.EMPLOYEE_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.EMPLOYEE_INVITATION_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.EMPLOYEE_INVITATION_ACCEPT_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.EMPLOYEE_INVITATION_REJECT_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.REQUEST_ALREADY_APPROVED, StatusCodes.Status409Conflict },
            { ResponseCodeConstant.REQUEST_ALREADY_CANCELLED, StatusCodes.Status409Conflict },
            { ResponseCodeConstant.ALREADY_CHECKED_OUT, StatusCodes.Status409Conflict },
            { ResponseCodeConstant.FILE_UPLOAD_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.FILE_DOWNLOAD_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.FILE_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.FILE_METADATA_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.FILE_METADATA_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.FILE_METADATA_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.INTERNAL_SERVER_ERROR, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ACCOUNT_INFO_ALREADY_EXISTS, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.EMPLOYEE_INVITATION_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.EMPLOYEE_INVITATION_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.LINK_EMPLOYEE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.EMPLOYEE_LEAVE_ALREADY_EXIST, StatusCodes.Status409Conflict },
            { ResponseCodeConstant.EMPLOYEE_LEAVE_EXPIRE_DATE_NOT_VALID, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.OUTSOURCE_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.OUTSOURCE_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.OUTSOURCE_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.OUTSOURCE_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.OUTSOURCE_PRICE_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.OUTSOURCE_PRICE_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.OUTSOURCE_PRICE_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.OUTSOURCE_LOGO_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.REQUEST_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.EVENT_CALENDAR_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.EVENT_CALENDAR_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.EVENT_CALENDAR_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.EVENT_CALENDAR_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.EVENT_CALENDAR_DATE_INVALID, StatusCodes.Status400BadRequest },
            { ResponseCodeConstant.EVENT_CALENDAR_EXISTS, StatusCodes.Status409Conflict },
            { ResponseCodeConstant.ATTENDANCE_REPORT_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.CATEGORY_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ITEM_PRICE_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.ITEM_PRICE_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ITEM_PRICE_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.INPUTCOST_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.ENTRYTYPE_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.INPUTCOSTITEM_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.PAYMENTTYPE_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.PAYMENTTYPE_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.PAYMENTTYPE_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.PAYMENTTYPE_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.UNIT_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.UNIT_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.UNIT_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CONSTRUCTION_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.CONSTRUCTION_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CONSTRUCTION_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CONSTRUCTION_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.SUB_CONSTRUCTION_EXIST, StatusCodes.Status409Conflict },
            { ResponseCodeConstant.CUSTOMER_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.CUSTOMER_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CUSTOMER_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CUSTOMER_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CUSTOMER_LOGO_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.CUSTOMER_TYPE_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.EMPLOYEE_INVITATION_EXIST, StatusCodes.Status409Conflict },
            { ResponseCodeConstant.CONSTRUCTION_COST_NOT_EXIST, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.CONSTRUCTION_COST_CREATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CONSTRUCTION_COST_UPDATE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.CONSTRUCTION_COST_DELETE_FAILED, StatusCodes.Status500InternalServerError },
            { ResponseCodeConstant.NO_AUDIT_LOG, StatusCodes.Status404NotFound },
            { ResponseCodeConstant.ATTENDANCE_REPORT_ALREADY_APPROVED, StatusCodes.Status409Conflict }
        };

        public static int MapFromCode(string errorCode)
        {
            try
            {
                if (CodeToStatusCodeMap.TryGetValue(errorCode, out int value))
                {
                    return value;
                }
                else
                {
                    return StatusCodes.Status500InternalServerError;
                }
            }
            catch (Exception)
            {
                return StatusCodes.Status500InternalServerError;
            }
        }

        public static void ValidateAllResponseCodesAreMapped()
        {
            var unmappedCodes = new List<string>();

            // Get all public constant string fields from ResponseCodeConstant
            var responseCodeFields = typeof(ResponseCodeConstant)
                .GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.FlattenHierarchy)
                .Where(fi => fi.IsLiteral && !fi.IsInitOnly && fi.FieldType == typeof(string));

            // Check each response code has a mapping
            foreach (var field in responseCodeFields)
            {
                string code = field.GetValue(null)?.ToString() ?? string.Empty;
                if (!string.IsNullOrEmpty(code) && !CodeToStatusCodeMap.ContainsKey(code))
                {
                    unmappedCodes.Add($"{field.Name}: {code}");
                }
            }

            if (unmappedCodes.Any())
            {
                throw new Exception($"The following response codes are not mapped to HTTP status codes:\n{string.Join("\n", unmappedCodes)}");
            }
        }

        public static void ValidateAllErrorCodesHaveMessages()
        {
            var missingMessages = new List<string>();

            // Get all error codes from ResponseCodeConstant
            var errorCodeFields = typeof(ResponseCodeConstant)
                .GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.FlattenHierarchy)
                .Where(fi => fi.IsLiteral && !fi.IsInitOnly && fi.FieldType == typeof(string));

            // Load resource managers for all languages
            ResourceManager rm = new("Kantoku.Api.Resources.ErrorMessage", Assembly.GetExecutingAssembly());

            // Check each error code has translations in all resource files
            foreach (var field in errorCodeFields)
            {
                var code = field.GetValue(null)?.ToString();

                if (string.IsNullOrEmpty(code))
                    continue;

                try
                {
                    if (rm.GetString(code) is null)
                    {
                        var m = $"{field.Name}: {code}";
                        missingMessages.Add(m);
                    }

                }
                catch (System.Exception)
                {
                    var m = $"{field.Name}: {code}";
                    missingMessages.Add(m);
                }
            }
            Console.WriteLine(rm.BaseName);
            if (missingMessages.Any())
            {
                Console.WriteLine($"{string.Join("\n", missingMessages)}");
            }
        }
    }
}


