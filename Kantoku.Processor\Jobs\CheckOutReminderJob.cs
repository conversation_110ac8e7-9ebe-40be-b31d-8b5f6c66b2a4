using Kantoku.Processor.Data.Contexts;
using Kantoku.Processor.Data.Repositories;
using Kantoku.Processor.Data.Seeders;
using Kantoku.Processor.Dtos;
using Kantoku.Processor.Jobs.Bases;
using Kantoku.Processor.Models.BatchJobManager;
using Kantoku.Processor.Services.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Processor.Jobs
{
    /// <summary>
    /// Job that sends a reminder to employees who haven't checked out
    /// after their scheduled shift end time (after 1 hour)
    /// </summary>
    public class CheckOutReminderJob : JobBase
    {
        private readonly ILogger<CheckOutReminderJob> _logger;

        // We'll use the IServiceScopeFactory instead of IServiceProvider
        // This allows us to create scopes on demand without relying on 
        // a potentially disposed IServiceProvider
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly IFirebaseService _firebaseService;
        private readonly DatabaseSeeder _databaseSeeder;


        // Job configuration properties
        public const string JobName = nameof(CheckOutReminderJob);

        public CheckOutReminderJob(
            ILogger<CheckOutReminderJob> logger,
            IServiceScopeFactory serviceScopeFactory,
            IFirebaseService firebaseService,
            DatabaseSeeder databaseSeeder)
        {
            _logger = logger;
            _serviceScopeFactory = serviceScopeFactory;
            _firebaseService = firebaseService;
            _databaseSeeder = databaseSeeder;
        }

        /// <summary>
        /// Ensures the job exists in the database with proper configuration
        /// </summary>
        public async Task EnsureJobExistsAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Checking if {JobName} exists in database", JobName);

            // Create a separate scope to handle the initialization
            using var scope = _serviceScopeFactory.CreateScope();
            var batchJobRepository = scope.ServiceProvider.GetRequiredService<IBatchJobRepository>();

            // Check if job exists, if not, seed it to database
            var job = await batchJobRepository.GetJobByNameAsync(JobName);

            if (job == null)
            {
                await _databaseSeeder.SeedCheckOutReminderJobAsync();
            }
        }

        public override async Task ExecuteAsync()
        {
            LogInformation("Starting check out reminder job execution");
            DateTime startTime = DateTime.UtcNow;

            // Create a new scope using the factory which won't be disposed
            using var scope = _serviceScopeFactory.CreateScope();
            var batchJobRepository = scope.ServiceProvider.GetRequiredService<IBatchJobRepository>();
            var appDbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            BatchJobHistory jobHistory = new BatchJobHistory
            {
                FiredId = Guid.NewGuid(),
                JobId = JobConfiguration.JobId,
                StartedAt = startTime,
                Status = JobExecutionStatus.InProgress
            };

            try
            {
                // Create initial job history record
                await batchJobRepository.CreateJobHistoryAsync(jobHistory);

                // Check if job is enabled
                if (!JobConfiguration.IsEnabled)
                {
                    LogInformation("Job is disabled, skipping execution");
                    jobHistory.Status = JobExecutionStatus.Cancelled;
                    jobHistory.ExecutionLog = GetExecutionLog();
                    jobHistory.CompletedAt = DateTime.UtcNow;
                    await batchJobRepository.UpdateJobHistoryAsync(jobHistory);
                    return;
                }

                var oneHourAgo = DateTime.UtcNow.AddHours(-1);

                var employeeIds = await appDbContext.EmployeeShifts
                    .Where(es =>
                        es.ScheduledEndTime.HasValue &&
                        !es.CheckOutTime.HasValue &&
                        (es.ScheduledEndTime + TimeSpan.FromMinutes(5) == DateTime.UtcNow
                        || es.ScheduledEndTime + TimeSpan.FromMinutes(10) == DateTime.UtcNow))
                    .Select(es => es.EmployeeUid)
                    .ToListAsync(CancellationToken);

                LogInformation($"Found {employeeIds.Count} employee shifts that need reminder to check out");

                var devicesTokens = await appDbContext.DeviceTokens
                    .Where(d => employeeIds.Contains(d.EmployeeUid))
                    .Select(d => d.FirebaseToken)
                    .ToListAsync(CancellationToken);

                foreach (var deviceToken in devicesTokens.Chunk(500))
                {
                    await _firebaseService.SendToDevices([.. deviceToken!], new FirebaseMessageDto
                    {
                        Title = "Check out reminder",
                        Body = "Your shift is ending soon, please check out"
                    });
                }

                LogInformation($"Successfully reminded {devicesTokens.Count} employees");

                // Update job execution status
                jobHistory.Status = JobExecutionStatus.Success;
            }
            catch (OperationCanceledException)
            {
                LogWarning("Check out reminder job was cancelled");
                jobHistory.Status = JobExecutionStatus.Cancelled;
                jobHistory.ErrorMessage = "Job was cancelled";
            }
            catch (Exception ex)
            {
                LogError($"Error executing check out reminder job: {ex.Message}", ex);
                jobHistory.Status = JobExecutionStatus.Failed;
                jobHistory.ErrorMessage = ex.Message;
            }
            finally
            {
                // Record job history
                jobHistory.CompletedAt = DateTime.UtcNow;
                jobHistory.ExecutionLog = GetExecutionLog();
                await batchJobRepository.UpdateJobHistoryAsync(jobHistory);

                // Update the job's LastRunAt time
                await batchJobRepository.UpdateJobLastRunTimeAsync(JobConfiguration.JobId, DateTime.UtcNow);
            }
        }
    }
}
