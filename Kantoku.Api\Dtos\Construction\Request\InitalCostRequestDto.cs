namespace Kantoku.Api.Dtos.Construction.Request;

public class InitalCostRequestDto
{
    /// <summary>
    /// Collection of initial contractual costs for the construction
    /// </summary>
    public IEnumerable<ContractualAmountRequestDto>? InitialContractualCosts { get; set; }

    /// <summary>
    /// Collection of modified contractual costs after changes
    /// </summary>
    public IEnumerable<ContractualAmountRequestDto>? ModifiedContractualCosts { get; set; }

    /// <summary>
    /// Collection of estimated costs for the construction
    /// </summary>
    public IEnumerable<ContractualAmountRequestDto>? InitalEstimatedCosts { get; set; }
}

/// <summary>
/// Data transfer object for contractual amount details
/// </summary>
public class ContractualAmountRequestDto
{
    /// <summary>
    /// Order sequence number for the contractual amount (*)
    /// </summary>
    public short SequenceNumber { get; set; }

    /// <summary>
    /// Additional description for the contractual amount
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// The contractual amount value (*)
    /// </summary>
    public long Amount { get; set; }

    /// <summary>
    /// Date when the amount was recorded
    /// </summary>
    public string? RecordedDate { get; set; }
}

