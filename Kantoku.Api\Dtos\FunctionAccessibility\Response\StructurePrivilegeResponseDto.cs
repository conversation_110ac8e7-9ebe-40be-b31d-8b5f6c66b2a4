using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.FunctionAccessibility.Response;

public class StructurePrivilegesResponseDto
{
    [JsonPropertyName("Items")]
    public IEnumerable<StructurePrivilegeResponseDto>? Items { get; set; }
}

public class StructurePrivilegeResponseDto
{
    [JsonPropertyName("StructureId")]
    public string? StructureId { get; set; }
    
    [JsonPropertyName("StructureName")]
    public string? StructureName { get; set; }

    [JsonPropertyName("RoleItems")]
    public IEnumerable<RolePrivilegeResponseDto>? RolesPrivileges { get; set; }
}