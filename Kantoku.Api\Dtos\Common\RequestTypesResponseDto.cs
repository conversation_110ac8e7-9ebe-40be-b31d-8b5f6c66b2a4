namespace Kantoku.Api.Dtos.Common;

public class RequestTypesResponseDto
{

    public IEnumerable<RequestTypeResponseDto> Items { get; set; } = [];
    public int PageIndex { get; set; }
    public int PageSize { get; set; }
    public int TotalRow { get; set; }
}
public class RequestTypeResponseDto
{
    public string? RequestTypeId { get; set; }

    public string? RequestTypeCode { get; set; }

    public string? RequestTypeName { get; set; }

    public string? Description { get; set; }

    public bool RequiredLevel1Approval { get; set; }

    public bool RequiredLevel2Approval { get; set; }
}

