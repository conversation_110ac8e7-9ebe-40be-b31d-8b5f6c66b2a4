[{"errorStatus": 200, "errorCode": "KAN00", "errorMessage": "<PERSON><PERSON><PERSON><PERSON> công"}, {"errorStatus": 404, "errorCode": "KAN01", "errorMessage": "<PERSON><PERSON><PERSON> nguyên yêu cầu không tồn tại"}, {"errorStatus": 304, "errorCode": "KAN02", "errorMessage": "<PERSON><PERSON><PERSON> nguyên không thay đổi"}, {"errorStatus": 400, "errorCode": "KAN03", "errorMessage": "<PERSON><PERSON><PERSON> nguyên không thay đổi"}, {"errorStatus": 401, "errorCode": "KAN04", "errorMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ph<PERSON>p"}, {"errorStatus": 403, "errorCode": "KAN05", "errorMessage": "<PERSON><PERSON><PERSON>"}, {"errorStatus": 500, "errorCode": "KAN500", "errorMessage": "Lỗi không xác định"}, {"errorStatus": 400, "errorCode": "AUTH01", "errorMessage": "<PERSON><PERSON><PERSON> kh<PERSON>u không ch<PERSON>h xác"}, {"errorStatus": 500, "errorCode": "AUTH02", "errorMessage": "<PERSON>hay đổi mật khẩu thất bại"}, {"errorStatus": 400, "errorCode": "AUTH03", "errorMessage": "<PERSON><PERSON><PERSON> nh<PERSON>n mật khẩu không khớp"}, {"errorStatus": 400, "errorCode": "AUTH04", "errorMessage": "<PERSON><PERSON><PERSON> khẩu không đáp ứng yêu cầu"}, {"errorStatus": 500, "errorCode": "AUTH05", "errorMessage": "Đặt lại mật khẩu thất bại"}, {"errorStatus": 403, "errorCode": "AUTH06", "errorMessage": "Ng<PERSON><PERSON>i dùng là lao động thuê ngoài, không thể đăng nhập"}, {"errorStatus": 500, "errorCode": "AUTH07", "errorMessage": "<PERSON><PERSON><PERSON> mật kh<PERSON>u thất bại"}, {"errorStatus": 401, "errorCode": "AUTH08", "errorMessage": "<PERSON><PERSON><PERSON> nh<PERSON>p thất bại"}, {"errorStatus": 500, "errorCode": "AUTH09", "errorMessage": "<PERSON><PERSON><PERSON> ký thất bại"}, {"errorStatus": 500, "errorCode": "AUTH10", "errorMessage": "<PERSON><PERSON><PERSON> nh<PERSON>p thất bại"}, {"errorStatus": 500, "errorCode": "AUTH11", "errorMessage": "Tạo mã OTP thất bại"}, {"errorStatus": 404, "errorCode": "AUTH12", "errorMessage": "Mã OTP không tồn tại"}, {"errorStatus": 400, "errorCode": "AUTH13", "errorMessage": "Mã OTP không chính xác"}, {"errorStatus": 401, "errorCode": "TKN01", "errorMessage": "To<PERSON> đã hết hạn"}, {"errorStatus": 401, "errorCode": "TKN02", "errorMessage": "<PERSON><PERSON> không hợp lệ"}, {"errorStatus": 500, "errorCode": "TKN03", "errorMessage": "<PERSON><PERSON><PERSON> thực token thất bại"}, {"errorStatus": 500, "errorCode": "TKN04", "errorMessage": "Tạo token thất bại"}, {"errorStatus": 404, "errorCode": "USR01", "errorMessage": "Người dùng không tồn tại"}, {"errorStatus": 500, "errorCode": "USR03", "errorMessage": "<PERSON><PERSON><PERSON> ng<PERSON>i dùng thất bại"}, {"errorStatus": 500, "errorCode": "USR04", "errorMessage": "<PERSON><PERSON><PERSON> nhật ngư<PERSON>i dùng thất bại"}, {"errorStatus": 500, "errorCode": "USR05", "errorMessage": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i dùng thất bại"}, {"errorStatus": 500, "errorCode": "USR06", "errorMessage": "<PERSON><PERSON> vai trò thất bại"}, {"errorStatus": 409, "errorCode": "USR07", "errorMessage": "ID đăng nhập đã tồn tại"}, {"errorStatus": 500, "errorCode": "USR08", "errorMessage": "<PERSON><PERSON><PERSON> nhật avatar ng<PERSON><PERSON>i dùng thất bại"}, {"errorStatus": 404, "errorCode": "USR09", "errorMessage": "Avatar người dùng chưa được đặt"}, {"errorStatus": 500, "errorCode": "USR10", "errorMessage": "Xóa avatar ng<PERSON><PERSON>i dùng thất bại"}, {"errorStatus": 404, "errorCode": "EML01", "errorMessage": "<PERSON><PERSON><PERSON><PERSON> tồn tại nghỉ phép của nhân viên"}, {"errorStatus": 500, "errorCode": "EML02", "errorMessage": "Khởi tạo nghỉ phép của nhân viên thất bại"}, {"errorStatus": 500, "errorCode": "EML03", "errorMessage": "<PERSON><PERSON><PERSON> nhật nghỉ phép của nhân viên thất bại"}, {"errorStatus": 500, "errorCode": "EML04", "errorMessage": "Xóa nghỉ phép của nhân viên thất bại"}, {"errorStatus": 409, "errorCode": "EML05", "errorMessage": "Nghỉ phép của nhân viên đã tồn tại"}, {"errorStatus": 400, "errorCode": "EML06", "errorMessage": "<PERSON><PERSON><PERSON> hết hạn nghỉ phép của nhân viên không hợp lệ"}, {"errorStatus": 500, "errorCode": "CRT01", "errorMessage": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> đồng thất bại"}, {"errorStatus": 500, "errorCode": "CRT02", "errorMessage": "<PERSON><PERSON><PERSON> nhật hợp đồng thất bại"}, {"errorStatus": 404, "errorCode": "ORG01", "errorMessage": "<PERSON><PERSON> chức không tồn tại"}, {"errorStatus": 500, "errorCode": "ORG02", "errorMessage": "<PERSON><PERSON><PERSON> tổ chức thất bại"}, {"errorStatus": 500, "errorCode": "ORG03", "errorMessage": "<PERSON><PERSON><PERSON> nhật tổ chức thất bại"}, {"errorStatus": 500, "errorCode": "ORG04", "errorMessage": "<PERSON><PERSON><PERSON> tổ chức thất bại"}, {"errorStatus": 403, "errorCode": "ORG05", "errorMessage": "<PERSON><PERSON><PERSON><PERSON> phải chủ sở hữu tổ chức"}, {"errorStatus": 403, "errorCode": "ORG06", "errorMessage": "<PERSON><PERSON><PERSON><PERSON> phải quản trị viên tổ chức"}, {"errorStatus": 404, "errorCode": "STR01", "errorMessage": "Phòng ban không tồn tại"}, {"errorStatus": 500, "errorCode": "STR02", "errorMessage": "<PERSON><PERSON><PERSON> phòng ban thất bại"}, {"errorStatus": 500, "errorCode": "STR03", "errorMessage": "<PERSON><PERSON><PERSON> nhật phòng ban thất bại"}, {"errorStatus": 500, "errorCode": "STR04", "errorMessage": "<PERSON><PERSON><PERSON> phòng ban thất bại"}, {"errorStatus": 400, "errorCode": "STR05", "errorMessage": "<PERSON><PERSON> liệu phòng ban không hợp lệ"}, {"errorStatus": 404, "errorCode": "POS01", "errorMessage": "<PERSON><PERSON> trí không tồn tại"}, {"errorStatus": 500, "errorCode": "POS02", "errorMessage": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> vụ thất bại"}, {"errorStatus": 500, "errorCode": "POS03", "errorMessage": "<PERSON><PERSON><PERSON> nh<PERSON>t chức vụ thất bại"}, {"errorStatus": 500, "errorCode": "POS04", "errorMessage": "<PERSON><PERSON><PERSON> ch<PERSON> vụ thất bại"}, {"errorStatus": 404, "errorCode": "RNK01", "errorMessage": "<PERSON><PERSON><PERSON> thợ không tồn tại"}, {"errorStatus": 500, "errorCode": "RNK02", "errorMessage": "<PERSON><PERSON><PERSON> bậc thợ thất bại"}, {"errorStatus": 500, "errorCode": "RNK03", "errorMessage": "<PERSON><PERSON><PERSON> nhật bậc thợ thất bại"}, {"errorStatus": 500, "errorCode": "RNK04", "errorMessage": "<PERSON><PERSON><PERSON> b<PERSON>c thợ thất bại"}, {"errorStatus": 404, "errorCode": "WPL01", "errorMessage": "<PERSON><PERSON><PERSON> làm vi<PERSON><PERSON> không tồn tại"}, {"errorStatus": 500, "errorCode": "WPL02", "errorMessage": "<PERSON><PERSON><PERSON> n<PERSON>i làm vi<PERSON>c thất bại"}, {"errorStatus": 500, "errorCode": "WPL03", "errorMessage": "<PERSON><PERSON><PERSON> nh<PERSON>t n<PERSON>i làm vi<PERSON>c thất bại"}, {"errorStatus": 404, "errorCode": "GLC01", "errorMessage": "<PERSON><PERSON><PERSON> hình toàn cầu không tồn tại"}, {"errorStatus": 404, "errorCode": "MAIL01", "errorMessage": "Mẫu email không tồn tại"}, {"errorStatus": 500, "errorCode": "MAIL02", "errorMessage": "Gửi email thất bại"}, {"errorStatus": 404, "errorCode": "ROLE01", "errorMessage": "<PERSON><PERSON> trò không tồn tại"}, {"errorStatus": 500, "errorCode": "ROLE02", "errorMessage": "<PERSON><PERSON>o vai trò thất bại"}, {"errorStatus": 500, "errorCode": "ROLE03", "errorMessage": "<PERSON><PERSON><PERSON> nhật vai trò thất bại"}, {"errorStatus": 500, "errorCode": "ROLE04", "errorMessage": "<PERSON><PERSON><PERSON> vai trò thất bại"}, {"errorStatus": 404, "errorCode": "COMN01", "errorMessage": "Đơn vị không tồn tại"}, {"errorStatus": 404, "errorCode": "COMN02", "errorMessage": "<PERSON><PERSON><PERSON> yêu cầu không tồn tại"}, {"errorStatus": 404, "errorCode": "COMN03", "errorMessage": "Loại nghỉ phép không tồn tại"}, {"errorStatus": 404, "errorCode": "COMN04", "errorMessage": "Tr<PERSON>ng thái không tồn tại"}, {"errorStatus": 404, "errorCode": "REQ01", "errorMessage": "<PERSON><PERSON><PERSON> c<PERSON>u không tồn tại"}, {"errorStatus": 500, "errorCode": "REQ02", "errorMessage": "<PERSON><PERSON><PERSON> y<PERSON>u cầu thất bại"}, {"errorStatus": 500, "errorCode": "REQ03", "errorMessage": "<PERSON><PERSON><PERSON> nhật yêu cầu thất bại"}, {"errorStatus": 500, "errorCode": "REQ04", "errorMessage": "<PERSON><PERSON> du<PERSON><PERSON>t điểm danh thất bại"}, {"errorStatus": 500, "errorCode": "REQ05", "errorMessage": "<PERSON><PERSON> du<PERSON>t yêu cầu thất bại"}, {"errorStatus": 500, "errorCode": "REQ06", "errorMessage": "Từ chối yêu cầu thất bại"}, {"errorStatus": 500, "errorCode": "REQ07", "errorMessage": "<PERSON><PERSON><PERSON> y<PERSON>u cầu thất bại"}, {"errorStatus": 400, "errorCode": "REQ08", "errorMessage": "<PERSON> tiết yêu c<PERSON>u không hợp lệ"}, {"errorStatus": 403, "errorCode": "REQ09", "errorMessage": "<PERSON><PERSON><PERSON><PERSON> có quyền phê du<PERSON>t"}, {"errorStatus": 400, "errorCode": "REQ10", "errorMessage": "<PERSON><PERSON> được xử lý hoặc hủy"}, {"errorStatus": 400, "errorCode": "REQ11", "errorMessage": "<PERSON><PERSON><PERSON><PERSON> có dự án nào được gán với yêu cầu này"}, {"errorStatus": 400, "errorCode": "REQ12", "errorMessage": "<PERSON><PERSON><PERSON> cầu nghỉ phép vào ngày nghỉ"}, {"errorStatus": 400, "errorCode": "REQ13", "errorMessage": "<PERSON><PERSON><PERSON> c<PERSON>u điểm danh hàng ngày chưa checkout"}, {"errorStatus": 500, "errorCode": "REQ14", "errorMessage": "<PERSON><PERSON><PERSON> nhật trạng thái yêu cầu thất bại"}, {"errorStatus": 500, "errorCode": "REQ15", "errorMessage": "<PERSON><PERSON><PERSON> điểm danh hàng ngày thất bại"}, {"errorStatus": 409, "errorCode": "REQ16", "errorMessage": "Tất cả ca làm việc cho ngày mong muốn này đã đư<PERSON><PERSON> yêu cầu"}, {"errorStatus": 400, "errorCode": "REQ17", "errorMessage": "<PERSON><PERSON><PERSON> c<PERSON>u vào/ra vào ngày nghỉ"}, {"errorStatus": 400, "errorCode": "REQ18", "errorMessage": "<PERSON><PERSON><PERSON> đi<PERSON>m danh hàng ngày không hợp lệ"}, {"errorStatus": 409, "errorCode": "REQ19", "errorMessage": "<PERSON><PERSON><PERSON> cầu đã đư<PERSON><PERSON> phê duy<PERSON>t"}, {"errorStatus": 409, "errorCode": "REQ20", "errorMessage": "<PERSON><PERSON><PERSON> c<PERSON>u đã bị hủy"}, {"errorStatus": 500, "errorCode": "REQ21", "errorMessage": "<PERSON><PERSON><PERSON> y<PERSON>u cầu thất bại"}, {"errorStatus": 404, "errorCode": "TRL01", "errorMessage": "<PERSON><PERSON><PERSON><PERSON> dịch không tồn tại"}, {"errorStatus": 404, "errorCode": "SFT01", "errorMessage": "<PERSON>a làm vi<PERSON><PERSON> không tồn tại"}, {"errorStatus": 500, "errorCode": "SFT02", "errorMessage": "Tạo ca làm vi<PERSON>c thất bại"}, {"errorStatus": 500, "errorCode": "SFT03", "errorMessage": "<PERSON><PERSON><PERSON> nhật ca làm việc thất bại"}, {"errorStatus": 500, "errorCode": "SFT04", "errorMessage": "Xóa ca làm vi<PERSON>c thất bại"}, {"errorStatus": 500, "errorCode": "SFT05", "errorMessage": "Check In thất bại"}, {"errorStatus": 500, "errorCode": "SFT06", "errorMessage": "Check Out thất bại"}, {"errorStatus": 500, "errorCode": "SFT07", "errorMessage": "Break In thất bại"}, {"errorStatus": 500, "errorCode": "SFT08", "errorMessage": "Break Out thất bại"}, {"errorStatus": 409, "errorCode": "SFT09", "errorMessage": "Đã Check In"}, {"errorStatus": 409, "errorCode": "SFT10", "errorMessage": "Đã trong giờ nghỉ"}, {"errorStatus": 409, "errorCode": "SFT11", "errorMessage": "Chưa Check In"}, {"errorStatus": 400, "errorCode": "SFT12", "errorMessage": "<PERSON>a làm việc đã kết thúc"}, {"errorStatus": 400, "errorCode": "SFT13", "errorMessage": "Không trong giờ nghỉ"}, {"errorStatus": 400, "errorCode": "SFT14", "errorMessage": "Chưa Break Out"}, {"errorStatus": 409, "errorCode": "SFT15", "errorMessage": "<PERSON>h<PERSON>i gian nghỉ vượt quá"}, {"errorStatus": 500, "errorCode": "SFT16", "errorMessage": "<PERSON><PERSON><PERSON> ca làm vi<PERSON><PERSON> b<PERSON> sung thất bại"}, {"errorStatus": 400, "errorCode": "SFT17", "errorMessage": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật ca làm việc đã được phê duyệt"}, {"errorStatus": 409, "errorCode": "SFT18", "errorMessage": "Đã Check Out"}, {"errorStatus": 500, "errorCode": "SFT19", "errorMessage": "Xóa ca làm vi<PERSON>c thất bại"}, {"errorStatus": 404, "errorCode": "SCH01", "errorMessage": "<PERSON><PERSON><PERSON> trình không tồn tại"}, {"errorStatus": 500, "errorCode": "SCH02", "errorMessage": "<PERSON><PERSON><PERSON> lịch trình thất bại"}, {"errorStatus": 500, "errorCode": "SCH03", "errorMessage": "<PERSON><PERSON><PERSON> nh<PERSON>t lịch trình thất bại"}, {"errorStatus": 500, "errorCode": "SCH04", "errorMessage": "<PERSON><PERSON><PERSON> lịch trình thất b<PERSON>i"}, {"errorStatus": 500, "errorCode": "SCH05", "errorMessage": "<PERSON><PERSON><PERSON> lịch trình thất bại"}, {"errorStatus": 409, "errorCode": "SCH06", "errorMessage": "<PERSON><PERSON> có lịch trình"}, {"errorStatus": 400, "errorCode": "SCH07", "errorMessage": "<PERSON><PERSON> tích lịch trình thất bại"}, {"errorStatus": 404, "errorCode": "PRJ01", "errorMessage": "Dự án không tồn tại"}, {"errorStatus": 500, "errorCode": "PRJ02", "errorMessage": "<PERSON>ạo dự án thất bại"}, {"errorStatus": 500, "errorCode": "PRJ03", "errorMessage": "<PERSON><PERSON><PERSON> nhật dự án thất bại"}, {"errorStatus": 500, "errorCode": "PRJ04", "errorMessage": "<PERSON><PERSON>a dự án thất bại"}, {"errorStatus": 404, "errorCode": "PRJ05", "errorMessage": "Tổng quan dự án không tồn tại"}, {"errorStatus": 404, "errorCode": "GPS01", "errorMessage": "<PERSON><PERSON> trí không tồn tại"}, {"errorStatus": 400, "errorCode": "GPS02", "errorMessage": "<PERSON>hô<PERSON> tin chức v<PERSON> không hợp lệ"}, {"errorStatus": 404, "errorCode": "HOL01", "errorMessage": "<PERSON><PERSON><PERSON> <PERSON> không tồn tại"}, {"errorStatus": 500, "errorCode": "HOL02", "errorMessage": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> lễ thất bại"}, {"errorStatus": 500, "errorCode": "HOL03", "errorMessage": "<PERSON><PERSON><PERSON> nhật ng<PERSON>y lễ thất bại"}, {"errorStatus": 404, "errorCode": "URF01", "errorMessage": "<PERSON><PERSON> trò chức năng không tồn tại"}, {"errorStatus": 500, "errorCode": "URF02", "errorMessage": "<PERSON><PERSON>o vai trò chức năng thất bại"}, {"errorStatus": 500, "errorCode": "URF03", "errorMessage": "<PERSON><PERSON><PERSON> nhật vai trò chức năng thất bại"}, {"errorStatus": 404, "errorCode": "FNC01", "errorMessage": "<PERSON><PERSON><PERSON> năng không tồn tại"}, {"errorStatus": 500, "errorCode": "FNC02", "errorMessage": "<PERSON><PERSON><PERSON> chức năng thất bại"}, {"errorStatus": 500, "errorCode": "FNC03", "errorMessage": "<PERSON><PERSON><PERSON> nhật chức năng thất bại"}, {"errorStatus": 500, "errorCode": "HST01", "errorMessage": "<PERSON><PERSON><PERSON> xu<PERSON><PERSON> lịch sử theo dõi thất bại"}, {"errorStatus": 500, "errorCode": "HST02", "errorMessage": "<PERSON><PERSON><PERSON> l<PERSON>ch sử theo dõi thất bại"}, {"errorStatus": 404, "errorCode": "MIO01", "errorMessage": "Bucket không tồn tại"}, {"errorStatus": 404, "errorCode": "MIO02", "errorMessage": "<PERSON><PERSON><PERSON> tượng không tồn tại"}, {"errorStatus": 404, "errorCode": "MIO03", "errorMessage": "Metadata đối tượng không tồn tại"}, {"errorStatus": 500, "errorCode": "MIO04", "errorMessage": "<PERSON><PERSON><PERSON> lên đối tượng thất bại"}, {"errorStatus": 500, "errorCode": "MIO05", "errorMessage": "<PERSON><PERSON><PERSON> xuống đối tượng thất bại"}, {"errorStatus": 500, "errorCode": "MIO06", "errorMessage": "<PERSON><PERSON><PERSON> đối tượng thất bại"}, {"errorStatus": 500, "errorCode": "MIO07", "errorMessage": "<PERSON><PERSON><PERSON> đối tượng thất bại"}, {"errorStatus": 500, "errorCode": "RPT01", "errorMessage": "<PERSON><PERSON><PERSON> báo cáo điểm danh thất bại"}, {"errorStatus": 500, "errorCode": "RPT02", "errorMessage": "<PERSON><PERSON><PERSON> nhật báo cáo điểm danh thất bại"}, {"errorStatus": 404, "errorCode": "RPT03", "errorMessage": "<PERSON><PERSON><PERSON> c<PERSON>o điểm danh không tồn tại"}, {"errorStatus": 409, "errorCode": "RPT04", "errorMessage": "<PERSON><PERSON><PERSON> cáo điểm danh đã đư<PERSON><PERSON> phê duy<PERSON>, kh<PERSON><PERSON> thể cập nhật"}, {"errorStatus": 409, "errorCode": "RPT05", "errorMessage": "<PERSON><PERSON>o cáo ngày đã tồn tại"}, {"errorStatus": 404, "errorCode": "RPT06", "errorMessage": "<PERSON><PERSON>o c<PERSON>o ngày không tồn tại"}, {"errorStatus": 500, "errorCode": "RPT07", "errorMessage": "<PERSON><PERSON><PERSON> báo cáo ngày thất bại"}, {"errorStatus": 500, "errorCode": "RPT08", "errorMessage": "<PERSON><PERSON><PERSON> nh<PERSON>t báo cáo ngày thất bại"}, {"errorStatus": 500, "errorCode": "RPT09", "errorMessage": "<PERSON><PERSON><PERSON> báo cáo ngày thất b<PERSON>i"}, {"errorStatus": 404, "errorCode": "CTG01", "errorMessage": "<PERSON><PERSON> mụ<PERSON> không tồn tại"}, {"errorStatus": 500, "errorCode": "CTG02", "errorMessage": "<PERSON><PERSON><PERSON> danh mục thất bại"}, {"errorStatus": 500, "errorCode": "CTG03", "errorMessage": "<PERSON><PERSON><PERSON> nh<PERSON>t danh mục thất bại"}, {"errorStatus": 500, "errorCode": "CTG04", "errorMessage": "<PERSON><PERSON><PERSON> danh mục thất bại"}, {"errorStatus": 404, "errorCode": "VDR01", "errorMessage": "<PERSON><PERSON>à cung cấp không tồn tại"}, {"errorStatus": 500, "errorCode": "VDR02", "errorMessage": "<PERSON><PERSON><PERSON> nhà cung cấp thất bại"}, {"errorStatus": 500, "errorCode": "VDR03", "errorMessage": "<PERSON><PERSON><PERSON> nhật nhà cung cấp thất bại"}, {"errorStatus": 500, "errorCode": "VDR04", "errorMessage": "<PERSON><PERSON><PERSON> nhà cung cấp thất bại"}, {"errorStatus": 404, "errorCode": "VDR05", "errorMessage": "Logo nhà cung cấp không tồn tại"}, {"errorStatus": 400, "errorCode": "VDR06", "errorMessage": "<PERSON><PERSON> liệu nhà cung cấp không hợp lệ"}, {"errorStatus": 404, "errorCode": "PRS01", "errorMessage": "<PERSON><PERSON> trình không tồn tại"}, {"errorStatus": 500, "errorCode": "PRS02", "errorMessage": "<PERSON><PERSON><PERSON> quy trình thất bại"}, {"errorStatus": 500, "errorCode": "PRS03", "errorMessage": "<PERSON><PERSON><PERSON> nhật quy trình thất bại"}, {"errorStatus": 404, "errorCode": "IT01", "errorMessage": "<PERSON><PERSON><PERSON> không tồn tại"}, {"errorStatus": 500, "errorCode": "IT02", "errorMessage": "<PERSON><PERSON><PERSON> mục thất bại"}, {"errorStatus": 500, "errorCode": "IT03", "errorMessage": "<PERSON><PERSON><PERSON> nh<PERSON>t mục thất bại"}, {"errorStatus": 500, "errorCode": "IT04", "errorMessage": "<PERSON><PERSON><PERSON> mục thất bại"}, {"errorStatus": 404, "errorCode": "IT05", "errorMessage": "<PERSON><PERSON><PERSON> minh h<PERSON>a mục không tồn tại"}, {"errorStatus": 404, "errorCode": "ITP01", "errorMessage": "<PERSON><PERSON><PERSON> m<PERSON>c không tồn tại"}, {"errorStatus": 500, "errorCode": "ITP02", "errorMessage": "<PERSON><PERSON><PERSON> gi<PERSON> mục thất bại"}, {"errorStatus": 500, "errorCode": "ITP03", "errorMessage": "<PERSON><PERSON><PERSON> nhật gi<PERSON> mục thất bại"}, {"errorStatus": 404, "errorCode": "IPC01", "errorMessage": "Chi phí đầu vào không tồn tại"}, {"errorStatus": 500, "errorCode": "IPC02", "errorMessage": "Tạo chi phí đầu vào thất bại"}, {"errorStatus": 500, "errorCode": "IPC03", "errorMessage": "<PERSON><PERSON><PERSON> nhật chi phí đầu vào thất bại"}, {"errorStatus": 500, "errorCode": "IPC04", "errorMessage": "<PERSON><PERSON><PERSON> chi phí đầu vào thất bại"}, {"errorStatus": 404, "errorCode": "ET01", "errorMessage": "<PERSON><PERSON><PERSON> mục không tồn tại"}, {"errorStatus": 500, "errorCode": "ET02", "errorMessage": "<PERSON><PERSON><PERSON> lo<PERSON>i mục thất bại"}, {"errorStatus": 500, "errorCode": "ET03", "errorMessage": "<PERSON><PERSON><PERSON> nh<PERSON>t lo<PERSON>i mục thất bại"}, {"errorStatus": 500, "errorCode": "ET04", "errorMessage": "<PERSON><PERSON><PERSON> lo<PERSON>i mục thất bại"}, {"errorStatus": 404, "errorCode": "MFTR01", "errorMessage": "<PERSON><PERSON>à sản xuất không tồn tại"}, {"errorStatus": 500, "errorCode": "MFTR02", "errorMessage": "<PERSON><PERSON><PERSON> nhà sản xuất thất bại"}, {"errorStatus": 500, "errorCode": "MFTR03", "errorMessage": "<PERSON><PERSON><PERSON> nhật nhà sản xuất thất bại"}, {"errorStatus": 500, "errorCode": "MFTR04", "errorMessage": "<PERSON><PERSON><PERSON> nh<PERSON> sản xuất thất bại"}, {"errorStatus": 404, "errorCode": "MFTR05", "errorMessage": "Logo nhà sản xuất không tồn tại"}, {"errorStatus": 404, "errorCode": "IPCI01", "errorMessage": "<PERSON><PERSON><PERSON> chi phí đầu vào không tồn tại"}, {"errorStatus": 500, "errorCode": "IPCI02", "errorMessage": "<PERSON><PERSON><PERSON> mục chi phí đầu vào thất bại"}, {"errorStatus": 500, "errorCode": "IPCI03", "errorMessage": "<PERSON><PERSON><PERSON> nhật mục chi phí đầu vào thất bại"}, {"errorStatus": 500, "errorCode": "IPCI04", "errorMessage": "<PERSON><PERSON><PERSON> mục chi phí đầu vào thất bại"}, {"errorStatus": 404, "errorCode": "PMT01", "errorMessage": "Lo<PERSON><PERSON> thanh toán không tồn tại"}, {"errorStatus": 500, "errorCode": "PMT02", "errorMessage": "<PERSON><PERSON><PERSON> lo<PERSON>i thanh toán thất bại"}, {"errorStatus": 500, "errorCode": "PMT03", "errorMessage": "<PERSON><PERSON><PERSON> nh<PERSON>t lo<PERSON>i thanh toán thất bại"}, {"errorStatus": 500, "errorCode": "PMT04", "errorMessage": "<PERSON><PERSON><PERSON> lo<PERSON>i thanh toán thất bại"}, {"errorStatus": 409, "errorCode": "AC01", "errorMessage": "<PERSON><PERSON><PERSON> khoản đã tồn tại"}, {"errorStatus": 404, "errorCode": "AC02", "errorMessage": "<PERSON><PERSON><PERSON> k<PERSON>n không tồn tại"}, {"errorStatus": 500, "errorCode": "AC03", "errorMessage": "<PERSON><PERSON><PERSON> tài k<PERSON>n thất bại"}, {"errorStatus": 500, "errorCode": "AC04", "errorMessage": "<PERSON><PERSON><PERSON> nhật tài k<PERSON>n thất bại"}, {"errorStatus": 500, "errorCode": "AC05", "errorMessage": "<PERSON><PERSON><PERSON> tài k<PERSON>n thất bại"}, {"errorStatus": 500, "errorCode": "AC06", "errorMessage": "<PERSON><PERSON><PERSON><PERSON> tài kho<PERSON>n thất bại"}, {"errorStatus": 500, "errorCode": "AC07", "errorMessage": "Mở khóa tài khoản thất bại"}, {"errorStatus": 400, "errorCode": "AC08", "errorMessage": "Email hoặc ID đăng nhập đã tồn tại"}, {"errorStatus": 404, "errorCode": "USI01", "errorMessage": "Thông tin người dùng không tồn tại"}, {"errorStatus": 409, "errorCode": "USI02", "errorMessage": "Thông tin người dùng đã tồn tại"}, {"errorStatus": 500, "errorCode": "USI03", "errorMessage": "<PERSON><PERSON><PERSON> thông tin người dùng thất bại"}, {"errorStatus": 500, "errorCode": "USI04", "errorMessage": "<PERSON><PERSON><PERSON> nhật thông tin người dùng thất bại"}, {"errorStatus": 500, "errorCode": "USI05", "errorMessage": "<PERSON><PERSON><PERSON> thông tin người dùng thất bại"}, {"errorStatus": 404, "errorCode": "USI06", "errorMessage": "Avatar thông tin người dùng chưa được đặt"}, {"errorStatus": 500, "errorCode": "USI07", "errorMessage": "<PERSON><PERSON><PERSON> nhật avatar thông tin người dùng thất bại"}, {"errorStatus": 500, "errorCode": "USI08", "errorMessage": "Xóa avatar thông tin người dùng thất bại"}, {"errorStatus": 404, "errorCode": "EMP01", "errorMessage": "<PERSON>hân viên không tồn tại"}, {"errorStatus": 409, "errorCode": "EMP02", "errorMessage": "<PERSON>hân viên đã tồn tại"}, {"errorStatus": 500, "errorCode": "EMP03", "errorMessage": "<PERSON><PERSON><PERSON> nhân viên thất bại"}, {"errorStatus": 500, "errorCode": "EMP04", "errorMessage": "<PERSON><PERSON><PERSON> nhật nhân viên thất bại"}, {"errorStatus": 500, "errorCode": "EMP05", "errorMessage": "<PERSON><PERSON><PERSON> nhân viên thất bại"}, {"errorStatus": 500, "errorCode": "EMP06", "errorMessage": "<PERSON><PERSON><PERSON> nhân viên thất bại"}, {"errorStatus": 404, "errorCode": "EMP07", "errorMessage": "<PERSON>ời mời nhân viên không tồn tại"}, {"errorStatus": 500, "errorCode": "EMP08", "errorMessage": "<PERSON><PERSON><PERSON> lời mời nhân viên thất bại"}, {"errorStatus": 500, "errorCode": "EMP09", "errorMessage": "<PERSON><PERSON><PERSON> nhận lời mời nhân viên thất bại"}, {"errorStatus": 500, "errorCode": "EMP10", "errorMessage": "Từ chối lời mời nhân viên thất bại"}, {"errorStatus": 409, "errorCode": "EMP11", "errorMessage": "<PERSON>ời mời nhân viên đã tồn tại"}, {"errorStatus": 500, "errorCode": "EMP12", "errorMessage": "<PERSON><PERSON><PERSON> kết tài khoản với tổ chức thất bại"}, {"errorStatus": 500, "errorCode": "FIL01", "errorMessage": "<PERSON><PERSON><PERSON> lên tệp thất bại"}, {"errorStatus": 500, "errorCode": "FIL02", "errorMessage": "<PERSON><PERSON><PERSON> xuống tệp thất bại"}, {"errorStatus": 500, "errorCode": "FIL03", "errorMessage": "<PERSON><PERSON><PERSON> t<PERSON>p thất b<PERSON>i"}, {"errorStatus": 404, "errorCode": "FIL04", "errorMessage": "Metadata t<PERSON><PERSON> không tồn tại"}, {"errorStatus": 500, "errorCode": "FIL05", "errorMessage": "Tạo metadata tệp thất bại"}, {"errorStatus": 500, "errorCode": "FIL06", "errorMessage": "<PERSON><PERSON><PERSON> nhật metadata tệp thất bại"}, {"errorStatus": 404, "errorCode": "WOS01", "errorMessage": "<PERSON>a làm vi<PERSON><PERSON> không tồn tại"}, {"errorStatus": 500, "errorCode": "WOS02", "errorMessage": "Tạo ca làm vi<PERSON>c thất bại"}, {"errorStatus": 500, "errorCode": "WOS03", "errorMessage": "<PERSON><PERSON><PERSON> nhật ca làm việc thất bại"}, {"errorStatus": 500, "errorCode": "WOS04", "errorMessage": "Xóa ca làm vi<PERSON>c thất bại"}, {"errorStatus": 500, "errorCode": "WOS05", "errorMessage": "<PERSON><PERSON> ca làm vi<PERSON><PERSON> thất bại"}, {"errorStatus": 400, "errorCode": "WOS06", "errorMessage": "<PERSON>h<PERSON><PERSON> gian nghỉ ca làm việc không hợp lệ"}, {"errorStatus": 400, "errorCode": "WOS07", "errorMessage": "<PERSON><PERSON><PERSON><PERSON> có ca làm việc đư<PERSON><PERSON> gán cho dự án"}, {"errorStatus": 404, "errorCode": "EVC01", "errorMessage": "<PERSON><PERSON> kiện không tồn tại"}, {"errorStatus": 500, "errorCode": "EVC02", "errorMessage": "<PERSON><PERSON><PERSON> sự kiện thất bại"}, {"errorStatus": 500, "errorCode": "EVC03", "errorMessage": "<PERSON><PERSON><PERSON> nhật sự kiện thất bại"}, {"errorStatus": 500, "errorCode": "EVC04", "errorMessage": "<PERSON><PERSON><PERSON> sự kiện thất bại"}, {"errorStatus": 400, "errorCode": "EVC05", "errorMessage": "<PERSON><PERSON><PERSON> c<PERSON>a sự kiện không hợp lệ"}, {"errorStatus": 409, "errorCode": "EVC06", "errorMessage": "Sự kiện đã tồn tại"}, {"errorStatus": 404, "errorCode": "UNT01", "errorMessage": "Đơn vị không tồn tại"}, {"errorStatus": 500, "errorCode": "UNT02", "errorMessage": "<PERSON><PERSON><PERSON> đơn vị thất bại"}, {"errorStatus": 500, "errorCode": "UNT03", "errorMessage": "<PERSON><PERSON><PERSON> nhật đơn vị thất bại"}, {"errorStatus": 500, "errorCode": "UNT04", "errorMessage": "<PERSON><PERSON><PERSON> đơn vị thất bại"}, {"errorStatus": 404, "errorCode": "OS01", "errorMessage": "<PERSON><PERSON><PERSON> ngoài không tồn tại"}, {"errorStatus": 500, "errorCode": "OS02", "errorMessage": "<PERSON><PERSON><PERSON> thuê ngoài thất bại"}, {"errorStatus": 500, "errorCode": "OS03", "errorMessage": "<PERSON><PERSON><PERSON> nhật thuê ngoài thất bại"}, {"errorStatus": 500, "errorCode": "OS04", "errorMessage": "<PERSON><PERSON><PERSON> thuê ngoài thất bại"}, {"errorStatus": 404, "errorCode": "OS05", "errorMessage": "<PERSON><PERSON><PERSON> thuê ngoài không tồn tại"}, {"errorStatus": 500, "errorCode": "OS06", "errorMessage": "Tạo giá thuê ngoài thất bại"}, {"errorStatus": 500, "errorCode": "OS07", "errorMessage": "<PERSON><PERSON><PERSON> nhật giá thuê ngoài thất bại"}, {"errorStatus": 404, "errorCode": "OS08", "errorMessage": "Logo thuê ngoài không tồn tại"}, {"errorStatus": 404, "errorCode": "PRT01", "errorMessage": "Loại dự án không tồn tại"}, {"errorStatus": 500, "errorCode": "PRT02", "errorMessage": "<PERSON><PERSON>o loại dự án thất bại"}, {"errorStatus": 500, "errorCode": "PRT03", "errorMessage": "<PERSON><PERSON><PERSON> nhật loại dự án thất bại"}, {"errorStatus": 404, "errorCode": "CON01", "errorMessage": "<PERSON><PERSON>ng trình không tồn tại"}, {"errorStatus": 500, "errorCode": "CON02", "errorMessage": "<PERSON><PERSON><PERSON> công trình thất bại"}, {"errorStatus": 500, "errorCode": "CON03", "errorMessage": "<PERSON><PERSON><PERSON> nhật công trình thất bại"}, {"errorStatus": 500, "errorCode": "CON04", "errorMessage": "<PERSON><PERSON><PERSON> công trình thất bại"}, {"errorStatus": 409, "errorCode": "CON05", "errorMessage": "<PERSON><PERSON>ng trình phụ đã tồn tại"}, {"errorStatus": 404, "errorCode": "CUS01", "errorMessage": "<PERSON><PERSON><PERSON><PERSON> hàng không tồn tại"}, {"errorStatus": 500, "errorCode": "CUS02", "errorMessage": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng thất bại"}, {"errorStatus": 500, "errorCode": "CUS03", "errorMessage": "<PERSON><PERSON><PERSON> nh<PERSON>t khách hàng thất bại"}, {"errorStatus": 500, "errorCode": "CUS04", "errorMessage": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng thất bại"}, {"errorStatus": 404, "errorCode": "CT01", "errorMessage": "<PERSON><PERSON><PERSON> khách hàng không tồn tại"}, {"errorStatus": 404, "errorCode": "CONC01", "errorMessage": "Chi phí công trình không tồn tại"}, {"errorStatus": 500, "errorCode": "CONC02", "errorMessage": "Tạo chi phí công trình thất bại"}, {"errorStatus": 500, "errorCode": "CONC03", "errorMessage": "<PERSON><PERSON><PERSON> nhật chi phí công trình thất bại"}, {"errorStatus": 500, "errorCode": "CONC04", "errorMessage": "<PERSON>ó<PERSON> chi phí công trình thất bại"}, {"errorStatus": 404, "errorCode": "AUD01", "errorMessage": "<PERSON><PERSON><PERSON><PERSON> có lịch sử thay đổi"}]