using System.Globalization;
using Kantoku.Api.Middlewares.Locale;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Helpers;

namespace Kantoku.Api.Services;

public interface IBaseService<TService>
{
    Guid GetCurrentAccountUid();
    Guid GetCurrentOrgUid();
    Guid GetCurrentEmployeeUid();
    bool GetCurrentAccountGuid(out Guid accountUid);
    bool GetCurrentOrgGuid(out Guid orgUid);
    bool GetCurrentEmployeeGuid(out Guid employeeUid);
    CultureInfo GetCurrentCulture();
}

public abstract class BaseService<TService> : IBaseService<TService>
{
    protected readonly Serilog.ILogger logger;
    protected readonly IHttpContextAccessor httpContextAccessor;
    public BaseService(
    Serilog.ILogger logger, IHttpContextAccessor httpContextAccessor)
    {
        this.logger = logger.ForContext<TService>();
        this.httpContextAccessor = httpContextAccessor;
    }

    public Guid GetCurrentAccountUid()
    {
        try
        {
            var accountUidClaim = httpContextAccessor.HttpContext?.Items[ClaimConstant.ACCOUNT_UID];
            return accountUidClaim is not null && Guid.TryParse(accountUidClaim.ToString(), out Guid accountUid) ? accountUid : Guid.Empty;
        }
        catch (System.Exception)
        {
            logger.Error("Error occurred while getting current account uid");
            return Guid.Empty;
        }
    }

    public Guid GetCurrentOrgUid()
    {
        try
        {
            var orgUidClaim = httpContextAccessor.HttpContext?.Items[ClaimConstant.ORG_UID];
            return orgUidClaim is not null && Guid.TryParse(orgUidClaim.ToString(), out Guid orgUid) ? orgUid : Guid.Empty;
        }
        catch (System.Exception)
        {
            logger.Error("Error occurred while getting current org uid");
            return Guid.Empty;
        }
    }

    public Guid GetCurrentEmployeeUid()
    {
        try
        {
            var employeeUidClaim = httpContextAccessor.HttpContext?.Items[ClaimConstant.EMPLOYEE_UID];
            return employeeUidClaim is not null && Guid.TryParse(employeeUidClaim.ToString(), out Guid employeeUid) ? employeeUid : Guid.Empty;
        }
        catch (System.Exception)
        {
            logger.Error("Error occurred while getting current employee uid");
            return Guid.Empty;
        }
    }

    public CultureInfo GetCurrentCulture()
    {
        try
        {
            return httpContextAccessor.HttpContext?.Items[LocaleMiddleware.CultureKey] as CultureInfo ?? CultureInfo.InvariantCulture;
        }
        catch (System.Exception)
        {
            logger.Error("Error occurred while getting current culture");
            return CultureInfo.InvariantCulture;
        }
    }

    public string GetCurrentLanguageCode()
    {
        return LocaleHelper.GetLanguageCode(GetCurrentCulture());
    }

    public bool GetCurrentAccountGuid(out Guid accountUid)
    {
        var accountUidClaim = httpContextAccessor.HttpContext?.Items[ClaimConstant.ACCOUNT_UID];
        if (accountUidClaim is null)
        {
            logger.Error("Current account uid claim is null");
            accountUid = Guid.Empty;
            return false;
        }
        if (Guid.TryParse(accountUidClaim.ToString(), out accountUid))
        {
            return true;
        }
        logger.Error("Current account uid claim is not a valid guid");
        return false;
    }

    public bool GetCurrentOrgGuid(out Guid orgUid)
    {
        var orgUidClaim = httpContextAccessor.HttpContext?.Items[ClaimConstant.ORG_UID];
        if (orgUidClaim is null)
        {
            logger.Error("Current org uid claim is null");
            orgUid = Guid.Empty;
            return false;
        }
        if (Guid.TryParse(orgUidClaim.ToString(), out orgUid))
        {
            return true;
        }
        logger.Error("Current org uid claim is not a valid guid");
        return false;
    }

    public bool GetCurrentEmployeeGuid(out Guid employeeUid)
    {
        var employeeUidClaim = httpContextAccessor.HttpContext?.Items[ClaimConstant.EMPLOYEE_UID];
        if (employeeUidClaim is null)
        {
            logger.Error("Current employee uid claim is null");
            employeeUid = Guid.Empty;
            return false;
        }
        if (Guid.TryParse(employeeUidClaim.ToString(), out employeeUid))
        {
            return true;
        }
        logger.Error("Current employee uid claim is not a valid guid");
        return false;
    }
}
