using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.FunctionAccessibility.Request;

/// <summary>
/// Request DTO for updating menu privileges
/// </summary>
public class UpdateMenuPrivilegeRequestDto
{
    /// <summary>
    /// (*) The unique identifier of the function
    /// </summary>
    [Required]
    public required Guid FunctionId { get; set; }

    /// <summary>
    /// (*) The unique identifier of the role
    /// </summary>
    [Required]
    public required Guid RoleId { get; set; }

    /// <summary>
    /// Flag indicating whether the role has read permission
    /// </summary>
    public bool? CanRead { get; set; }

    /// <summary>
    /// Flag indicating whether the role has create permission
    /// </summary>
    public bool? CanCreate { get; set; }

    /// <summary>
    /// Flag indicating whether the role has update permission
    /// </summary>
    public bool? CanUpdate { get; set; }

    /// <summary>
    /// Flag indicating whether the role has delete permission
    /// </summary>
    public bool? CanDelete { get; set; }
}
