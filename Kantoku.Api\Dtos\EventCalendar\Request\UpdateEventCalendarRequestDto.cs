using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.EventCalendar.Request;

public class UpdateEventCalendarRequestDto
{
    /// <summary>
    /// Event name
    /// The name of the event
    /// </summary>
    public string? EventName { get; set; }

    /// <summary>
    /// Event start date (YYYY-MM-DD)
    /// The date that event starts
    /// </summary>
    [DateTimeValidator(typeof(DateOnly))]
    public string? EventStartDate { get; set; }

    /// <summary>
    /// Event end date (YYYY-MM-DD)
    /// The date that event ends
    /// </summary>
    [DateTimeValidator(typeof(DateOnly))]
    public string? EventEndDate { get; set; }

    /// <summary>
    /// Event start time (HH:MM:SS)
    /// The time that event starts
    /// </summary>
    [DateTimeValidator(typeof(TimeOnly))]
    public string? EventStartTime { get; set; }

    /// <summary>
    /// Event end time (HH:MM:SS)
    /// The time that event ends
    /// </summary>
    [DateTimeValidator(typeof(TimeOnly))]
    public string? EventEndTime { get; set; }

    /// <summary>
    /// Indicates if the event is recurring
    /// true: recurring, false: not recurring
    /// </summary>
    public bool IsRecurring { get; set; }

    /// <summary>
    /// Recurring start date (YYYY-MM-DD)
    /// The date that recurring starts
    /// </summary>
    [DateTimeValidator(typeof(DateOnly))]
    public string? RecurringFrom { get; set; }

    /// <summary>
    /// Recurring end date (YYYY-MM-DD)
    /// The date that recurring ends
    /// </summary>
    [DateTimeValidator(typeof(DateOnly))]
    public string? RecurringTo { get; set; }

    /// <summary>
    /// The type of recurrence for the event
    /// Recurring type: DAILY, WEEKLY, MONTHLY, YEARLY 
    /// </summary>
    public string? RecurringType { get; set; }

    /// <summary>
    /// Days of the week when the event recurs (only if RecurringType is WEEKLY)
    /// For example: [1, 2, 3, 4, 5, 6, 7] as day of week 
    /// 1: Monday, 2: Tuesday, 3: Wednesday, 4: Thursday, 5: Friday, 6: Saturday, 7: Sunday
    /// </summary>
    public ICollection<int>? RecurringDay { get; set; }

    /// <summary>
    /// Weeks of the month when the event recurs (only if RecurringType is MONTHLY)
    /// For example: [1, 2, 3, 4] as week of month
    /// 1: first week, 2: second week, 3: third week, 4: fourth week
    /// </summary>
    public ICollection<int>? RecurringWeek { get; set; }

    /// <summary>
    /// Months of the year when the event recurs (only if RecurringType is MONTHLY)
    /// For example: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12] as month of year
    /// 1: January, 2: February, 3: March, 4: April, 5: May, 6: June, 7: July, 8: August, 9: September, 10: October, 11: November, 12: December
    /// </summary>
    public ICollection<int>? RecurringMonth { get; set; }

    /// <summary>
    /// Indicates if the event is a day off
    /// true: day off, false: not day off
    /// </summary>
    public bool? IsDayOff { get; set; }

    /// <summary>
    /// Additional description or notes about the event
    /// </summary>
    public string? Description { get; set; }
}
