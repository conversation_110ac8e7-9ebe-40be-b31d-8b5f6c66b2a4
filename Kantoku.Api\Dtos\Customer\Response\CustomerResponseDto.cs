using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Base.Response;

namespace Kantoku.Api.Dtos.Customer.Response;

public class CustomerResponseDto : BaseResponseDto
{
    /// <summary>
    /// Customer UID
    /// </summary>
    public string? CustomerId { get; set; }

    /// <summary>
    /// Customer code
    /// </summary>
    public string? CustomerCode { get; set; }

    /// <summary>
    /// Customer name
    /// </summary>
    public string? CustomerName { get; set; }

    /// <summary>
    /// Customer sub name
    /// </summary>
    public string? CustomerSubName { get; set; }

    /// <summary>
    /// Customer type code
    /// </summary>
    public string? CustomerTypeCode { get; set; }

    /// <summary>
    /// Customer type name
    /// </summary>
    public string? CustomerTypeName { get; set; }

    /// <summary>
    /// Corporate number
    /// </summary>
    public string? CorporateNumber { get; set; }

    /// <summary>
    /// Address
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Phone number
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Email
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// Contact information
    /// </summary>
    public ContactPerson? ContactPerson { get; set; }

    /// <summary>
    /// Description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Logo URL
    /// </summary>
    public string? LogoUrl { get; set; }
}