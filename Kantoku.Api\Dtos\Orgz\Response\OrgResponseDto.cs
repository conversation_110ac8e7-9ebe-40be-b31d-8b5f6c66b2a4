using Kantoku.Api.Dtos.Base.Response;

namespace Kantoku.Api.Dtos.Orgz.Response;

public class OrgResponseDto : BaseResponseDto
{
    public string? OrgId { get; set; }

    public string? OrgCode { get; set; }

    public string? OrgName { get; set; }

    public string? OrgSubName { get; set; }

    public string? PostalCode { get; set; }

    public string? Address { get; set; }

    public string? PhoneNumber { get; set; }

    public string? Email { get; set; }

    public string? Fax { get; set; }

    public string? Website { get; set; }

    public string? RegistrationNumber { get; set; }

    public string? RegistrationDate { get; set; }

    public bool? RegistrationLicenseType { get; set; }

    public string? LegalOrgNumber { get; set; }

    public string? LegalTaxNumber { get; set; }

    public string? LegalRepresentative { get; set; }

    public string? Description { get; set; }

    public string? LogoUrl { get; set; }
}