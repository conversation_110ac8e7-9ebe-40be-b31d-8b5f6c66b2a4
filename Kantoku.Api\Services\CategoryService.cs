﻿using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Helpers;
using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Dtos.Category.Response;
using Kantoku.Api.Dtos.Category.Request;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public interface ICategoryService
{
    Task<ResultDto<CategoryResponseDto>> GetById(Guid categoryId);
    Task<ResultDto<CategoriesResponseDto>> GetByFilter(CategoryFilter filter);
    Task<ResultDto<CategoryResponseDto>> Create(CreateCategoryRequestDto requestDto);
    Task<ResultDto<CategoryResponseDto>> Update(Guid categoryId, UpdateCategoryRequestDto requestDto);
    Task<ResultDto<bool>> Delete(Guid categoryId);
}

[Service(ServiceLifetime.Scoped)]
public class CategoryService : BaseService<CategoryService>, ICategoryService
{
    private readonly ICategoryRepository categoryRepository;
    private readonly CategoryQueryableOptions options = new()
    {
        IncludedParent = true,
        IncludedChildren = true,
        IncludedItems = true
    };

    public CategoryService(
        ICategoryRepository categoryRepository,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor)
        : base(logger, httpContextAccessor)
    {
        this.categoryRepository = categoryRepository;
    }

    public async Task<ResultDto<CategoriesResponseDto>> GetByFilter(CategoryFilter filter)
    {
        var (categories, total) = await categoryRepository.GetByFilter(filter, options);
        if (categories is null || !categories.Any() || total == 0)
        {
            logger.Error("Categories not found");
            return new ErrorResultDto<CategoriesResponseDto>(ResponseCodeConstant.CATEGORY_NOT_EXIST);
        }
        var languageCode = GetCurrentLanguageCode();
        var res = categories.ToCategoriesResponseDto(filter.PageNum, filter.PageSize, total, languageCode);
        return new SuccessResultDto<CategoriesResponseDto>(res);
    }

    public async Task<ResultDto<CategoryResponseDto>> GetById(Guid categoryId)
    {
        var category = await categoryRepository.GetById(categoryId, options);
        if (category is null)
        {
            logger.Error("Category not found");
            return new ErrorResultDto<CategoryResponseDto>(ResponseCodeConstant.CATEGORY_NOT_EXIST);
        }
        var languageCode = GetCurrentLanguageCode();
        var res = category.ToCategoryResponseDto(languageCode);
        return new SuccessResultDto<CategoryResponseDto>(res);
    }

    public async Task<ResultDto<CategoryResponseDto>> Create(CreateCategoryRequestDto requestDto)
    {

        var parentCategory = await categoryRepository.GetById(requestDto.ParentId, options);
        if (parentCategory is null)
        {
            logger.Error("Parent category not found");
            return new ErrorResultDto<CategoryResponseDto>(ResponseCodeConstant.CATEGORY_NOT_EXIST);
        }
        if (!GetCurrentOrgGuid(out var orgUid))
        {
            logger.Error("Organization not found");
            return new ErrorResultDto<CategoryResponseDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        var parentCategoryCode = parentCategory.IsDefault ? parentCategory.CategoryCode : parentCategory.RootCategoryCode;
        var newCategory = requestDto.ToEntity(orgUid, parentCategoryCode);
        if (newCategory is null)
        {
            logger.Error("Category create failed");
            return new ErrorResultDto<CategoryResponseDto>(ResponseCodeConstant.CATEGORY_CREATE_FAILED);
        }
        var createdCategory = await categoryRepository.Create(newCategory, options);
        if (createdCategory is null)
        {
            logger.Error("Category create failed");
            return new ErrorResultDto<CategoryResponseDto>(ResponseCodeConstant.CATEGORY_CREATE_FAILED);
        }
        var languageCode = GetCurrentLanguageCode();
        var res = createdCategory.ToCategoryResponseDto(languageCode);
        return new SuccessResultDto<CategoryResponseDto>(res);
    }

    public async Task<ResultDto<CategoryResponseDto>> Update(Guid categoryId, UpdateCategoryRequestDto requestDto)
    {
        var existCategory = await categoryRepository.GetById(categoryId, options);
        if (existCategory is null)
        {
            logger.Error("Category not found");
            return new ErrorResultDto<CategoryResponseDto>(ResponseCodeConstant.CATEGORY_NOT_EXIST);
        }
        existCategory.UpdateFromDto(requestDto);
        if (requestDto.ParentId is not null)
        {
            var parentCategory = await categoryRepository.GetById(requestDto.ParentId.Value, options);
            if (parentCategory is not null)
            {
                existCategory.ParentUid = parentCategory.CategoryUid;
                existCategory.RootCategoryCode = parentCategory.IsDefault ? parentCategory.CategoryCode : parentCategory.RootCategoryCode;
            }
        }

        var updatedCategory = await categoryRepository.Update(existCategory, options);
        if (updatedCategory is null)
        {
            logger.Error("Category update failed");
            return new ErrorResultDto<CategoryResponseDto>(ResponseCodeConstant.CATEGORY_UPDATE_FAILED);
        }
        var languageCode = GetCurrentLanguageCode();
        var res = updatedCategory.ToCategoryResponseDto(languageCode);
        return new SuccessResultDto<CategoryResponseDto>(res);
    }

    public async Task<ResultDto<bool>> Delete(Guid categoryId)
    {
        var existCategory = await categoryRepository.GetById(categoryId, new CategoryQueryableOptions());
        if (existCategory is null)
        {
            logger.Error("Category not found");
            return new ErrorResultDto<bool>(ResponseCodeConstant.CATEGORY_NOT_EXIST);
        }

        existCategory.IsDeleted = true;
        var isDeleted = await categoryRepository.Update(existCategory);
        if (!isDeleted)
        {
            logger.Error("Category delete failed");
            return new ErrorResultDto<bool>(ResponseCodeConstant.CATEGORY_DELETE_FAILED);
        }
        return new SuccessResultDto<bool>(true);
    }
}
