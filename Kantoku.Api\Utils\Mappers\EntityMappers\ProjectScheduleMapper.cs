using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.Schedule;
using Kantoku.Api.Dtos.Schedule.Request;
using Kantoku.Api.Dtos.Schedule.Response;
using Kantoku.Api.Utils.Helpers;

namespace Kantoku.Api.Utils.Mappers.EntityMappers;

public static class ProjectScheduleMapper
{
    #region Entity to DTO mappings

    /// <summary>
    /// Maps a ProjectSchedule entity to a ProjectScheduleResponseDto
    /// </summary>
    /// <param name="schedule">The ProjectSchedule entity to map</param>
    /// <param name="employeeShifts">The list of EmployeeShift entities to map</param>
    /// <param name="outSourceShifts">The list of OutSourceShift entities to map</param>
    /// <returns>A ProjectScheduleResponseDto containing the mapped project schedule</returns>
    public static ProjectScheduleResponseDto ToProjectScheduleResponseDto(
        this ProjectSchedule schedule,
        IEnumerable<EmployeeShift>? employeeShifts,
        IEnumerable<OutSourceShift>? outSourceShifts
    )
    {
        if (schedule == null)
            return new ProjectScheduleResponseDto();

        var actualWorkload = 0.0f;
        if (employeeShifts is not null && employeeShifts.Any())
        {
            actualWorkload = (float)Math.Round(employeeShifts.Sum(es => es.TotalScheduledWorkTime > 0 ? es.TotalWorkTime / es.TotalScheduledWorkTime : 0), 2);
        }
        if (outSourceShifts is not null && outSourceShifts.Any())
        {
            actualWorkload += (float)Math.Round(outSourceShifts.Sum(os => os.AssignedWorkload), 2);
        }

        return new ProjectScheduleResponseDto
        {
            ProjectScheduleId = schedule.ProjectScheduleUid.ToString(),
            ProjectId = schedule.ProjectUid.ToString(),
            WorkingDate = schedule.WorkingDate.ToString("yyyy-MM-dd"),
            PlannedWorkload = schedule.PlannedWorkload,
            EstimatedWorkload = schedule.PresignedWorkload,
            ActualWorkload = actualWorkload,
            EmployeeShifts = employeeShifts.ToScheduledEmployeeShiftResponseDto(schedule),
            OutSourceShifts = outSourceShifts.ToScheduledOutSourceShiftResponseDto(schedule),
        };
    }

    /// <summary>
    /// Maps a list of ProjectSchedule entities to a ProjectSchedulesResponseDto
    /// </summary>
    /// <param name="schedules"></param>
    /// <param name="projects"></param>
    /// <param name="dateFrom"></param>
    /// <param name="dateTo"></param>
    /// <param name="pageNum"></param>
    /// <param name="pageSize"></param>
    /// <returns></returns>
    public static ProjectSchedulesResponseDto ToProjectSchedulesResponseDto(
        this IEnumerable<ProjectSchedule> schedules,
        IEnumerable<Project> projects,
        DateOnly dateFrom,
        DateOnly dateTo,
        int pageNum,
        int pageSize
    )
    {
        var scheduleByProject = schedules
            .GroupBy(s => s.ProjectUid)
            .ToDictionary(g => g.Key, g => g.ToList());

        var projectDtos = new List<ProjectInfoDto>();
        foreach (var project in projects)
        {
            if (!scheduleByProject.TryGetValue(project.ProjectUid, out var projectSchedules))
            {
                projectDtos.Add(new ProjectInfoDto
                {
                    ProjectId = project.ProjectUid.ToString(),
                    ProjectCode = project.ProjectCode,
                    ProjectName = project.ProjectName,
                    Schedules = [],
                    EmployeeShifts = [],
                    OutsourceShifts = [],
                });
                continue;
            }

            var employeeShifts = new List<ScheduledEmployeeShiftResponseDto>();
            var outsourceShifts = new List<ScheduledOutSourceShiftResponseDto>();
            var scheduleInfos = new List<ProjectScheduleInfoDto>();

            for (var date = dateFrom; date <= dateTo; date = date.AddDays(1))
            {
                var dateStr = date.ToString("yyyy-MM-dd");

                var daySchedule = projectSchedules?
                    .Where(s => s.IsDeleted == false)
                    .FirstOrDefault(s => s.WorkingDate == date);

                if (daySchedule is not null)
                {
                    var actualWorkload = 0.0f;

                    var scheduledEmployeeShifts = daySchedule.EmployeeShifts
                        .Where(es => es.IsDeleted == false)
                        .Where(es => es.TotalScheduledWorkTime > 0);

                    if (scheduledEmployeeShifts is not null && scheduledEmployeeShifts.Any())
                    {
                        actualWorkload = (float)Math.Round(scheduledEmployeeShifts
                            .Sum(es => es.TotalWorkTime / es.TotalScheduledWorkTime), 2);
                    }

                    var scheduledOutsourceShifts = daySchedule.OutSourceShifts
                        .Where(oss => oss.IsDeleted == false);

                    if (scheduledOutsourceShifts is not null && scheduledOutsourceShifts.Any())
                    {
                        actualWorkload += (float)Math.Round(scheduledOutsourceShifts
                            .Sum(os => os.AssignedWorkload), 2);
                    }

                    scheduleInfos.Add(new ProjectScheduleInfoDto
                    {
                        ProjectScheduleId = daySchedule.ProjectScheduleUid.ToString(),
                        WorkingDate = dateStr,
                        PlannedWorkload = (float)Math.Round(daySchedule.PlannedWorkload, 2),
                        EstimatedWorkload = (float)Math.Round(daySchedule.PresignedWorkload, 2),
                        ActualWorkload = actualWorkload,
                    });

                    //calculate outsource shifts
                    var dayOutsourceShifts = daySchedule.OutSourceShifts
                        .Where(os => os.IsDeleted == false)
                        .Where(os => DateTimeHelper.ParseToLocalTime(os.ScheduledStartTime)?.ToString("yyyy-MM-dd") == dateStr);

                    if (dayOutsourceShifts is not null && dayOutsourceShifts.Any())
                    {
                        outsourceShifts.AddRange(dayOutsourceShifts.Select(dos => new ScheduledOutSourceShiftResponseDto
                        {
                            ProjectScheduleId = daySchedule.ProjectScheduleUid.ToString(),
                            OutSourceShiftId = dos.OutSourceShiftUid.ToString(),
                            OutSourceName = dos.OutSource?.OutSourceName,
                            OutSourceCode = dos.OutSource?.OutSourceCode,
                            WorkingDate = dateStr,
                            ScheduledStartTime = dos.ScheduledStartTime.ToString("HH:mm:ss"),
                            ScheduledEndTime = DateTimeHelper.AdjustBeyondTime(dos.ScheduledStartTime, dos.ScheduledEndTime),
                            AssignedWorkload = dos.AssignedWorkload,
                            WorkingRole = dos.Role
                        }));
                    }

                    //calculate employee shifts
                    var dayEmployeeShifts = daySchedule.EmployeeShifts
                        .Where(es => es.IsDeleted == false)
                        .Where(es => es.ScheduledStartTime is not null
                        && DateTimeHelper.ParseToLocalTime(es.ScheduledStartTime)?.ToString("yyyy-MM-dd") == dateStr);

                    if (dayEmployeeShifts is not null && dayEmployeeShifts.Any())
                    {
                        employeeShifts.AddRange(dayEmployeeShifts.Select(des => new ScheduledEmployeeShiftResponseDto
                        {
                            ProjectScheduleId = daySchedule.ProjectScheduleUid.ToString(),
                            EmployeeShiftId = des.EmployeeShiftUid.ToString(),
                            EmployeeName = des.Employee?.EmployeeName,
                            EmployeeCode = des.Employee?.EmployeeCode,
                            WorkingDate = DateTimeHelper.ParseToLocalTime(des.ScheduledStartTime)?.ToString("yyyy-MM-dd"),
                            ScheduledStartTime = DateTimeHelper.ParseToLocalTime(des.ScheduledStartTime)?.ToString("HH:mm:ss"),
                            ScheduledEndTime = DateTimeHelper.AdjustBeyondTime(
                                des.ScheduledStartTime,
                                des.ScheduledEndTime),
                            TotalScheduledWorkTime = des.TotalScheduledWorkTime,
                            AssignedWorkload = des.TotalScheduledWorkTime,
                            WorkingRole = des.AssignedRole
                        }));
                    }
                }
            }

            projectDtos.Add(new ProjectInfoDto
            {
                ProjectId = project.ProjectUid.ToString(),
                ProjectCode = project.ProjectCode,
                ProjectName = project.ProjectName,
                Schedules = [.. scheduleInfos.OrderBy(s => s.WorkingDate)],
                EmployeeShifts = [.. employeeShifts.OrderBy(s => s.WorkingDate)],
                OutsourceShifts = [.. outsourceShifts.OrderBy(s => s.WorkingDate)],
            });
        }

        return new ProjectSchedulesResponseDto
        {
            Projects = projectDtos,
            PageNum = pageNum,
            PageSize = pageSize,
            TotalRecords = scheduleByProject.Keys.Count
        };
    }

    /// <summary>
    /// Maps a list of EmployeeShift entities to a list of ScheduledEmployeeShiftResponseDto
    /// </summary>
    /// <param name="employeeShifts">The list of EmployeeShift entities to map</param>
    /// <param name="schedule">The ProjectSchedule entity to map</param>
    /// <returns>A list of ScheduledEmployeeShiftResponseDto containing the mapped employee shifts</returns>
    private static IEnumerable<ScheduledEmployeeShiftResponseDto> ToScheduledEmployeeShiftResponseDto(
        this IEnumerable<EmployeeShift>? employeeShifts,
        ProjectSchedule schedule)
    {
        if (employeeShifts == null || !employeeShifts.Any())
            return [];

        var result = employeeShifts
            .Where(es => es.IsDeleted == false)
            .Select(es => new ScheduledEmployeeShiftResponseDto
            {
                EmployeeShiftId = es.EmployeeShiftUid.ToString(),
                EmployeeName = es.Employee?.EmployeeName,
                EmployeeCode = es.Employee?.EmployeeCode,
                WorkingDate = schedule.WorkingDate.ToString("yyyy-MM-dd"),
                ScheduledStartTime = es.ScheduledStartTime?.ToString("HH:mm:ss"),
                ScheduledEndTime = DateTimeHelper.AdjustBeyondTime(es.ScheduledEndTime, es.ScheduledStartTime),
                TotalScheduledWorkTime = es.TotalScheduledWorkTime,
                AssignedWorkload = es.TotalWorkTime,
                WorkingRole = es.AssignedRole
            });

        return result;
    }

    /// <summary>
    /// Maps a list of OutSourceShift entities to a list of ScheduledOutSourceShiftResponseDto
    /// </summary>
    /// <param name="outSourceShifts">The list of OutSourceShift entities to map</param>
    /// <param name="schedule">The ProjectSchedule entity to map</param>
    /// <returns>A list of ScheduledOutSourceShiftResponseDto containing the mapped outsource shifts</returns>
    private static IEnumerable<ScheduledOutSourceShiftResponseDto> ToScheduledOutSourceShiftResponseDto(
        this IEnumerable<OutSourceShift>? outSourceShifts,
        ProjectSchedule schedule)
    {
        if (outSourceShifts == null || !outSourceShifts.Any())
            return [];

        var result = outSourceShifts
            .Where(os => os.IsDeleted == false)
            .Select(os => new ScheduledOutSourceShiftResponseDto
            {
                OutSourceShiftId = os.OutSourceShiftUid.ToString(),
                OutSourceName = os.OutSource?.OutSourceName,
                OutSourceCode = os.OutSource?.OutSourceCode,
                WorkingDate = schedule.WorkingDate.ToString("yyyy-MM-dd"),
                ScheduledStartTime = os.ScheduledStartTime.ToString("HH:mm:ss"),
                ScheduledEndTime = DateTimeHelper.AdjustBeyondTime(os.ScheduledEndTime, os.ScheduledStartTime),
                AssignedWorkload = os.AssignedWorkload,
                WorkingRole = os.Role
            });

        return result;
    }

    /// <summary>
    /// Maps a list of ProjectSchedule entities to a list of PrintableScheduleDto
    /// </summary>
    /// <param name="projectSchedules">The list of ProjectSchedule entities to map</param>
    /// <returns>A list of PrintableScheduleDto containing the mapped project schedules</returns>
    public static IEnumerable<PrintableScheduleDto> ToPrintableScheduleDto(this IEnumerable<ProjectSchedule> projectSchedules)
    {
        if (projectSchedules == null || !projectSchedules.Any())
            return [];

        var printableSchedules = new List<PrintableScheduleDto>();
        foreach (var projectSchedule in projectSchedules)
        {
            var employeeShifts = projectSchedule.EmployeeShifts
                .Where(es => es.IsDeleted == false)
                .DistinctBy(es => es.EmployeeUid);

            var outsourceShifts = projectSchedule.OutSourceShifts
                .Where(os => os.IsDeleted == false)
                .DistinctBy(os => os.OutSourceUid);

            var employeeSchedules = employeeShifts
                .Select(es => new ScheduleInProjectDto
                {
                    ProjectName = projectSchedule.Project.ProjectName,
                    Employees = [new ScheduledEmployeeDto
                        {
                            EmployeeName = es.Employee?.EmployeeName,
                            IsOutSource = false
                        }]
                }) ?? [];

            var outsourceSchedules = outsourceShifts
                .Select(os => new ScheduleInProjectDto
                {
                    ProjectName = projectSchedule.Project.ProjectName,
                    Employees = [new ScheduledEmployeeDto
                        {
                            EmployeeName = os.OutSource?.OutSourceName,
                            IsOutSource = true
                        }]
                }) ?? [];

            var schedule = new PrintableScheduleDto
            {
                WorkingDate = projectSchedule.WorkingDate,
                Schedules = [.. employeeSchedules.Concat(outsourceSchedules)]
            };
            printableSchedules.Add(schedule);
        }
        return printableSchedules;
    }

    #endregion

    #region DTO to Entity mappings

    /// <summary>
    /// Maps a CreateProjectScheduleRequestDto to a ProjectSchedule entity
    /// </summary>
    /// <param name="dto">The CreateProjectScheduleRequestDto to map</param>
    /// <param name="projectUid">The project uid</param>
    /// <returns>The mapped ProjectSchedule entity</returns>
    public static ProjectSchedule? ToEntity(this CreateProjectScheduleRequestDto dto, Guid projectUid)
    {
        if (dto == null)
            return null;

        var entity = new ProjectSchedule
        {
            ProjectScheduleUid = GuidHelper.GenerateUUIDv7(),
            ProjectUid = projectUid,
            WorkingDate = dto.WorkingDate,
            PlannedWorkload = dto.PlannedWorkload,
            PresignedWorkload = dto.PresignedWorkload,
            Description = dto.Description,
            IsDeleted = false
        };

        // Map employee shifts if exists
        if (dto.EmployeeShifts != null && dto.EmployeeShifts.Any())
        {
            entity.EmployeeShifts = dto.EmployeeShifts.Select(co => new EmployeeShift
            {
                EmployeeShiftUid = GuidHelper.GenerateUUIDv7(),
                ProjectUid = projectUid,
                EmployeeUid = co.EmployeeId,
                ProjectScheduleUid = entity.ProjectScheduleUid,
                ScheduledStartTime = new DateTime(
                    entity.WorkingDate.Year,
                    entity.WorkingDate.Month,
                    entity.WorkingDate.Day,
                    co.StartTime.Hour,
                    co.StartTime.Minute,
                    0),
                ScheduledEndTime = new DateTime(
                    entity.WorkingDate.Year,
                    entity.WorkingDate.Month,
                    entity.WorkingDate.Day,
                    co.EndTime.Hour,
                    co.EndTime.Minute,
                    0),
                TotalScheduledWorkTime = co.TotalScheduledWorkTime,
                AssignedRole = co.AssignedRole,
                IsDeleted = false
            }).ToList();
        }

        // Map outsource shifts if exists
        if (dto.OutSourceShifts != null && dto.OutSourceShifts.Any())
        {
            entity.OutSourceShifts = dto.OutSourceShifts.Select(os => new OutSourceShift
            {
                OutSourceShiftUid = GuidHelper.GenerateUUIDv7(),
                OutSourceUid = os.OutSourceId,
                ProjectScheduleUid = entity.ProjectScheduleUid,
                ScheduledStartTime = new DateTime(
                    entity.WorkingDate.Year,
                    entity.WorkingDate.Month,
                    entity.WorkingDate.Day,
                    os.StartTime.Hour,
                    os.StartTime.Minute,
                    0),
                ScheduledEndTime = new DateTime(
                    entity.WorkingDate.Year,
                    entity.WorkingDate.Month,
                    entity.WorkingDate.Day,
                    os.EndTime.Hour,
                    os.EndTime.Minute,
                    0),
                AssignedWorkload = os.AssignedWorkload,
                Role = os.AssignedRole,
                IsDeleted = false
            }).ToList();
        }

        return entity;
    }

    /// <summary>
    /// Updates a ProjectSchedule entity from an UpdateProjectScheduleRequestDto
    /// </summary>
    /// <param name="entity">The ProjectSchedule entity to update</param>
    /// <param name="dto">The UpdateProjectScheduleRequestDto to update the entity from</param>
    public static void UpdateFromDto(this ProjectSchedule entity, UpdateProjectScheduleRequestDto dto)
    {
        if (entity == null || dto == null)
            return;

        if (dto.PlannedWorkload != null)
        {
            entity.PlannedWorkload = dto.PlannedWorkload.Value;
        }

        if (dto.PresignedWorkload != null)
        {
            entity.PresignedWorkload = dto.PresignedWorkload.Value;
        }

        if (dto.Description != null)
        {
            entity.Description = dto.Description;
        }
    }

    /// <summary>
    /// Deletes a ProjectSchedule entity and all related EmployeeShifts and OutSourceShifts
    /// </summary>
    /// <param name="entity">The ProjectSchedule entity to delete</param>   
    public static void Delete(this ProjectSchedule entity)
    {
        if (entity == null)
            return;

        entity.IsDeleted = true;

        if (entity.EmployeeShifts != null && entity.EmployeeShifts.Any())
        {
            foreach (var shift in entity.EmployeeShifts)
            {
                shift.IsDeleted = true;
            }
        }

        if (entity.OutSourceShifts != null && entity.OutSourceShifts.Any())
        {
            foreach (var shift in entity.OutSourceShifts)
            {
                shift.IsDeleted = true;
            }
        }
    }

    /// <summary>
    /// Clones a ProjectSchedule entity to a new ProjectSchedule entity
    /// </summary>
    /// <param name="entity">The ProjectSchedule entity to clone</param>
    /// <param name="projectUid">The project uid</param>
    /// <param name="workingDate">The working date</param>
    /// <returns>The cloned ProjectSchedule entity</returns>
    public static ProjectSchedule? Clone(this ProjectSchedule entity, Guid projectUid, DateOnly workingDate)
    {
        if (entity == null)
            return null;

        var newSchedule = new ProjectSchedule
        {
            ProjectScheduleUid = GuidHelper.GenerateUUIDv7(),
            ProjectUid = projectUid,
            WorkingDate = workingDate,
            PlannedWorkload = entity.PlannedWorkload,
            PresignedWorkload = entity.PresignedWorkload,
            Description = entity.Description,
            IsDeleted = false
        };

        // Map employee shifts if exists
        if (entity.EmployeeShifts != null && entity.EmployeeShifts.Count > 0)
        {
            newSchedule.EmployeeShifts = entity.EmployeeShifts
                .Select(es => new EmployeeShift
                {
                    EmployeeShiftUid = GuidHelper.GenerateUUIDv7(),
                    ProjectUid = projectUid,
                    EmployeeUid = es.EmployeeUid,
                    ProjectScheduleUid = newSchedule.ProjectScheduleUid,
                    ScheduledStartTime = new DateTime(
                        workingDate.Year,
                        workingDate.Month,
                        workingDate.Day,
                        es.ScheduledStartTime!.Value.Hour,
                        es.ScheduledStartTime!.Value.Minute,
                        0),
                    ScheduledEndTime = new DateTime(
                        workingDate.Year,
                        workingDate.Month,
                        workingDate.Day,
                        es.ScheduledEndTime!.Value.Hour,
                        es.ScheduledEndTime!.Value.Minute,
                        0),
                    TotalScheduledWorkTime = es.TotalScheduledWorkTime,
                    AssignedRole = es.AssignedRole,
                    IsDeleted = false
                }).ToList();
        }

        // Map outsource shifts if exists
        if (entity.OutSourceShifts != null && entity.OutSourceShifts.Count > 0)
        {
            newSchedule.OutSourceShifts = entity.OutSourceShifts.Select(os => new OutSourceShift
            {
                OutSourceShiftUid = GuidHelper.GenerateUUIDv7(),
                OutSourceUid = os.OutSourceUid,
                ProjectScheduleUid = newSchedule.ProjectScheduleUid,
                ScheduledStartTime = os.ScheduledStartTime,
                ScheduledEndTime = os.ScheduledEndTime,
                AssignedWorkload = os.AssignedWorkload,
                Role = os.Role,
                IsDeleted = false
            }).ToList();
        }

        return newSchedule;
    }

    /// <summary>
    /// Copies properties (employee shifts and outsource shifts) from source to target 
    /// </summary>
    /// <param name="source">The source ProjectSchedule entity</param>
    /// <param name="target">The target ProjectSchedule entity</param>
    public static void Copy(this ProjectSchedule source, ProjectSchedule target)
    {
        if (source == null || target == null)
            return;

        if (source.EmployeeShifts != null && source.EmployeeShifts.Count > 0)
        {
            foreach (var shift in source.EmployeeShifts)
            {
                target.EmployeeShifts.Add(new EmployeeShift
                {
                    EmployeeShiftUid = shift.EmployeeShiftUid,
                    ProjectUid = target.ProjectUid,
                    EmployeeUid = shift.EmployeeUid,
                    ProjectScheduleUid = target.ProjectScheduleUid,
                    ScheduledStartTime = shift.ScheduledStartTime,
                    ScheduledEndTime = shift.ScheduledEndTime,
                    TotalScheduledWorkTime = shift.TotalScheduledWorkTime,
                    AssignedRole = shift.AssignedRole,
                    IsDeleted = false
                });
            }
        }

        if (source.OutSourceShifts != null && source.OutSourceShifts.Count > 0)
        {
            foreach (var shift in source.OutSourceShifts)
            {
                target.OutSourceShifts.Add(new OutSourceShift
                {
                    OutSourceShiftUid = shift.OutSourceShiftUid,
                    OutSourceUid = shift.OutSourceUid,
                    ProjectScheduleUid = target.ProjectScheduleUid,
                    ScheduledStartTime = shift.ScheduledStartTime,
                    ScheduledEndTime = shift.ScheduledEndTime,
                    AssignedWorkload = shift.AssignedWorkload,
                    Role = shift.Role,
                    IsDeleted = false
                });
            }
        }
    }

    #endregion
}