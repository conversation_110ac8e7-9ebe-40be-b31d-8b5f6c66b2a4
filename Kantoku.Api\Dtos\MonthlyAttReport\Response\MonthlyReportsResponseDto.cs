using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.MonthlyAttReport;

public class MonthlyReportsResponseDto
{
    public IEnumerable<MonthlyReportResponseDto> Items { get; set; } = [];
    public int PageNum { get; set; }
    public int PageSize { get; set; }
    public int TotalRecords { get; set; }
}

public class MonthlyReportResponseDto
{
    [JsonPropertyName("employeeId")]
    public string? EmployeeId { get; set; }

    [JsonPropertyName("employeeName")]
    public string? EmployeeName { get; set; }

    [JsonPropertyName("code")]
    public string? EmployeeCode { get; set; }

    [JsonPropertyName("workTime")]
    public float? WorkTime { get; set; }

    [JsonPropertyName("overtime")]
    public float? Overtime { get; set; }

    [JsonPropertyName("workDays")]
    public float? WorkDays { get; set; }

    [JsonPropertyName("offdays")]
    public float? Offdays { get; set; }

    [JsonPropertyName("usedLeaves")]
    public float? UsedLeaves { get; set; }

    [JsonPropertyName("remainLeaves")]
    public float? RemainLeaves { get; set; }

    [JsonPropertyName("comment")]
    public string? Comment { get; set; }

    [JsonPropertyName("isRequested")]
    public bool IsRequested { get; set; }

    [JsonPropertyName("isApproved")]
    public bool? IsApproved { get; set; }

    [JsonPropertyName("approverName")]
    public string? ApproverName { get; set; }

    [JsonPropertyName("approvedTime")]
    public string? ApprovedTime { get; set; }

}