using Hangfire;

namespace Kantoku.Processor.Jobs.Hangfire;

/// <summary>
/// Base class for Hangfire job implementations with logging and error handling
/// </summary>
public abstract class HangfireJobBase : IHangfireJob
{
    protected readonly ILogger Logger;
    protected readonly IServiceProvider ServiceProvider;

    protected HangfireJobBase(ILogger logger, IServiceProvider serviceProvider)
    {
        Logger = logger;
        ServiceProvider = serviceProvider;
    }

    /// <summary>
    /// Main execution method that needs to be implemented by derived classes
    /// </summary>
    public abstract Task ExecuteAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Executes the job with comprehensive error handling and logging
    /// </summary>
    [AutomaticRetry(Attempts = 3)]
    public async Task Execute(CancellationToken cancellationToken = default)
    {
        var jobName = GetType().Name;
        var startTime = DateTime.UtcNow;
        
        Logger.LogInformation("Starting execution of job {JobName} at {StartTime}", jobName, startTime);

        try
        {
            await ExecuteAsync(cancellationToken);
            
            var duration = DateTime.UtcNow - startTime;
            Logger.LogInformation("Successfully completed job {JobName} in {Duration}ms", 
                jobName, duration.TotalMilliseconds);
        }
        catch (OperationCanceledException)
        {
            Logger.LogWarning("Job {JobName} was cancelled", jobName);
            throw;
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - startTime;
            Logger.LogError(ex, "Job {JobName} failed after {Duration}ms: {ErrorMessage}", 
                jobName, duration.TotalMilliseconds, ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Creates a service scope for dependency injection
    /// </summary>
    protected IServiceScope CreateScope()
    {
        return ServiceProvider.CreateScope();
    }

    /// <summary>
    /// Logs information message
    /// </summary>
    protected void LogInformation(string message, params object[] args)
    {
        Logger.LogInformation($"{GetType().Name}: {message}", args);
    }

    /// <summary>
    /// Logs warning message
    /// </summary>
    protected void LogWarning(string message, params object[] args)
    {
        Logger.LogWarning($"{GetType().Name}: {message}", args);
    }

    /// <summary>
    /// Logs error message
    /// </summary>
    protected void LogError(string message, Exception? ex = null, params object[] args)
    {
        if (ex != null)
        {
            Logger.LogError(ex, $"{GetType().Name}: {message}", args);
        }
        else
        {
            Logger.LogError($"{GetType().Name}: {message}", args);
        }
    }

    /// <summary>
    /// Checks if cancellation has been requested and throws if it has
    /// </summary>
    protected static void ThrowIfCancellationRequested(CancellationToken cancellationToken)
    {
        cancellationToken.ThrowIfCancellationRequested();
    }
}
