﻿namespace Kantoku.Api.Dtos.Manufacturer.Response;

/// <summary>
/// Represents the response containing a list of manufacturers and pagination information.
/// </summary>
public class ManufacturersResponseDto
{
    /// <summary>
    ///  the collection of manufacturer response data. (*)
    /// </summary>
    public IEnumerable<ManufacturerResponseDto> Items { get; set; } = [];

    /// <summary>
    ///  the current page number. (*)
    /// </summary>
    public int PageNum { get; set; }

    /// <summary>
    ///  the size of the page. (*)
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    ///  the total number of records available. (*)
    /// </summary>
    public int TotalRecords { get; set; }
}
