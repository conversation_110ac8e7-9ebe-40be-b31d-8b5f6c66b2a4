using System.Text.Json.Serialization;

namespace Kantoku.Api.Dtos.Schedule.Request;

public class UpdateScheduledOutSourceShiftRequestDto
{
    /// <summary>
    /// new start time, format: HH:mm:ss 
    /// </summary>
    [JsonPropertyName("startTime")]
    public required TimeOnly StartTime { get; set; }

    /// <summary>
    /// new end time, format: HH:mm:ss
    /// </summary>
    [JsonPropertyName("endTime")]
    public required TimeOnly EndTime { get; set; }

    /// <summary>
    /// Assigned workload
    /// </summary>
    [JsonPropertyName("assignedWorkload")]
    public float? AssignedWorkload { get; set; }

    /// <summary>
    /// Assigned role
    /// </summary>
    [JsonPropertyName("assignedRole")]
    public string? AssignedRole { get; set; }
}