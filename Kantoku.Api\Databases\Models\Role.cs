﻿using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Models;

public class Role : AuditableEntity
{
    public Guid RoleUid { get; set; }
    public Guid OrgUid { get; set; }
    public Guid? StructureUid { get; set; }

    [AuditProperty]
    public string RoleName { get; set; } = null!;
    [AuditProperty]
    public string? Description { get; set; }

    public bool IsDeleted { get; set; } = false;
    public bool IsDefault { get; set; } = false;
    public bool IsHidden { get; set; } = false;


    public virtual Org Org { get; set; } = null!;

    public virtual Structure? Structure { get; set; }

    public virtual ICollection<EmployeeRole> EmployeeRoles { get; set; } = [];

    public virtual ICollection<RoleFunction> RoleFunctions { get; set; } = [];
}
