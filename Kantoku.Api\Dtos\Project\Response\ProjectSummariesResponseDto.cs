using Kantoku.Api.Dtos.ConstructionCost.Response;

namespace Kantoku.Api.Dtos.Project.Response;

public class ProjectSummariesResponseDto
{
    public IEnumerable<ProjectSummaryResponseDto> Items { get; set; } = [];
    public int PageNum { get; set; }
    public int PageSize { get; set; }
    public int TotalRecords { get; set; }
}

public class ProjectSummaryResponseDto
{
    public string? ProjectId { get; set; }

    public string? ProjectCode { get; set; }

    public string? ProjectName { get; set; }

    public string? ProjectTypeName { get; set; }

    public string? Address { get; set; }

    public string? CustomerName { get; set; }

    public string? ContractorName { get; set; }

    public string? PrimaryManager { get; set; }

    public string? ProjectStatus { get; set; }

    public string? ExpectedStartDate { get; set; }

    public string? ExpectedEndDate { get; set; }

    public string? ActualStartDate { get; set; }

    public string? ActualEndDate { get; set; }

    public ProjectProgressResponseDto? ProjectProgress { get; set; }

    public ProjectAttendanceResponseDto? ProjectAttendance { get; set; }

    public ProjectCostResponseDto? ProjectCosts { get; set; }
}
