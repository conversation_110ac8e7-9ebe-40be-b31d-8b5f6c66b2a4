namespace Kantoku.Api.Dtos.Construction.Response;

public class ConstructionSimpleResponseDto
{
    /// <summary>
    /// Constructions List
    /// </summary>
    public IEnumerable<ConstructionBaseResponseDto>? Constructions { get; set; }
}

public class ConstructionBaseResponseDto
{
    /// <summary>
    /// Construction Id
    /// </summary>
    public string? ConstructionId { get; set; }

    /// <summary>
    /// Construction Name
    /// </summary>
    public string? ConstructionName { get; set; }

    /// <summary>
    /// Description
    /// </summary>  
    public string? Description { get; set; }

    /// <summary>
    /// Is Primary (true if it is the main construction)
    /// </summary>
    public bool IsPrimary { get; set; }
}

