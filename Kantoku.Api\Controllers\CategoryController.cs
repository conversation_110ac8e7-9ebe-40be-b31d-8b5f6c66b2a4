﻿using Kantoku.Api.Databases.Models;
using Kantoku.Api.Dtos.AuditLog;
using Kantoku.Api.Dtos.Category.Request;
using Kantoku.Api.Dtos.Category.Response;
using Kantoku.Api.Factories.Response;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Services;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Exceptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Kantoku.Api.Controllers;

[Route("api/v1/cost/[controller]")]
[ApiController]
[Authorize(Policy = PolicyConstant.ORG_ACCESS)]
public class CategoryController : BaseController
{
    private readonly ICategoryService categoryService;
    private readonly IAuditLogService auditLogService;
    public CategoryController(ICategoryService categoryService,
        ITResponseFactory responseFactory,
        IAuditLogService auditLogService) :
        base(responseFactory)
    {
        this.categoryService = categoryService;
        this.auditLogService = auditLogService;
    }

    /// <summary>
    /// Get categories by filter criteria
    /// </summary>
    /// <param name="filter">Filter parameters for categories</param>
    /// <returns>List of categories matching the filter criteria</returns>
    [HttpGet]
    public async Task<GeneralResponse<CategoriesResponseDto>> GetByFilter([FromQuery] CategoryFilter filter)
    {
        try
        {
            var result = await categoryService.GetByFilter(filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<CategoriesResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get a specific category by its ID
    /// </summary>
    /// <param name="id">Category ID</param>
    /// <returns>Category details if found</returns>
    [HttpGet("{id}")]
    public async Task<GeneralResponse<CategoryResponseDto>> GetById([FromRoute] Guid id)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<CategoryResponseDto>();

            var result = await categoryService.GetById(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<CategoryResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Create a new category
    /// </summary>
    /// <param name="requestDto">Category creation data</param>
    /// <returns>Created category details</returns>
    [HttpPost]
    public async Task<GeneralResponse<CategoryResponseDto>> Create([FromBody] CreateCategoryRequestDto requestDto)
    {
        try
        {
            if (!ModelState.IsValid)
                return BadRequest<CategoryResponseDto>();

            var result = await categoryService.Create(requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<CategoryResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Update an existing category
    /// </summary>
    /// <param name="id">Category ID to update</param>
    /// <param name="requestDto">Category update data</param>
    /// <returns>Updated category details</returns>
    [HttpPut("{id}")]
    public async Task<GeneralResponse<CategoryResponseDto>> Update([FromRoute] Guid id, [FromBody] UpdateCategoryRequestDto requestDto)
    {
        try
        {
            var result = await categoryService.Update(id, requestDto);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<CategoryResponseDto>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Delete a category
    /// </summary>
    /// <param name="id">Category ID to delete</param>
    /// <returns>Success response with no content</returns>
    [HttpDelete("{id}")]
    public async Task<GeneralResponse<bool>> Delete([FromRoute] Guid id)
    {
        try
        {
            var result = await categoryService.Delete(id);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<bool>(e.ErrorCode);
        }
    }

    /// <summary>
    /// Get audit logs for a specific category
    /// </summary>
    /// <param name="id">Category ID to get logs for</param>
    /// <param name="filter">Filter parameters for audit logs</param>
    /// <returns>List of audit logs for the category</returns>
    [HttpGet("{id}/logs")]
    public async Task<GeneralResponse<AuditLogResponseDto>> GetLogs([FromRoute] string id, [FromQuery] AuditLogFilter filter)
    {
        try
        {
            var result = await auditLogService.GetAuditLogsByEntity<Category>(id, filter);
            return SuccessFromResult(result);
        }
        catch (BusinessException e)
        {
            return Fail<AuditLogResponseDto>(e.ErrorCode);
        }
    }
}
