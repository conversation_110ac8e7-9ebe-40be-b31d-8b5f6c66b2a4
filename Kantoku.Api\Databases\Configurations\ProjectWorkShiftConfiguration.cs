using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class ProjectWorkShiftConfiguration(string schema) : IEntityTypeConfiguration<ProjectWorkShift>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<ProjectWorkShift> builder)
    {
        builder.HasKey(e => new { e.ProjectUid, e.WorkShiftUid }).HasName("project_workshift_pkey");

        builder.ToTable("project_workshift", Schema);

        builder.Property(e => e.ProjectUid)
            .HasColumnName("project_uid");
        builder.Property(e => e.WorkShiftUid)
            .HasColumnName("workshift_uid");
        builder.Property(e => e.IsDefault)
            .HasDefaultValue(false)
            .HasColumnName("is_default");

        builder.HasOne(d => d.Project)
            .WithMany(p => p.ProjectWorkShifts)
            .HasForeignKey(d => d.ProjectUid)
            .OnDelete(DeleteBehavior.Cascade)
            .HasConstraintName("project_workshift_project_id_fkey");

        builder.HasOne(d => d.WorkShift)
            .WithMany(p => p.ProjectWorkShifts)
            .HasForeignKey(d => d.WorkShiftUid)
            .OnDelete(DeleteBehavior.Cascade)
            .HasConstraintName("project_workshift_workshift_id_fkey");
    }
} 