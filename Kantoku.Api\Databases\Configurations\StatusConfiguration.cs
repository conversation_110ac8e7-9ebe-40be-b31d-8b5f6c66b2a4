using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Kantoku.Api.Databases.Configurations;

public class StatusConfiguration(string schema) : IEntityTypeConfiguration<Status>
{
    public string Schema { get; set; } = schema;

    public void Configure(EntityTypeBuilder<Status> builder)
    {
        builder.ToTable("status", Schema, tb => tb.HasComment("Bảng trạng thái dùng chung"));

        builder.HasKey(e => e.StatusCode).HasName("status_pkey");

        builder.HasIndex(e => e.StatusCode).IsUnique().HasDatabaseName("idx_status_code");
        builder.HasIndex(e => e.Group).HasDatabaseName("idx_status_group");

        builder.Property(e => e.StatusCode)
            .HasColumnName("status_code");
        builder.Property(e => e.Group)
            .HasColumnName("group");
        builder.Property(e => e.TranslatedStatus)
            .HasColumnName("translated_status")
            .HasColumnType("jsonb")
            .HasConversion(
                v => Newtonsoft.Json.JsonConvert.SerializeObject(v),
                v => Newtonsoft.Json.JsonConvert.DeserializeObject<IEnumerable<TranslatedStatus>>(v) ?? Enumerable.Empty<TranslatedStatus>()
            ).Metadata.SetValueComparer(
                new ValueComparer<IEnumerable<TranslatedStatus>>(
                    (c1, c2) => (c1 ?? Enumerable.Empty<TranslatedStatus>()).SequenceEqual(c2 ?? Enumerable.Empty<TranslatedStatus>()),
                    c => c.Aggregate(0, (a, v) => HashCode.Combine(a, v.GetHashCode())),
                    c => c.ToList()
                )
            );
    }
} 