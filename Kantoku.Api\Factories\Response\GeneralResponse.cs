namespace Kantoku.Api.Factories.Response;

public interface IGeneralResponse
{
    int? Status { get; set; }
    string? Code { get; set; }
    string? Message { get; set; }
}

public class GeneralResponse : IGeneralResponse
{
    public int? Status { get; set; }
    public string? Code { get; set; }
    public string? Message { get; set; }

    public GeneralResponse(int status, string code, string message)
    {
        Status = status;
        Code = code;
        Message = message;
    }

    public GeneralResponse()
    {
        Status = default;
        Code = default;
        Message = default;
    }

    public GeneralResponse WithStatus(int statusCode)
    {
        Status = statusCode;
        return this;
    }

    public GeneralResponse WithCode(string code)
    {
        Code = code;
        return this;
    }

    public GeneralResponse WithMessage(string message)
    {
        Message = message;
        return this;
    }       
}

public class GeneralResponse<T> : IGeneralResponse
{
    public int? Status { get; set; }
    public string? Code { get; set; }
    public string? Message { get; set; }
    public T? Data { get; set; }

    public GeneralResponse(int status, string code, string message, T data)
    {
        Status = status;
        Code = code;
        Message = message;
        Data = data;
    }

    public GeneralResponse(int status, string code, string message)
    {
        Status = status;
        Code = code;
        Message = message;
        Data = default;
    }

    public GeneralResponse()
    {
        Status = default;
        Code = default;
        Message = default;
        Data = default;
    }

    public GeneralResponse<T> WithStatus(int statusCode)
    {
        Status = statusCode;
        return this;
    }

    public GeneralResponse<T> WithCode(string code)
    {
        Code = code;
        return this;
    }

    public GeneralResponse<T> WithMessage(string message)
    {
        Message = message;
        return this;
    }

    public GeneralResponse<T> WithData(T? data)
    {
        Data = data;
        return this;
    }
}