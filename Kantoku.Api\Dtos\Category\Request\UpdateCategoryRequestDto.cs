﻿using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.Category.Request;

/// <summary>
/// Data transfer object for updating a category
/// </summary>
public class UpdateCategoryRequestDto
{
    /// <summary>
    /// Unique code for the category
    /// </summary>
    public string? CategoryCode { get; set; }

    /// <summary>
    /// Name of the category
    /// </summary>
    public string? CategoryName { get; set; }

    /// <summary>
    /// Optional description of the category
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Optional ID of the parent category
    /// </summary>
    public Guid? ParentId { get; set; }
}
