namespace Kantoku.Api.Dtos.Project.Response;

public class ProjectAttendanceResponseDto
{
    /// <summary>
    /// Presigned Workload
    /// </summary>
    public float PresignedWorkload { get; set; }

    /// <summary>
    /// Actual Workload
    /// </summary>
    public float ActualWorkload { get; set; }

    /// <summary>
    /// Employee Attendances
    /// </summary>
    public IEnumerable<EmployeeAttendanceStatusResponseDto> EmployeeAttendances { get; set; } = [];
}

public class EmployeeAttendanceStatusResponseDto
{
    /// <summary>
    /// Employee Name
    /// </summary>
    public string? EmployeeName { get; set; }

    /// <summary>
    /// Is Checked In
    /// </summary>
    public bool IsCheckedIn { get; set; }

    /// <summary>
    /// Is Requested Off
    /// </summary>
    public bool IsRequestedOff { get; set; }
}