using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.InputCostItem.Request;

public class UpdateMultipleInputCostItemsRequestDto
{
    public IEnumerable<UpdateMultipleInputCostItemRequestDto> Items { get; set; } = [];
}

public class UpdateMultipleInputCostItemRequestDto : UpdateInputCostItemRequestDto
{
    public Guid? InputCostItemId { get; set; }
    public bool IsDeleted { get; set; } = false;
}
