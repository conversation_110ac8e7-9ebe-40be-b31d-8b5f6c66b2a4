using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.Ranking.Request;

public class UpdateRankingRequestDto
{
    /// <summary>
    /// The name of the ranking
    /// </summary>
    public string? RankingName { get; set; }

    /// <summary>
    /// The description of the ranking
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// The minimum value of the ranking
    /// </summary>
    public int? MinValue { get; set; }

    /// <summary>
    /// The maximum value of the ranking
    /// </summary>
    public int? MaxValue { get; set; }
}
