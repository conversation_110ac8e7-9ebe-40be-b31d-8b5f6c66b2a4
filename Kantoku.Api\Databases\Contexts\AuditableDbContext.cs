using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Kantoku.Api.Databases.Models;
using Newtonsoft.Json;
using Kantoku.Api.Utils.Helpers;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Attributes.Property;

namespace Kantoku.Api.Databases.Contexts;

public abstract class AuditableDbContext : DbContext
{
    private readonly IHttpContextAccessor httpContextAccessor;
    private readonly Serilog.ILogger logger;

    public AuditableDbContext(DbContextOptions options, IHttpContextAccessor httpContextAccessor, Serilog.ILogger logger)
        : base(options)
    {
        this.httpContextAccessor = httpContextAccessor;
        this.logger = logger;
    }


    public required DbSet<AuditLog> AuditLogs { get; set; }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            await OnBeforeSaveChanges(cancellationToken);
            var result = await base.SaveChangesAsync(cancellationToken);
            return result;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error saving changes");
            throw;
        }
    }

    public async Task OnBeforeSaveChanges(CancellationToken cancellationToken = default)
    {
        var currentAccountUid = GetCurrentAccountUid();
        var timestamp = DateTime.UtcNow;

        // Handle auditable entities
        HandleAuditProperties(currentAccountUid, timestamp);

        // Track detailed changes
        await TrackDetailedChanges(currentAccountUid, timestamp, cancellationToken);
    }

    private void HandleAuditProperties(string? currentAccountUid, DateTime timestamp)
    {
        var auditableEntries = ChangeTracker.Entries<IAuditableEntity>();

        foreach (var entry in auditableEntries)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedBy = currentAccountUid;
                    entry.Entity.CreatedTime = timestamp;
                    entry.Entity.LastModifiedBy = currentAccountUid;
                    entry.Entity.LastModifiedTime = timestamp;
                    break;

                case EntityState.Modified:
                    entry.Entity.LastModifiedBy = currentAccountUid;
                    entry.Entity.LastModifiedTime = timestamp;
                    break;
            }
        }
    }

    private async Task TrackDetailedChanges(string? currentAccountUid, DateTime timestamp, CancellationToken cancellationToken)
    {
        ChangeTracker.DetectChanges();

        var entries = ChangeTracker.Entries()
            .Where(e => e.State != EntityState.Unchanged && e.State != EntityState.Detached)
            .ToList();

        var auditLogs = new List<AuditLog>();

        foreach (var entry in entries)
        {
            // Skip audit log entries themselves
            if (entry.Entity is AuditLog) continue;

            var auditLog = CreateAuditLogEntry(entry, currentAccountUid, timestamp);
            if (auditLog is not null)
            {
                auditLogs.Add(auditLog);
            }
        }

        if (auditLogs.Count > 0)
        {
            await AuditLogs.AddRangeAsync(auditLogs, cancellationToken);
        }
    }

    private AuditLog? CreateAuditLogEntry(EntityEntry entry, string? currentAccountUid, DateTime timestamp)
    {
        var isSoftDelete = IsSoftDeleteOperation(entry);
        var action = isSoftDelete ? "Delete" : GetAction(entry.State);

        var oldValues = GetOldValues(entry);
        var newValues = GetNewValues(entry);
        // Check if any collections are modified
        var hasCollectionChanges = entry.Collections.Any(c => c.IsModified);

        // Only create audit log if there are actual changes
        if (entry.State == EntityState.Modified && !hasCollectionChanges && !entry.Properties.Any(p => p.IsModified))
        {
            return null;
        }

        var auditLog = new AuditLog
        {
            AuditLogUid = GuidHelper.GenerateUUIDv7(),
            EntityName = entry.Entity.GetType().Name,
            EntityId = GetEntityId(entry),
            Action = action,
            AccountUid = Guid.TryParse(currentAccountUid, out Guid accountGuid) ? accountGuid : null,
            Timestamp = timestamp,
            OldValues = JsonConvert.SerializeObject(oldValues),
            NewValues = JsonConvert.SerializeObject(newValues)
        };

        return auditLog;
    }

    private bool IsSoftDeleteOperation(EntityEntry entry)
    {
        if (entry.State != EntityState.Modified) return false;

        // Check if the entity type has IsDeleted property
        var hasIsDeletedProperty = entry.Entity.GetType()
            .GetProperties()
            .Any(p => p.Name == "IsDeleted");

        if (!hasIsDeletedProperty) return false;

        var isDeletedProperty = entry.Property("IsDeleted");
        if (isDeletedProperty is null) return false;

        var oldValue = (bool?)isDeletedProperty.OriginalValue;
        var newValue = (bool?)isDeletedProperty.CurrentValue;

        return oldValue == false && newValue == true;
    }

    private string GetAction(EntityState state) => state switch
    {
        EntityState.Added => "Create",
        EntityState.Modified => "Update",
        EntityState.Deleted => "Delete",
        _ => "Unknown"
    };

    private Dictionary<string, object?> GetOldValues(EntityEntry entry)
    {
        var values = new Dictionary<string, object?>();
        var auditableProperties = GetAuditableProperties(entry.Entity.GetType());

        foreach (var property in entry.Properties)
        {
            // Skip if property is not marked as auditable
            if (!auditableProperties.Contains(property.Metadata.Name)) continue;

            if (entry.State == EntityState.Added)
            {
                values[property.Metadata.Name] = null;
            }
            else if (property.IsModified ||
                    (property.Metadata.Name == "IsDeleted" && IsSoftDeleteOperation(entry)))
            {
                values[property.Metadata.Name] = property.OriginalValue;
            }
        }

        return values;
    }

    private Dictionary<string, object?> GetNewValues(EntityEntry entry)
    {
        var values = new Dictionary<string, object?>();
        var auditableProperties = GetAuditableProperties(entry.Entity.GetType());

        foreach (var property in entry.Properties)
        {
            // Skip if property is not marked as auditable
            if (!auditableProperties.Contains(property.Metadata.Name)) continue;

            if (entry.State == EntityState.Deleted)
            {
                values[property.Metadata.Name] = null;
            }
            else if (entry.State == EntityState.Added ||
                     property.IsModified ||
                    (property.Metadata.Name == "IsDeleted" && IsSoftDeleteOperation(entry)))
            {
                values[property.Metadata.Name] = property.CurrentValue;
            }
        }

        return values;
    }

    private HashSet<string> GetAuditableProperties(Type entityType)
    {
        var properties = entityType.GetProperties();
        var auditProperties = properties.Where(p => p.GetCustomAttributes(typeof(AuditPropertyAttribute), true).Any());
        return auditProperties.Select(p => p.Name).ToHashSet();
    }

    private string GetEntityId(EntityEntry entry)
    {
        var keyValues = entry.Metadata.FindPrimaryKey()?.Properties
            .Select(p => entry.Property(p.Name).CurrentValue)
            .Where(value => value is not null);

        return keyValues is not null ? string.Join(",", keyValues) : "Unknown";
    }

    public string GetCurrentAccountUid()
    {
        try
        {
            var accountUidClaim = httpContextAccessor.HttpContext?.Items[ClaimConstant.ACCOUNT_UID];
            return accountUidClaim is not null && Guid.TryParse(accountUidClaim.ToString(), out Guid accountUid) ? accountUid.ToString() : string.Empty;
        }
        catch (System.Exception)
        {
            return string.Empty;
        }
    }
}
