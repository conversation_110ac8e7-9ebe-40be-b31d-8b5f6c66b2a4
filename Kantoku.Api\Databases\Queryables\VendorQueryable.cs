using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Databases.Queryables;

public class VendorQueryableOptions : EntityQueryableOptions
{
    public bool IncludedAll { get; set; } = false;
    public bool IncludedInputCosts { get; set; } = false;
    public bool IncludedItems { get; set; } = false;
}

public interface IVendorQueryable
{
    IQueryable<Vendor> GetVendorQuery(
        VendorQueryableOptions options
    );
    IQueryable<Vendor> GetVendorQueryIncluded(
        VendorQueryableOptions options,
        IQueryable<Vendor>? query = null
    );

    IQueryable<Vendor> GetVendorQueryByFilter(
        VendorFilter filter,
        VendorQueryableOptions options,
        IQueryable<Vendor>? query = null
    );
}

[Queryable(ServiceLifetime.Scoped)]
public class VendorQueryable(PostgreDbContext context) :
    BaseQueryable<Vendor>(context), IVendorQueryable
{
    public IQueryable<Vendor> GetVendorQuery(
        VendorQueryableOptions options
    )
    {
        var query = GetQuery(options);
        query = query.Where(p => p.IsDeleted == false);
        return query;
    }

    public IQueryable<Vendor> GetVendorQueryIncluded(
        VendorQueryableOptions options,
        IQueryable<Vendor>? query = null
    )
    {
        query ??= GetVendorQuery(options);

        if (options.IncludedInputCosts || options.IncludedAll)
        {
            query = query.Include(s => s.InputCosts)
                .ThenInclude(s => s.InputCostItems);
        }
        if (options.IncludedItems || options.IncludedAll)
        {
            query = query.Include(s => s.ItemPrices);
        }

        return query;
    }

    public IQueryable<Vendor> GetVendorQueryByFilter(
        VendorFilter filter,
        VendorQueryableOptions options,
        IQueryable<Vendor>? query = null
    )
    {
        query ??= GetVendorQueryIncluded(options);
        if (filter.OrgId is not null && filter.OrgId != Guid.Empty)
        {
            query = query.Where(p => p.OrgUid == filter.OrgId);
        }
        if (filter.Keyword is not null)
        {
            query = query.Where(p => p.VendorCode.Contains(filter.Keyword)
            || p.VendorName.Contains(filter.Keyword));
        }

        return query;
    }
}
