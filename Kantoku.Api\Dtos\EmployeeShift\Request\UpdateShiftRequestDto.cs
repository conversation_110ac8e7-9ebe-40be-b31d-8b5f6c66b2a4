using System.Text.Json.Serialization;
using Kantoku.Api.Extensions.Validators;

namespace Kantoku.Api.Dtos.EmployeeShift.Request;

public class UpdateShiftRequestDto
{
    /// <summary>
    /// The project where the shift takes place
    /// </summary>
    public Guid? ProjectId { get; set; }

    /// <summary>
    /// The location where the shift takes place
    /// </summary>
    public string? WorkingLocation { get; set; }

    /// <summary>
    /// The time when the shift starts
    /// </summary>
    [DateTimeValidator(typeof(DateTime))]
    public string? CheckInTime { get; set; }

    /// <summary>
    /// The time when the shift ends
    /// </summary>
    [DateTimeValidator(typeof(DateTime))]
    public string? CheckOutTime { get; set; }

    /// <summary>
    /// List of break periods during the shift
    /// </summary>
    public IEnumerable<UpdateBreakItem>? BreakList { get; set; }

    /// <summary>
    /// Additional notes or description about the shift
    /// </summary>
    public string? Description { get; set; }
}

public class UpdateBreakItem
{
    /// <summary>
    /// The time when the break starts
    /// </summary>
    [JsonPropertyName("breakInTime")]
    [DateTimeValidator(typeof(DateTime))]
    public string? BreakInTime { get; set; }

    /// <summary>
    /// The time when the break ends
    /// </summary>
    [JsonPropertyName("breakOutTime")]
    [DateTimeValidator(typeof(DateTime))]
    public string? BreakOutTime { get; set; }
}