using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;

namespace Kantoku.Api.Databases.Repositories;

public interface IRequestTypeRepository
{
    Task<RequestType?> GetByCode(string code);
    Task<IEnumerable<RequestType>> GetAll();
}

[Repository(ServiceLifetime.Scoped)]
public class RequestTypeRepository : BaseRepository<RequestType>, IRequestTypeRepository
{
    public RequestTypeRepository(
        PostgreDbContext context,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor
    )
        : base(context, logger, httpContextAccessor)
    {
    }

    public async Task<IEnumerable<RequestType>> GetAll()
    {
        try
        {
            return await context.RequestTypes
                .ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting request types");
            return [];
        }
    }

    public async Task<RequestType?> GetByCode(string code)
    {
        try
        {
            return await context.RequestTypes
                .Where(rt => rt.RequestTypeCode.Equals(code))
                .FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting request type by code {Code}", code);
            return null;
        }
    }
}
