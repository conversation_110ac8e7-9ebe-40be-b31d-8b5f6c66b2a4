namespace Kantoku.Api.Dtos.Construction.Response;

public class ConstructionsEstimateCostResponseDto
{
    /// <summary>
    /// Main construction estimate cost
    /// </summary>
    public EstimateCostResponseDto? MainConstructionEstimateCost { get; set; }

    /// <summary>
    /// Sub construction estimate cost
    /// </summary>
    public EstimateCostResponseDto? SubConstructionEstimateCost { get; set; }

    /// <summary>
    /// Overall construction estimate cost
    /// </summary>
    public EstimateCostResponseDto? OverallConstructionEstimateCost { get; set; }
}

public class EstimateCostResponseDto
{
    /// <summary>
    /// Items of the estimate cost
    /// </summary>
    public IEnumerable<EstimateCostItemResponseDto> EstimateCostItems { get; set; } = [];

    /// <summary>
    /// Total amount of the estimate cost
    /// </summary>
    public long TotalEstimateCost { get; set; } = 0;
}

public class EstimateCostItemResponseDto
{
    /// <summary>
    /// Sequence number for the estimate cost 1st, 2nd, 3rd, etc.
    /// </summary>
    public short SequenceNumber { get; set; } = 0;

    /// <summary>
    /// Amount of the estimate cost
    /// </summary>
    public long Amount { get; set; } = 0;
}
