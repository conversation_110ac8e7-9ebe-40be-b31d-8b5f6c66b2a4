using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Kantoku.Api.Dtos.MonthlyAttReport.Request;

public class RequestApprovalRequestDto
{
    /// <summary>
    /// Start date of the report (YYYY-MM-DD) (*)
    /// </summary>
    [Required]
    public string ReportFrom { get; set; } = null!;

    /// <summary>
    /// End date of the report (YYYY-MM-DD) (*)
    /// </summary>
    [Required]
    public string ReportTo { get; set; } = null!;

    /// <summary>
    /// Description of the report
    /// </summary>
    public string? Description { get; set; }
}
