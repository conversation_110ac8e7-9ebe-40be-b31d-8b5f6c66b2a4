using Microsoft.EntityFrameworkCore;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Databases.Contexts;
using Kantoku.Api.Databases.Models;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Databases.Queryables;

namespace Kantoku.Api.Databases.Repositories;

public interface INotificationRepository
{
    Task<(IEnumerable<Notification>, int)> GetByFilter(OrgNotificationFilter filter, NotificationQueryableOptions options);
    Task<Notification?> GetById(Guid notificationId, NotificationQueryableOptions options);
    Task<Guid?> Create(Notification notification);
    Task<bool> Update(Notification notification);
    Task<bool> Delete(Notification notification);

    Task<Guid?> CreateTarget(NotificationTarget target);
    Task<IEnumerable<Guid>> CreateTargets(IEnumerable<NotificationTarget> targets);
    Task<bool> UpdateTarget(NotificationTarget target);
    Task<bool> UpdateTargets(IEnumerable<NotificationTarget> targets);
}

[Repository(ServiceLifetime.Scoped)]
public class NotificationRepository : BaseRepository<Notification>, INotificationRepository
{
    private readonly INotificationQueryable notificationQueryable;
    public NotificationRepository(
        PostgreDbContext context,
        INotificationQueryable notificationQueryable,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor
    )
        : base(context, logger, httpContextAccessor)
    {
        this.notificationQueryable = notificationQueryable;
    }

    public async Task<(IEnumerable<Notification>, int)> GetByFilter(OrgNotificationFilter filter, NotificationQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = notificationQueryable.GetNotificationQueryFiltered(filter, options);

            var total = await query.CountAsync();

            var result = await query
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();
            return (result, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting notifications");
            return ([], 0);
        }
    }

    public async Task<Notification?> GetById(Guid notificationId, NotificationQueryableOptions options)
    {
        try
        {
            var query = notificationQueryable.GetNotificationQueryIncluded(options)
                .Where(n => n.NotificationUid == notificationId);

            return await query.FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting notification by id {NotificationId}", notificationId);
            return null;
        }
    }

    public async Task<Guid?> Create(Notification notification)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Notifications.AddAsync(notification);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return notification.NotificationUid;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating notification: {Notification}", notification);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<bool> Update(Notification notification)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Notifications.Update(notification);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating notification: {Notification}", notification);
            await transaction.RollbackAsync();
            return false;
        }
    }

    public async Task<bool> Delete(Notification notification)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error deleting notification with ID {NotificationId}", notification.NotificationUid);
            await transaction.RollbackAsync();
            return false;
        }
    }

    public async Task<Guid?> CreateTarget(NotificationTarget target)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.NotificationTargets.AddAsync(target);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return target.NotificationTargetUid;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating notification target: {Target}", target);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<IEnumerable<Guid>> CreateTargets(IEnumerable<NotificationTarget> targets)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.NotificationTargets.AddRangeAsync(targets);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return targets.Select(t => t.NotificationTargetUid);
            }
            await transaction.RollbackAsync();
            return [];
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating notification targets: {Targets}", targets);
            await transaction.RollbackAsync();
            return [];
        }
    }

    public async Task<bool> UpdateTarget(NotificationTarget target)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.NotificationTargets.Update(target);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating notification target: {Target}", target);
            await transaction.RollbackAsync();
            return false;
        }
    }

    public async Task<bool> UpdateTargets(IEnumerable<NotificationTarget> targets)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.NotificationTargets.UpdateRange(targets);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating notification targets");
            await transaction.RollbackAsync();
            return false;
        }
    }
}

