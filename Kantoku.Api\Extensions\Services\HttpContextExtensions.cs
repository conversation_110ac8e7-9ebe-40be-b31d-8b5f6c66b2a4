using System.Globalization;
using Kantoku.Api.Middlewares.Locale;

public static class HttpContextExtensions
{
    public static CultureInfo GetCurrentCulture(this HttpContext context)
    {
        if (context.Items.TryGetValue(LocaleMiddleware.CultureKey, out var culture))
        {
            if (culture is CultureInfo cultureInfo)
            {
                return cultureInfo;
            }
        }

        return CultureInfo.CurrentCulture; // fallback
    }
}