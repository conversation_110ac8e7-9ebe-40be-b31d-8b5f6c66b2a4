﻿using Kantoku.Api.Databases.Models;
using Kantoku.Api.Databases.Queryables;
using Kantoku.Api.Databases.Repositories;
using Kantoku.Api.Dtos.Base;
using Kantoku.Api.Dtos.Item;
using Kantoku.Api.Dtos.Item.Request;
using Kantoku.Api.Dtos.Price.Request;
using Kantoku.Api.Dtos.Price.Response;
using Kantoku.Api.Dtos.File;
using Kantoku.Api.Filters.Domains;
using Kantoku.Api.Utils.Attributes.Class;
using Kantoku.Api.Utils.Constants;
using Kantoku.Api.Utils.Helpers;
using Kantoku.Api.Utils.Mappers.EntityMappers;

namespace Kantoku.Api.Services;

public interface IItemService
{
    //item
    Task<ResultDto<ItemResponseDto>> GetById(Guid itemId);
    Task<ResultDto<ItemsResponseDto>> GetByFilter(ItemFilter filter);

    Task<ResultDto<byte[]>> GetItemImage(Guid itemId, string orgId);
    Task<ResultDto<ItemResponseDto>> Create(CreateItemRequestDto requestDto);
    Task<ResultDto<ItemResponseDto>> Update(Guid itemId, UpdateItemRequestDto requestDto);
    Task<ResultDto<bool>> Delete(Guid itemId);

    //item price
    Task<ResultDto<ItemPricesResponseDto>> GetPriceByItemId(Guid itemId, ItemPriceFilter filter);
    Task<ResultDto<ItemPriceResponseDto>> CreatePrice(Guid itemId, CreateItemPriceRequestDto requestDto);
}

[Service(ServiceLifetime.Scoped)]
public class ItemService : BaseService<ItemService>, IItemService
{
    private readonly IItemRepository itemRepository;
    private readonly IItemPriceRepository itemPriceRepository;
    private readonly IVendorRepository vendorRepository;
    private readonly IFileService fileService;
    private readonly ItemQueryableOptions options = new()
    {
        IncludedCategory = true,
        IncludedManufacturer = true
    };
    public ItemService(IItemRepository itemRepository,
        IVendorRepository vendorRepository,
        IItemPriceRepository itemPriceRepository,
        IFileService fileService,
        Serilog.ILogger logger,
        IHttpContextAccessor httpContextAccessor)
        : base(logger, httpContextAccessor)
    {
        this.itemRepository = itemRepository;
        this.itemPriceRepository = itemPriceRepository;
        this.vendorRepository = vendorRepository;
        this.fileService = fileService;
    }

    public async Task<ResultDto<ItemsResponseDto>> GetByFilter(ItemFilter filter)
    {
        var (items, total) = await itemRepository.GetByFilter(filter, options);
        if (items is null || !items.Any())
        {
            logger.Error("No items found");
            return new ErrorResultDto<ItemsResponseDto>(ResponseCodeConstant.ITEM_NOT_EXIST);
        }
        var res = items.ToItemsResponseDto(filter.PageNum, filter.PageSize, total);
        return new SuccessResultDto<ItemsResponseDto>(res);
    }

    public async Task<ResultDto<byte[]>> GetItemImage(Guid itemUid, string orgId)
    {
        var item = await itemRepository.GetById(itemUid, options);
        if (item == null)
        {
            logger.Error("Item not found");
            return new ErrorResultDto<byte[]>(ResponseCodeConstant.ITEM_NOT_EXIST);
        }
        if (item.ImageUrl == null)
        {
            logger.Error("Image not found");
            return new ErrorResultDto<byte[]>(ResponseCodeConstant.ITEM_ILLUSTRATION_NOT_EXIST);
        }
        var image = await DownloadImage(item.ImageUrl, orgId);
        if (image == null)
        {
            logger.Error("Image not found");
            return new ErrorResultDto<byte[]>(ResponseCodeConstant.ITEM_ILLUSTRATION_NOT_EXIST);
        }
        return new SuccessResultDto<byte[]>(image);
    }

    public async Task<ResultDto<ItemResponseDto>> GetById(Guid itemUid)
    {
        var item = await itemRepository.GetById(itemUid, options);
        if (item is null)
        {
            logger.Error("Item not found");
            return new ErrorResultDto<ItemResponseDto>(ResponseCodeConstant.ITEM_NOT_EXIST);
        }
        var res = item.ToItemResponseDto();
        return new SuccessResultDto<ItemResponseDto>(res);
    }

    public async Task<ResultDto<ItemPricesResponseDto>> GetPriceByItemId(Guid itemUid, ItemPriceFilter filter)
    {
        var (total, itemPrices) = await itemPriceRepository.GetByItemId(itemUid, filter, new ItemPriceQueryableOptions
        {
            IncludedVendor = true,
            IncludedItem = true
        });
        if (itemPrices is null || !itemPrices.Any())
        {
            logger.Error("No item prices found");
            return new ErrorResultDto<ItemPricesResponseDto>(ResponseCodeConstant.ITEM_PRICE_NOT_EXIST);
        }
        var res = ItemPricesResponseDtoParser(itemPrices, total, filter);
        return new SuccessResultDto<ItemPricesResponseDto>(res);
    }

    public async Task<ResultDto<ItemPriceResponseDto>> CreatePrice(Guid itemUid, CreateItemPriceRequestDto requestDto)
    {
        var item = await itemRepository.GetById(itemUid, new ItemQueryableOptions
        {
            IncludedCategory = true,
            IncludedManufacturer = true
        });
        if (item is null)
        {
            logger.Error("Item not found");
            return new ErrorResultDto<ItemPriceResponseDto>(ResponseCodeConstant.ITEM_NOT_EXIST);
        }

        if (requestDto.Vendor.VendorId is null)
        {
            var newVendor = new Vendor
            {
                VendorCode = $"UNKNOWN_CODE_{DateTime.Now:yyyyMMdd}",
                VendorName = requestDto.Vendor.VendorName ?? $"UNKNOWN_NAME_{DateTime.Now:yyyyMMdd}",
            };
            var createdVendor = await vendorRepository.Create(newVendor, new VendorQueryableOptions());
            if (createdVendor is null)
            {
                logger.Error("Vendor create failed");
                return new ErrorResultDto<ItemPriceResponseDto>(ResponseCodeConstant.VENDOR_CREATE_FAILED);
            }
            requestDto.Vendor.VendorId = createdVendor.VendorUid.ToString();
        }

        var newItemPrice = new ItemPrice
        {
            ItemUid = item.ItemUid,
            VendorUid = Guid.Parse(requestDto.Vendor.VendorId),
            Unit = requestDto.Unit,
            Price = requestDto.Price,
            ValidFrom = DateOnly.TryParse(requestDto.ValidFrom, out var validFrom)
                ? validFrom
                : DateOnly.FromDateTime(DateTime.Now),
            ValidTo = DateOnly.TryParse(requestDto.ValidTo, out var validTo)
                ? validTo
                : null
        };
        var createdItemPrice = await itemPriceRepository.Create(newItemPrice, new ItemPriceQueryableOptions());
        if (createdItemPrice is null)
        {
            logger.Error("Item price create failed");
            return new ErrorResultDto<ItemPriceResponseDto>(ResponseCodeConstant.ITEM_PRICE_CREATE_FAILED);
        }
        var result = ItemPriceResponseDtoParser(createdItemPrice);
        return new SuccessResultDto<ItemPriceResponseDto>(result);
    }

    public async Task<ResultDto<ItemResponseDto>> Create(CreateItemRequestDto requestDto)
    {
        if (!GetCurrentOrgGuid(out var orgUid))
        {
            logger.Error("Organization not found");
            return new ErrorResultDto<ItemResponseDto>(ResponseCodeConstant.INTERNAL_SERVER_ERROR);
        }
        var newItem = requestDto.ToEntity(orgUid);
        if (newItem is null)
        {
            logger.Error("Item create failed");
            return new ErrorResultDto<ItemResponseDto>(ResponseCodeConstant.ITEM_CREATE_FAILED);
        }
        if (requestDto.Image != null)
        {
            var fileMetadata = await UploadImage(newItem.ItemUid.ToString(), requestDto.Image);
            if (fileMetadata != null)
            {
                newItem.ImageUrl = fileMetadata.FileUrl;
            }
        }
        var createdItem = await itemRepository.Create(newItem, options);
        if (createdItem is null)
        {
            logger.Error("Item create failed");
            return new ErrorResultDto<ItemResponseDto>(ResponseCodeConstant.ITEM_CREATE_FAILED);
        }
        if (requestDto.Image != null)
        {
            var fileMetadata = await UploadImage(createdItem.ItemUid.ToString(), requestDto.Image);
            if (fileMetadata != null)
            {
                createdItem.ImageUrl = fileMetadata.FileUrl;
                await itemRepository.Update(createdItem, options);
            }
        }
        var result = createdItem.ToItemResponseDto();
        return new SuccessResultDto<ItemResponseDto>(result);
    }

    public async Task<ResultDto<ItemResponseDto>> Update(Guid itemUid, UpdateItemRequestDto requestDto)
    {
        var existItem = await itemRepository.GetById(itemUid, new ItemQueryableOptions
        {
            IncludedCategory = true,
            IncludedManufacturer = true
        });
        if (existItem is null)
        {
            logger.Error("Item not found");
            return new ErrorResultDto<ItemResponseDto>(ResponseCodeConstant.ITEM_NOT_EXIST);
        }
        existItem.UpdateFromDto(requestDto);
        if (requestDto.Image != null)
        {
            _ = await UploadImage(existItem.ItemUid.ToString(), requestDto.Image);
        }

        var updatedItem = await itemRepository.Update(existItem, options);
        if (updatedItem is null)
        {
            logger.Error("Item update failed");
            return new ErrorResultDto<ItemResponseDto>(ResponseCodeConstant.ITEM_UPDATE_FAILED);
        }

        var result = updatedItem.ToItemResponseDto();
        return new SuccessResultDto<ItemResponseDto>(result);
    }

    public async Task<ResultDto<bool>> Delete(Guid itemId)
    {
        var item = await itemRepository.GetById(itemId, new ItemQueryableOptions());
        if (item is null)
        {
            logger.Error("Item not found");
            return new ErrorResultDto<bool>(ResponseCodeConstant.ITEM_NOT_EXIST, false);
        }
        item.IsDeleted = true;
        var isDeleted = await itemRepository.Update(item);
        if (isDeleted == false)
        {
            logger.Error("Item delete failed");
            return new ErrorResultDto<bool>(ResponseCodeConstant.ITEM_DELETE_FAILED, false);
        }
        return new SuccessResultDto<bool>(true);
    }

    private ItemPricesResponseDto ItemPricesResponseDtoParser(IEnumerable<ItemPrice> itemPrices, int total, ItemPriceFilter filter)
    {
        try
        {
            var itemPricesDto = itemPrices
                .Select(ItemPriceResponseDtoParser)
                .Where(p => p.ItemPriceId is not null)
                .OrderBy(p => p.VendorCode)
                .ThenBy(p => p.ValidFrom)
                .ThenBy(p => p.ValidTo);
            var res = new ItemPricesResponseDto
            {
                Items = itemPricesDto,
                PageNum = filter.PageNum,
                PageSize = filter.PageSize,
                TotalRecords = total
            };
            return res;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error parsing item prices response dto");
            return new ItemPricesResponseDto();
        }
    }

    private ItemPriceResponseDto ItemPriceResponseDtoParser(ItemPrice itemPrice)
    {
        try
        {
            var res = new ItemPriceResponseDto
            {
                ItemPriceId = itemPrice.ItemPriceUid.ToString(),
                ItemId = itemPrice.ItemUid.ToString(),
                ItemCode = itemPrice.Item.ItemCode,
                ItemName = itemPrice.Item.ItemName,
                VendorId = itemPrice.VendorUid.ToString(),
                VendorCode = itemPrice.Vendor.VendorCode,
                VendorName = itemPrice.Vendor.VendorName,
                Unit = itemPrice.Unit,
                Price = itemPrice.Price.ToString(),
                ValidFrom = itemPrice.ValidFrom.ToString("yyyy-MM-dd"),
                ValidTo = itemPrice.ValidTo?.ToString("yyyy-MM-dd"),
                CreateTime = DateTimeHelper.ParseToLocalTime(itemPrice.CreatedTime)?.ToString("yyyy-MM-dd HH:mm:ss"),
                UpdateTime = DateTimeHelper.ParseToLocalTime(itemPrice.LastModifiedTime)?.ToString("yyyy-MM-dd HH:mm:ss")
            };
            return res;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error parsing item price response dto");
            return new ItemPriceResponseDto();
        }
    }

    private async Task<byte[]> DownloadImage(string imageUrl, string orgId)
    {
        try
        {
            var file = await fileService.DownloadFile(imageUrl, orgId);
            if (file == null || file.Data == null || file.Data.DataAsBytes.Length == 0)
            {
                return [];
            }
            return file.Data.DataAsBytes;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting illustration as base64");
            return [];
        }
    }

    private async Task<FileMetadataResponseDto?> UploadImage(string itemUid, IFormFile image)
    {
        try
        {
            var path = StorageConstant.ItemImage(itemUid);
            var fileName = "image.jpg";
            var objectName = path + fileName;
            var fileMetadata = await fileService.UploadFileAsync(image, objectName);
            if (fileMetadata == null || fileMetadata.Data == null || fileMetadata.Data.FileUrl == null)
            {
                logger.Error("Upload image failed");
                return null;
            }
            return fileMetadata.Data;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error uploading image");
            return new FileMetadataResponseDto();
        }
    }
}
