﻿using System.ComponentModel.DataAnnotations;
using Kantoku.Api.Utils.Attributes.Binder;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Kantoku.Api.Dtos.Vendor.Request;
using Kantoku.Api.Dtos.Item.Request;

namespace Kantoku.Api.Dtos.InputCost.Request;

/// <summary>
/// Data transfer object for creating a new input cost
/// </summary>
public class CreateInputCostRequestDto
{
    /// <summary>
    /// Title of the input cost
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// Issue date of the input cost (*)
    /// </summary>
    [Required]
    public string IssueDate { get; set; } = null!;

    /// <summary>
    /// Payment date of the input cost
    /// </summary>
    public string? PaymentDate { get; set; }

    /// <summary>
    /// Original reference number (*)
    /// </summary>
    [Required]
    public string OriginalNumber { get; set; } = null!;

    /// <summary>
    /// ID of the associated construction (*)
    /// </summary>
    [Required]
    public string ConstructionId { get; set; } = null!;

    /// <summary>
    /// ID of the entry type (*)
    /// </summary>
    public string? EntryTypeId { get; set; }

    /// <summary>
    /// ID of the vendor 
    /// </summary>
    public CreateVendorBaseRequestDto? Vendor { get; set; }

    /// <summary>
    /// ID of the payment type
    /// </summary>
    public string? PaymentTypeId { get; set; }

    /// <summary>
    /// Total amount of the input cost
    /// </summary>
    public long? TotalAmount { get; set; }

    /// <summary>
    /// Description of the input cost
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Collection of input cost items 
    /// </summary>
    [FromForm]
    [ModelBinder(BinderType = typeof(JsonModelBinder))]
    public IEnumerable<CreateInputCostItemDto>? InputCostItems { get; set; }

    /// <summary>
    /// The image of the input cost
    /// </summary>
    [FromForm]
    public IEnumerable<IFormFile>? Images { get; set; }
}

public class CreateInputCostItemDto
{
    /// <summary>
    /// The transaction date of the cost item (*)
    /// </summary>
    [Required]
    public string TransactionDate { get; set; } = null!;

    /// <summary>
    /// The ID of the item (*)
    /// </summary>
    [Required]
    public CreateItemBaseRequestDto Item { get; set; } = null!;

    /// <summary>
    /// The unit of measurement for the item (*)
    /// </summary>
    [Required]
    public string Unit { get; set; } = null!;

    /// <summary>
    /// The quantity of the item
    /// </summary>
    public float Quantity { get; set; }

    /// <summary>
    /// The price per unit
    /// </summary>
    public int Price { get; set; }

    /// <summary>
    /// The tax rate applied
    /// </summary>
    public float? TaxRate { get; set; }

    /// <summary>
    /// The total amount before tax
    /// </summary>
    public long? TotalNonTaxed { get; set; }

    /// <summary>
    /// The total amount after tax
    /// </summary>
    public long? TotalTaxed { get; set; }

    /// <summary>
    /// Additional description or notes
    /// </summary>
    public string? Description { get; set; }
}